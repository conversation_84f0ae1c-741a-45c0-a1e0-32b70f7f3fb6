<template>
	<view class="ux-textarea" :style="[style]">
		<textarea class="ux-textarea__field" 
				:style="[inputStyle]"
				:name="name" 
				:value="value"
				v-model="innerValue"
				:auto-height="autoHeight"
				:placeholder="placeholder" 
				:placeholder-style="placeholderStyle" 
				:placeholder-class="placeholderClass" 
				:maxlength="maxlength" 
				:cursor-spacing="cursorSpacing" 
				:auto-focus="autoFocus" 
				:focus="focus"
				:confirm-hold="confirmHold" 
				:confirm-type="confirmType" 
				:cursor="cursor" 
				:fixed="fixed"
				:selection-start="selectionStart" 
				:selection-end="selectionEnd" 
				:adjust-position="adjustPosition" 
				:hold-keyboard="holdKeyboard"
				:disabled="disabled || readonly" 
				@input="inputChange" 
				@focus="focusChange" 
				@blur="blurChange" 
				@linechange="lineChange"
				@confirm="confirmChange" 
				@keyboardheightchange="keyboardheightChange"/>
				
		<view v-if="showWords" class="ux-textarea__count">
			<text class="ux-textarea__count--text">{{ innerValue.length }}/{{ maxlength }}</text>
		</view>
		
		<!-- #ifdef H5 -->
		<view v-if="disabled || readonly" class="ux-textarea__mask"></view>
		<!-- #endif -->
		
		<view v-if="clearable && focused && innerValue != ''" class="ux-textarea__clearable" @click="onClear">
			<ux-icon type="closecircle-outline" color="#acacac" :size="16"></ux-icon>
		</view>
	</view>
</template>

<script setup lang="ts">

	/**
	* Textarea 文本域
	* @description 扩展支持聚焦高亮、字数统计、自动增高、可清空
	* @demo pages/component/textarea.uvue
	* @tutorial https://www.uxframe.cn/component/textarea.html
	* @property {String} 			theme=[primary|warning|success|error|info]		String | 主题颜色 (默认 primary)
	* @value primary 	主色
	* @value warning 		警告
	* @value success 	成功
	* @value error 		错误
	* @value info 		文本
	* @property {String} 			name											String | 表单的控件名称，作为键值对的一部分与表单(form组件)一同提交
	* @property {String} 			value											String | 输入框的初始内容
	* @property {Boolean}			clearable=[true|false]							Boolean | 是否显示清除控件 (默认 false)
	* @property {Boolean}			showWords=[true|false]							Boolean | 是否显示输入字数统计，只在 type ="text"或type ="textarea"时有效 (默认 false)
	* @property {String} 			placeholder										String | 输入框为空时占位符
	* @property {String} 			placeholderStyle								String | 指定 placeholder 的样式
	* @property {String} 			placeholderClass								String | 指定 placeholder 的样式类
	* @property {Number} 			maxlength										Number | 最大输入长度，设置为 -1 的时候不限制最大长度（默认 140)
	* @property {Any} 				height											Any | 高度（默认 65)
	* @property {Any} 				maxHeight										Any | 高度（默认 450)
	* @property {Boolean} 			autoHeight=[true|false]							Boolean | 是否自动增高，设置auto-height时，style.height不生效（默认 false)
	* @property {Boolean} 			autoFocus=[true|false]							Boolean | 自动获取焦点，与focus属性对比，此属性只会首次生效（默认 false)
	* @property {Boolean}			focus=[true|false]								Boolean | 获取焦点（默认 false)
	* @property {Number} 			cursor											Number | 指定focus时的光标位置（默认 0)
	* @property {Number}			cursorSpacing									Number | 指定光标与键盘的距离，单位 px 。取 input 距离底部的距离和 cursor-spacing 指定的距离的最小值作为光标与键盘的距离（默认 0)
	* @property {String}			confirmType=[return|send|search|next|go|done]	String | 设置键盘右下角按钮的文字（默认 done)
	* @value return 换行
	* @value send 发送
	* @value search 搜索
	* @value next 下一个
	* @value go 前往
	* @value done 完成
	* @property {Boolean}			confirmHold=[true|false]						Boolean | 点击键盘右下角按钮时是否保持键盘不收起（默认 false)
	* @property {Number} 			selectionStart									Number | 光标起始位置，自动聚集时有效，需与selection-end搭配使用（默认 -1)
	* @property {Number} 			selectionEnd									Number | 光标结束位置，自动聚集时有效，需与selection-satrt搭配使用（默认 -1)
	* @property {Boolean} 			adjustPosition=[true|false]						Boolean | 键盘弹起时，是否自动上推页面（默认 true)
	* @property {Boolean} 			holdKeyboard=[true|false]						Boolean | focus时，点击页面的时候不收起键盘（默认 false)
	* @property {Any}				size											Any | 输入框字体的大小 (默认 $ux.Conf.fontSize)
	* @property {String}			color											String | 输入框字体颜色 (默认 $ux.Conf.fontColor)
	* @property {String} 			darkColor=[none|auto|color]						String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	* @value none 不显示
	* @value auto 自动适配深色模式
	* @value color 其他颜色
	* @property {String}			background										String | 输入框背景颜色 (默认 $ux.Conf.backgroundColor)
	* @property {String} 			backgroundDark=[none|auto|color]				String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	* @value none 不显示
	* @value auto 自动适配深色模式
	* @value color 其他颜色
	* @property {String}			disabledColor									String | 输入框禁用颜色
	* @property {String} 			disabledDark=[none|auto|color]					String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	* @value none 不显示
	* @value auto 自动适配深色模式
	* @value color 其他颜色
	* @property {String}			border=[surround|bottom|none]					String | 边框类型 (默认 surround)
	* @value surround 四周边框
	* @value bottom 底部边框
	* @value none 无边框
	* @property {String}			borderColor									String | 输入框边框颜色
	* @property {String} 			borderColorDark=[none|auto|color]			String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	* @value none 不显示
	* @value auto 自动适配深色模式
	* @value color 其他颜色
	* @property {Boolean}			focusBorder=[true|false]						Boolean | 聚焦时显示边框 (默认 true)
	* @property {Boolean}			innerPadding=[true|false]						Boolean | 内部padding (默认 true)
	* @property {Boolean}			fixed=[true|false]								Boolean | 如果textarea是在一个position:fixed的区域，需要显示指定属性fixed为true (默认 false)
	* @property {Boolean}			readonly=[true|false]							Boolean | 是否只读，与disabled不同之处在于disabled会置灰组件，而readonly则不会 (默认 false)
	* @property {Boolean} 			disabled=[true|false]							Boolean | 是否禁用（默认 false)
	* @property {Array}				margin											Array | 边距 [上 右 下 左] [上下 左右] [上下左右]
	* @property {Any}				mt												Any | 距上 单位px
	* @property {Any}				mr												Any | 距右 单位px
	* @property {Any}				mb												Any | 距下 单位px
	* @property {Any}				ml												Any | 距左 单位px
	* @property {Array}				padding											Array | 填充 [上 右 下 左] [上下 左右] [上下左右]
	* @property {Any}				pt												Any | 上内边距 单位px
	* @property {Any}				pr												Any | 右内边距 单位px
	* @property {Any}				pb												Any | 下内边距 单位px
	* @property {Any}				pl												Any | 左内边距 单位px
	* @property {Array}				xstyle											Array<any> | 自定义样式
	* @event {Function} 			clear 											Function |  点击清空时触发
	* @event {Function} 			input 											Function |  当键盘输入时，触发input事件，处理函数可以直接 return 一个字符串，将替换输入框的内容
	* @event {Function} 			focus 											Function |  输入框聚焦时触发
	* @event {Function} 			blur 											Function |  输入框失去焦点时触发
	* @event {Function} 			confirm 										Function |  点击完成按钮时触发
	* @event {Function} 			linechange 										Function |  输入框行数变化时触发
	* @event {Function} 			keyboardheightchange 							Function |  键盘高度发生变化的时候触发此事件
	* <AUTHOR>
	* @date 2024-12-01 14:32:28
	*/
	
	import { ref, computed, watch } from 'vue'
	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	import { useForegroundColor, useFontColor, useFontSize, useThemeColor, useBorderColor, UxThemeType } from '../../libs/use/style.uts'
	
	defineOptions({
		name: 'ux-textarea',
		mixins: [xstyleMixin]
	})
	
	const emit = defineEmits(['update:modelValue', 'input', 'focus', 'blur', 'confirm', 'linechange', 'clear', 'keyboardheightchange'])
	
	const props = defineProps({
		theme: {
			type: String,
			default: 'primary'
		},
		name: {
			type: String,
			default: ''
		},
		value: {
			type: String,
			default: ''
		},
		modelValue: {
			type: String,
			default: ''
		},
		clearable: {
			type: Boolean,
			default: false
		},
		showWords: {
			type: Boolean,
			default: false
		},
		placeholder: {
			type: String,
			default: ''
		},
		placeholderStyle: {
			type: String,
			default: ''
		},
		placeholderClass: {
			type: String,
			default: ''
		},
		maxlength: {
			type: Number,
			default: 140
		},
		height: {
			default: 65
		},
		maxHeight: {
			default: 450
		},
		autoHeight: {
			type: Boolean,
			default: false
		},
		autoFocus: {
			type: Boolean,
			default: false
		},
		focus: {
			type: Boolean,
			default: false
		},
		confirmType: {
			type: String,
			default: 'done'
		},
		confirmHold: {
			type: Boolean,
			default: false
		},
		cursorSpacing: {
			type: Number,
			default: 0
		},
		cursor: {
			type: Number,
			default: 0
		},
		selectionStart: {
			type: Number,
			default: 0
		},
		selectionEnd: {
			type: Number,
			default: 0
		},
		adjustPosition: {
			type: Boolean,
			default: true
		},
		holdKeyboard: {
			type: Boolean,
			default: false
		},
		size: {
			default: 0
		},
		color: {
			type: String,
			default: ''
		},
		darkColor: {
			type: String,
			default: ''
		},
		background: {
			type: String,
			default: ''
		},
		backgroundDark: {
			type: String,
			default: ''
		},
		disabledColor: {
			type: String,
			default: ''
		},
		disabledDark: {
			type: String,
			default: ''
		},
		border: {
			type: String,
			default: 'surround'
		},
		borderColor: {
			type: String,
			default: '#d9d9d9'
		},
		borderColorDark: {
			type: String,
			default: '#111111'
		},
		focusBorder: {
			type: Boolean,
			default: true
		},
		innerPadding: {
			type: Boolean,
			default: true
		},
		fixed: {
			type: Boolean,
			default: false
		},
		readonly: {
			type: Boolean,
			default: false
		},
		disabled: {
			type: Boolean,
			default: false
		},
	})
	
	const innerValue = ref('')
	const focused = ref(false)
	
	const value = computed(() : string => {
		return props.value
	})
	
	const modelValue = computed(() : string => {
		return props.modelValue
	})
	
	const fontColor = computed((): string => {
		return useFontColor(props.color, props.darkColor)
	})
	
	const fontSize = computed(() : number => {
		return useFontSize($ux.Util.getPx(props.size), 1)
	})
	
	const backgroundColor = computed((): string => {
		return useForegroundColor(props.background, props.backgroundDark)
	})
	
	const borderColor = computed((): string => {
		if(!focused.value || !props.focusBorder || props.disabled || props.readonly) {
			return useBorderColor(props.borderColor, props.borderColorDark) 
		} else {
			return useThemeColor(props.theme as UxThemeType) 
		}
	})
	
	const fontDisabledColor = computed(() : string => {
		return $ux.Color.getLightColor(fontColor.value, 40)
	})
	
	const backgroundDisabledColor = computed(() : string => {
		if(props.disabledColor != '') {
			return useForegroundColor(props.disabledColor, props.disabledDark)
		} else {
			return  $ux.Color.getLightColor(backgroundColor.value, 40)
		}
	})
	
	const style = computed(() => {
		let css = {}
		
		if(!props.autoHeight) {
			css['height'] = $ux.Util.addUnit($ux.Util.getPx(props.height) + (props.showWords? 22  : 0))
		} else {
			css['max-height'] = $ux.Util.addUnit(props.maxHeight)
		}
		
		css['border-radius'] = $ux.Util.addUnit(4)
		css['background-color'] = props.disabled ? backgroundDisabledColor.value : backgroundColor.value
		
		if(props.border == 'bottom') {
			css['border-bottom'] = `1px solid ${css['background-color']}`
		} else if(props.border == 'surround') {
			css['border'] = `1px solid ${css['background-color']}`
		}
		
		if(focused.value && props.focusBorder) {
			if(props.border == 'surround') {
				css['border'] = `1px solid ${borderColor.value}`
			} else if(props.border == 'bottom') {
				css['border-bottom'] = `1px solid ${borderColor.value}`
			}
		}
		
		css = $ux.Util.xStyle(css, props.margin, props.mt, props.mr, props.mb, props.ml, props.padding, props.pt, props.pr, props.pb, props.pl)
		
		return css
	})
	
	const inputStyle = computed(() => {
		let css = {}
		
		if(!props.autoHeight) {
			css['height'] = $ux.Util.addUnit(props.height)
		} else {
			css['max-height'] = $ux.Util.addUnit(props.maxHeight)
		}
		
		css['color'] = props.disabled ? fontDisabledColor.value : fontColor.value
		css['font-size'] = $ux.Util.addUnit(fontSize.value)
		css['line-height'] = $ux.Util.addUnit(fontSize.value + 5)
		
		// #ifdef WEB
		css['cursor'] = props.disabled? 'not-allowed' : 'pointer'
		// #endif
		
		if(props.innerPadding) {
			css['padding'] = `${$ux.Util.addUnit(5)} ${$ux.Util.addUnit(8)}`
		}
		
		return css
	})
	
	watch(value, () => {
		if(modelValue.value == '') {
			innerValue.value = value.value
		}
	}, {immediate: true})
	
	watch(modelValue, () => {
		if(value.value == '') {
			innerValue.value = modelValue.value
		}
	}, {immediate: true})
	
	function inputChange(e: InputEvent) {
		innerValue.value = e.detail.value
		
		emit('update:modelValue', innerValue.value)
		emit('input', innerValue.value)
	}
	
	function focusChange(e: TextareaFocusEvent) {
		focused.value = true
		innerValue.value = e.detail.value
		
		emit('focus', innerValue.value)
	}
	
	function blurChange(e: TextareaBlurEvent) {
		innerValue.value = e.detail.value
			
		setTimeout(function() {
			focused.value = false
			emit('blur', innerValue.value)
		}, 100);
	}
	
	function lineChange(e: TextareaLineChangeEvent) {
		emit('linechange', e)
	}
	
	function confirmChange(e: InputConfirmEvent) {
		innerValue.value = e.detail.value
		
		emit('confirm', innerValue.value)
	}
	
	function keyboardheightChange(e: InputKeyboardHeightChangeEvent) {
		emit('keyboardheightchange', e)
		
		focused.value = e.detail.height as number != 0
	}
	
	function onClear(e: MouseEvent) {
		innerValue.value = ''
		
		emit('clear', innerValue.value)
		emit('update:modelValue', innerValue.value)
		emit('input', innerValue.value)
		
		e.stopPropagation()
	}
</script>

<style lang="scss" scoped>
	/* #ifdef H5 */
	uni-view {
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: stretch;
		align-content: stretch;
		justify-content: flex-start;
		box-sizing: border-box;
		min-height: 0px;
		min-width: 0px;
		overflow: hidden;
	}
	/* #endif */
	
	.ux-textarea {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		position: relative;
		
		&__field {
			width: 100%;
		}
		
		&__count {
			width: 100%;
			height: 20px;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: flex-end;
			
			&--text {
				margin-right: 5px;
				font-size: 12px;
				color: #a9a9a9;
			}
		}

		&__clearable {
			position: absolute;
			top: 40%;
			right: 5px;
			width: 18px;
			height: 18px;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
		}
		
		&__mask {
			position: absolute;
			width: 100%;
			height: 100%;
		}
		
	}
</style>