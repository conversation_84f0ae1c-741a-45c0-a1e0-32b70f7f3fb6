<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" xmlns:tools="http://schemas.android.com/tools"
	package="uts.sdk.modules.uxPlus">
	
	<uses-permission android:name="android.permission.INTERNET" />
	<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
	<uses-permission android:name="android.permission.CALL_PHONE" />
	<uses-permission android:name="android.permission.VIBRATE" />
	<uses-permission android:name="android.permission.READ_CLIPBOARD_IN_BACKGROUND" />
	<uses-permission android:name="android.permission.WRITE_CLIPBOARD_IN_BACKGROUND" />
	
	<application>
		<meta-data android:name="EasyGoClient" android:value="true"/>
	</application>
</manifest>
