<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/qr?title=Qr"></Mobile>

# Qr
> 组件类型：UxQrComponentPublicInstance

支持修改尺寸、背景色、前景色、增加Logo

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| text | String |  | 二维码内容 |
| size | Any | 128 | 二维码尺寸 |
| background | String | #fff | 背景色 |
| foreground | String | #000 | 前景色 |
| img | String |  | 二维码图标，仅支持网络图片 |
| imgSize | Any | 45 | 二维码图标尺寸 |
| border | Any | 4 | 边框 |
| showLoading | Boolean | true | 显示加载 |
| loadingText | String | 加载中... | 加载文案 |
| delay | Number | 0 | 延迟生成单位ms |
| margin | Array |  | 边距[上右下左][上下左右][上下左右] |
| mt | Any |  | 距上单位px |
| mr | Any |  | 距右单位px |
| mb | Any |  | 距下单位px |
| ml | Any |  | 距左单位px |
| padding | Array |  | 填充[上右下左][上下左右][上下左右] |
| pt | Any |  | 上内边距单位px |
| pr | Any |  | 右内边距单位px |
| pb | Any |  | 下内边距单位px |
| pl | Any |  | 左内边距单位px |
| xstyle | Array |  | 自定义样式 |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| click | 被点击时触发 |  |
| change | 调用make生成二维码时触发 |  |
| error | 发生错误时触发 |  |
  
  