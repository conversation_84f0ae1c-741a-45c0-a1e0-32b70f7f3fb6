import * as $ux from '@/uni_modules/ux-frame'
import theme from '@/theme.json'

export type ColorType = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8

/**
 * 颜色
 * @param level 颜色等级
 * @example 
 * |-------|---------|---------|---------|---------|---------|---------|---------|---------|
 * | Level |    1    |    2    |    3    |    4    |    5    |    6    |    7    |    8    |
 * |-------|---------|---------|---------|---------|---------|---------|---------|---------|
 * | Light | #000000 | #111111 | #333333 | #888888 | #c5c5c5 | #eeeeee | #f5f5f5 | #ffffff |
 * |-------|---------|---------|---------|---------|---------|---------|---------|---------|
 * | Dark  | #ffffff | #f5f5f5 | #eeeeee | #c5c5c5 | #888888 | #333333 | #111111 | #000000 |
 * |-------|---------|---------|---------|---------|---------|---------|---------|---------|
 */
export const useColor = (level: ColorType) : string => {
	let t = theme as UTSJSONObject
	let color = t.getString('light.color' + level) ?? ''
	let darkColor = t.getString('dark.color' + level) ?? ''
	return $ux.useColor(color, darkColor)
}

/**
 * 反转颜色
 * @param level 颜色等级
 * @example 
 * |-------|---------|---------|---------|---------|---------|---------|---------|---------|
 * | Level |    1    |    2    |    3    |    4    |    5    |    6    |    7    |    8    |
 * |-------|---------|---------|---------|---------|---------|---------|---------|---------|
 * | Light | #000000 | #111111 | #333333 | #888888 | #c5c5c5 | #eeeeee | #f5f5f5 | #ffffff |
 * |-------|---------|---------|---------|---------|---------|---------|---------|---------|
 * | Dark  | #ffffff | #f5f5f5 | #eeeeee | #c5c5c5 | #888888 | #333333 | #111111 | #000000 |
 * |-------|---------|---------|---------|---------|---------|---------|---------|---------|
 */
export const useInverseColor = (level: ColorType) : string => {
	let t = theme as UTSJSONObject
	let color = t.getString('dark.color' + level) ?? ''
	let darkColor = t.getString('light.color' + level) ?? ''
	return $ux.useColor(color, darkColor)
}

/**
 * Light颜色
 * @param level 颜色等级
 * @example 
 * |-------|---------|---------|---------|---------|---------|---------|---------|---------|
 * | Level |    1    |    2    |    3    |    4    |    5    |    6    |    7    |    8    |
 * |-------|---------|---------|---------|---------|---------|---------|---------|---------|
 * | Light | #000000 | #111111 | #333333 | #888888 | #c5c5c5 | #eeeeee | #f5f5f5 | #ffffff |
 * |-------|---------|---------|---------|---------|---------|---------|---------|---------|
 * | Dark  | #ffffff | #f5f5f5 | #eeeeee | #c5c5c5 | #888888 | #333333 | #111111 | #000000 |
 * |-------|---------|---------|---------|---------|---------|---------|---------|---------|
 */
export const getLightColor = (level: ColorType) : string => {
	let t = theme as UTSJSONObject
	return t.getString('light.color' + level) ?? ''
}

/**
 * Dark颜色
 * @param level 颜色等级
 * @example 
 * |-------|---------|---------|---------|---------|---------|---------|---------|---------|
 * | Level |    1    |    2    |    3    |    4    |    5    |    6    |    7    |    8    |
 * |-------|---------|---------|---------|---------|---------|---------|---------|---------|
 * | Light | #000000 | #111111 | #333333 | #888888 | #c5c5c5 | #eeeeee | #f5f5f5 | #ffffff |
 * |-------|---------|---------|---------|---------|---------|---------|---------|---------|
 * | Dark  | #ffffff | #f5f5f5 | #eeeeee | #c5c5c5 | #888888 | #333333 | #111111 | #000000 |
 * |-------|---------|---------|---------|---------|---------|---------|---------|---------|
 */
export const getDarkColor = (level: ColorType) : string => {
	let t = theme as UTSJSONObject
	return t.getString('dark.color' + level) ?? ''
}

/**
 * 自定义颜色
 * @param color 浅色
 * @param darkColor 森色
 */
export const getColor = (color: string, darkColor: string) : string => {
	return $ux.useColor(color, darkColor)
}

/**
 * 主色调
 */
export const usePrimaryColor = () : string => {
	let t = theme as UTSJSONObject
	let color = t.getString('light.primaryColor') ?? ''
	let darkColor = t.getString('dark.primaryColor') ?? ''
	return $ux.useColor(color, darkColor)
}

/**
 * 文字颜色
 * @param dark 深色
 */
export const getFontColor = (dark: boolean) : string => {
	let t = theme as UTSJSONObject
	let color = t.getString('light.fontColor') ?? ''
	let darkColor = t.getString('dark.fontColor') ?? ''
	return dark ? darkColor: color
}

/**
 * 文字颜色
 */
export const useFontColor = () : string => {
	let t = theme as UTSJSONObject
	let color = t.getString('light.fontColor') ?? ''
	let darkColor = t.getString('dark.fontColor') ?? ''
	return $ux.useColor(color, darkColor)
}

/**
 * 背景色
 * @param dark 深色
 */
export const getBackgroundColor = (dark: boolean) : string => {
	let t = theme as UTSJSONObject
	let color = t.getString('light.backgroundColor') ?? ''
	let darkColor = t.getString('dark.backgroundColor') ?? ''
	return dark ? darkColor: color
}

/**
 * 背景色
 */
export const useBackgroundColor = () : string => {
	let t = theme as UTSJSONObject
	let color = t.getString('light.backgroundColor') ?? ''
	let darkColor = t.getString('dark.backgroundColor') ?? ''
	return $ux.useColor(color, darkColor)
}

/**
 * 前景色
 * @param dark 深色
 */
export const getForegroundColor = (dark: boolean) : string => {
	let t = theme as UTSJSONObject
	let color = t.getString('light.foregroundColor') ?? ''
	let darkColor = t.getString('dark.foregroundColor') ?? ''
	return dark ? darkColor: color
}

/**
 * 前景色
 */
export const useForegroundColor = () : string => {
	let t = theme as UTSJSONObject
	let color = t.getString('light.foregroundColor') ?? ''
	let darkColor = t.getString('dark.foregroundColor') ?? ''
	return $ux.useColor(color, darkColor)
}

/**
 * tabbar背景色
 * @param dark 深色
 */
export const getTabbarBackgroundColor = (dark: boolean) : string => {
	let t = theme as UTSJSONObject
	let color = t.getString('light.tabbarBackgroundColor') ?? ''
	let darkColor = t.getString('dark.tabbarBackgroundColor') ?? ''
	return dark ? darkColor: color
}

/**
 * tabbar背景色
 */
export const useTabbarBackgroundColor = () : string => {
	let t = theme as UTSJSONObject
	let color = t.getString('light.tabbarBackgroundColor') ?? ''
	let darkColor = t.getString('dark.tabbarBackgroundColor') ?? ''
	return $ux.useColor(color, darkColor)
}

/**
 * tabbar文本色
 * @param dark 深色
 */
export const getTabbarTextColor = (dark: boolean) : string => {
	let t = theme as UTSJSONObject
	let color = t.getString('light.tabbarTextColor') ?? ''
	let darkColor = t.getString('dark.tabbarTextColor') ?? ''
	return dark ? darkColor: color
}

/**
 * tabbar文本色
 */
export const useTabbarTextColor = () : string => {
	let t = theme as UTSJSONObject
	let color = t.getString('light.tabbarTextColor') ?? ''
	let darkColor = t.getString('dark.tabbarTextColor') ?? ''
	return $ux.useColor(color, darkColor)
}