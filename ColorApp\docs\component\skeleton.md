<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/skeleton?title=Skeleton"></Mobile>

# Skeleton
> 组件类型：UxSkeletonComponentPublicInstance

此组件仅是占位显示，需要辉光效果请用ux-shimmer插件包裹

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| rows | Array | '100%' | 段落占位图行数,值是宽度 |
| height | Array | [18] | 段落高度 |
| spacing | Any | 5 | 段落间隔 |
| avatar | Boolean | 32 | 显示头像 |
| avatarWidth | Any | 32 | 头像宽度 |
| avatarHeight | Any | 32 | 头像高度 |
| [shape](#shape) | String | square | 头像占位图的形状 |
| background | String | #e9e9e9 | 段落背景色 |
| margin | Array |  | 边距[上右下左][上下左右][上下左右] |
| mt | Any |  | 距上单位px |
| mr | Any |  | 距右单位px |
| mb | Any |  | 距下单位px |
| ml | Any |  | 距左单位px |
| padding | Array |  | 填充[上右下左][上下左右][上下左右] |
| pt | Any |  | 上内边距单位px |
| pr | Any |  | 右内边距单位px |
| pb | Any |  | 下内边距单位px |
| pl | Any |  | 左内边距单位px |
| xstyle | Array |  | 自定义样式 |

### [shape](#shape)

| 值   | 说明 |
|:------:|:----:|
| circle圆形
 |  |
| square方形
 |  |

