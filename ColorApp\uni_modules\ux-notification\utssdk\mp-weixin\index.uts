import { CreateNotificationProgress, CancelNotificationProgress, FinishNotificationProgress, UxCreateNotificationProgressOptions, UxFinishNotificationProgressOptions } from '../interface.uts'

/**
 * 创建显示进度的通知栏消息
 * @param UxCreateNotificationProgressOptions options 参数
 */
export const createNotificationProgress : CreateNotificationProgress = function (options : UxCreateNotificationProgressOptions) : void {
	
}

/**
 * 取消显示进度的通知栏消息
 */
export const cancelNotificationProgress : CancelNotificationProgress = function () : void {
	
}

/**
 * 完成显示进度的通知栏消息
 * @param UxFinishNotificationProgressOptions options 参数
 */
export const finishNotificationProgress : FinishNotificationProgress = function (options : UxFinishNotificationProgressOptions) : void {
	
}