
export type UxOpenFileManagerOptions = {
	mimeTypes ?: UxMimeType[]
	multiple ?: boolean
	// #ifdef APP-IOS
	success ?: (files : any) => void
	// #endif
	// #ifndef APP-IOS
	success ?: (files : UxFile[]) => void
	// #endif
	fail ?: (err : UxFilesFail) => void
}

export type UxSearchFilesOptions = {
	mimeTypes ?: UxMimeType[]
	ignore ?: string[],
	// #ifdef APP-IOS
	success ?: (file : any) => void
	// #endif
	// #ifndef APP-IOS
	success ?: (file : UxFile) => void
	// #endif
	fail ?: (err : UxFilesFail) => void
}

export type UxMimeType = 'image' | 'video' | 'audio' | 'txt' | 'pdf' | 'doc' | 'xls' | 'ppt' | 'zip' | 'other'

export type UxFile = {
	name : string,
	path : string,
	extension : string,
	blob ?: any | null,
	size : number,
	date : string
}

export type UxFilesErrorCode = -1 | -2;

export interface UxFilesFail extends IUniError {
	errCode : UxFilesErrorCode
}

export type OpenFileManager = (options : UxOpenFileManagerOptions) => void

export type SearchFiles = (options : UxSearchFilesOptions) => void

export type StopSearch = () => void