<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/collapse-item?title=Collapse-item"></Mobile>

# Collapse-item
> 组件类型：UxCollapseItemComponentPublicInstance

支持配置标题颜色、大小、加粗、背景色。折叠面板子项 需搭配 `ux-collapse` 使用

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| title | String |  | 标题 |
| color | String |  | 标题文字颜色 |
| [darkColor](#darkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| size | Any | `$ux.Conf.fontSize` | 标题文字大小 |
| bold | Boolean | false | 标题文字加粗 |
| titleBackground | String |  | 标题背景颜色 |
| [titleBackgroundDark](#titleBackgroundDark) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| background | String |  | 背景颜色 |
| [backgroundDark](#backgroundDark) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| [open](#open) | Boolean | false | 默认是否打开 |
| [border](#border) | Boolean | true | 显示下边框 |
| disabled | Boolean | false | 是否禁用 |

### [darkColor](#darkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [titleBackgroundDark](#titleBackgroundDark)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [backgroundDark](#backgroundDark)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [open](#open)

| 值   | 说明 |
|:------:|:----:|
| true
 |  |
| false
 |  |

### [border](#border)

| 值   | 说明 |
|:------:|:----:|
| true
 |  |
| false
 |  |

