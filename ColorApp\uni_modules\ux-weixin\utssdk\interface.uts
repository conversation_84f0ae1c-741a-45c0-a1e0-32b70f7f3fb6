/**
 * 注册
 */
export declare function register(options : UxRegisterOptions): void

/**
 * 登录
 */
export declare function login(options : UxLoginOptions): void

/**
 * 分享
 */
export declare function share(options : UxShareOptions): void

/**
 * 请求支付
 */
export declare function requestPayment(options : UxPayOptions): void

/**
 * 打开小程序
 */
export declare function openMiniProgram(options : UxShareOptions): void

/**
 * 打开微信
 */
export declare function openApp(): void

/**
 * 微信app是否安装
 */
export declare function isInstalled(): boolean

/**
 * 错误码
 * [9010000, 'unregister']
 * [9010001, 'register fail']
 * [9010002, 'share fail']
 * [9010003, 'login fail']
 * [9010004, 'auth denied']
 * [9010005, 'user cancel']
 * [9010006, 'unsupport']
 * [9010007, 'uninstall weixin']
 * [9010008, 'unknow error']
 * [9010009, 'pay fail']
 */
export type MyErrorCode = 9010000 | 9010001 | 9010002 | 9010003 | 9010004 | 9010005 | 9010006 | 9010007 | 9010008 | 9010009;

/**
 * 错误回调参数
 */
export interface UxWeixinFail extends IUniError {
  errCode : MyErrorCode
};

/**
 * 成功回调
 */
export type UxWeixinSuccess = {
	code : string,
	msg : any,
}

/**
 * 注册
 */
export type UxRegisterOptions = {
	/**
	 * 微信appid
	 */
	appid : string
	/**
	 * ios 通用链接
	 */
	universalLink : string
	/**
	* 接口调用成功的回调
	*/
	success ?: (res : UxWeixinSuccess) => void
	/**
	* 接口调用失败的回调函数
	*/
	fail ?: (res : UxWeixinFail) => void
}

/**
 * 登录
 */
export type UxLoginOptions = {
	/**
	* 接口调用成功的回调
	*/
	success ?: (res : UxWeixinSuccess) => void
	/**
	* 接口调用失败的回调函数
	*/
	fail ?: (res : UxWeixinFail) => void
}

/**
 * 分享
 */
export type UxShareOptions = {
	/**
	 * 分享服务提供商（即weixin|qq|sinaweibo|ali|douyin|google）
	 */
	provider? : string
	/**
	 * 分享形式，如图文、纯文字、纯图片、音乐、视频、小程序等
	 * 0	图文			weixin、sinaweibo
	 * 1	纯文字		weixin、qq
	 * 2	纯图片		weixin、qq
	 * 3	音乐			weixin、qq
	 * 4	视频			weixin、sinaweibo
	 * 5	小程序		weixin
	 */
	type ?: number
	/**
	 * 分享内容的标题
	 */
	title ?: string
	/**
	 * 分享场景，provider 为 weixin 时必选
	 * WXSceneSession		分享到聊天界面
	 * WXSceneTimeline		分享到朋友圈
	 * WXSceneFavorite		分享到微信收藏
	 */
	scene ?: string
	/**
	 * 分享内容的摘要，type 为 1 时必选
	 */
	summary ?: string
	/**
	 * 跳转链接，type 为 0 时必选
	 */
	href ?: string
	/**
	 * 图片地址，type为0时，推荐使用小于20Kb的图片，type 为 0、2、5 时必选
	 */
	imageUrl ?: string
	/**
	 * 音视频地址，type 为 3、4 时必选
	 */
	mediaUrl ?: string
	/**
	 * 分享小程序必要参数，type 为 5 时必选
	 */
	miniProgram ?: UxMiniProgram
	/**
	 * 是否启用拉起客服功能，仅微信支持
	 */
	openCustomerServiceChat ?: boolean
	/**
	 * 企业id, openCustomerServiceChat = true 时必填
	 */
	corpid ?: string
	/**
	 * 客服的页面路径, openCustomerServiceChat = true 时必填
	 */
	customerUrl ?: string
	/**
	* 接口调用成功的回调
	*/
	success ?: (res : UxWeixinSuccess) => void
	/**
	* 接口调用失败的回调函数
	*/
	fail ?: (res : UxWeixinFail) => void
}

export type UxMiniProgram = {
	/**
	 * 微信小程序原始id
	 */
	id : string
	/**
	 * 点击链接进入的页面
	 */
	path : string
	/**
	 * 微信小程序版本类型，可取值： 0-正式版； 1-测试版； 2-体验版。 默认值为0。
	 */
	type : number
	/**
	 * 兼容低版本的网页链接
	 */
	webUrl ?: string
}

export type UxPayOptions = {
	/**
	 * 商户号
	 */
	mchid: string
	/**
	 * 预付单
	 */
	prepayId: string
	/**
	 * 签名
	 */
	sign: string
	/**
	 * 签名算法，应与后台下单时的值一致
	 */
	signType: string
	/**
	 * 随机字符串，长度为32个字符以下
	 */
	nonceStr: string
	/**
	 * 时间戳从1970年1月1日至今的秒数，即当前的时间
	 */
	timeStamp: string
	/**
	* 接口调用成功的回调
	*/
	success ?: (res : UxWeixinSuccess) => void
	/**
	* 接口调用失败的回调函数
	*/
	fail ?: (res : UxWeixinFail) => void
}