import { UxRegisterOptions, UxLoginOptions, UxShareOptions, UxPayOptions, UxWeixinFail, UxWeixinSuccess } from '../interface.uts'
import { MyFailImpl } from '../unierror';
import { UIApplication, UIImage } from "UIKit";
import { URL } from "Foundation";
import { Int32 } from 'Swift';
import Util from './Util.uts';

@UTSiOS.keyword('private') var appid : string = ''
@UTSiOS.keyword('private') var loginOptions : UxLoginOptions | null = null
@UTSiOS.keyword('private') var shareOptions : UxShareOptions | null = null

/**
* 注册
* @param {UxRegisterOptions} options
* @tutorial https://www.uxframe.cn/api/weixin.html#register
*/
export function register(options : UxRegisterOptions): void {
	appid = options.appid
	WXApi.registerApp(options.appid, universalLink = options.universalLink)
	
	options.success?.({
		code: '0',
		msg: 'register success'
	} as UxWeixinSuccess)
}

/**
* 登录
* @param {UxLoginOptions} options
* @tutorial https://www.uxframe.cn/api/weixin.html#login
*/
export function login(options : UxLoginOptions): void {
	if (!WXApi.isWXAppInstalled()) {
		options.fail?.(new MyFailImpl(9010007))
		return
	}
	
	loginOptions = options
	
	let req = new SendAuthReq()
	req.state = `ux-weixin-${new Date().getTime()}`;
	req.scope = 'snsapi_userinfo'
	
	DispatchQueue.main.async(execute = () : void => {
		WXApi.send(req)
	})
}

/**
* 分享
* @param {UxShareOptions} options
* @tutorial https://www.uxframe.cn/api/weixin.html#share
*/
export function share(options : UxShareOptions): void {
	if (!WXApi.isWXAppInstalled()) {
		options.fail?.(new MyFailImpl(9010007))
		return
	}
	
	shareOptions = options
	
	switch (options.type) {
		case 0:
			shareWebpage(options)
			break;
		case 1:
			shareText(options)
			break;
		case 2:
			shareImage(options)
			break;
		case 3:
			shareMusic(options)
			break;
		case 4:
			shareVideo(options)
			break;
		case 5:
			shareMp(options)
			break;
		default:
			if (options.openCustomerServiceChat ?? false) {
				openCustomerService(options)
			}
	
			break;
	}
}

/**
* 图文
*/
function shareWebpage(options : UxShareOptions) {
	let webpage = new WXWebpageObject()
	webpage.webpageUrl = options.href ?? ''
	
	let msg = new WXMediaMessage()
	msg.title = options.title ?? ''
	msg.description = options.summary ?? ''
	msg.mediaObject = webpage
	
	Util.getImage(options.imageUrl, 100, 100, (image : UIImage | null) => {
		if (image != null) {
			msg.setThumbImage(image!)
		}
		
		let req = new SendMessageToWXReq()
		req.bText = false
		req.scene = getScene(options.scene ?? '')
		req.message = msg;
		
		DispatchQueue.main.async(execute = () : void => {
			WXApi.send(req)
		})
	})
}
	
/**
* 纯文本
*/
function shareText(options : UxShareOptions) {
	let req = new SendMessageToWXReq()
	req.bText = true
	req.text = options.title ?? ''
	req.scene = getScene(options.scene ?? '')
	
	DispatchQueue.main.async(execute = () : void => {
		WXApi.send(req)
	})
}

/**
 * 纯图片
 */
function shareImage(options : UxShareOptions) {
	if(options.imageUrl == null || options.imageUrl == '') {
		return
	}
	
	let req = new SendMessageToWXReq()
	req.bText = false
	req.scene = getScene(options.scene ?? '')
	
	if(Util.isNetworkImage(options.imageUrl!)) {
		Util.getImage(options.imageUrl, 100, 100, (image : UIImage | null) => {
			if (image != null) {
				let obj = new WXImageObject()
				obj.imageData = image!.jpegData(compressionQuality = 0.7)!
				
				let msg = new WXMediaMessage()
				msg.mediaObject = obj
				msg.thumbData = image!.jpegData(compressionQuality = 0.5)!
				
				req.message = msg
				
				DispatchQueue.main.async(execute = () : void => {
					WXApi.send(req)
				})
			}
		})
	} else {
		let obj = new WXImageObject()
		obj.imageData = UIImage(contentsOfFile = options.imageUrl!)!.jpegData(compressionQuality = 0.7)!
		
		let msg = new WXMediaMessage()
		msg.mediaObject = obj
		
		req.message = msg
		
		DispatchQueue.main.async(execute = () : void => {
			WXApi.send(req)
		})
	}
}

/**
 * 视频
 */
function shareVideo(options : UxShareOptions) {
	let video = new WXVideoObject()
	video.videoUrl = options.mediaUrl ?? '';
	video.videoLowBandUrl = options.mediaUrl ?? '';
	
	let msg = new WXMediaMessage()
	msg.title = options.title ?? ''
	msg.description = options.summary ?? ''
	msg.mediaObject = video
	
	Util.getImage(options.imageUrl, 100, 100, (image : UIImage | null) => {
		if (image != null) {
			msg.setThumbImage(image!)
		}
		
		let req = new SendMessageToWXReq()
		req.bText = false
		req.scene = getScene(options.scene ?? '')
		req.message = msg;
		
		DispatchQueue.main.async(execute = () : void => {
			WXApi.send(req)
		})
	})
}

/**
 * 音乐
 */
function shareMusic(options : UxShareOptions) {
	let music = new WXMusicObject()
	music.musicUrl = options.mediaUrl ?? ''
	music.musicLowBandUrl = options.mediaUrl ?? ''
	
	let msg = new WXMediaMessage();
	msg.title = options.title ?? ''
	msg.description = options.summary ?? ''
	msg.mediaObject = music
	
	Util.getImage(options.imageUrl, 100, 100, (image : UIImage | null) => {
		if (image != null) {
			msg.setThumbImage(image!)
		}
		
		let req = new SendMessageToWXReq()
		req.bText = false
		req.scene = getScene(options.scene ?? '')
		req.message = msg;
		
		DispatchQueue.main.async(execute = () : void => {
			WXApi.send(req)
		})
	})
}

/**
 * 小程序
 */
function shareMp(options : UxShareOptions) {
	if (options.miniProgram == null) {
		return
	}
	
	let miniProgramObj = new WXMiniProgramObject();
	miniProgramObj.webpageUrl = options.miniProgram!.webUrl ?? ''
	// 正式版:0，测试版:1，体验版:2
	if (options.miniProgram!.type == 0) {
		miniProgramObj.miniProgramType = WXMiniProgramType.release;
	} else if (options.miniProgram!.type == 1) {
		miniProgramObj.miniProgramType = WXMiniProgramType.test;
	} else if (options.miniProgram!.type == 2) {
		miniProgramObj.miniProgramType = WXMiniProgramType.preview;
	}
	miniProgramObj.userName = options.miniProgram!.id
	miniProgramObj.path = options.miniProgram!.path
	
	Util.getImage(options.imageUrl, 500, 400, (image : UIImage | null) => {
		
		miniProgramObj.hdImageData = image!.jpegData(compressionQuality = 0.7)!
		
		let msg = new WXMediaMessage();
		msg.title = options.title ?? ''
		msg.description = options.summary ?? ''
		msg.mediaObject = miniProgramObj
		
		if (image != null) {
			msg.setThumbImage(image!)
		}
		
		let req = new SendMessageToWXReq()
		req.bText = false
		req.scene = Int32(bitPattern = WXSceneSession.rawValue)
		req.message = msg;
		
		DispatchQueue.main.async(execute = () : void => {
			WXApi.send(req)
		})
	})
}

/**
 * 微信客服
 */
function openCustomerService(options : UxShareOptions) {
	let req = new WXOpenCustomerServiceReq();
	req.corpid = options.corpid ?? ''
	req.url = options.customerUrl ?? ''
	
	DispatchQueue.main.async(execute = () : void => {
		WXApi.send(req)
	})
}

/**
* 打开微信小程序
* @param {UxShareOptions} options
* @tutorial https://www.uxframe.cn/api/weixin.html#openMiniProgram
*/
export function openMiniProgram(options : UxShareOptions) {
	
	let req = new WXLaunchMiniProgramReq();
	req.userName = options.miniProgram!.id
	req.path = options.miniProgram!.path
	
	// 正式版:0，测试版:1，体验版:2
	if (options.miniProgram!.type == 0) {
		req.miniProgramType = WXMiniProgramType.release;
	} else if (options.miniProgram!.type == 1) {
		req.miniProgramType = WXMiniProgramType.test;
	} else if (options.miniProgram!.type == 2) {
		req.miniProgramType = WXMiniProgramType.preview;
	}
	
	DispatchQueue.main.async(execute = () : void => {
		WXApi.send(req)
	})
}
	
/**
* 请求支付
* @param {UxPayOptions} options
* @tutorial https://www.uxframe.cn/api/weixin.html#requestPayment
*/
export function requestPayment(options : UxPayOptions): void {
	// let req = new PayReq();
	
	// req.appId = appid
	// req.partnerId = options.mchid
	// req.prepayId = options.prepayId
	// req.nonceStr = options.nonceStr
	// req.package = 'Sign=WXPay'
	// req.sign = options.sign
	// req.signType = options.signType
	// req.timeStamp = options.timeStamp
	
	// DispatchQueue.main.async(execute = () : void => {
	// 	WXApi.send(req)
	// })
}

/**
* 微信app是否安装
* @tutorial https://www.uxframe.cn/api/weixin.html#isInstalled
*/
export function isInstalled(): boolean {
	return WXApi.isWXAppInstalled()
}

/**
* 打开微信app
* @tutorial https://www.uxframe.cn/api/weixin.html#openApp
*/
export function openApp(): void {
	if(UTSiOS.available("iOS 10.0, *")) {
		if (WXApi.isWXAppInstalled()) {
			UIApplication.shared.open(new URL(string = 'weixin://')!, options = new Map(), completionHandler = null)
		}
	} else {
		console.error('【ux-weixin】iOS10.0及以上版本支持');
	}
}

function getScene(scene : string) : Int32 {
	if (scene == 'WXSceneSession') {
		return Int32(bitPattern = WXSceneSession.rawValue)
	} else if (scene == 'WXSceneTimeline') {
		return Int32(bitPattern = WXSceneTimeline.rawValue)
	} else if (scene == 'WXSceneFavorite') {
		return Int32(bitPattern = WXSceneFavorite.rawValue)
	} else {
		return Int32(bitPattern = WXSceneSession.rawValue)
	}
}
	
/**
 * 回调
 */
export class SnSignInWxPluginClass implements UTSiOSHookProxy, WXApiDelegate {
	// uts 插件创建时的回调。
	onCreate() {
	}
	// 通过 url scheme 方式唤起 app 时的回调函数。(iOS9 之前的系统回调此方法，iOS9 之后的系统请使用 applicationOpenURLOptions)
	applicationHandleOpenURL(application : UIApplication | null, url : URL | null) : boolean {
		return WXApi.handleOpen(url!, delegate = this)
	}

	// 通过 url scheme 方式唤起 app 时的回调函数。
	applicationOpenURLOptions(app : UIApplication | null, url : URL, options : Map<UIApplication.OpenURLOptionsKey, any> | null = null) : boolean {
		return WXApi.handleOpen(url, delegate = this)
	}

	// 当应用程序接收到与用户活动相关的数据时调用此方法，例如，当用户使用 Universal Link 唤起应用时。
	applicationContinueUserActivityRestorationHandler(application : UIApplication | null, userActivity : NSUserActivity | null, restorationHandler : ((res : [any] | null) => void) | null = null) : boolean {
		return WXApi.handleOpenUniversalLink(userActivity!, delegate = this)
	}

	onReq(req : BaseReq) {

	}

	onResp(resp : BaseResp) {
		if (resp.isKind(of = SendAuthResp.self)) {
			let res = resp as SendAuthResp | null
			if (res != null && res!.code != null && res!.code != '') {
				loginOptions?.success?.({
					code: res!.code,
					msg: 'auth success'
				} as UxWeixinSuccess)
			} else {
				loginOptions?.fail?.(new MyFailImpl(9010005))
			}
		} else {
			// 其他Resp
		}
	}

}