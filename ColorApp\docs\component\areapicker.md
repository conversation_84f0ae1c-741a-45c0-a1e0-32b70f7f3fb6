<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/areapicker?title=Areapicker"></Mobile>

# Areapicker
> 组件类型：UxAreapickerComponentPublicInstance



## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| title | String |  | 选择器的标题 |
| labelKey | String |  | label的键值默认为label |
| valueKey | String |  | value的键值默认为value |
| activeColor | String |  | 激活选中的颜色默认#3c9cff |
| load | Function |  | 查询下级数据的钩子 |
| defaultLoad | Function |  | 默认回显数据的钩子 |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| change | 选择完毕时触发 |  |
| close | 点击关闭时触发 |  |
  
  