<template>
	<view class="ux-table__body" :style="bodyStyle(null, tables.length)">
		<view class="ux-table__wrap" :style="[cellStyle(col, i), col.style]" v-for="(col, i) in columns" :key="i">
			<text class="ux-table__text" :style="[cellTextStyle, col.textStyle]">{{ totals(col) }}</text>
		</view>
	</view>
</template>

<script setup lang="ts">
	/**
	 * Table Total
	 * @demo pages/component/table.uvue
	 * @tutorial https://www.uxframe.cn/component/table.html
	 * <AUTHOR>
	 * @date 2024-12-07 01:08:19
	 */
	
	import { PropType, computed } from 'vue'
	import { $ux } from '../../index'
	import { useFontSize, useFontColor, useForegroundColor } from '../../libs/use/style.uts'
	import { UxTableColumn } from '../../libs/types/types.uts'
	
	defineOptions({
		name: 'ux-table-total'
	})
	
	const props = defineProps({
		fixed: {
			type: Boolean,
			default: false
		},
		tables: {
			type: Array as PropType<UTSJSONObject[]>,
			default: () : UxTableColumn[] => [] as UxTableColumn[]
		},
		columns: {
			type: Array as PropType<UxTableColumn[]>,
			default: () : UxTableColumn[] => [] as UxTableColumn[]
		},
		tableWidth: {
			type: Number,
			default: 0
		},
		totalWidth: {
			type: Number,
			default: 0
		},
		cellHeight: {
			default: 40
		},
		color: {
			type: String,
			default: "#333333"
		},
		fontSize: {
			default: 0
		},
		background: {
			type: String,
			default: "#fafafa"
		},
		highlight: {
			type: String,
			default: "#ecf5ff"
		},
		borderColor: {
			type: String,
			default: "#e8e8e8"
		},
		border: {
			type: Boolean,
			default: false
		},
		ripple: {
			type: Boolean,
			default: false
		},
		rippleColor: {
			type: String,
			default: "#f5f5f5"
		},
	})
	
	const selectKey = '__SELECT__'
	
	const fontSize = computed((): number => {
		return useFontSize($ux.Util.getPx(props.fontSize), 1)
	})
	
	const color = computed((): string => {
		return useFontColor(props.color, 'auto')
	})
	
	const background = computed((): string => {
		return useForegroundColor(props.background, 'auto')
	})
	
	const rippleColor = computed((): string => {
		return useForegroundColor(props.rippleColor, 'auto')
	})
	
	const highlight = computed((): string => {
		return useForegroundColor(props.highlight, 'auto')
	})
	
	const cellTextStyle = computed(() => {
		let css = {}
		
		css['fontSize'] = $ux.Util.addUnit(fontSize.value)
		css['color'] = color.value
		
		return css
	})
	
	function getCellWidth(col: UxTableColumn) : string {
		let w = col.width ?? '20%'
		if (w == '') {
			return 'auto'
		}
		
		if (w.indexOf('%') != -1) {
			w = `${parseFloat(w.replace('%', '')) * props.tableWidth / 100}px`
		}
		
		return w
	}
	
	function bodyStyle(cell: UTSJSONObject | null, i: number) {
		let css = {}
		
		if(!props.fixed && props.totalWidth > 0) {
			css['width'] = $ux.Util.addUnit(props.totalWidth)
			css['min-width'] = $ux.Util.addUnit(props.totalWidth)
		}
		
		css['height'] = $ux.Util.addUnit(props.cellHeight)
		
		if(cell != null && cell[selectKey] == selectKey) {
			css['background-color'] = highlight.value
		} else {
			if((i+1)%2 == 0 && props.ripple){
				css['background-color'] = rippleColor.value
			} else {
				css['background-color'] = background.value
			}
		}
		
		return css
	}
	
	function cellStyle(col: UxTableColumn, i: number) {
		let cellWidth = getCellWidth(col)
		
		let css = {}
		
		if(cellWidth == 'auto') {
			css['flex'] = '1'
		} else {
			css['width'] = cellWidth
			css['min-width'] = cellWidth
		}
		
		css['height'] = $ux.Util.addUnit(props.cellHeight)
		
		if(props.border) {
			css['border-top'] = `1px solid ${props.borderColor}`
			css['border-right'] = `1px solid ${props.borderColor}`
		}
		
		if(col.align == 'left') {
			css['align-items'] = 'flex-start'
		} else if(col.align == 'right') {
			css['align-items'] = 'flex-end'
		} else {
			css['align-items'] = 'center'
		}
		
		return css
	}
	
	function totals(col: UxTableColumn): string {
		if(col.total ?? false) {
			let all = 0
			props.tables.forEach((e: UTSJSONObject) => {
				try{
					all += parseInt(e[col.key]!.toString())
				}catch(err){
					try{
						all += parseFloat(e[col.key]!.toString())
					}catch(err){}
				}
			})
			
			return `合计：${all}`
		}
		
		return ''
	}
	
	
</script>

<style lang="scss" scoped>
	/* #ifdef H5 */
	uni-view {
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: stretch;
		align-content: stretch;
		justify-content: flex-start;
		box-sizing: border-box;
		min-height: 0px;
		min-width: 0px;
		overflow: hidden;
	}
	/* #endif */
	
	.ux-table {
		
		&__body {
			display: flex;
			flex-direction: row;
			flex-wrap: nowrap;
			/* #ifdef WEB */
			cursor: pointer;
			/* #endif */
		}
		
		&__body--dark {
			display: flex;
			flex-direction: row;
			flex-wrap: nowrap;
			/* #ifdef WEB */
			cursor: pointer;
			/* #endif */
		}
		
		&__hover {
			background-color: #f0f0f0 !important;
		}
		
		&__hover--dark {
			background-color: #666666 !important;
		}
		
		/* #ifdef WEB */
		&__body:hover {
			background-color: #f0f0f0 !important;
		}
		
		&__body--dark:hover {
			background-color: #666666 !important;
		}
		/* #endif */
		
		&__wrap {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
		}
		
		&__text {
			text-align: center;
			padding: 4px 8px;
			/* #ifdef WEB */
			word-break: break-all;
			/* #endif */
		}
	}
</style>