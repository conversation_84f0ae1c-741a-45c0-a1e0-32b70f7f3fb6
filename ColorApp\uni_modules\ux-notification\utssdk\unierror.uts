
import { UxNotificationCallback } from "./interface.uts"

// #ifndef UNI-APP-X
class UniError {
	
}
// #endif

export class UxNotificationSuccessCallbackImpl extends UniError implements UxNotificationCallback {

	constructor(errMsg : string) {
		super();

		this.errSubject = 'ux-notification'
		this.errCode = 0
		this.errMsg = errMsg
	}
}

export class UxNotificationErrorCallbackImpl extends UniError implements UxNotificationCallback {

	constructor(errMsg : string) {
		super();

		this.errSubject = 'ux-notification'
		this.errCode = 1
		this.errMsg = errMsg
	}
}