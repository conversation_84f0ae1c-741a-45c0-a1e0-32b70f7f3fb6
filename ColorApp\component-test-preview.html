<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>uni-app x 组件动态测试预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            border-left: 4px solid #007AFF;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 122, 255, 0.15);
        }
        
        .feature-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .feature-card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .feature-list {
            list-style: none;
        }
        
        .feature-list li {
            color: #555;
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        
        .feature-list li:before {
            content: "✓";
            color: #007AFF;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
        
        .demo-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .demo-section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .component-categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .category-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            border: 2px solid #e9ecef;
            transition: border-color 0.3s ease;
        }
        
        .category-card:hover {
            border-color: #007AFF;
        }
        
        .category-card h4 {
            color: #007AFF;
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        
        .component-list {
            list-style: none;
        }
        
        .component-list li {
            color: #666;
            margin-bottom: 5px;
            font-size: 0.9em;
        }
        
        .usage-section {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 30px;
        }
        
        .usage-section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .step {
            display: flex;
            align-items: flex-start;
            margin-bottom: 25px;
            padding-bottom: 25px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .step:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        
        .step-number {
            background: #007AFF;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 20px;
            flex-shrink: 0;
        }
        
        .step-content h4 {
            color: #333;
            margin-bottom: 8px;
            font-size: 1.1em;
        }
        
        .step-content p {
            color: #666;
            line-height: 1.6;
        }
        
        .code-block {
            background: #f1f3f4;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
            color: #333;
            overflow-x: auto;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            border-left: 4px solid #007AFF;
        }
        
        .highlight h3 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .highlight p {
            color: #555;
            line-height: 1.6;
        }
        
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            border-top: 1px solid #e9ecef;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 12px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .content {
                padding: 20px;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .component-categories {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 uni-app x 组件动态测试</h1>
            <p>强大的组件动态加载与测试平台</p>
        </div>
        
        <div class="content">
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🎯 动态组件加载</h3>
                    <p>支持运行时动态加载和渲染项目中的各种组件，无需重新编译。</p>
                    <ul class="feature-list">
                        <li>实时组件切换</li>
                        <li>属性动态配置</li>
                        <li>事件监听记录</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>🔧 属性实时配置</h3>
                    <p>提供可视化的属性配置界面，实时修改组件属性并查看效果。</p>
                    <ul class="feature-list">
                        <li>字符串/数字输入</li>
                        <li>布尔值开关</li>
                        <li>实时预览更新</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>📊 事件监控</h3>
                    <p>完整的事件日志系统，记录组件触发的所有事件和数据。</p>
                    <ul class="feature-list">
                        <li>事件时间戳</li>
                        <li>事件类型标识</li>
                        <li>事件数据详情</li>
                    </ul>
                </div>
            </div>
            
            <div class="demo-section">
                <h2>📦 支持的组件分类</h2>
                <div class="component-categories">
                    <div class="category-card">
                        <h4>基础组件</h4>
                        <ul class="component-list">
                            <li>ux-button - 按钮组件</li>
                            <li>ux-icon - 图标组件</li>
                            <li>ux-text - 文本组件</li>
                        </ul>
                    </div>
                    
                    <div class="category-card">
                        <h4>表单组件</h4>
                        <ul class="component-list">
                            <li>ux-input - 输入框组件</li>
                            <li>ux-switch - 开关组件</li>
                            <li>ux-slider - 滑块组件</li>
                        </ul>
                    </div>
                    
                    <div class="category-card">
                        <h4>布局组件</h4>
                        <ul class="component-list">
                            <li>ux-card - 卡片组件</li>
                            <li>ux-divider - 分割线组件</li>
                        </ul>
                    </div>
                    
                    <div class="category-card">
                        <h4>反馈组件</h4>
                        <ul class="component-list">
                            <li>ux-loading - 加载组件</li>
                            <li>ux-badge - 徽章组件</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="usage-section">
                <h2>🎮 使用指南</h2>
                
                <div class="step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h4>启动项目</h4>
                        <p>在 HBuilderX 中运行项目，或使用命令行启动开发服务器。</p>
                        <div class="code-block">npm run dev:app</div>
                    </div>
                </div>
                
                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h4>访问测试页面</h4>
                        <p>从首页点击"组件动态测试"进入测试页面，或直接导航到测试页面。</p>
                        <div class="code-block">uni.navigateTo({ url: '/pages/component-test' })</div>
                    </div>
                </div>
                
                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h4>选择组件分类</h4>
                        <p>在页面顶部选择要测试的组件分类，如基础组件、表单组件等。</p>
                    </div>
                </div>
                
                <div class="step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h4>加载组件</h4>
                        <p>从组件列表中选择要测试的组件，点击"加载"按钮进行动态加载。</p>
                    </div>
                </div>
                
                <div class="step">
                    <div class="step-number">5</div>
                    <div class="step-content">
                        <h4>配置属性</h4>
                        <p>在预览区域下方的属性配置面板中，实时修改组件的各种属性。</p>
                    </div>
                </div>
                
                <div class="step">
                    <div class="step-number">6</div>
                    <div class="step-content">
                        <h4>监控事件</h4>
                        <p>与组件交互时，所有事件都会被记录在事件日志中，便于调试和分析。</p>
                    </div>
                </div>
            </div>
            
            <div class="highlight">
                <h3>💡 技术特点</h3>
                <p>
                    该测试页面使用了 uni-app x 的 <code>&lt;component :is="componentName"&gt;</code> 动态组件特性，
                    结合响应式数据绑定和事件监听，实现了完全动态的组件加载和测试功能。
                    支持属性的实时配置和事件的完整监控，为组件开发和调试提供了强大的工具。
                </p>
            </div>
        </div>
        
        <div class="footer">
            <p>© 2025 uni-app x 组件动态测试平台 - 让组件测试更简单</p>
        </div>
    </div>
</body>
</html>