<template>
	<!-- #ifndef MP -->
	<sticky-header :id="myId" :padding="padding">
		<slot></slot>
	</sticky-header>
	<!-- #endif -->
	<!-- #ifdef MP -->
	<ux-placeholder v-if="fixed" :height="height"></ux-placeholder>
	<view :id="myId" class="ux-sticky-header" :style="style">
		<slot></slot>
	</view>
	<!-- #endif -->
</template>

<script setup>
	
	/**
	 * StickyHeader 吸顶布局容器, 当一个容器视图设置多个sticky-header时，后一个sticky-header会停靠在前一个sticky-header的末尾处
	 * @demo pages/component/sticky.uvue
	 * @tutorial https://www.uxframe.cn/component/sticky.html
	 * @property {String}		headerId					String | id
	 * @property {Number[]}		padding						Number[] | 长度为 4 的数组，按 top、right、bottom、left 顺序指定内边距
	 * <AUTHOR>
	 * @date 2024-12-05 15:20:35
	 */
	
	import { $ux } from '../../index'

	defineOptions({
		name: 'ux-sticky-header'
	})

	const props = defineProps({
		headerId: {
			type: String,
			default: ''
		},
		padding: {
			type: Array as PropType<Array<number>>,
			default: (): number[] => {
				return [0,0,0,0] as number[]
			}
		}
	})
	
	const myId = computed(() => {
		return props.headerId == '' ? `ux-sticky-header-${$ux.Random.uuid()}` : props.headerId
	})
	
	// #ifdef MP
	const instance = getCurrentInstance()?.proxy
	const top = ref(0)
	const left = ref(0)
	const width = ref(0)
	const height = ref(0)
	const fixed = ref(false)
	const childIndex = ref(0)
	
	const pushPinnedHeader = inject('pushPinnedHeader', false) as boolean
	const index = inject('index', 0) as number
	const scrollTop = inject('scrollTop', ref(0)) as Ref<number>
	
	const offset = computed(() => {
		return pushPinnedHeader ? 0 : (index * height.value + childIndex.value * height.value)
	})
	
	const style = computed(() => {
		let css = new Map<string, any>()
		
		if(fixed.value) {
			css.set('position', fixed.value ? 'fixed' : 'relative')
			css.set('top', `${scrollTop.value + offset.value}px`)
			css.set('left', `${left.value}px`)
			css.set('width', `${width.value}px`)
			css.set('height', `${height.value}px`)
			css.set('z-index', 1)
		} else {
			css.set('position', 'relative')
			css.set('z-index', 0)
		}
		
		if(props.padding.length > 0) {
			css.set('padding', props.padding.map(e => $ux.Util.addUnit(e)))
		}
		
		return css
	})
	
	function uptStatus(y: number, i: number) {
		childIndex.value = i
		
		let v = top.value - (scrollTop.value + offset.value)
		fixed.value = Math.max(y, 0) > Math.max(v, 0)
	}
	
	function register() {
		$ux.Util.$dispatch(instance! as ComponentPublicInstance, 'ux-sticky-section', 'register', instance, top.value)
	}
	
	onMounted(() => {
		let f = async () => {
			const rect = await $ux.Util.getBoundingClientRect(`#${myId.value}`, instance)
			top.value = rect?.top ?? 0
			left.value = rect?.left ?? 0
			width.value = rect?.width ?? 0
			height.value = rect?.height ?? 0
			register()
		}
		
		setTimeout(() => {
			f()
		}, 100);
	})
	
	defineExpose({
		uptStatus
	})
	// #endif
</script>

<style lang="scss">
	.ux-sticky-header {
		top: 0;
		overflow: hidden;
	}
</style>