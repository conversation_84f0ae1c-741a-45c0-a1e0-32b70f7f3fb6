// #ifndef UNI-APP-X
import { onUnmounted } from 'vue'
// #endif

/**
 * 监听尺寸变化
 * @param callback 回调函数
 */
export const useResize = (ele : UniElement | null, callback : () => void) => {
	// #ifdef APP-ANDROID
	let resizeObserver = null as UniResizeObserver | null
	
	onPageShow(() => {
		if (resizeObserver == null) {
			resizeObserver = new UniResizeObserver((entries : Array<UniResizeObserverEntry>) => {
				entries.forEach(entry => {
					if(entry.target == ele) {
						callback()
					}
				})
			})
		}
		
		if(ele != null) {
			resizeObserver?.observe(ele)
		}
	})
	onPageHide(() => {
		if(ele != null) {
			resizeObserver?.unobserve(ele)
		}
	})
	// #endif
	
	// #ifdef WEB
	let resizeTimeout = 0
	let handleResizeThrottled = () => {
		if (resizeTimeout) {
			clearTimeout(resizeTimeout);
		}

		resizeTimeout = setTimeout(() => {
			callback()
		}, 100);
	}

	window.addEventListener('resize', handleResizeThrottled);

	onUnmounted(() => {
		window.removeEventListener('resize', handleResizeThrottled);
	})
	// #endif
	
	// #ifdef MP
	
	// #endif
}