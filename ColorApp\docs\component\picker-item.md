<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/picker-item?title=Picker-item"></Mobile>

# Picker-item
> 组件类型：UxPickerItemComponentPublicInstance

内部使用

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| text | String |  | 文本 |
| size | Any |  | 字体大小 |
| color | String |  | 字体颜色 |
| selectColor | String |  | 选择颜色 |
| index | Number |  | 下标 |
| selectedIndex | Number |  | 已选择下标 |

