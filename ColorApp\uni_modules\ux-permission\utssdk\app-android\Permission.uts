import { UxRequestPermissionNameOptions, UxRequestPermissionTipsOptions, UxRequestPermissionsOptions, UxOpenAppAuthorizeSettingOptions } from "../interface";

import Intent from 'android.content.Intent';
import Settings from 'android.provider.Settings';
import Uri from 'android.net.Uri';
import Build from 'android.os.Build';
import PackageManager from 'android.content.pm.PackageManager';
import RelativeLayout from 'android.widget.RelativeLayout';
import LinearLayout from 'android.widget.LinearLayout';
import Color from 'android.graphics.Color';
import TextView from 'android.widget.TextView';
import ViewGroup from 'android.view.ViewGroup';
import Activity from 'android.app.Activity';
import HashMap from 'java.util.HashMap';
import AnimationUtils from 'android.view.animation.AnimationUtils';
import Html from 'android.text.Html';
import View from 'android.view.View';

var permissionTipsView : View | null = null
var permissionTips : HashMap<String, String> = new HashMap<String, String>()

export default class Permission {
	
	getPermissions(name ?: UxRequestPermissionNameOptions, permissions ?: string[]): string[] {
		let _permissions = [] as string[]
		
		if(permissions != null) {
			_permissions = permissions
		}
		
		if(name == 'network') {
			_permissions.push('android.permission.INTERNET')
			_permissions.push('android.permission.ACCESS_NETWORK_STATE')
		} else if(name == 'location') {
			_permissions.push('android.permission.ACCESS_FINE_LOCATION')
		} else if(name == 'push') {
			_permissions.push('android.permission.POST_NOTIFICATIONS')
		} else if(name == 'camera') {
			_permissions.push('android.permission.CAMERA')
		} else if(name == 'photo') {
			_permissions.push('android.permission.READ_EXTERNAL_STORAGE')
			_permissions.push('android.permission.WRITE_EXTERNAL_STORAGE')
			_permissions.push('android.permission.CAMERA')
		} else if(name == 'bluetooth') {
			_permissions.push('android.permission.BLUETOOTH')
			_permissions.push('android.permission.BLUETOOTH_ADMIN')
			_permissions.push('android.permission.ACCESS_FINE_LOCATION')
		} else if(name == 'microphone') {
			_permissions.push('android.permission.RECORD_AUDIO')
		} else if(name == 'calendar') {
			_permissions.push('android.permission.READ_CALENDAR')
			_permissions.push('android.permission.WRITE_CALENDAR')
		} else if(name == 'contact') {
			_permissions.push('android.permission.READ_CONTACTS')
			_permissions.push('android.permission.WRITE_CALENDAR')
		} else if(name == 'sms') {
			_permissions.push('android.permission.READ_SMS')
			_permissions.push('android.permission.SEND_SMS')
			_permissions.push('android.permission.RECEIVE_SMS')
		} else if(name == 'phone') {
			_permissions.push('android.permission.CALL_PHONE')
		} else if(name == 'phone_state') {
			_permissions.push('android.permission.READ_PHONE_STATE')
		}
		
		if(name != null && _permissions.length == 0) {
			_permissions = [name]
		}
		
		return _permissions
	}
	
	openAppAuthorizeSetting(options : UxOpenAppAuthorizeSettingOptions) {
		let activity = UTSAndroid.getUniActivity()!
		
		if(options == 'notification') {
			let packageName = activity.getPackageName();
			let intent = new Intent('android.settings.APP_NOTIFICATION_SETTINGS');
			if (Build.VERSION.SDK_INT >= 26) {
				intent.putExtra('android.provider.extra.APP_PACKAGE', packageName);
			} else if (Build.VERSION.SDK_INT >= 23) {
				let appInfo = activity.getPackageManager().getApplicationInfo(packageName, PackageManager.GET_ACTIVITIES);
				intent.putExtra('android.provider.extra.APP_PACKAGE', packageName);
				intent.putExtra("app_package", packageName);
				intent.putExtra("app_uid", appInfo.uid);
			} else {
				let uri = Uri.fromParts("package", packageName, null)
				intent.setData(uri);
			}
			intent.putExtra('android.content.Intent.setFlags', Intent.FLAG_ACTIVITY_NEW_TASK);
			activity.startActivity(intent);
		} else {
			let intent = new Intent();
			intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
			
			let uri = Uri.fromParts("package", activity.getPackageName(), null);
			intent.setData(uri);
			activity.startActivity(intent);
		}
	}

	setRequestPermissionTips(options : UxRequestPermissionTipsOptions[]) {
		permissionTips.clear()

		options.forEach(e => {
			if (e.permission != '' && e.content != '') {
				let permissions = this.getPermissions(e.permission, null)
				permissions.forEach(permission => {
					permissionTips.put(permission, e.content)
				})
			}
		})
	}

	requestPermissions(options : UxRequestPermissionsOptions) {
		
		let permissions = this.getPermissions(options.name, options.permissions)
		
		this.hideTips()
		this.showTips(permissions)
		
		UTSAndroid.requestSystemPermission(UTSAndroid.getUniActivity()!, permissions, function(allRight:boolean, grantedList:string[]){
			if(allRight){
				// 用户同意了全部权限
				options.complete?.(allRight, grantedList, false)
			}else{
				// 用户仅同意了 grantedList中的权限
			}
			
			this.hideTips()
		},function(doNotAskAgain:boolean, grantedList:string[]){
			// 用户拒绝了部分权限，仅允许了grantedList中的权限
			if(doNotAskAgain){
				// 用户拒绝了权限，并且选择不再询问
			}
			
			options.complete?.(false, grantedList, doNotAskAgain)
			this.hideTips()
		})
	}
	
	showTips(permissions: string[]) {
		if (permissions.length > 0) {
			let activity = UTSAndroid.getUniActivity()!
			permissionTipsView = this.createPermissionWindow(activity, permissions);
			if (permissionTipsView != null) {
				(activity.findViewById(android.R.id.content) as ViewGroup).addView(permissionTipsView!)
			}
		}
	}
	
	hideTips() {
		if (permissionTipsView != null && permissionTipsView!.getParent() != null) {
			permissionTipsView!.setAnimation(null);
			((permissionTipsView!.getParent()) as ViewGroup).removeView(permissionTipsView)
		}
	}

	createPermissionWindow(activity : Activity, permissions : Array<string>) : ViewGroup | null {
		
		let permission_background = UTSAndroid.getAppContext()!.resources.getIdentifier('permission_background', "drawable", UTSAndroid.getAppContext()!.packageName)
		let popupwindow_enter = UTSAndroid.getAppContext()!.resources.getIdentifier('popupwindow_enter', "anim", UTSAndroid.getAppContext()!.packageName)
		
		let rootView = new RelativeLayout(activity);
		rootView.setBackgroundColor(Color.TRANSPARENT);
		
		let backgroundView = new LinearLayout(activity);
		backgroundView.setPadding(30, 0, 30, 30);
		backgroundView.setOrientation(1)
		backgroundView.setBackgroundResource(permission_background);

		let permissionTipsList : Array<string> = new Array<string>()
		for (let i = 0; i < permissions.length; i++) {
			let p = permissions[i]
			if (permissionTips.containsKey(p) && permissionTipsList.indexOf(permissionTips.get(p)) == -1) {
				permissionTipsList.push(permissionTips.get(p)!)
			}
		}
		
		for (let i = 0; i < permissionTipsList.length; i++) {
			let p = permissionTipsList[i]
			
			let text = new TextView(activity);
			text.setText(Html.fromHtml(p, Html.FROM_HTML_SEPARATOR_LINE_BREAK_HEADING))
			text.setPadding(0, 30, 0, 0)
			text.setTextSize((5 * this.getScale()).toFloat())
			text.setTextColor(Color.BLACK)
			backgroundView.addView(text)
		}
		
		if (backgroundView.getChildCount() == 0) {
			return null;
		}

		let layout = new RelativeLayout.LayoutParams(-1, -2)
		layout.topMargin = (UTSAndroid.getStatusBarHeight() * this.getScale()).toInt();
		layout.leftMargin = 30;
		layout.rightMargin = 30;
		layout.bottomMargin = 30;

		rootView.addView(backgroundView, layout)
		rootView.setAnimation(AnimationUtils.loadAnimation(activity, popupwindow_enter));

		return rootView;
	}

	removePermissionWindow() {
		if (permissionTipsView != null) {
			if (permissionTipsView!.getParent() != null) {
				permissionTipsView!.setAnimation(null);
				((permissionTipsView!.getParent()) as ViewGroup).removeView(permissionTipsView)
			}

			permissionTipsView = null
		}
	}

	@Suppress("DEPRECATION")
	getScale() : Float {
		if (UTSAndroid.getUniActivity() != null) {
			return UTSAndroid.getUniActivity()!.resources.displayMetrics.scaledDensity
		}

		return (0 as number).toFloat();
	}
}