{"version": "1.0", "pageId": "page1", "header": {"title": "用户信息", "showBackButton": true}, "dataSources": {"ds1": {"id": "", "type": "api", "options": {"endpoint": "/api/user", "method": "POST", "params": {}}, "data": {}, "autoLoad": true}}, "router": {"path": "/login"}, "pageLayout": {"id": "mainLayout", "component": "flex-layout", "props": {"flexDirection": "column", "justifyContent": "center", "alignItems": "center", "class": "bg-primary"}}, "components": [{"id": "formContainer", "component": "view", "props": {"class": "text-white"}, "dataSource": "ds1", "children": [{"id": "usernameField", "component": "input", "props": {"type": "text", "label": "用户名", "dataBind": "username", "required": true}}, {"id": "passwordField", "component": "input", "props": {"type": "password", "label": "密码", "dataBind": "password", "required": true}}, {"id": "submitBtn", "component": "button", "props": {"color": "primary"}, "value": {"zh-CN": "登录", "en-US": "<PERSON><PERSON>"}, "events": {"click": [{"type": "validateForm", "target": "formContainer"}, {"type": "apiRequest", "options": {"endpoint": "/api/user", "method": "POST", "params": {}}, "payload": "ds1", "onSuccess": {"type": "redirect", "path": "/dashboard"}}]}}]}]}