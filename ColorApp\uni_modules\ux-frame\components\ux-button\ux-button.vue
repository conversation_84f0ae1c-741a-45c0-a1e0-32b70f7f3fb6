<template>
	<view ref="uxButtonRef" class="ux-button" :style="[style, xstyle]" 
		:hover-class="disabled?'none':'ux-button__hover'" 
		:hover-start-time="hoverStartTime"
		:hover-stay-time="hoverStayTime"
		@click="click"
		@longpress="longpress">
		<template v-if="loading">
			<ux-loading :type="loadingType" :color="loadingColor" :text-size="loadingSize" >{{ loadingText }}</ux-loading>
		</template>
		<template v-else>
			<ux-icon v-if="icon != ''" :type="icon" :size="iconSize" :color="iconColor == '' ? color : iconColor"
				:custom-family="customFamily"></ux-icon>
			<text v-if="text != ''" :style="[textStyle]">{{ text }}</text>
		</template>
	</view>
</template>

<script setup lang="ts">
	/**
	 * button 按钮
	 * @description 支持多种主题按钮、镂空按钮，可配置加载中状态，可开启节流等功能
	 * @tutorial https://www.uxframe.cn/component/button.html
	 * @property {String}			theme=[text|info|primary|success|warning|error]	String | 按钮类型 (默认 info)
	 * @value text			文字
	 * @value info   		默认
	 * @value primary   	主要
	 * @value success  		成功
	 * @value warning   	警告
	 * @value error  		错误
	 * @property {String}			text								String | 按钮文字
	 * @property {String}			color								String | 按钮文字颜色
	 * @property {String} 			darkColor=[none|auto|color]			String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Any}				width								Any | 按钮宽度
	 * @property {Any}				height								Any | 按钮高度
	 * @property {Any}				size								Any | 按钮文字大小 (默认 $ux.Conf.fontSize)
	 * @property {String}			sizeType=[normal|small|big]			String | 字体大小类型（默认 small ）
	 * @value normal 正常
	 * @value small 较小
	 * @value big 较大
	 * @property {Boolean}			bold = [true|false]					Boolean | 按钮文字加粗 (默认 false)
	 * @property {String}			background							String | 按钮背景颜色
	 * @property {String} 			backgroundDark=[none|auto|color]	String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Array}			colors											String[] | 背景色 多个渐变
	 * @property {String} 			gradientDirection=[top|bottom|left|right]		String | 渐变色方向 (默认 bottom)
	 * @value top 向上
	 * @value bottom 向下
	 * @value left 向左
	 * @value right 向右
	 * @property {Boolean}			border = [true|false]				Boolean | 显示边框 (默认 true)
	 * @property {Any}				corner								Any | 圆角 (默认 5)
	 * @property {Boolean}			plain = [true|false]				Boolean | 是否镂空 (默认 false)
	 * @property {Boolean}			loading = [true|false]				Boolean | 显示加载中 (默认 false)
	 * @property {String} 			loadingType = [circular|spinner] 	String | loading类型 (默认 spinner)
	 * @value circular 圆环
	 * @value spinner  菊花
	 * @property {String}			loadingText							String | 加载中文字 (默认 加载中...)
	 * @property {Any}				loadingSize							Any | 加载中文字大小 (默认 $ux.Conf.fontSize)
	 * @property {String}			loadingColor						String | 加载中颜色 (默认 $ux.Conf.placeholderColor)
	 * @property {String}			icon								String | 按钮图标
	 * @property {Any}				iconSize							Any | 图标大小 (默认 $ux.Conf.fontSize)
	 * @property {String}			iconColor							String | 图标文字颜色
	 * @property {String} 			iconColorDark=[none|auto|color]		xString | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {String}			customFamily						String | 自定义字体family
	 * @property {String}			direction=[row|column]				String | 布局方向 (默认 row)
	 * @value row 水平
	 * @value column 垂直
	 * @property {String}			path								String | 点击跳转的页面路径
	 * @property {Number}			throttleTime						Number | 节流，一定时间内只能触发一次，单位毫秒 (默认 0)
	 * @property {Number}			hoverStartTime						Number | 点击态出现时间，单位毫秒 (默认 0)
	 * @property {Number}			hoverStayTime						Number | 点击态保留时间，单位毫秒 (默认 150)
	 * @property {Boolean}			hover = [true|false]				Boolean | 是否显示点击态 (默认 true)
	 * @property {Array}			margin								Array | 边距 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}				mt									Any | 距上 单位px
	 * @property {Any}				mr									Any | 距右 单位px
	 * @property {Any}				mb									Any | 距下 单位px
	 * @property {Any}				ml									Any | 距左 单位px
	 * @property {Array}			padding								Array | 填充 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}				pt									Any | 上内边距 单位px
	 * @property {Any}				pr									Any | 右内边距 单位px
	 * @property {Any}				pb									Any | 下内边距 单位px
	 * @property {Any}				pl									Any | 左内边距 单位px
	 * @property {Array}			xstyle								Array<any> | 自定义样式
	 * @property {Boolean}			stopPropagation = [true|false]		Boolean | 阻止向上冒泡 (默认 false)
	 * @property {Boolean}			disabled = [true|false]				Boolean | 是否禁用 (默认 false)
	 * @event {Function}			click								Function | 非禁止并且非加载中，点击触发
	 * @event {Function}			longpress							Function | 非禁止并且非加载中，长按触发
	 * @event {Function}			touchstart							Function | 非禁止手指触摸动作开始时触发
	 * @event {Function}			touchend							Function | 非禁止手指触摸动作结束时触发
	 * @event {Function}			touchmove							Function | 非禁止手指触摸动作移动时触发
	 * @event {Function}			touchcancel							Function | 手指触摸动作被打断时触发
	 * <AUTHOR>
	 * @date 2024-12-01 01:40:08
	 */
	
	import { ref, computed, PropType } from 'vue'
	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	import { useThemeColor, useForegroundColor, useFontColor, useFontSize, UxThemeType } from '../../libs/use/style.uts'
	
	defineOptions({
		name: 'ux-button',
		mixins: [xstyleMixin]
	})
	
	const emit = defineEmits(['click', 'longpress'])
	
	const props = defineProps({
		theme: {
			type: String,
			default: 'info'
		},
		text: {
			type: String,
			default: ''
		},
		color: {
			type: String,
			default: ''
		},
		darkColor: {
			type: String,
			default: ''
		},
		width: {
			type: String,
			default: ''
		},
		height: {
			type: String,
			default: ''
		},
		size: {
			default: 0
		},
		sizeType: {
			type: String,
			default: 'small'
		},
		bold: {
			type: Boolean,
			default: false
		},
		background: {
			type: String,
			default: ''
		},
		backgroundDark: {
			type: String,
			default: ''
		},
		colors: {
			type: Array as PropType<Array<string>>,
			default: ():string[] => {
				return [] as string[]
			}
		},
		gradientDirection: {
			type: String,
			default: 'bottom'
		},
		border: {
			type: Boolean,
			default: true
		},
		corner: {
			default: 5
		},
		plain: {
			type: Boolean,
			default: false
		},
		loading: {
			type: Boolean,
			default: false
		},
		loadingType: {
			type: String,
			default: 'spinner'
		},
		loadingText: {
			type: String,
			default: '加载中...'
		},
		loadingSize: {
			default: 0
		},
		loadingColor: {
			type: String,
			default: ''
		},
		icon: {
			type: String,
			default: ''
		},
		iconSize: {
			default: 0
		},
		iconColor: {
			type: String,
			default: ''
		},
		iconColorDark: {
			type: String,
			default: ''
		},
		customFamily: {
			type: String,
			default: ''
		},
		direction: {
			type: String,
			default: 'row'
		},
		path: {
			type: String,
			default: ''
		},
		throttleTime: {
			type: Number,
			default: 0
		},
		hoverStartTime: {
			type: Number,
			default: 0
		},
		hoverStayTime: {
			type: Number,
			default: 150
		},
		hover: {
			type: Boolean,
			default: true
		},
		stopPropagation: {
			type: Boolean,
			default: false
		}
	})
	
	const theme = computed(():string => {
		return props.theme
	})
	
	const fontSize = computed(():number => {
		let type = props.sizeType == 'big' ? 2 : (props.sizeType == 'small' ? 1 : 0)
		return useFontSize($ux.Util.getPx(props.size), type)
	})
	
	const fontColor = computed(():string => {
		return useFontColor(props.color, props.darkColor)
	})
	
	const iconColor = computed(():string => {
		return useFontColor(props.iconColor, props.iconColorDark)
	})
	
	const infoColor = computed(():string => {
		return $ux.Conf.darkMode.value ? $ux.Conf.infoColor.dark.value : $ux.Conf.infoColor.color.value
	})

	const borderColor = computed(():string => {
		return $ux.Conf.darkMode.value ? $ux.Conf.borderColor.dark.value : $ux.Conf.borderColor.color.value
	})
	
	const color = computed(() : string => {
		if(props.plain) {
			if (theme.value == 'text') {
				return fontColor.value
			} else  {
				return useThemeColor(theme.value as UxThemeType)
			}
		} else {
			if (theme.value == 'text') {
				return fontColor.value
			} else if (theme.value == 'info') {
				return props.color == '' ? infoColor.value : props.color
			} else {
				return props.color == '' ? '#ffffff' : props.color
			}
		}
	})
	
	const backgroundColor = computed(() : string => {
		
		if(props.background != '') {
			return useForegroundColor(props.background, props.backgroundDark)
		}
		
		if(props.plain == true) {
			return 'transparent'
		} else {
			if (theme.value == 'text') {
				return 'transparent'
			} else if (theme.value == 'info') {
				return 'transparent'
			} else {
				return useThemeColor(theme.value as UxThemeType)
			}
		}
	})
	
	const disabledColor = computed(():string => {
		if(props.plain) {
			return $ux.Color.getRgba(color.value, 0.5)
		} else {
			return $ux.Color.getRgba(backgroundColor.value, 0.5)
		}
	})
	
	const border = computed(() : string => {
		if (theme.value == 'text') {
			return 'transparent'
		} else if (theme.value == 'info') {
			return borderColor.value
		} else {
			return useThemeColor(theme.value as UxThemeType)
		}
	})
	
	const style = computed(() => {
		let colors = [] as string[]
			
		props.colors.forEach((e : any | null) => {
			colors.push(e! as string)
		})
		
		let css = {}
		
		if($ux.Util.getPx(props.width) != 0) {
			css['width'] = $ux.Util.addUnit(props.width)
		}
		
		if($ux.Util.getPx(props.height) != 0) {
			css['height'] = $ux.Util.addUnit(props.height)
		}
		
		css['flex-direction'] = props.direction
		
		if(props.disabled) {
			if (theme.value != 'text') {
				if(props.border) {
					css['border'] = `1px solid ${$ux.Color.getRgba(border.value, 0.6)}`
				}
				
				if(!props.plain && theme.value != 'info') {
					if (colors.length == 0) {
						css['background-color'] = $ux.Color.getRgba(backgroundColor.value, 0.6)
					} else if (colors.length == 1) {
						css['background-color'] = $ux.Color.getRgba(colors[0], 0.6)
					} else {
						css['background'] = `linear-gradient(to ${props.gradientDirection}, ${colors.join(',')})`
						css['opacity'] = 0.8
					}
				}
			}
		} else {
			if(theme.value != 'text' && props.border) {
				css['border'] = `1px solid ${border.value}`
			}
			
			if (colors.length == 0) {
				css['background-color'] = backgroundColor.value
			} else if (colors.length == 1) {
				css['background-color'] = colors[0]
			} else {
				css['background'] = `linear-gradient(to ${props.gradientDirection}, ${colors.join(',')})`
			}
		}
		
		// #ifdef WEB
		css['cursor'] = props.disabled ? 'not-allowed' : 'pointer'
		// #endif
		
		css['box-sizing'] = 'border-box'
		css['border-radius'] = $ux.Util.addUnit(props.corner)
		css['padding'] = '10px 10px'
		
		css = $ux.Util.xStyle(css, props.margin, props.mt, props.mr, props.mb, props.ml, props.padding, props.pt, props.pr, props.pb, props.pl)
		
		return css
	})
	
	const textStyle = computed(() => {
		let css = {}
		
		css['white-space'] = 'nowrap'
		css['font-size'] = $ux.Util.addUnit(fontSize.value)
		css['margin-top'] = '1px'
		css['color'] = props.disabled ? disabledColor.value : color.value
		css['font-weight'] = props.bold?'bold':'normal'
		
		if (props.icon != '') {
			if(props.direction == 'row') {
				css['margin-left'] = '4px'
			} else {
				css['margin-top'] = '8px'
			}
		}
		
		return css
	})
	
	const uxButtonRef = ref<Element | null>(null)
	
	function click(e : MouseEvent) {
		if (props.disabled || props.loading) {
			return
		}
		
		let f = () : void => {
			if (props.path != '') {
				uni.navigateTo({
					url: props.path
				})
			} else {
				emit('click', e)
			}
		
			if (props.stopPropagation) {
				e.stopPropagation()
			}
		}
		
		if(props.throttleTime > 0) {
			$ux.Util.throttle(f, props.throttleTime)
		} else {
			f()
		}
	}
	
	function longpress(e : TouchEvent) {
		if (props.disabled || props.loading) {
			return
		}
		
		emit('longpress', e)
	}
</script>

<style lang="scss" scoped>
	/* #ifdef H5 */
	uni-view {
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: stretch;
		align-content: stretch;
		justify-content: flex-start;
		box-sizing: border-box;
		min-height: 0px;
		min-width: 0px;
		overflow: hidden;
	}
	/* #endif */
	
	.ux-button {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		
		&__hover {
			opacity: 0.7;
		}
	}
</style>