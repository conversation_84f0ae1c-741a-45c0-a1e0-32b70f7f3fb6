<template>
	<sticky-header>
		<view class="ux-table__header" :style="headerStyle">
			<view class="ux-table__cell" :style="headerCellStyle(cell)" v-for="(cell, i) in columns" :key="i" @click="onSort(cell)">
				<view class="ux-table__wrap">
					<ux-radio v-if="selection && cell.key == selectKey" :readonly="true" :checked="isAll" shape="square" :color="activeColor" :value="cell.key"></ux-radio>
					<text v-else class="ux-table__text" :style="headerTextStyle">{{ cell.title }}</text>
				</view>
				<view v-if="cell.sorted == true" class="ux-table__sort">
					<ux-icon class="ux-table__sort--up" type="trigonup-filled" :color="cell.desc == 'desc' ? activeColor : '#a9a9a9'"></ux-icon>
					<ux-icon class="ux-table__sort--down" type="trigondown-filled" :color="cell.desc == 'asc' ? activeColor : '#a9a9a9'"></ux-icon>
				</view>
			</view>
		</view>
	</sticky-header>
</template>

<script setup>
	/**
	 * Table Header
	 * @demo pages/component/table.uvue
	 * @tutorial https://www.uxframe.cn/component/table.html
	 * <AUTHOR>
	 * @date 2024-07-25 23:23:24
	 */
	
	import { $ux } from '../../index'
	import { useFontSize, useFontColor, useForegroundColor, useThemeColor } from '../../libs/use/style.uts'
	import { UxTableColumn } from '../../libs/types/types.uts'
	
	defineOptions({
		name: 'ux-table-header'
	})
	
	const emit = defineEmits(['sort'])

	const props = defineProps({
		fixed: {
			type: Boolean,
			default: false
		},
		columns: {
			type: Array as PropType<UxTableColumn[]>,
			default: () : UxTableColumn[] => [] as UxTableColumn[]
		},
		totalWidth: {
			type: Number,
			default: 0
		},
		tableWidth: {
			type: Number,
			default: 0
		},
		cellHeight: {
			default: 40
		},
		headerColor: {
			type: String,
			default: "#333333"
		},
		headerSize: {
			default: 0
		},
		headerBold: {
			type: Boolean,
			default: true
		},
		headerBackground: {
			type: String,
			default: "#f5f5f5"
		},
		borderColor: {
			type: String,
			default: "#e8e8e8"
		},
		border: {
			type: Boolean,
			default: false
		},
		selection: {
			type: Boolean,
			default: false
		},
		isAll: {
			type: Boolean,
			default: false
		}
	})
	
	const selectKey = '__SELECT__'
	
	const activeColor = computed((): string => {
		return useThemeColor('primary')
	})
	
	const headerSize = computed((): number => {
		return useFontSize($ux.Util.getPx(props.headerSize), 1)
	})
	
	const headerColor = computed((): string => {
		return useFontColor(props.headerColor, 'auto')
	})
	
	const headerBackground = computed((): string => {
		return useForegroundColor(props.headerBackground, 'auto')
	})
	
	const headerStyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		if(!props.fixed && props.totalWidth > 0) {
			css.set('width', $ux.Util.addUnit(props.totalWidth))
		}
		
		css.set('height', $ux.Util.addUnit(props.cellHeight))
		
		return css
	})
	
	const headerTextStyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('fontSize', $ux.Util.addUnit(headerSize.value))
		css.set('color', headerColor.value)
		
		if(props.headerBold) {
			css.set('font-weight', 'bold')
		}
		
		return css
	})
	
	function getCellWidth(col: UxTableColumn) : string {
		let w = col.width ?? '20%'
		if (w == '') {
			return 'auto'
		}
		
		if (w.indexOf('%') != -1) {
			w = `${parseFloat(w.replace('%', '')) * props.tableWidth / 100}px`
		}
		
		return w
	}
	
	function headerCellStyle(col: UxTableColumn): Map<string, any> {
		let cellWidth = getCellWidth(col)
		
		let css = new Map<string, any>()
		
		if(cellWidth == 'auto') {
			css.set('flex', '1')
		} else {
			css.set('width', cellWidth)
		}
		
		css.set('height', $ux.Util.addUnit(props.cellHeight))
		css.set('background-color', headerBackground.value)
		
		if(props.border) {
			css.set('border-top', `1px solid ${props.borderColor}`)
			css.set('border-right', `1px solid ${props.borderColor}`)
		}
		
		if(col.align == 'left') {
			css.set('justify-content', 'flex-start')
		} else if(col.align == 'right') {
			css.set('justify-content', 'flex-end')
		} else {
			css.set('justify-content', 'center')
		}
		
		return css
	}
	
	function onSort(col: UxTableColumn) {
		emit('sort', col)
	}
	
</script>

<style lang="scss">
	.ux-table {
		
		&__header {
			display: flex;
			flex-direction: row;
			flex-wrap: nowrap;
		}
		
		&__sort {
			width: 30px;
			height: 30px;
			display: flex;
			flex-direction: column;
			position: relative;
			
			&--up {
				position: absolute;
				top: 2px;
			}
			
			&--down {
				position: absolute;
				top: 10px;
			}
		}
		
		&__cell {
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
		}
		
		&__wrap {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
		}
		
		&__text {
			text-align: center;
			padding: 4px 8px;
			/* #ifdef WEB */
			word-break: break-all;
			/* #endif */
		}
	}
</style>