<template>
  <view class="mp-html">
    <mp-html-node 
      v-for="(node, index) in nodes" 
      :key="index"
      :node="node"
      :config="config"
      @link-tap="onLinkTap"
      @img-tap="onImgTap"
    />
  </view>
</template>

<script setup lang="uts">
import MpHtmlNode from './mp-html-node.uvue'
import { HtmlParser } from '../../libs/parser.uts'
import { HtmlNodeType, ParserConfigType, LinkTapEventType, ImgTapEventType, ErrorEventType } from '../../libs/types.uts'

// 定义属性
interface Props {
  /** HTML内容 */
  content?: string
  /** 是否可选择文本 */
  selectable?: boolean
  /** 是否显示图片菜单 */
  showImgMenu?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  content: '',
  selectable: false,
  showImgMenu: true
})

// 定义事件
const emit = defineEmits<{
  'link-tap': [event: LinkTapEventType]
  'img-tap': [event: ImgTapEventType]
  'error': [event: ErrorEventType]
}>()

// 响应式数据
const nodes = ref<HtmlNodeType[]>([])
const config = ref<ParserConfigType>({
  showImgMenu: props.showImgMenu,
  selectable: props.selectable,
  baseFontSize: 16
})

// 解析HTML内容
const parseContent = () => {
  try {
    if (props.content == '') {
      nodes.value = []
      return
    }
    
    const parser = new HtmlParser(props.content)
    nodes.value = parser.parse()
  } catch (error: any) {
    console.error('HTML解析错误:', error)
    emit('error', { error: error.toString() })
    nodes.value = []
  }
}

// 处理链接点击
const onLinkTap = (event: LinkTapEventType) => {
  emit('link-tap', event)
}

// 处理图片点击
const onImgTap = (event: ImgTapEventType) => {
  emit('img-tap', event)
}

// 监听内容变化
watch(() => props.content, () => {
  parseContent()
}, { immediate: true })

// 监听配置变化
watch(() => [props.selectable, props.showImgMenu], () => {
  config.value = {
    showImgMenu: props.showImgMenu,
    selectable: props.selectable,
    baseFontSize: 16
  }
}, { immediate: true })

// 初始化
onMounted(() => {
  parseContent()
})
</script>

<style scoped>
.mp-html {
  flex: 1;
  flex-direction: column;
}
</style>