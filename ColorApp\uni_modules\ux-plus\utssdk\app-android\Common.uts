import Intent from 'android.content.Intent'
import Uri from 'android.net.Uri'
import Context from 'android.content.Context';
import Vibrator from 'android.os.Vibrator';
import Build from 'android.os.Build';
import VibrationEffect from 'android.os.VibrationEffect';
import Paint from 'android.graphics.Paint';
import ColorMatrix from 'android.graphics.ColorMatrix';
import ColorMatrixColorFilter from 'android.graphics.ColorMatrixColorFilter';
import View from 'android.view.View';
import InputMethodManager from 'android.view.inputmethod.InputMethodManager'
import ClipData from "android.content.ClipData";
import ClipboardManager from "android.content.ClipboardManager";

import { UxOpenURLOptions, UxOpenWebOptions } from '../interface.uts'
import { UxMakePhoneCallOptions } from '../interface.uts'
import { UxVibrateOptions } from '../interface.uts'
import { UxPlusSuccessCallbackImpl, UxPlusErrorCallbackImpl } from '../unierror.uts'

export default class Common {
	
	hideKeyboard() {
		let window = UTSAndroid.getUniActivity()!.window
		
		if (window.getCurrentFocus() != null) {
		    let inputMethodManager = UTSAndroid.getAppContext()!.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
		    inputMethodManager.hideSoftInputFromWindow(window.getCurrentFocus()!.getWindowToken(), InputMethodManager.HIDE_NOT_ALWAYS);
		}
	}
	
	setGray(gray: number) {
		var colorMatrix = new ColorMatrix()
		colorMatrix.setSaturation((1-gray).toFloat())
		
		let filter = new ColorMatrixColorFilter(colorMatrix)
		
		var paint = new Paint()
		paint.setColorFilter(filter)
		
		UTSAndroid.getUniActivity()!.window.decorView.setLayerType(View.LAYER_TYPE_HARDWARE, paint)
	}
	
	openURL(options : UxOpenURLOptions) {
		if (options.url.length > 0) {
			const context = UTSAndroid.getUniActivity()!
			const uri = Uri.parse(options.url)
			const intent = new Intent(Intent.ACTION_VIEW, uri)
			intent.setData(uri)
			context.startActivity(intent)
			
			const res = new UxPlusSuccessCallbackImpl('openURL:success')
			options.success?.(res)
			options.complete?.(res)
		} else {
			const err = new UxPlusErrorCallbackImpl('openURL:failed')
			options.fail?.(err)
			options.complete?.(err)
		}
	}
	
	openWeb(options : UxOpenWebOptions) {
		let darkMode =  false
		// let data = uni.getStorageSync('UxFrameConf')
		// if(data instanceof UTSJSONObject) {
		// 	darkMode = data.getBoolean('darkMode') ?? false
		// }
		
		let ok = UxOpenWeb.show(options.title ?? '', options.url, darkMode, () => {
			const res = new UxPlusSuccessCallbackImpl('openWeb:complete')
			options.complete?.(res)
		})
		
		if(ok) {
			const res = new UxPlusSuccessCallbackImpl('openWeb:success')
			options.success?.(res)
		} else {
			const res = new UxPlusErrorCallbackImpl('openWeb:failed')
			options.fail?.(res)
		}
	}
	
	makePhoneCall(options : UxMakePhoneCallOptions) {
		const context = UTSAndroid.getUniActivity()!
		let permission = ["android.permission.CALL_PHONE"]
		const err = new UxPlusErrorCallbackImpl('makePhoneCall:failed')
		UTSAndroid.requestSystemPermission(UTSAndroid.getUniActivity()!, permission, function (allRight : boolean, _ : string[]) {
			if (allRight) {
				// 用户同意了全部权限
				const intent = new Intent(Intent.ACTION_DIAL)
				intent.setData(Uri.parse(`tel:${options.phoneNumber}`));
				if (intent.resolveActivity(context.getPackageManager()) != null) {
					//TOOD 严谨处理应该判断是否有电话app，比如某些pad和电视就没有电话app，无法启动
					context.startActivity(intent);
					const res = new UxPlusSuccessCallbackImpl('makePhoneCall:success')
					options.success?.(res)
					options.complete?.(res)
				} else {
					options.fail?.(err)
					options.complete?.(err)
				}
			} else {
				options.fail?.(err)
				options.complete?.(err)
				// 用户仅同意了 grantedList中的权限
			}
		}, function (_ : boolean, _ : string[]) {
			// 用户拒绝了部分权限，仅允许了grantedList中的权限
			options.fail?.(err)
			options.complete?.(err)
		})
	}
	
	@Suppress("DEPRECATION")
	vibrate(options : UxVibrateOptions) {
		const context = UTSAndroid.getUniActivity()!;
		const vibrator = context.getSystemService(Context.VIBRATOR_SERVICE) as Vibrator;
		if (vibrator.hasVibrator()) {
			let duration = 100;
			if (options.type == 'heavy') {
				duration = 300;
			} else if (options.type == 'medium') {
				duration = 200;
			} else if (options.type == 'light') {
				duration = 100;
			} else if (options.type == 'lighter') {
				duration = 10;
			}
			
			duration = duration.toLong();
			if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
				const vibrationEffect = VibrationEffect.createOneShot(duration, VibrationEffect.DEFAULT_AMPLITUDE)
				vibrator.vibrate(vibrationEffect)
			} else {
				vibrator.vibrate(duration)
			}
			
			const res = new UxPlusSuccessCallbackImpl('vibrate:success')
			options.success?.(res);
			options.complete?.(res);
		} else {
			const err = new UxPlusErrorCallbackImpl('vibrate:failed')
			options.fail?.(err)
			options.complete?.(err)
		}
	}
	
	setClipboardData(data : string) {
		try {
			const context = UTSAndroid.getAppContext();
			if (context != null) {
				const clipboard = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager;
				const clip = ClipData.newPlainText('label', data);
				clipboard.setPrimaryClip(clip);
			}
		} catch (e) {}
	}
	
	getClipboardData(): string {
		try {
			const context = UTSAndroid.getAppContext();
			if (context != null) {
				const clipboard = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager;
				const clip = clipboard.getPrimaryClip();
				if (clip != null && clip.getItemCount() > 0) {
					const text = clip.getItemAt(0).getText();
					if (text != null) {
						return text.toString()
					}
				}
			}
		} catch (e) {}
		
		return ''
	}
}