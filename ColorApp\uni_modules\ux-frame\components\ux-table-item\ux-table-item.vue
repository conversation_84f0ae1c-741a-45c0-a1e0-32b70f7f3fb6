<template>
	<view class="ux-table__body" :style="bodyStyle(cell, index)" :hover-class="isDark? 'ux-table__hover--dark' : 'ux-table__hover'" :hover-stay-time="200">
		<view class="ux-table__wrap" :style="[cellStyle(col, i), col.style]" v-for="(col, i) in columns" :key="i" @click="onClick(cell, col.key, index, i)">
			<ux-radio v-if="selection && col.key == selectKey" :readonly="true" shape="square" :color="activeColor" :checked="cell[col.key] == col.key"></ux-radio>
			<slot v-else-if="edit && col.key == editKey" name="edit" :row="index"></slot>
			<slot v-else :name="col.key" :row="index" :col="i - len">
				<input v-if="col.editable" class="ux-table__input" :style="[cellTextStyle, col.textStyle]" :type="col.inputType ?? 'text'" :value="cell[col.key]" @input="(e: InputEvent) => onInput(e, index, col.key)"/>
				<text v-else class="ux-table__text" :style="[cellTextStyle, col.textStyle]">{{ format(cell, col) }}</text>
			</slot>
		</view>
	</view>
</template>

<script setup lang="ts">
	/**
	 * Table Item
	 * @demo pages/component/table.uvue
	 * @tutorial https://www.uxframe.cn/component/table.html
	 * <AUTHOR>
	 * @date 2024-12-07 23:41:17
	 */
	
	import { PropType, computed } from 'vue'
	import { $ux } from '../../index'
	import { useFontSize, useFontColor, useForegroundColor, useThemeColor } from '../../libs/use/style.uts'
	import { UxTableColumn } from '../../libs/types/types.uts'
	
	// defineSlots<{
	// 	name(props : { row: number, col : number }) : any
	// }>()
	
	defineOptions({
		name: 'ux-table-item'
	})

	const emit = defineEmits(['click', 'input'])
	
	const props = defineProps({
		fixed: {
			type: Boolean,
			default: false
		},
		index: {
			type: Number,
			default: 0
		},
		cell: {
			type: Object as PropType<UTSJSONObject>,
			default: (): UTSJSONObject => {
				return {} as UTSJSONObject
			}
		},
		columns: {
			type: Array as PropType<UxTableColumn[]>,
			default: () : UxTableColumn[] => [] as UxTableColumn[]
		},
		tableWidth: {
			type: Number,
			default: 0
		},
		totalWidth: {
			type: Number,
			default: 0
		},
		cellHeight: {
			type: Number,
			default: 40
		},
		color: {
			type: String,
			default: "#333333"
		},
		fontSize: {
			default: 0
		},
		background: {
			type: String,
			default: "#fafafa"
		},
		highlight: {
			type: String,
			default: "#ecf5ff"
		},
		borderColor: {
			type: String,
			default: "#e8e8e8"
		},
		border: {
			type: Boolean,
			default: false
		},
		ripple: {
			type: Boolean,
			default: false
		},
		rippleColor: {
			type: String,
			default: "#f5f5f5"
		},
		inputColor: {
			type: String,
			default: "transparent"
		},
		nums: {
			type: Boolean,
			default: false
		},
		numSort: {
			type: Boolean,
			default: false
		},
		selection: {
			type: Boolean,
			default: false
		},
		edit: {
			type: Boolean,
			default: false
		},
		len: {
			type: Number,
			default: 0
		}
	})
	
	const selectKey = '__SELECT__'
	const editKey = '__EDIT__'
	
	const isDark = computed((): boolean => {
		return $ux.Conf.darkMode.value
	})
	
	const activeColor = computed((): string => {
		return useThemeColor('primary')
	})
	
	const fontSize = computed((): number => {
		return useFontSize($ux.Util.getPx(props.fontSize), 1)
	})
	
	const color = computed((): string => {
		return useFontColor(props.color, 'auto')
	})
	
	const background = computed((): string => {
		return useForegroundColor(props.background, 'auto')
	})
	
	const rippleColor = computed((): string => {
		return useForegroundColor(props.rippleColor, 'auto')
	})
	
	const highlight = computed((): string => {
		return useForegroundColor(props.highlight, 'auto')
	})
	
	const cellTextStyle = computed(() => {
		let css = {}
		
		css['fontSize'] = $ux.Util.addUnit(fontSize.value)
		css['color'] = color.value
		
		return css
	})
	
	function getCellWidth(col: UxTableColumn) : string {
		let w = col.width ?? '20%'
		if (w == '') {
			return 'auto'
		}
		
		if (w.indexOf('%') != -1) {
			w = `${parseFloat(w.replace('%', '')) * props.tableWidth / 100}px`
		}
		
		return w
	}
	
	function bodyStyle(cell: UTSJSONObject | null, i: number) {
		let css = {}
		
		if(!props.fixed && props.totalWidth > 0) {
			css['width'] = $ux.Util.addUnit(props.totalWidth)
			css['min-width'] = $ux.Util.addUnit(props.totalWidth)
		}
		
		css['height'] = $ux.Util.addUnit(props.cellHeight)
		
		if(cell != null && cell[selectKey] == selectKey) {
			css['background-color'] = highlight.value
		} else {
			if((i+1)%2 == 0 && props.ripple){
				css['background-color'] = rippleColor.value
			} else {
				css['background-color'] = background.value
			}
		}
		
		return css
	}
	
	function cellStyle(col: UxTableColumn, i: number) {
		let cellWidth = getCellWidth(col)
		
		let css = {}
		
		if(cellWidth == 'auto') {
			css['flex'] = '1'
		} else {
			css['width'] = cellWidth
			css['min-width'] = cellWidth
		}
		
		css['height'] = $ux.Util.addUnit(props.cellHeight)
		
		if(props.border) {
			css['border-top'] = `1px solid ${props.borderColor}`
			css['border-right'] = `1px solid ${props.borderColor}`
		}
		
		if(col.align == 'left') {
			css['align-items'] = 'flex-start'
		} else if(col.align == 'right') {
			css['align-items'] = 'flex-end'
		} else {
			css['align-items'] = 'center'
		}
		
		if(col.editable ?? false) {
			css['background-color'] = props.inputColor
		}
		
		return css
	}
	
	function onClick(cell : UTSJSONObject, key : string, row: number, col: number) {
		emit('click', cell, key, row, col)
	}
	
	function onInput(cell: InputEvent, row: number, key: string) {
		emit('input', cell, row, key)
	}
	
	function format(cell: UTSJSONObject, col: UxTableColumn) : string {
		let f = col.format ?? ''
		let text = cell[col.key] == null ? '' : `${cell[col.key]}`
		
		if(f != '') {
			if(col.date ?? false) {
				text = $ux.Date.fmtDate(text, f)
			} else {
				if(f == 'fixed') {
					// toFixed
					let decimals = col.decimals ?? 0
					try{
						text = parseInt(text).toFixed(decimals)
					}catch(e){
						try{
							text = parseFloat(text).toFixed(decimals)
						}catch(e){}
					}
				} else if(f == 'encrypt') {
					// 脱敏
					text = $ux.Fmt.encryptText(text)
				} else if(f == 'cmoney') {
					// 大写金额
					text = $ux.Fmt.upperMoney(text)
				} else if(f == 'qmoney') {
					// 金额千分制
					text = $ux.Fmt.fmtMoney(text, false)
				} else if(f == 'wmoney') {
					// 金额万分制
					text = $ux.Fmt.fmtMoney(text, true)
				}
			}
		}
		
		return text
	}
</script>

<style lang="scss" scoped>
	/* #ifdef H5 */
	uni-view {
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: stretch;
		align-content: stretch;
		justify-content: flex-start;
		box-sizing: border-box;
		min-height: 0px;
		min-width: 0px;
		overflow: hidden;
	}
	/* #endif */
	
	.ux-table {
		
		&__body {
			display: flex;
			flex-direction: row;
			flex-wrap: nowrap;
			/* #ifdef WEB */
			cursor: pointer;
			/* #endif */
		}
		
		&__body--dark {
			display: flex;
			flex-direction: row;
			flex-wrap: nowrap;
			/* #ifdef WEB */
			cursor: pointer;
			/* #endif */
		}
		
		&__hover {
			background-color: #f0f0f0 !important;
		}
		
		&__hover--dark {
			background-color: #666666 !important;
		}
		
		/* #ifdef WEB */
		&__body:hover {
			background-color: #f0f0f0 !important;
		}
		
		&__body--dark:hover {
			background-color: #666666 !important;
		}
		/* #endif */
		
		&__cell {
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
		}
		
		&__input {
			flex: 1;
			font-size: 14px;
			padding: 5px 7px;
		}
		
		&__wrap {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
		}
		
		&__text {
			text-align: center;
			padding: 4px 8px;
			/* #ifdef WEB */
			word-break: break-all;
			/* #endif */
		}
	}
</style>