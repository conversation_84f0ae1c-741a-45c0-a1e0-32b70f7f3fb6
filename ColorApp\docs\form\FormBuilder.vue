<template>
  <!-- 表单 -->
  <Form ref="formRef" :model="dataModel" :rules="formRules">
    <!-- 表单顶部内容 -->
    <slot name="top_form_item"></slot>
    <!-- 表单分组 -->
    <FormGroup
      v-for="(group, index) in fieldGroups"
      :key="index"
      :group="group"
      :group-index="index"
      :data-model="dataModel"
      :title-class="titleClass"
      :label-cell="labelCell"
      :fold="fold"
      @change="handleChange"
      @field-change="handleFieldChange"
    />
    <!-- 表单底部内容 -->
    <slot></slot>
  </Form>
</template>

<script setup>
import { ref, computed, watch, nextTick } from "vue";
import { useUserStore } from "@/store";
import { checkFormRules } from "@/utils/util";
import useUtils from "@/hooks/utils";

const { getLang } = useUtils();

const props = defineProps({
  // 表单绑定数据
  modelValue: Object,
  // 表单字段配置
  fields: Array,
  // 表单项标题宽度，按24网格计算，24表示100%宽度
  labelCell: {
    type: Number,
    default: 24,
  },
  // 表单字段配置
  rules: {
    type: Object,
    default: () => {
      return { required: [] };
    },
  },
  // 表单标题样式
  titleClass: {
    type: String,
    default: "h-form-item",
  },
  // 表单分栏数量，1表示单栏，2表示双栏，3表示三栏
  cols: {
    type: Number,
    default: 3,
  },
  // 是否启用分组折叠功能
  fold: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["change", "fieldChange"]);

const formRef = ref(null);
const dataModel = ref({});
const formRules = ref(props.rules);
const currFields = ref(props.fields);

const userStore = useUserStore();
const user = computed(() => userStore.user);
const permissions = computed(() => (user.value ? user.value.permissions : []));

const fieldGroups = computed(() => {
  let groups = [];
  let items = [];
  let lastGroup = null;
  let lastGroupClass = null;

  for (let i = 0; i < props.fields.length; i++) {
    const item = props.fields[i];
    const group = item.Group || "";
    if (lastGroup != group) {
      if (lastGroup != null) {
        groups.push({
          //分组标题
          title: lastGroup,
          //分组样式
          groupClass: lastGroupClass,
          //该分组下的字段列表
          fields: items,
          // 所有分组都显示，不再根据fold属性控制
          show: true,
        });
      }
      items = [];
      lastGroup = group;
      lastGroupClass = item.GroupClass || "";
    }
    items.push(item);
  }
  if (items.length) {
    groups.push({
      title: lastGroup,
      fields: items,
      show: true, // 所有分组都显示
      groupClass: lastGroupClass,
    });
  }
  return groups;
});

watch(
  () => props.rules,
  (val) => {
    formRules.value = val;
    checkRules();
  }
);

watch(
  () => props.modelValue,
  (val) => {
    updateModel(val);
    checkFieldRules();
  }
);

watch(
  () => props.fields,
  (val) => {
    currFields.value = val;
  }
);

const updateModel = (model) => {
  const newDataModel = model || {};
  props.fields.forEach((field) => {
    if (newDataModel[field.Id] === undefined) {
      newDataModel[field.Id] = field.DefaultValue || "";
    }
  });
  dataModel.value = newDataModel;
};

const checkFieldRules = () => {
  if (!dataModel.value) return;
  props.fields.forEach((field) => checkFormRules(field));
  checkRules();
};

const handleChange = (data) => {
  console.log("###change：", data);
  if (data.type === "sendCode") {
    // 处理发送验证码逻辑
    return;
  }
  if (
    data.field.Type == "Image" ||
    data.field.Type == "File" ||
    data.field.Type == "Face"
  ) {
    formRef.value.validField(data.field.Id);
  }
  const emitData = { field: data.field, data: data.data };
  if (checkFormRules(data.field, emitData.data)) {
    checkRules();
  }
  emit("change", emitData);
};

const handleFieldChange = (data) => {
  emit("fieldChange", data);
};


const checkRules = () => {
  const newFormRules = props.rules
    ? JSON.parse(JSON.stringify(props.rules))
    : {};
  if (!newFormRules.required) newFormRules.required = [];
  if (!newFormRules.rules) newFormRules.rules = {};
  if (!newFormRules.email) newFormRules.email = [];
  if (!newFormRules.tel) newFormRules.tel = [];
  if (!newFormRules.url) newFormRules.url = [];
  if (!newFormRules.mobile) newFormRules.mobile = [];
  if (!newFormRules.number) newFormRules.number = [];
  if (!newFormRules.combineRules) newFormRules.combineRules = [];

  for (let i = 0; i < props.fields.length; i++) {
    const field = props.fields[i];
    if (field.Required) newFormRules.required.push(field.Id);
    if (field.InputType === "email") newFormRules.email.push(field.Id);
    if (field.InputType === "mobile") newFormRules.mobile.push(field.Id);
    if (field.InputType === "number") newFormRules.number.push(field.Id);
    if (field.InputType === "url") newFormRules.url.push(field.Id);

    if (
      field.ValidationType === "equal" ||
      field.ValidationType === "greaterThan" ||
      field.ValidationType === "lessThan"
    ) {
      newFormRules.combineRules.push({
        refs: [field.Id, field.ValidField],
        valid: {
          valid: field.ValidationType,
          message: getLang(field.ValidMesasge, field.ValidMesasgeEN),
        },
      });
    } else if (field.ValidationType === "pattern") {
      newFormRules.rules[field.Id] = {
        valid: {
          pattern: new RegExp(field.Validation),
          message:
            getLang(field.ValidMesasge, field.ValidMesasgeEN) ||
            getLang("请输入正确的格式！", "Please enter the correct format!"),
        },
      };
    } else if (
      (field.Type === "ChinaAddress" || field.Type === "Address") &&
      field.Required
    ) {
      newFormRules.rules[field.Id] = {
        valid(prop) {
          const isChina =
            prop.Country === "中国" || prop.Country === "中国大陆";
          if (
            field.Data.indexOf("国家") !== -1 &&
            (!prop.Country || prop.Country === "国家")
          ) {
            return getLang(
              `为必选项！`.replace(field.Name, ""),
              ` is required`
            );
          }
          if (
            field.Data.indexOf("省") !== -1 &&
            isChina &&
            (!prop.Province ||
              prop.Province === "省" ||
              prop.Province === "省/直辖市")
          ) {
            return getLang(
              `省/直辖市为必选项！`.replace(field.Name, ""),
              ` - Province is required`
            );
          }
          if (
            field.Data.indexOf("市") !== -1 &&
            isChina &&
            (!prop.City ||
              prop.City === "市" ||
              prop.City === "市/区" ||
              prop.City === "城市")
          ) {
            return getLang(
              `${
                field.Data.indexOf("城市") === -1 ? "市/区" : "城市"
              }为必选项！`.replace(field.Name, ""),
              ` - City is required`
            );
          }

          return true;
        },
      };
    }
    if (field.Flags && field.Flags.indexOf("身份证") !== -1) {
      const idTypeField = props.fields.find(
        (a) => a.Flags.indexOf("身证件类别") !== -1
      );
      let val = dataModel.value ? dataModel.value[idTypeField.Id] : "";
      if (!val) val = idTypeField.DefaultValue;
      if (idTypeField) {
        checkIDCardField(field, val, newFormRules);
      }
    }
    const val = dataModel.value[field.Id];
    if (!val && field.DefaultValue)
      dataModel.value[field.Id] = field.DefaultValue;
  }
  formRules.value = newFormRules;
};

const checkIDCardField = (field, idType, newFormRules) => {
  let isInit = true;
  if (!newFormRules) {
    newFormRules = formRules.value;
    isInit = false;
  }
  if (field.Flags.indexOf("身份证") !== -1) {
    if (idType === "身份证") {
      field.InputType = "idcard";
      newFormRules.rules[field.Id] = {
        valid: {
          pattern:
            /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}[0-9Xx]$)/,
          message: "身份证格式错误！",
        },
      };
    } else {
      if (newFormRules.rules[field.Id]) delete newFormRules.rules[field.Id];
      field.InputType = "text";
    }
    if (!isInit) formRef.value.updateRules();
    return true;
  }
  return false;
};

const isValid = () => {
  let showCheck = true;
  for (let i = 0; i < this.fields.length; i++) {
    const field = this.fields[i];
    if (field.Type === "Custom" && !field.Hide) {
      showCheck = this.$refs[field.Id][0].isValid();
      if (!showCheck) return showCheck;
    }
  }
  if (showCheck) {
    return this.$refs.form.valid().result;
  }
  return showCheck;
};

const valid = () => {
  return formRef.value.valid();
};

// Expose methods to parent component
defineExpose({
  isValid,
  valid,
});
// 检查字段权限逻辑
const checkPermissions = () => {
  let noPermissionFields = [];
  for (let i = 0; i < currFields.value.length; i++) {
    const field = currFields.value[i];
    if (
      field.Data &&
      field.Data.indexOf("auto-size") !== -1 &&
      field.Flags &&
      field.Flags.indexOf("autosize") === -1
    ) {
      field.Flags.push("autosize");
    }
    if (field.Permissions && field.Permissions.length > 0) {
      let myPermissionFields = field.Permissions.filter(
        (a) => permissions.value.indexOf(a) !== -1
      );
      if (myPermissionFields.length != field.Permissions.length)
        noPermissionFields.push(field); //权限数量不足
    }
  }
  //移除没有权限的字段
  if (noPermissionFields.length > 0) {
    noPermissionFields.forEach((item) => {
      currFields.value.splice(currFields.value.indexOf(item), 1);
    });
  }
};

const updateCountry = () => {
  // 更新国家信息逻辑
  console.log("更新国家信息");
};

// Initialize
if (props.fields) {
  updateModel(props.modelValue);
  checkPermissions();
  updateCountry();
  nextTick(() => {
    checkFieldRules();
  });
}
</script>

<style lang="less" scoped>
.h-numberinput-show .h-numberinput-input {
  min-width: 50px !important;
}

.screen-labe {
  line-height: 21px;
  padding: 4px 14px;
  background: #eeeeee;
}
</style>
