<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/navbar?title=Navbar"></Mobile>

# Navbar
> 组件类型：UxNavbarComponentPublicInstance

支持左右插槽，更多自定义样式

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| left | Slot |  | 左侧插槽 |
| title | Slot |  | 标题插槽 |
| right | Slot |  | 右侧插槽 |
| title | String |  | 标题 |
| alignLeft | Boolean | false | 标题居左 |
| color | String | `$ux.Conf.titleColor` | 标题颜色 |
| [darkColor](#darkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| size | Any | `$ux.Conf.fontSize` | 字体大小 |
| bold | Boolean | false | 字体加粗 |
| height | Any | 44 | 导航栏高度 |
| border | Boolean | true | 显示下边框 |
| borderColor | String | `$ux.Conf.borderColor` | 下边框颜色 |
| colors | Array |  | 背景色多个渐变 |
| [gradientDirection](#gradientDirection) | String | bottom | 渐变色方向 |
| background | String |  | 背景图 |
| [backgroundMode](#backgroundMode) | String | aspectFill | 背景图裁剪模式 |
| showBlur | Boolean | false | 开启模糊背景 |
| blurRadius | Number | 10 | 模糊半径 |
| blurAlpha | Number | 0.5 | 模糊透明度 |
| fixed | Boolean | false | 固定定位 |
| zIndex | Number | 10000 | 层级z-index |
| goback | Boolean | true | 显示返回按钮 |
| gobackText | String |  | 返回按钮文字 |

### [darkColor](#darkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示 | 
 |
| auto自动适配深色模式 | 
 |
| color其他颜色
 |  |

### [gradientDirection](#gradientDirection)

| 值   | 说明 |
|:------:|:----:|
| top向上
 |  |
| bottom向下
 |  |
| left向左
 |  |
| right向右
 |  |

### [backgroundMode](#backgroundMode)

| 值   | 说明 |
|:------:|:----:|
| scaleToFill | 不保持纵横比缩放图片，使图片的宽高完全拉伸至填满image元素
 |
| aspectFit | 保持纵横比缩放图片，使图片的长边能完全显示出来。也就是说，可以完整地将图片显示出来
 |
| aspectFill | 保持纵横比缩放图片，只保证图片的短边能完全显示出来。也就是说，图片通常只在水平或垂直方向是完整的，另一个方向将会发生截取
 |
| widthFix | 宽度不变，高度自动变化，保持原图宽高比不变
 |
| heightFix | 高度不变，宽度自动变化，保持原图宽高比不变
 |
| top | 不缩放图片，只显示图片的顶部区域
 |
| bottom | 不缩放图片，只显示图片的底部区域
 |
| center | 不缩放图片，只显示图片的中间区域
 |
| left | 不缩放图片，只显示图片的左边区域
 |
| right | 不缩放图片，只显示图片的右边区域
 |
| topleft | 不缩放图片，只显示图片的左上边区域
 |
| topright | 不缩放图片，只显示图片的右上边区域
 |
| bottomleft | 不缩放图片，只显示图片的左下边区域
 |
| bottomright | 不缩放图片，只显示图片的右下边区域
 |

