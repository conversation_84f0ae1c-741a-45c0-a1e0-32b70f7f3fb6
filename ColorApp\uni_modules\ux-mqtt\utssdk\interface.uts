export type UxMqttQos = 0 | 1 | 2

export type UxMqttOptions = {
	/**
	 * broker连接地址
	 */
	brokerUrl : string,
	/**
	 * 客户端id
	 */
	clientId : string,
	/**
	 * 用户名
	 */
	username ?: string,
	/**
	 * 密码
	 */
	password ?: string,
	/**
	 * 清除会话
	 */
	cleanSession ?: boolean
	/**
	 * 自动重连
	 */
	automaticReconnect ?: boolean
	/**
	 * 超时时间 单位: s
	 */
	connectionTimeout ?: number
	/**
	 * 心跳周期 单位: s
	 */
	keepAliveInterval ?: number
	/**
	 * 服务状态监听
	 */
	statusListener ?: (online: boolean, err: string) => void,
	/**
	 * 消息接收监听
	 */
	messageListener ?: (topic: string, msg: string) => void
}

export type UxMqttStatusListener = (online: boolean, err: string) => void;

export type UxMqttMessageListener = (topic: string, msg: string) => void;

export type UxMqttConnectOptions = {
	/**
	 * 服务状态监听
	 */
	statusListener ?: UxMqttStatusListener,
	/**
	 * 消息接收监听
	 */
	messageListener ?: UxMqttMessageListener
}

export type UxMqttSubscribeOptions = {
	/**
	 * 主题
	 */
	topic : string,
	/**
	 * 服务质量 默认 2
	 * 0 最多一次, 不保证对方是否收到
	 * 1 至少一次, 消息可能会重复
	 * 2 恰好一次, 消息不会丢失，也不会重复
	 */
	qos ?: UxMqttQos,
	/**
	 * 成功回调
	 */
	success ?: () => void,
	/**
	 * 失败回调
	 */
	fail ?: (err: string) => void,
}

export type UxMqttPublishOptions = {
	/**
	 * 主题
	 */
	topic : string,
	/**
	 * 消息id
	 */
	id ?: number,
	/**
	 * 消息
	 */
	msg: string
	/**
	 * 负载 (实际数据)
	 */
	payload ?: string,
	/**
	 * 服务质量 默认 2
	 * 0 最多一次, 不保证对方是否收到
	 * 1 至少一次, 消息可能会重复
	 * 2 恰好一次, 消息不会丢失，也不会重复
	 */
	qos ?: UxMqttQos,
	/**
	 * 消息是否在服务器持久化
	 */
	retained ?: boolean,
	/**
	 * 成功回调
	 */
	success ?: () => void,
	/**
	 * 失败回调
	 */
	fail ?: (err: string) => void
}