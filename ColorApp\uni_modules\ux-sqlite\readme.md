
<p align="center">
    <img alt="logo" src="https://www.uxframe.cn/logo/logo.png" width="120" height="120" style="margin-bottom: 10px;">
</p>
<h3 align="center" style="margin: 30px 0 30px;font-weight: bold;font-size:40px;">UxFrame 本地数据库sqlite SDK 1.0.1</h3>

## 特别说明

如果您已经购买了[UxFrame 低代码高性能UI框架](https://ext.dcloud.net.cn/plugin?id=16148), 则无需再次购买本插件，请点击上方`进入交流群`联系我免费获取离线版插件！

## 说明

支持Android、iOS，可对本地数据库进行增删改查！

### 示例

``` ts

import * as db from '@/uni_modules/ux-sqlite'

// 打开数据库
db.openDatabase({
	database: 'app',
	success: (res: db.UxSqlSuccess) => {
		uni.showToast({
			title: 'open db success',
			icon: 'none'
		})
	}
} as db.OpenDatabaseOptions)

// 关闭数据库
db.closeDatabase()

// 删除数据库
db.deleteDatabase({
	database: 'app'
} as db.DeleteDatabaseOptions)

// 数据库路径
db.getPath()

// 数据库是否打开
db.isOpen()

// 执行原生sql
db.execSQL({
	sql: 'SELECT * from user'
} as db.ExecSQLOptions)

// 事务
db.transaction({
	operations: (): boolean => {
		// 执行其他事务
		return true
	}
} as db.ExecTransactionOptions)

``` 

## 文档教程 ⬇️

[https://www.uxframe.cn](https://www.uxframe.cn)
