<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/camera-view?title=Camera-view"></Mobile>

# Camera-view
> 组件类型：UxCameraViewComponentPublicInstance

支持拍照、切换镜头、闪光灯、手电筒功能

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| mode | 模式，注意：watermark拍照模式性能没有take好 |  |
| take | 拍照时触发 |  |
  
  