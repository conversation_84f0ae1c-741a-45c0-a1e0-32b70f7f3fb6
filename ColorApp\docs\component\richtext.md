<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/richtext?title=Richtext"></Mobile>

# Richtext
> 组件类型：UxRichtextComponentPublicInstance

可渲染文字样式、图片、超链接，支持部分HTML标签

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| nodes | String |  | 节点列表 |
| html | String |  | HTML标签 |
| selectable | Boolean | false | 文本是否可选，Android设置selectable属性为true时，click事件不触发 |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| click | 被点击时触发 |  |
  
  