<template>
	<text v-if="isFont" class="ux-icon" :style="[style, xstyle]" @click="click">
		<slot>
			{{ icon }}
		</slot>
	</text>
	<image v-else class="ux-icon" :style="[imgStyle]" :src="icon"></image>
</template>

<script setup>
	/**
	 * 字体图标
	 * @description 常用字体图标集，包含了大多数常见场景的图标
	 * @demo pages/component/icon.uvue
	 * @tutorial https://www.uxframe.cn/component/icon.html
	 * @property {String}			type=[addcircle-filled|addcircle-outline|addimg-outline|addsquare-filled|addsquare-outline|adduser-outline|apple-filled|apple-outline|application-filled|application-outline|arrow2down|arrow2left|arrow2right|arrow2up|arrowdown|arrowleft|arrowright|arrowup|article-outline|attention-filled|attention-outline|bankcard-filled|bankcard-outline|battery-full-filled|batteryempty-filled|batteryempty-outline|batteryfull-outline|batterywork-filled|batterywork-outline|bezier-filled|bezier-outline|bookmark-filled|bookmark-outline|bookopen-filled"|bookopen-outline|branch-filled|branch-outline|branchdown-filled|branchdown-outline|brightness-outline|bug-filled|bug-outline|calculator-filled|calculator-outline|call-filled|call-outline|camera-filled|camera-outline|cancel-outline|cartadd-filled|cartadd-outline|cartdel-filled|cartdel-outline|chat-filled|chat-outline|check-filled|check-outline|click-filled|click-outline|closecircle-filled|closecircle-outline|close|comment-filled|comment-outline|concern-filled|concern-outline|coupon-filled|coupon-outline|delete-filled|delete-outline|down|download-outline|edit-outline|editarticle-outline|editpassword-outline|effects-filled|effects-outline|equalizer-filled|equalizer-outline|eyeopen-filled|eyeopen-outline|facebook-filled|facebook-outline|female-filled|female-outline|financing-filled|financing-outline|flag-filled|flag-outline|flipcamera-filled|flipcamera-outline|focus-filled|focus-outline|folderclose-filled|folderclose-outline|folderopen-filled|folderopen-outline|framework-filled|framework-outline|friends-filled|friends-outline|fullscreen-outline|func-filled|gift-filled|gift-outline|goods-filled|goods-outline|google-filled|google-outline|guideboard-filled|guideboard-outline|harm-filled|harm-outline|headset-filled|headset-outline|heart-filled|heart-outline|help-filled|help-outline|history-outline|homestay-filled|homestay-outline|hourglass-filled|hourglass-outline|img-outline|info-filled|info-outline|keyboard-filled|keyboard-outline|label-filled|label-outline|left|lightning-filled|lightning-outline|like-outline|local-filled|local-outline|localpin-filled|localpin-outline|lock-outline|luggage-filled|luggage-outline|magic-filled|magic-outline|male-filled|male-outline|mallbag-filled|mallbag-outline|menu-filled|menu-outline|message-filled|message-outline|microphone-filled|microphone-outline|monitor-filled|monitor-outlinemore-filled|more-outline|morethen-filled|morethen-outline|movie-filled|movie-outline|music-filled|music-outline|navigation-filled|navigation-outline|pause-filled|pause-outline|pennant-filled|pennant-outline|pic-filled|pic-outline|platte-filled|platte-outline|play-filled|play-outline|playwrong-filled|playwrong-outline|plus|ppt-filled|ppt-outline|print-filled|print-outline|protect-filled|protect-outline|percentage|redo-outline|reducecircle-outline|reducesquare-outline|refresh-outline|replay-filled|replay-outline|right|ring-filled|ring-outline|roadsign-filled|roadsign-outline|rotate-filled|rotate-outline|save-filled|save-outline|scale-filled|scale-outline|scan-outline|screenshot-outline|search-filled|search-outline|setting-filled|setting-outline|share-outline|sharing-filled|sharing-outline|shop-filled|shop-outline|shutdown-outline|soapbubble-filled|soapbubble-outline|sort-filled|sort-outline|star-filld|starhalf-filld|success-filled|success-outline|system-outline|tag-filled|tag-outline|time-outline|tips-filled|tips-outline|tool-filled|tool-outline|trademark-filled|trademark-outline|tree-filled|tree-outline|trigondown-filled|trigondown-outline|trigonleft-filled|trigonleft-outline|trigonright-filled|trigonright-outline|trigonup-filledtrigonup-outline|turndown-filled|turndown-outline|turnleft-filled|turnleft-outline|turnright-filled|turnright-outline|turnup-filled|turnup-outline|unfullscreen-outline|unlock-outline|up|upload-outlin|users-outline|voice-filled|voice-outline|volume-fille|volume-outline|volumedown-filled|volumedown-outline|volumemute-filled|volumemute-outline|volumenotice-filled|volumenotice-outline|volumeup-filled|volumeup-outline|wallet-filled|wallet-outline|wifi-outline|workbench-filled|workbench-outline|zoomin-filled|zoomin-outline|zoomout-fille|zoomout-outline|keyhole-filled|keyhole-outline|headphone-filled|headphone-outline|iphone-filled|iphone-outline|code-input]			String | 字体类型
     * @value addcircle-filled
     * @value addcircle-outline
     * @value addimg-outline
     * @value addsquare-filled
     * @value addsquare-outline
     * @value adduser-outline
     * @value apple-filled
     * @value apple-outline"
     * @value application-filled
     * @value application-outline
     * @value arrow2down
     * @value arrow2left
     * @value arrow2right
     * @value arrow2up
     * @value arrowdown
     * @value arrowleft
     * @value arrowright
     * @value arrowup
     * @value article-outline
     * @value attention-filled
     * @value attention-outline
     * @value bankcard-filled
     * @value bankcard-outline
     * @value battery-full-filled
     * @value batteryempty-filled
     * @value batteryempty-outline
     * @value batteryfull-outline
     * @value batterywork-filled
     * @value batterywork-outline
     * @value bezier-filled
     * @value bezier-outline
     * @value bookmark-filled
     * @value bookmark-outline
     * @value bookopen-filled"
     * @value bookopen-outline
     * @value branch-filled
     * @value branch-outline
     * @value branchdown-filled
     * @value branchdown-outline
     * @value brightness-outline
     * @value bug-filled
     * @value bug-outline
     * @value calculator-filled
     * @value calculator-outline
     * @value call-filled
     * @value call-outline
     * @value camera-filled
     * @value camera-outline
     * @value cancel-outline
     * @value cartadd-filled
     * @value cartadd-outline
     * @value cartdel-filled
     * @value cartdel-outline
     * @value chat-filled
     * @value chat-outline
     * @value check-filled
     * @value check-outline
     * @value click-filled
     * @value click-outline
     * @value closecircle-filled
     * @value closecircle-outline
     * @value close
     * @value comment-filled
     * @value comment-outline
     * @value concern-filled
     * @value concern-outline
     * @value coupon-filled
     * @value coupon-outline
     * @value delete-filled
     * @value delete-outline
     * @value down
     * @value download-outline
     * @value edit-outline
     * @value editarticle-outline
     * @value editpassword-outline
     * @value effects-filled
     * @value effects-outline
     * @value equalizer-filled
     * @value equalizer-outline
     * @value eyeopen-filled
     * @value eyeopen-outline
     * @value facebook-filled
     * @value facebook-outline
     * @value female-filled
     * @value female-outline
     * @value financing-filled
     * @value financing-outline
     * @value flag-filled
     * @value flag-outline
     * @value flipcamera-filled
     * @value flipcamera-outline
     * @value focus-filled
     * @value focus-outline
     * @value folderclose-filled
     * @value folderclose-outline
     * @value folderopen-filled
     * @value folderopen-outline
     * @value framework-filled
     * @value framework-outline
     * @value friends-filled
     * @value friends-outline
     * @value fullscreen-outline
     * @value func-filled
     * @value gift-filled
     * @value gift-outline
     * @value goods-filled
     * @value goods-outline
     * @value google-filled
     * @value google-outline
     * @value guideboard-filled
     * @value guideboard-outline
     * @value harm-filled
     * @value harm-outline
     * @value headset-filled
     * @value headset-outline
     * @value heart-filled
     * @value heart-outline
     * @value help-filled
     * @value help-outline
     * @value history-outline
     * @value homestay-filled
     * @value homestay-outline
     * @value hourglass-filled
     * @value hourglass-outline
     * @value img-outline
     * @value info-filled
     * @value info-outline
	 * @value keyboard-filled
	 * @value keyboard-outline
     * @value label-filled
     * @value label-outline
     * @value left
     * @value lightning-filled
     * @value lightning-outline
     * @value like-outline
     * @value local-filled
     * @value local-outline
     * @value localpin-filled
     * @value localpin-outline
     * @value lock-outline
     * @value luggage-filled
     * @value luggage-outline
     * @value magic-filled
     * @value magic-outline
     * @value male-filled
     * @value male-outline
     * @value mallbag-filled
     * @value mallbag-outline
     * @value menu-filled
     * @value menu-outline
     * @value message-filled
     * @value message-outline
     * @value microphone-filled
     * @value microphone-outline
     * @value monitor-filled
     * @value monitor-outline
     * @value more-filled
     * @value more-outline
     * @value morethen-filled
     * @value morethen-outline
     * @value movie-filled
     * @value movie-outline
     * @value music-filled
     * @value music-outline
     * @value navigation-filled
     * @value navigation-outline
     * @value pause-filled
     * @value pause-outline
     * @value pennant-filled
     * @value pennant-outline
     * @value pic-filled
     * @value pic-outline
     * @value platte-filled
     * @value platte-outline
     * @value play-filled
     * @value play-outline
     * @value playwrong-filled
     * @value playwrong-outline
     * @value plus
     * @value ppt-filled
     * @value ppt-outline
     * @value print-filled
     * @value print-outline
     * @value protect-filled
     * @value protect-outline
	 * @value percentage
     * @value redo-outline
     * @value reducecircle-outline
     * @value reducesquare-outline
     * @value refresh-outline
     * @value replay-filled
     * @value replay-outline
     * @value right
     * @value ring-filled
     * @value ring-outline
     * @value roadsign-filled
     * @value roadsign-outline
     * @value rotate-filled
     * @value rotate-outline
     * @value save-filled
     * @value save-outline
     * @value scale-filled
     * @value scale-outline
     * @value scan-outline
     * @value screenshot-outline
     * @value search-filled
     * @value search-outline
     * @value setting-filled
     * @value setting-outline
     * @value share-outline
     * @value sharing-filled
     * @value sharing-outline
     * @value shop-filled
     * @value shop-outline
     * @value shutdown-outline
     * @value soapbubble-filled
     * @value soapbubble-outline
     * @value sort-filled
     * @value sort-outline
     * @value star-filld
     * @value starhalf-filld
     * @value success-filled
     * @value success-outline
     * @value system-outline
     * @value tag-filled
     * @value tag-outline
     * @value time-outline
     * @value tips-filled
     * @value tips-outline
     * @value tool-filled
     * @value tool-outline
     * @value trademark-filled
     * @value trademark-outline
     * @value tree-filled
     * @value tree-outline
     * @value trigondown-filled
     * @value trigondown-outline
     * @value trigonleft-filled
     * @value trigonleft-outline
     * @value trigonright-filled
     * @value trigonright-outline
     * @value trigonup-filled
     * @value trigonup-outline
     * @value turndown-filled
     * @value turndown-outline
     * @value turnleft-filled
     * @value turnleft-outline
     * @value turnright-filled
     * @value turnright-outline
     * @value turnup-filled
     * @value turnup-outline
     * @value unfullscreen-outline
     * @value unlock-outline
     * @value up
     * @value upload-outlin
     * @value users-outline
     * @value voice-filled
     * @value voice-outline
     * @value volume-fille
     * @value volume-outline
     * @value volumedown-filled
     * @value volumedown-outline
     * @value volumemute-filled
     * @value volumemute-outline
     * @value volumenotice-filled
     * @value volumenotice-outline
     * @value volumeup-filled
     * @value volumeup-outline
     * @value wallet-filled
     * @value wallet-outline
     * @value wifi-outline
     * @value workbench-filled
     * @value workbench-outline
     * @value zoomin-filled
     * @value zoomin-outline
     * @value zoomout-fille
     * @value zoomout-outline
	 * @value keyhole-filled
	 * @value keyhole-outline
	 * @value headphone-filled
	 * @value headphone-outline
	 * @value iphone-filled
	 * @value iphone-outline
	 * @value code-input
	 * @property {String}			color								String | 字体颜色 (默认 $ux.Conf.fontColor)
	 * @property {String} 			darkColor=[none|auto|color]			String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Any}				size								Any | 字体大小 (默认 $ux.Conf.fontSize)
	 * @property {String}			fontFamily							String | 字体family (默认 ux-iconfont)
	 * @property {Array}			margin								Array | 边距 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}				mt									Any | 距上 单位px
	 * @property {Any}				mr									Any | 距右 单位px
	 * @property {Any}				mb									Any | 距下 单位px
	 * @property {Any}				ml									Any | 距左 单位px
	 * @property {Array}			padding								Array | 填充 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}				pt									Any | 上内边距 单位px
	 * @property {Any}				pr									Any | 右内边距 单位px
	 * @property {Any}				pb									Any | 下内边距 单位px
	 * @property {Any}				pl									Any | 左内边距 单位px
	 * @property {Array}			xstyle								Array<any> | 自定义样式
	 * @event {Function}			click								Function | 文字被点击时触发
	 * <AUTHOR>
	 * @date 2023-10-03 00:58:22
	 */

	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	import { useFontColor, useFontSize } from '../../libs/use/style.uts'
	import { iconfont } from './iconfont.uts'

	defineOptions({
		name: 'ux-icon',
		mixins: [xstyleMixin]
	})

	const emit = defineEmits(['click'])

	const props = defineProps({
		type: {
			type: String,
			default: ''
		},
		color: {
			type: String,
			default: ''
		},
		darkColor: {
			type: String,
			default: ''
		},
		size: {
			default: 0
		},
		fontFamily: {
			type: String,
			default: 'ux-iconfont'
		},
		// 向下兼容 保留参数
		customFamily: {
			type: String,
			default: ''
		}
	})

	const fontColor = computed(() : string => {
		return useFontColor(props.color, props.darkColor)
	})
	
	const fontSize = computed(() : number => {
		return useFontSize($ux.Util.getPx(props.size), 0)
	})
	
	const icon = computed(() : any => {
		return iconfont[props.type] ?? props.type
	})
	
	const isFont = computed(() : boolean => {
		let _icon = icon.value as string
		
		if (_icon == '') {
			return true
		}
		
		if ($ux.Verify.isURL(_icon)) {
			return false
		}
		
		if (_icon.indexOf('/') != -1) {
			return false
		}
		
		return true
	})
	
	const style = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('color', fontColor.value)
		css.set('font-size', $ux.Util.addUnit(fontSize.value))
		css.set('font-family', props.customFamily == '' ? props.fontFamily : props.customFamily)
		
		css = $ux.Util.xStyle(css, props.margin, props.mt, props.mr, props.mb, props.ml, props.padding, props.pt, props.pr, props.pb, props.pl)
		
		return css
	})
	
	const imgStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('width', $ux.Util.addUnit(fontSize.value))
		css.set('height', $ux.Util.addUnit(fontSize.value))
		
		return css
	})
	
	onMounted(() => {
		
	})
	
	function click(e: MouseEvent) {
		emit('click', e)
	}
</script>

<style lang="scss">
	.ux-icon {
		font-family: ux-iconfont;
	}
</style>