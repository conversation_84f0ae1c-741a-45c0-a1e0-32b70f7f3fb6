import { UxFilesErrorCode, UxFilesFail } from "./interface.uts"

export const UniErrorSubject = 'ux-files';

export const UxFilesErrors : Map<UxFilesErrorCode, string> = new Map([
  [-1, 'open files error'],
  [-2, 'search files error'],
])

export class UxFilesFailImpl extends UniError implements UxFilesFail {
  constructor(errCode : UxFilesErrorCode) {
    super();
    this.errSubject = UniErrorSubject;
    this.errCode = errCode;
    this.errMsg = UxFilesErrors.get(errCode) ?? "";
  }
}
