import { OpenDatabase, OpenDatabaseOptions } from '../interface.uts'
import { CloseDatabase } from '../interface.uts'
import { DeleteDatabase, DeleteDatabaseOptions } from '../interface.uts'
import { ExecSQL, ExecSQLOptions } from '../interface.uts'
import { ExecQuery, ExecQueryOptions } from '../interface.uts'
import { ExecRawQuery, ExecRawQueryOptions } from '../interface.uts'
import { ExecInsert, ExecInsertOptions } from '../interface.uts'
import { ExecUpdate, ExecUpdateOptions } from '../interface.uts'
import { ExecDelete, ExecDeleteOptions } from '../interface.uts'
import { ExecTransaction, ExecTransactionOptions } from '../interface.uts'
import { ExecCount, ExecCountOptions } from '../interface.uts'
import { GetColumns, GetColumnsOptions } from '../interface.uts'
import { GetPath } from '../interface.uts'
import { IsOpen } from '../interface.uts'
import { UxSqlSuccess } from '../interface.uts'
import { UxSqlFailImpl } from '../unierror.uts'

/**
 * 打开数据库
 * @param { OpenDatabaseOptions } options 参数
 */
export const openDatabase : OpenDatabase = function (options : OpenDatabaseOptions) {
	options.complete?.()
}

/**
 * 关闭数据库
 */
export const closeDatabase : CloseDatabase = function (): boolean {
	return false
}

/**
 * 删除数据库
 */
export const deleteDatabase : DeleteDatabase = function (options: DeleteDatabaseOptions): boolean {
	return  false
}

/**
 * 执行SQL
 */
export const execSQL : ExecSQL = function (options: ExecSQLOptions): boolean {
	return true
}

/**
 * 查询
 */
export const query : ExecQuery = function (options: ExecQueryOptions): Array<Map<string, any | null>> {
	return []
}

/**
 * SQL查询
 */
export const rawQuery : ExecRawQuery = function (options: ExecRawQueryOptions): Array<Map<string, any | null>> {
	return []
}

/**
 * 插入SQL
 */
export const insert : ExecInsert = function (options: ExecInsertOptions): number {
	return 0
}

/**
 * 更新SQL
 */
export const update : ExecUpdate = function (options: ExecUpdateOptions): number {
	return 0
}

/**
 * 删除SQL
 */
export const del : ExecDelete = function (options: ExecDeleteOptions): number {
	return 0
}

/**
 * 执行事务
 */
export const transaction : ExecTransaction = function (options: ExecTransactionOptions): boolean {
	return true
}

/**
 * 行数
 */
export const count : ExecCount = function (options: ExecCountOptions): number {
	let count = 0
	
	return count
}

/**
 * 获取表字段
 */
export const getColumns : GetColumns = function (options: GetColumnsOptions): string[] {
	let columns: string[] = []
	
	return columns
}

/**
 * 获取数据库路径
 */
export const getPath : GetPath = function (): string {
	return ''
}

/**
 * 是否打开数据库
 */
export const isOpen : IsOpen = function (): boolean {
	return false
}

/**
 * date from string
 */
function fromDate (date: string): Date {
	let d = date.split(/[^0-9]/)
	
	let year = d.length < 1 ? 0 : parseInt(d[0])
	let month = d.length < 2 ? 0 : parseInt(d[1]) - 1
	let day = d.length < 3 ? 0 : parseInt(d[2])
	let hour = d.length < 4 ? 0 : parseInt(d[3])
	let minute = d.length < 5 ? 0 : parseInt(d[4])
	let second = d.length < 6 ? 0 : parseInt(d[5])
	
	return new Date(year, month, day, hour, minute, second)
}

/**
 * date to string
 */
function toDate (date: Date): string {
	let padding = (n: number): string => {
		return `${n}`.padStart(2, '0')
	}
	
	return `${date.getFullYear()}-${padding(date.getMonth() + 1)}-${padding(date.getDate())} ${padding(date.getHours())}:${padding(date.getMinutes())}:${padding(date.getSeconds())}`
}