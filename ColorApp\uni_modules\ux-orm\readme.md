
<p align="center">
    <img alt="logo" src="https://www.uxframe.cn/logo/logo.png" width="120" height="120" style="margin-bottom: 10px;">
</p>
<h3 align="center" style="margin: 30px 0 30px;font-weight: bold;font-size:40px;">UxFrame 零SQL本地数据库ORM SDK 1.0.1</h3>

## 特别说明

如果您已经购买了[UxFrame 低代码高性能UI框架](https://ext.dcloud.net.cn/plugin?id=16148), 则无需再次购买本插件，请点击上方`进入交流群`联系我免费获取离线版插件！

## 说明

本插件依赖[ux-sqlite](https://ext.dcloud.net.cn/plugin?id=21732)原生插件

通过链式调用实体对象，零SQL对本地数据库进行增删改查！

- 实体映射
- 类型映射（string, boolean, int, double, float, long, short, blob, timestamp, datetime, json）
- 主键
- 默认值
- 别名
- 运算符（ =, !=, >, >=, <. <=, like, not like, in, not in, between, not between, is null, is not null）
- 排序（ASC, DESC）
- 分页
- 表结构自动升级
- 删表
- 删库跑路
- 插入（insert）
- 批量插入（inserts）
- 删（delete）
- 改（update）
- 查（query）
- 行数（count）
- 支持 select
- 支持 groupBy
- 支持 having
- 支持原生sql
- 支持实体数据集
- 支持json数据集

### 定义表结构

``` ts

import { Table, TableColumn, Column } from '@/uni_modules/ux-orm'

export class UserColumn extends TableColumn {
	
	readonly id = Column.int('id').primaryKey()
	readonly uuid = Column.string('uuid')
	readonly nickname = Column.string('nickname').aliased('name')
	readonly phone = Column.string('phone')
	readonly avatar = Column.blob('avatar') // ios暂不支持ByteArray，后续官方会支持
	readonly sex = Column.short('sex').default(1)
	readonly age = Column.int('age')
	readonly vip = Column.boolean('is_vip').default(false)
	readonly extInfo = Column.json('ext_info').default({'city': '北京'})
	readonly createAt = Column.datetime('create_at').default(new Date())
	readonly updateAt = Column.timestamp('update_at')
	
	public constructor() {
	    super()
	}
}
```

### 实体映射

``` ts

export class UserTable extends Table {
	// 覆写表名称
	override name = 'tb_user'
	// 覆写表结构
	override columns: TableColumn = new UserColumn()
	
	// 字段映射
	public id ?: number = null
	public uuid ?: string = null
	public nickname ?: string = null
	public phone ?: string = null
	public avatar ?: ByteArray = null
	public sex ?: number = null
	public age ?: number = null
	public vip ?: boolean = null
	public extInfo ?: UTSJSONObject = null
	public createAt ?: Date = null
	public updateAt ?: number = null

	public constructor() {
		super()
	}
}

```

### 操作数据库之前需先初始化数据库

``` ts
import * as db from '@/uni_modules/ux-sqlite'

db.openDatabase({
	database: 'app',
	success: (res: db.UxSqlSuccess) => {
		uni.showToast({
			title: 'open db success',
			icon: 'none'
		})
	}
} as db.OpenDatabaseOptions)

``` 

### insert

``` ts

let user = new UserTable()

user.uuid = '1'
user.nickname = '老6'
user.phone = '18888888888'
user.avatar = new ByteArray(1)
user.sex = 1
user.age = 16
user.vip = true
user.extInfo = {
	'province': '北京',
	'city': '北京'
}
user.updateAt = new Date().getTime()
user.createAt = new Date()

// 返回行id
let id = user.insert()

``` 

### update

``` ts

let user = new UserTable()
user.nickname = '老登'

// 返回影响行数
let columns = user.columns as UserColumn
let i = user.where(columns.id.eq(2))
			.update()

``` 

### delete

``` ts

let user = new UserTable()

// 返回影响行数
let columns = user.columns as UserColumn
let i = user.where(columns.id.eq(2)).delete()

``` 

### count

``` ts

let user = new UserTable()
let columns = user.columns as UserColumn

// 返回满足条件的总行数
let count = user.where(columns.age.eq(15))
				.count()

``` 

### query

``` ts

let user = new UserTable()
let columns = user.columns as UserColumn

// 返回实体数据集
let datas = user.orderBy(columns.id.desc())
	.where(columns.nickname.like('老6%'))
	.query<UserTable>()

// 返回json数据集
let datas = user.orderBy(columns.id.desc())
	.where(columns.age.between(16, 25))
	.fromJson()
	.query<UTSJSONObject>()
	
// 先查询后过滤并打印
user.orderBy(columns.id.desc())
	.where(columns.nickname.like('老6%'))
	.query<UserTable>()
	.filter((e): boolean => {
		return e.age < 16
	})
	.forEach(e => {
		console.log(e)
	})
	
// 分页查询
let datas = user.orderBy(columns.id.desc())
	.where(columns.age.between(16, 25))
	.or(columns.nickname.like('老6%'))
	.paging(0, 10)
	.pagingInfo(pageInfo => {
		// 分页信息
		console.log(pageInfo);
	})
	.query<UTSJSONObject>()
	
// 分组查询
let datas = user.orderBy(columns.id.desc())
	.where(columns.age.between(16, 25))
	.or(columns.nickname.like('老6%'))
	.groupBy(columns.id.getName())
	.having(columns.id.gte(2))
	.fromJson()
	.query<UTSJSONObject>()
	
``` 

## 文档教程 ⬇️

[https://www.uxframe.cn](https://www.uxframe.cn)
