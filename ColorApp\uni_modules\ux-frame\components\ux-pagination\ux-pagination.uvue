<template>
	<view class="pagination">
		<text class="pagination-text" :class="{'pagination-disabled': currentPage == 1}" @click="prevPage">上一页</text>
		<text class="pagination-text" v-if="totalPages > 3 && currentPage != 1" @click="goToPage(1)">首页</text>
		<text style="margin-top:-7px;font-size:16px;" v-if="startPage > 1">...</text>
		
		<text class="pagination-text" v-for="page in pages" :key="page"
			:class="{ 'pagination-active': currentPage == page }" @click="goToPage(page)">{{ page }}</text>

		<text style="margin-top:-7px;font-size:16px;" v-if="endPage < totalPages">...</text>
		<text class="pagination-text" v-if="totalPages > 3 && currentPage != totalPages"
			@click="goToPage(totalPages)">尾页</text>
		
		<text class="pagination-text" :class="{'pagination-disabled': currentPage == totalPages}"
			@click="nextPage">下一页</text>
		
		<view class="pagination-skip">
			<text class="pagination-more">到第</text>
			<input class="pagination-input" type="number" v-model="inputPage" :max="totalPages" :min="1"
				@blur="validateInputPage" />
			<text class="pagination-more">页</text>
			<text class="pagination-submit" @click="jumpToPage">确定</text>
		</view>

		<text class="pagination-more">共 {{ totalItems }} 条</text>
		
		<picker mode="selector" :value="itemsPerPageIndex" :range="itemsPerPageOptions" @change="changeItemsPerPage">
			<text class="pagination-picker">{{ itemsPerPage }}条 / 页</text>
		</picker>
	</view>
</template>

<script>
	export default {
		name: 'cg-pagination',
		emits: ['pageChange', 'itemsPageChange'],
		props: {
			// 总条数
			totalItems: {
				type: Number,
				required: true
			},
			// 每页条数
			initialItemsPerPage: {
				type: Number,
				default: 20
			},
			// 当前页
			initialPage: {
				type: Number,
				default: 1
			}
		},
		data() {
			return {
				currentPage: this.initialPage, // 当前页
				itemsPerPage: this.initialItemsPerPage, // 每页条数
				itemsPerPageOptions: [10, 20, 30, 50, 70, 100], // 可选择的每页条数
				itemsPerPageIndex: 1, // 选中的序号
				visiblePages: 1, // 当前页前后显示的页数，确保始终显示三个页码（当前页 + 左右各1个）
				inputPage: this.initialPage // 跳转到页码输入框的值
			}
		},
		computed: {
			totalPages() : number {
				return Math.ceil(this.totalItems / this.itemsPerPage) // 总页数
			},
			startPage() : number {
				// 根据当前页计算起始页，确保始终显示3个页码
				if (this.currentPage - this.visiblePages <= 1) {
					return 1
				}
				if (this.currentPage + this.visiblePages >= this.totalPages) {
					return Math.max(1, this.totalPages - 2) // 保证至少显示3个页码
				}
				return this.currentPage - this.visiblePages
			},
			endPage() : number {
				// 根据当前页计算结束页，确保始终显示3个页码
				if (this.currentPage + this.visiblePages >= this.totalPages) {
					return this.totalPages
				}
				if (this.currentPage - this.visiblePages <= 1) {
					return Math.min(this.totalPages, 3) // 保证至少显示3个页码
				}
				return this.currentPage + this.visiblePages
			},
			pages() : number {
				let pages = []
				for (let i = this.startPage; i <= this.endPage; i++) {
					pages.push(i)
				}
				return pages
			}
		},
		methods: {
			goToPage(page : number) {
				if (page >= 1 && page <= this.totalPages) {
					this.currentPage = page
					this.inputPage = page // 同步输入框的值
					this.$emit('pageChange', this.currentPage); // 将当前页码传递给父组件
				}
			},
			prevPage() {
				if (this.currentPage > 1) {
					this.goToPage(this.currentPage - 1)
				}
			},
			nextPage() {
				if (this.currentPage < this.totalPages) {
					this.goToPage(this.currentPage + 1)
				}
			},
			changeItemsPerPage(event) {
				this.itemsPerPage = this.itemsPerPageOptions[event.detail.value] // 更新每页条数
				this.currentPage = 1 // 每次更改时重置到第一页
				this.inputPage = 1 // 同步输入框页码
				this.$emit('itemsPageChange', this.itemsPerPage) // 通知父组件更改了每页条目数
			},
			jumpToPage() {
				const page = parseInt(this.inputPage)
				if (page >= 1 && page <= this.totalPages) {
					this.goToPage(page) // 跳转到输入的页码
				}
				else {
					// 页码无效，恢复输入框为当前页码
					this.inputPage = this.currentPage
				}
			},
			validateInputPage() {
				const page = parseInt(this.inputPage)
				if (isNaN(page) || page < 1 || page > this.totalPages) {
					this.inputPage = this.currentPage // 恢复到当前页
				}
			}
		}
	}
</script>

<style>
	.pagination {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		flex-wrap: wrap;
		padding-top: 50px;
	}

	.pagination-text {
		padding: 0 10px;
		/* #ifdef WEB */
		cursor: pointer;
		/* #endif */
		font-size: 14px;
	}

	.pagination-text:hover {
		color: #0960bd;
	}

	.pagination-active {
		height: 26px;
		line-height: 26px;
		background-color: #0960bd;
		color: #fff;
		border-radius: 2px;
		padding: 0 7px;
	}

	.pagination-active:hover {
		color: #fff;
	}

	.pagination-disabled {
		color: #d2d2d2 !important;
		/* #ifdef WEB */
		cursor: not-allowed !important;
		/* #endif */
	}

	.pagination-more {
		padding: 0 10px;
		font-size: 14px;
	}

	.pagination-skip {
		display: flex;
		flex-direction: row;
		align-items: center;
	}

	.pagination-input {
		width: 40px;
		height: 26px;
		line-height: 25px;
		text-align: center;
		font-size: 13px;
		border: 1px solid #e3e3e3;
	}

	.pagination-submit {
		width: 40px;
		height: 26px;
		line-height: 25px;
		text-align: center;
		font-size: 13px;
		background-color: #0960bd;
		color: #fff;
		border-radius: 2px;
		/* #ifdef WEB */
		cursor: pointer;
		/* #endif */
	}

	.pagination-submit:hover {
		opacity: 0.8;
	}

	.pagination-picker {
		font-size: 14px;
		display: block;
		;
		height: 27px;
		line-height: 26px;
		padding: 0 10px;
		border: 1px solid #e3e3e3;
	}
</style>