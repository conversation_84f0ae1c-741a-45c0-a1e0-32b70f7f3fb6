import { defineConfig } from 'vite';
import uni from '@dcloudio/vite-plugin-uni';

// 原子化css - 可选
import autocss from './uni_modules/ux-frame/libs/plugins/autocss/index.mjs';

// 文档生成 - 内部使用不用配置
import doc from './uni_modules/ux-frame/libs/plugins/doc/index.mjs';

export default defineConfig({
	plugins: [
		uni(),
		autocss({
			debug: false,
			autoUseSnippets: true,
			generateGlobalCss : false,
			configFile: './autocss/config.js',
			cssFile: 'autocss/index.css',
			cssSnippetsFile: 'autocss/snippets.css',
			exclude: ['uni_modules/*', 'node_modules/*']
		}),
		doc({
			output: 'docs',
			component: [
				'uni_modules/ux-frame/components',
				'uni_modules/ux-blur/components',
				'uni_modules/ux-camera-view/utssdk/doc',
				'uni_modules/ux-lottie/components',
				'uni_modules/ux-movable/components',
				'uni_modules/ux-shimmer/utssdk/doc',
				'uni_modules/ux-sprite/utssdk/doc',
				'uni_modules/ux-svg/components',
			],
			suffix: 'uvue'
		})
	]
});