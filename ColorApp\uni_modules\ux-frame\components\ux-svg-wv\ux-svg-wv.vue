<template>
	<web-view :id="myId" class="ux-svg" src="/uni_modules/ux-frame/hybrid/html/svg.html" @message="change"/>
</template>

<script setup lang="ts">
	
	/**
	 * Svg 图片
	 * @description SVG加载组件
	 * @demo pages/component/svg.uvue
	 * @tutorial https://www.uxframe.cn/component/svg.html
	 * @event {Function} 			change 						Function | svg生成时触发
	 * <AUTHOR>
	 * @date 2024-11-28 14:17:45
	 */
	
	import { ref, computed } from 'vue'
	import { $ux } from '../../index'
	
	defineOptions({
		name: 'ux-svg'
	})
	
	const emit = defineEmits(['load', 'error'])
	
	const props = defineProps({
		src: {
			type: String,
			default: ''
		},
	})
	
	const myId = `ux-svg-${$ux.Random.uuid()}`
	
	function change(event : UniWebViewMessageEvent) {
		const data = event.detail.data![0]
		if ((data['init'] ?? false) == true) {
			const ctx = uni.createWebviewContext(myId, getCurrentInstance()?.proxy);
			
			if(ctx == null) {
				emit('error')
			} else {
				ctx?.evalJS(`onReceiveSvg('${props.src}')`);
			}
		} else {
			let png = data['png'] ?? ''
			
			if(png == '') {
				emit('error')
			} else {
				emit('load', png)
			}
		};
	}
	
</script>

<style lang="scss" scoped>
	.ux-svg {
		display: flex;
	}

</style>