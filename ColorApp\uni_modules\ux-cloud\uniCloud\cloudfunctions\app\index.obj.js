const db = uniCloud.database();
const appVersion = db.collection('app-version')

module.exports = {
	_before: function() { // 通用预处理器

	},
	/**
	 * 发布版本
	 * @param {string} version 版本
	 * @param {string} platform 平台
	 * @returns {object} 返回值描述
	 */
	async publishVersion({
		type,
		version,
		title,
		contents,
		url,
		platform,
		mandatory
	}) {
		
		let data = {
			type: type,
			version: version,
			title: title,
			contents: contents,
			url: url,
			platform: platform,
			mandatory: mandatory
		}
		
		await appVersion.add([data])
	
		return {
			errCode: 0,
			errMsg: 'publishVersion:ok',
		}
	},
	/**
	 * 检查版本
	 * @param {string} version 版本
	 * @param {string} platform 平台
	 * @returns {object} 返回值描述
	 */
	async checkVersion({
		version,
		platform,
		uniPlatform
	}) {
		let {
			data: [data]
		} = await appVersion.where({
			platform: uniPlatform == 'web' ? 'web' : platform,
		}).get()
		
		let update = false
		let type = ''
		let title = ''
		let contents = ''
		let url = ''
		let pkgName = ''
		let mandatory = false
		
		if(data) {
			let v1 = version.split('.')
			let v2 = data.version.split('.')
			
			let c1 = Number(v1[0]) * 1000 + Number(v1[1]) * 100 + Number(v1[2]) * 10
			let c2 = Number(v2[0]) * 1000 + Number(v2[1]) * 100 + Number(v2[2]) * 10
			
			if(c1 < c2) {
				update = true
				version = data.version
				type = data.type
				title = data.title
				contents = data.contents
				url = data.url
				mandatory = false
			}
		}

		return {
			update: update,
			type: type,
			version: version,
			title: title,
			contents: contents,
			url: url,
			pkgName: pkgName,
			mandatory: mandatory
		}
	}

}