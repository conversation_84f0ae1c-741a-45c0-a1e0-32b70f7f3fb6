// #ifndef UNI-APP-X
import { ref } from 'vue'
// #endif

export class SizeBase {
	
	/**
	 * 字体尺寸
	 */
	normal = ref(0)
	
	/**
	 * 字体迷你尺寸
	 */
	mini = ref(0)
	
	/**
	 * 字体尺寸较小值
	 */
	small = ref(0)
	
	/**
	 * 字体尺寸较大值
	 */
	large = ref(0)
	
	/**
	 * 字体尺寸超大值
	 */
	huge = ref(0)
	
	constructor(fontSize: number) {
		this.normal.value = fontSize
		this.mini.value = fontSize - 4
		this.small.value = fontSize - 2
		this.large.value = fontSize + 2
		this.huge.value = fontSize + 4
	}

	/**
	 * 重置字体大小
	 */
	reset(fontSize: number) {
		this.normal.value = fontSize
		this.mini.value = fontSize - 4
		this.small.value = fontSize - 2
		this.large.value = fontSize + 2
		this.huge.value = fontSize + 4
	}
}
