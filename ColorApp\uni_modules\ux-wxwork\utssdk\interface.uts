/**
 * 注册
 */
export declare function register(options : UxRegisterOptions): void

/**
 * 登录
 */
export declare function login(options : UxLoginOptions): void

/**
 * 分享
 */
export declare function share(options : UxShareOptions): void

/**
 * 企业微信app是否安装
 */
export declare function isInstalled(): boolean

/**
 * 打开企业微信
 */
export declare function openApp(): void

/**
 * 错误码
 * [9010000, 'unregister']
 * [9010001, 'register fail']
 * [9010002, 'share fail']
 * [9010003, 'login fail']
 * [9010004, 'auth denied']
 * [9010005, 'user cancel']
 * [9010006, 'unsupport']
 * [9010007, 'uninstall weixin']
 * [9010008, 'unknow error']
 */
export type MyErrorCode = 9010000 | 9010001 | 9010002 | 9010003 | 9010004 | 9010005 | 9010006 | 9010007 | 9010008;

/**
 * 错误回调参数
 */
export interface UxWxworkFail extends IUniError {
  errCode : MyErrorCode
};

/**
 * 成功回调
 */
export type UxWxworkSuccess = {
	code : string,
	msg : any,
}

/**
 * 注册
 */
export type UxRegisterOptions = {
	/**
	 * 企业微信corpId
	 */
	appid : string
	/**
	 * 应用id
	 */
	agentid: string
	/**
	 * schema
	 */
	schema : string
	/**
	* 接口调用成功的回调
	*/
	success ?: (res : UxWxworkSuccess) => void
	/**
	* 接口调用失败的回调函数
	*/
	fail ?: (res : UxWxworkFail) => void
}

/**
 * 登录
 */
export type UxLoginOptions = {
	/**
	* 接口调用成功的回调
	*/
	success ?: (res : UxWxworkSuccess) => void
	/**
	* 接口调用失败的回调函数
	*/
	fail ?: (res : UxWxworkFail) => void
}

/**
 * 分享
 */
export type UxShareOptions = {
	/**
	 * 分享形式，如图文、纯文字、纯图片、文件、视频、小程序等
	 * 0	图文
	 * 1	纯文字
	 * 2	纯图片
	 * 3	文件
	 * 4	视频
	 * 5	小程序
	 */
	type ?: number
	/**
	 * 分享内容的标题
	 */
	title ?: string
	/**
	 * 分享内容的摘要，type 为 1 时必选
	 */
	summary ?: string
	/**
	 * 跳转链接，type 为 0 时必选
	 */
	href ?: string
	/**
	 * 图片地址，type为0时，推荐使用小于20Kb的图片，type 为 0、2、5 时必选
	 */
	imageUrl ?: string
	/**
	 * 音视频地址，type 为 4 时必选
	 */
	mediaUrl ?: string
	
	/**
	 * 文件地址，type 为 3 时必选
	 */
	filePath ?: string
	/**
	 * 分享小程序必要参数，type 为 5 时必选
	 */
	miniProgram ?: UxMiniProgram
	/**
	* 接口调用成功的回调
	*/
	success ?: (res : UxWxworkSuccess) => void
	/**
	* 接口调用失败的回调函数
	*/
	fail ?: (res : UxWxworkFail) => void
}

export type UxMiniProgram = {
	/**
	 * 微信小程序原始id
	 */
	id : string
	/**
	 * 点击链接进入的页面
	 */
	path : string
	/**
	 * 微信小程序版本类型，可取值： 0-正式版； 1-测试版； 2-体验版。 默认值为0。
	 */
	type : number
	/**
	 * 兼容低版本的网页链接
	 */
	webUrl ?: string
}