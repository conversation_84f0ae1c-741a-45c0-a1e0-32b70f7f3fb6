<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/calendar?title=Calendar"></Mobile>

# Calendar
> 组件类型：UxCalendarComponentPublicInstance

支持农历显示、节假日显示、单选、多选、拖拽范围选择模式

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| format | String |  | 格式化规则 |
| value | Array | [now] | 值 |
| range | Array |  | 范围(例如['2000-01-01','2023-12-10']) |
| [selectMode](#selectMode) | String | single | 选择模式 |
| [lunar](#lunar) | Boolean | true | 显示农历 |
| [holiday](#holiday) | Boolean | true | 显示节日 |
| [touchable](#touchable) | Boolean | false | 滑动翻页 |
| theme | String | `$ux.Conf.primaryColor` | 主题 |
| color | String |  | 颜色优先级高于主题 |
| background | String | `$ux.Conf.backgroundColor` | 背景色 |
| [backgroundDark](#backgroundDark) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| [showToday](#showToday) | Boolean | false | 显示今日按钮 |
| disabled | Boolean | false | 是否禁用 |

### [selectMode](#selectMode)

| 值   | 说明 |
|:------:|:----:|
| single单选
 |  |
| muti多选
 |  |
| range范围
 |  |

### [lunar](#lunar)

| 值   | 说明 |
|:------:|:----:|
| true
 |  |
| false
 |  |

### [holiday](#holiday)

| 值   | 说明 |
|:------:|:----:|
| true
 |  |
| false
 |  |

### [touchable](#touchable)

| 值   | 说明 |
|:------:|:----:|
| true
 |  |
| false
 |  |

### [backgroundDark](#backgroundDark)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [showToday](#showToday)

| 值   | 说明 |
|:------:|:----:|
| true
 |  |
| false
 |  |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| click | 点击时触发 |  |
| change | 确定选择时触发 |  |
  
  