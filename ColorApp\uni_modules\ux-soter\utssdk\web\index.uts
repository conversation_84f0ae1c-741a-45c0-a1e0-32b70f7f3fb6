import { StartAuth, StartAuthOptions } from '../interface.uts'
import { Cancel } from '../interface.uts'
import { IsSupport } from '../interface.uts'
import { IsEnrolled } from '../interface.uts'
import { IsFingerprint } from '../interface.uts'
import { IsPIN } from '../interface.uts'
import { UxSoterFailImpl } from '../unierror.uts'

/**
 * 开始 SOTER 生物认证
 * @param options StartAuthOptions
 */
export const startAuth : StartAuth = (options : StartAuthOptions) => {
	
}

/**
 * 取消验证
 */
export const cancel : Cancel = () => {
	
}

/**
 * 是否支持生物认证
 */
export const isSupport : IsSupport = () : boolean => {
	return false
}

/**
 * 设备是否录入如指纹等生物信息
 */
export const isEnrolled : IsEnrolled = () : boolean => {
	return false
}

/**
 * 设备是否录入指纹
 */
export const isFingerprint : IsFingerprint = () : boolean => {
	return false
}

/**
 * 设备是否设置登录密码
 */
export const isPIN : IsPIN = () : boolean => {
	return false
}
