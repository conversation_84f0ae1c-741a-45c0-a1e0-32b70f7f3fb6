<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/backtop?title=Backtop"></Mobile>

# Backtop
> 组件类型：UxBacktopComponentPublicInstance

支持形状、图标、大小、颜色、背景色、插槽等自定义样式

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| default | Slot |  | 默认插槽 |
| parent | String |  | 父级[scroll-viewlist-view]id，如果未指定系统会自动查找父级或平级下的[scroll-viewlist-view] |
| [shape](#shape) | String | circle | 形状 |
| icon | String | arrowup | 图标 |
| text | String | TOP | 文字 |
| duration | Number | 100 | 返回顶部滚动时间 |
| right | Any | 30 | 距右距离 |
| bottom | Any | 100 | 距底距离 |
| iconSize | Any | 18 | 图标大小 |
| fontSize | Any | 14 | 字体大小 |
| fontBold | Boolean | false | 字体加粗 |
| customFamily | String |  | 字体family |
| background | String | #FFFFFF | 背景色 |
| zIndex | Number | 10000 | 层级z-index |
| fixed | Boolean | true | 固定定位 |

### [shape](#shape)

| 值   | 说明 |
|:------:|:----:|
| circle圆形
 |  |
| square矩形
 |  |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| click | 点击按钮时触发 |  |
  
  