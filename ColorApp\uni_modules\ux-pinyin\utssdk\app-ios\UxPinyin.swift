import DCloudUTSFoundation

public class UxPinyin {

  static func toPinyin(hanzi: String) -> String {
    let mutableString = NSMutableString(string: hanzi) as CFMutableString
	
    // 转换为带音标的拼音（如 "zhōng"）
    CFStringTransform(mutableString, nil, kCFStringTransformMandarinLatin, false)
	
    // 移除音标（如 "zhong"）
    CFStringTransform(mutableString, nil, kCFStringTransformStripDiacritics, false)
	
    return mutableString as String
  }
  
  static func isChinese(hanzi: String) -> Bool {
	let pattern = "[\\u4E00-\\u9FA5\\u3400-\\u4DB5]"
	return hanzi.range(of: pattern, options: .regularExpression) != nil
  }
}