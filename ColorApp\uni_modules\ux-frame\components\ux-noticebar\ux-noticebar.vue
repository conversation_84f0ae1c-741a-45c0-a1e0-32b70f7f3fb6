<template>
	<view :id="myId" class="ux-noticebar" :style="[style]" @click="onClick">
		<view v-if="mutiline" class="ux-noticebar__mutiline">
			<text class="ux-noticebar__mutiline--text" :style="textStyle" v-for="(t, i) in texts" :key="i">{{ t }}</text>
		</view>
		<view v-else-if="direction == 'row'" class="ux-noticebar__warp">
			<view :id="warpId" class="ux-noticebar__warp__row transform" :style="rowStyle" @transitionend="onEnd">
				<text :id="textId" class="ux-noticebar__warp__row--text" :style="textStyle" v-for="(t, i) in texts" :key="i">{{ t }}</text>
			</view>
		</view>
		<view v-else-if="direction == 'col'" class="ux-noticebar__col">
			<swiper class="ux-noticebar__swiper" :vertical="true" :circular="true" :indicator-dots="false" :autoplay="true" :interval="swiperInterval">
				<swiper-item v-for="(t, i) in texts" :key="i">
					<text class="ux-noticebar__col--text" :style="textStyle">{{ t }}</text>
				</swiper-item>
			</swiper>
		</view>
		
		<view v-if="icon != ''" class="ux-noticebar__icon" :style="iconStyle">
			<slot name="left-icon">
				<ux-icon :type="icon" :size="iconSize" :color="iconColor"></ux-icon>
			</slot>
		</view>
		
		<view v-if="rightIcon != ''" class="ux-noticebar__right" :style="iconStyle" @click="onHandle">
			<slot name="right-icon">
				<ux-icon :type="rightIcon" :size="iconSize" :color="iconColor"></ux-icon>
			</slot>
		</view>
	</view>
</template>

<script setup lang="ts">
	
	/**
	 * NoticeBar 滚动通告栏
	 * @description 支持水平滚动、垂直翻页，可配置滚动速度、右侧按钮
	 * @demo pages/component/noticebar.uvue
	 * @tutorial https://www.uxframe.cn/component/noticebar.html
	 * @property {Array} 			texts 												String[] | 通告内容
	 * @property {String} 			theme=[primary|warning|success|error|info]			String | 主题颜色
	 * @value primary 	主色
	 * @value warning 	警告
	 * @value success 	成功
	 * @value error 	错误
	 * @value info 		文本
	 * @property {String} 			color 								String | 文字颜色 (默认 $ux.Conf.fontColor)
	 * @property {String} 			darkColor=[none|auto|color]			String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Any} 				size 								Any | 内容文字大小 (默认 $ux.Conf.fontSize)
	 * @property {String} 			icon 								String | 左侧图标 (默认 volumenotice-filled)
	 * @property {String} 			rightIcon 							String | 右侧图标
	 * @property {Any} 				iconSize 							Any | 图标尺寸 (默认 $ux.Conf.fontSize)
	 * @property {String} 			iconColor 							String | 图标颜色 (默认 $ux.Conf.fontColor)
	 * @property {String} 			background 							String | 背景颜色 (默认 $ux.Conf.backgroundColor)
	 * @property {String} 			backgroundDark=[none|auto|color]	String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Any} 				radius 								Any | 圆角 (默认 5)
	 * @property {Boolean} 			scrollable = [true|false] 			Boolean | 开启滚动 (默认 true)
	 * @property {String} 			direction=[row|col] 				String | 滚动方向 (默认 row)
	 * @value row 水平滚动
	 * @value col 垂直翻页
	 * @property {Number} 			speed 								Number | 文字水平垂直滚动的速度 (默认 60 px/秒)
	 * @property {Boolean} 			mutiline = [true|false] 			Number | 是否多行显示，多行则无滚动效果
	 * @event {Function} 			click 								Function | 点击时触发
	 * @event {Function} 			handle 								Function | 点击右侧按钮时触发
	 * @property {Array}			margin								Array | 边距 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}				mt									Any | 距上 单位px
	 * @property {Any}				mr									Any | 距右 单位px
	 * @property {Any}				mb									Any | 距下 单位px
	 * @property {Any}				ml									Any | 距左 单位px
	 * @property {Array}			padding								Array | 填充 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}				pt									Any | 上内边距 单位px
	 * @property {Any}				pr									Any | 右内边距 单位px
	 * @property {Any}				pb									Any | 下内边距 单位px
	 * @property {Any}				pl									Any | 左内边距 单位px
	 * @property {Array}			xstyle								Array<any> | 自定义样式
	 * <AUTHOR>
	 * @date 2024-12-03 11:33:28
	 */
	
	import { PropType, computed, getCurrentInstance, onMounted, ref, watch } from 'vue'
	import { onShow, onHide } from '@dcloudio/uni-app'
	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	import { useThemeColor, useForegroundColor, useFontColor, UxThemeType } from '../../libs/use/style.uts'
	
	defineOptions({
		name: 'ux-noticebar',
		mixins: [xstyleMixin]
	})
	
	const emit = defineEmits(['click', 'handle'])
	
	const props = defineProps({
		texts: {
			type: Array as PropType<Array<string>>,
			default: () : string[] => {
				return [] as string[]
			}
		},
		theme: {
			type: String,
			default: ''
		},
		color: {
			type: String,
			default: ''
		},
		darkColor: {
			type: String,
			default: ''
		},
		size: {
			default: 0
		},
		icon: {
			type: String,
			default: 'volumenotice-filled'
		},
		rightIcon: {
			type: String,
			default: ''
		},
		iconSize: {
			default: 0
		},
		iconColor: {
			type: String,
			default: ''
		},
		background: {
			type: String,
			default: ''
		},
		backgroundDark: {
			type: String,
			default: ''
		},
		radius: {
			default: 5
		},
		scrollable: {
			type: Boolean,
			default: true
		},
		direction: {
			type: String,
			default: 'row'
		},
		speed: {
			type: Number,
			default: 60
		},
		mutiline: {
			type: Boolean,
			default: false
		}
	})
	
	let instance = getCurrentInstance()?.proxy
	
	const myId = `ux-noticebar-${$ux.Random.uuid()}`
	const textId = `ux-noticebar-text-${$ux.Random.uuid()}`
	const warpId = `ux-noticebar-warp-${$ux.Random.uuid()}`
	const boxWidth = ref(0)
	const textWidth = ref(0)
	const duration = ref(3)
	const stop = ref(false)
	const scrolling = ref(false)
	
	const backgroundColor = computed((): string => {
		if(props.theme == '') {
			return useForegroundColor(props.background, props.backgroundDark)
		} else {
			return useThemeColor(props.theme as UxThemeType) 
		}
	})

	const fontSize = computed(() => {
		return useFontSize($ux.Util.getPx(props.size), 1)
	})
	
	const fontColor = computed((): string => {
		return useFontColor(props.color, props.darkColor)
	})
	
	const style = computed(() => {
		let css = {}
		
		css['background-color'] = backgroundColor.value
		css['border-radius'] = $ux.Util.addUnit(props.radius)
		
		if(props.mutiline) {
			css['min-height'] = $ux.Util.addUnit(25)
		} else {
			css['height'] = $ux.Util.addUnit(25)
		}
		
		css = $ux.Util.xStyle(css, props.margin, props.mt, props.mr, props.mb, props.ml, props.padding, props.pt, props.pr, props.pb, props.pl)
		
		return css
	})
	
	const rowStyle = computed(() => {
		let css = {}
		
		css['visibility'] = scrolling.value? 'visible' : 'hidden'
		css['width'] = `${scrolling.value? textWidth.value : boxWidth.value}px`
		
		return css
	})
	
	const textStyle = computed((): string => {
		return `color: ${fontColor.value};font-size:${fontSize.value}px`
	})
	
	const iconStyle = computed((): string => {
		return `background-color: ${backgroundColor.value}`
	})
	
	const swiperInterval = computed((): number => {
		return props.speed * 1000 / 20
	})
	
	function onEnd() {
		if(stop.value) {
			scrolling.value = false
			uni.getElementById(warpId)?.style?.setProperty('transition-duration', `${30}ms`)
			uni.getElementById(warpId)?.style?.setProperty('transform', `translateX(${boxWidth.value}px)`)
			return
		}
		
		if (scrolling.value) {
			scrolling.value = false
			uni.getElementById(warpId)?.style?.setProperty('transition-duration', `${30}ms`)
			uni.getElementById(warpId)?.style?.setProperty('transform', `translateX(${boxWidth.value}px)`)
		} else {
			scrolling.value = true
			uni.getElementById(warpId)?.style?.setProperty('transition-duration', `${duration.value}ms`)
			uni.getElementById(warpId)?.style?.setProperty('transform', `translateX(${-textWidth.value}px)`)
		}
	}
	
	async function init() {
		const rect = await $ux.Util.getBoundingClientRect(`#${myId}`, getCurrentInstance()?.proxy)
		
		let width = 0
		let children = await $ux.Util.getBoundingClientRectList(`#${textId}`, getCurrentInstance()?.proxy)
		if(children != null) {
			for (let i = 0; i < children.length; i++) {
				width += children[i].width
			}
		}
		
		let _width = rect.width
		boxWidth.value = Math.ceil(_width)
		textWidth.value = Math.ceil(Math.max(width, boxWidth.value))
		duration.value = Math.ceil((boxWidth.value + textWidth.value) / props.speed) * 1000
		
		if (props.scrollable) {
			uni.getElementById(warpId)?.style?.setProperty('transition-duration', `${duration.value}ms`)
			uni.getElementById(warpId)?.style?.setProperty('transform', `translateX(${-textWidth.value}px)`)
			scrolling.value = true
		}
	}
	
	function onClick(e : MouseEvent) {
		emit('click', e)
	}
	
	function onHandle(e : MouseEvent) {
		emit('handle', e)
		
		e.stopPropagation()
	}
	
	const texts = computed((): string[] => {
		return props.texts
	})
	
	watch(texts, () => {
		init()
	})
	
	onMounted(() => {
		setTimeout(() => {
			init()
		}, 200);
	})
	
	onShow(() => {
		if(stop.value) {
			stop.value = false
			init()
		}
	})
	
	onHide(() => {
		stop.value = true
	})
</script>

<style lang="scss" scoped>
	/* #ifdef H5 */
	uni-view {
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: stretch;
		align-content: stretch;
		justify-content: flex-start;
		box-sizing: border-box;
		min-height: 0px;
		min-width: 0px;
		overflow: hidden;
	}
	/* #endif */
	
	.ux-noticebar {
		display: flex;
		
		&__warp {
			flex: 1;

			&__row {
				flex: 1;
				display: flex;
				flex-direction: row;
				flex-wrap: nowrap;
				justify-content: flex-start;
				align-items: center;

				&--text {
					padding-left: 10px;
					font-size: 14px;
					line-height: 25px;
					color: white;
				}
			}
		}

		&__col {
			flex: 1;
			height: 100%;
			display: flex;
			flex-direction: row;
			justify-content: flex-start;
			align-items: center;

			&--text {
				padding-left: 25px;
				padding-bottom: 2px;
				font-size: 14px;
				lines: 1;
				line-height: 25px;
			}
		}
		
		&__swiper {
			flex: 1;
			height: 100%;
		}

		&__mutiline {
			height: 100%;
			padding: 5px 0;
			display: flex;
			flex-direction: row;
			align-items: flex-start;

			&--text {
				flex: 1;
				padding-left: 25px;
				font-size: 14px;
			}
		}

		&__icon {
			position: absolute;
			left: 0;
			width: 25px;
			height: 100%;
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
		}

		&__right {
			position: absolute;
			right: 0;
			width: 25px;
			height: 100%;
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
		}
	}

	.transform {
		transition-property: transform;
		transition-duration: 0ms;
		transition-timing-function: linear;
	}
	
</style>