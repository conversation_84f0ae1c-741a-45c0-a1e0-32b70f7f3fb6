
import { MyErrorCode, UxWeixinFail } from "./interface.uts"

export const UniErrorSubject = 'ux-weixin';

  /**
   * 错误码及对应的错误信息
   */
export const UniErrors : Map<MyErrorCode, string> = new Map([
	[9010000, 'unregister'],
	[9010001, 'register fail'],
	[9010002, 'share fail'],
	[9010003, 'login fail'],
	[9010004, 'auth denied'],
	[9010005, 'user cancel'],
	[9010006, 'unsupport'],
	[9010007, 'uninstall weixin'],
	[9010008, 'unknow error'],
	[9010009, '仅支持本地路径，请先下载后重试'],
]);

export class MyFailImpl extends UniError implements UxWeixinFail {

  constructor(errCode : MyErrorCode) {
    super();
	
    this.errSubject = UniErrorSubject;
    this.errCode = errCode;
    this.errMsg = UniErrors[errCode] ?? '';
  }
}