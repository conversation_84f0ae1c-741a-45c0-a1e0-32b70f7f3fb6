<template>
	<view slot="refresher" class="ux-refresher" @click="click">
		<view v-if="state == 0 || state == 1" class="ux-refresher__icon transform" :class="[state == 0? '' : 'ux-refresher__rotate']">
			<ux-icon type="arrow-down"></ux-icon>
		</view>
		<ux-loading v-else-if="state == 2" type="icon"></ux-loading>
		<view v-else-if="state == 3" class="ux-refresher__icon">
			<ux-icon type="check"></ux-icon>
		</view>
		
		<text class="ux-refresher__text">{{ states[state] }}</text>
	</view>
</template>

<script setup lang="ts">
	/**
	 * 下拉刷新
	 * @description 支持自定义文案
	 * @demo pages/component/refresher.uvue
	 * @tutorial https://www.uxframe.cn/component/refresher.html
	 * @property {Number}		state  				Number | 状态下标 (默认 [0)
	 * @property {Array} 		states 				string[] | 状态文案 (默认 ['下拉刷新', '释放刷新', '刷新中...', '刷新成功'])
	 * @event {Function}		click				Function | 点击时触发
	 * <AUTHOR>
	 * @date 2023-11-03 14:13:18
	 */
	
	defineOptions({
		name: 'ux-refresher'
	})
	
	const emit = defineEmits(['click'])
	
	defineProps({
		state: {
			type: Number,
			default: 0
		},
		states: {
			type: Array,
			default: (): string[] => {
				return ['下拉刷新', '释放刷新', '刷新中...', '刷新成功'] as string[]
			}
		},
	})
	
	function click(e: MouseEvent) {
		emit('click', e)
	}
</script>

<style lang="scss" scoped>

	.ux-refresher {
		width: 100%;
		height: 50px;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		
		&__text {
			color: gray;
			font-size: 13px;
			margin-left: 5px;
		}
		
		&__icon {
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
			height: 22px;
			transform: rotate(0deg);
		}
		
		&__rotate {
			transform: rotate(180deg);
		}
	}
	
	.transform {
		transition-property: transform;
		transition-duration: 100ms;
	}
	
</style>