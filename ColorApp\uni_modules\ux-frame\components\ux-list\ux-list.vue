<template>
	<!-- #ifndef APP-NVUE -->
	<scroll-view
		:id="myId" 
		class="ux-list" 
		:style="style"
		:scroll-y="true"
		:upper-threshold="upperThreshold"
		:lower-threshold="lowerThreshold"
		:scroll-top="_scrollTop"
		:scroll-left="_scrollLeft"
		:scroll-into-view="scrollIntoView"
		:scroll-with-animation="scrollWithAnimation" 
		:enable-back-to-top="enableBackToTop"
		:show-scrollbar="showScrollbar"
		:refresher-enabled="refresherEnabled"
		:refresher-threshold="refresherThreshold"
		refresher-default-style="none"
		:refresher-background="refresherBackground"
		:refresher-triggered="refresherTriggered"
		@refresherpulling="refresherpulling"
		@refresherrefresh="refresherrefresh"
		@refresherrestore="refresherrestore"
		@refresherabort="refresherabort"
		@scrolltoupper="scrolltoupper"
		@scrolltolower="scrolltolower"
		@scroll="scroll"
		@scrollend="scrollend">
		<ux-refresher v-if="refresherEnabled" class="ux-refresher" :style="refresherStyle" :states="refresherStates" :state="refresherState"></ux-refresher>
		
		<ux-list-item ref="cell-header" id="cell-header">
			<slot name="header"></slot>
		</ux-list-item>
		
		<ux-list-item :ref="`cell-${index}`" :id="`cell-${index}`" v-for="(item, index) in list" :key="index">
			<slot name="cell" :item="item" :index="index"></slot>
		</ux-list-item>
		
		<slot></slot>
		
		<ux-list-item ref="cell-footer" id="cell-footer">
			<slot name="footer"></slot>
		</ux-list-item>
		
		<ux-loadmore v-if="loadmoreEnabled" :states="loadmoreStates" :state="loadmoreState"></ux-loadmore>
		<ux-placeholder v-if="placeholder" :tabbar="true"></ux-placeholder>
		
		<view style="position: fixed;">
			<ux-backtop v-if="backtop && $slots['backtop'] == null" ref="uxBacktopRef" :parent="myId" @click="toTop"></ux-backtop>
			<ux-backtop v-if="backtop && $slots['backtop'] != null" ref="uxBacktopRef" :parent="myId" @click="toTop">
				<slot name="backtop"></slot>
			</ux-backtop>
		</view>
	</scroll-view>
	<!-- #endif -->
	<!-- #ifdef APP-NVUE -->
	<list
		:id="myId" 
		class="ux-list" 
		:style="style"
		:show-scrollbar="showScrollbar"
		:bounces="bounces"
		:loadmoreoffset="lowerThreshold"
		:offset-accuracy="offsetAccuracy"
		:pagingEnabled="pagingEnabled"
		:enable-back-to-top="enableBackToTop"
		:render-reverse="renderReverse" 
		:scrollable="disabled"
		@loadmore="scrolltolower"
		@scroll="scroll"
		@scrollend="scrollend">
		<cell slot="refresher">
			<ux-refresher v-if="refresherEnabled" :states="refresherStates" :state="refresherState"></ux-refresher>
		</cell>
		
		<ux-list-item ref="cell-header" id="cell-header">
			<slot name="header"></slot>
		</ux-list-item>
		
		<ux-list-item :ref="`cell-${index}`" :id="`cell-${index}`" v-for="(item, index) in list" :key="index">
			<slot name="cell" :item="item" :index="index"></slot>
		</ux-list-item>
		
		<ux-list-item ref="cell-footer" id="cell-footer">
			<slot name="footer"></slot>
		</ux-list-item>
		
		<cell>
			<ux-loadmore v-if="loadmoreEnabled" :states="loadmoreStates" :state="loadmoreState"></ux-loadmore>
		</cell>
		
		<cell>
			<ux-placeholder v-if="placeholder" :tabbar="true"></ux-placeholder>
		</cell>
		
		<cell style="position: fixed;">
			<ux-backtop v-if="backtop && $slots['backtop'] == null" ref="uxBacktopRef" :parent="myId" @click="toTop"></ux-backtop>
			<ux-backtop v-if="backtop && $slots['backtop'] != null" ref="uxBacktopRef" :parent="myId" @click="toTop">
				<slot name="backtop"></slot>
			</ux-backtop>
		</cell>
	</list>
	<!-- #endif -->
</template>

<script setup lang="ts">
	
	/**
	 * list-view 高性能虚拟滚动列表；nuve是系统的list组件，非虚拟列表
	 * @description 扩展支持自定义下拉刷新、上拉加载自定义样式，可显示返回顶部按钮
	 * @demo pages/component/list.uvue
	 * @tutorial https://www.uxframe.cn/component/list.html
	 * @property {Slot}			cell  									Slot | cell slot
	 * @property {Slot}			header  								Slot | header slot
	 * @property {Slot}			footer  								Slot | footer slot
	 * @property {Slot}			refresher  								Slot | refresher slot
	 * @property {Slot}			loadmore  								Slot | loadmore slot
	 * @property {Slot}			backtop  								Slot | backtop slot
	 * @property {Object}		list									Object[] 虚拟节点数据列表
	 * @property {Number}		pageSize								Number | 单页item数量 (默认 10)
	 * @property {Number}		cachePages								Number | 缓存上下页面数 (默认 1）
	 * @property {Boolean}  	bounces = [true|false]  				Boolean | 控制是否回弹效果，只支持app-nvue (默认 true)
	 * @property {Number}		upperThreshold  						Number | 距顶部/左边多远时（单位px），触发 scrolltoupper 事件（默认 50 ）
	 * @property {Number} 		lowerThreshold 							Number | 距底部/右边多远时（单位px），触发 scrolltolower 事件 （默认 50 ）
	 * @property {Number} 		scrollTop 								Number | 设置竖向滚动条位置 （默认 0 ）
	 * @property {Number}  		scrollLeft								Number | 设置横向滚动条位置（默认 0）
	 * @property {String}  		scrollIntoView							String | 值应为某子元素id（id不能以数字开头）。设置哪个方向可滚动，则在哪个方向滚动到该元素
	 * @property {Boolean}  	enableBackToTop							Boolean | iOS点击顶部状态栏滚动条返回顶部，只支持app-nvue，微信小程序 (默认 false)
	 * @property {Number}  		offsetAccuracy							Number | 控制 onscroll 事件触发的频率：表示两次onscroll事件之间列表至少滚动了10px。注意，将该值设置为较小的数值会提高滚动事件采样的精度，但同时也会降低页面的性能，只支持app-nvue（默认 10）
	 * @property {Boolean}  	pagingEnabled							Boolean | 是否按分页模式显示List，只支持app-nvue（默认 false）
	 * @property {Boolean}  	renderReverse							Boolean | 定义是否从底部渲染，需搭配cell的属性render-reverse-position共同使用，单独配置会导致渲染异常，只支持app-nvue（默认 false）
	 * @property {Boolean}  	scrollWithAnimation = [true|false]  	Boolean | 是否在设置滚动条位置时使用滚动动画，设置false没有滚动动画 (默认 true)
	 * @property {Boolean}  	refresherEnabled = [true|false]  		Boolean | 开启下拉刷新 (默认 false)
	 * @property {Number}  		refresherThreshold  					Number | 设置下拉刷新阈值（默认 45 ）
	 * @property {String}		refresherBackground						String | 设置下拉刷新区域背景颜色 (默认 transparent)
	 * @property {Boolean}		refresherTriggered = [true|false]		Boolean | 设置当前下拉刷新状态，true 表示下拉刷新已经被触发，false 表示下拉刷新未被触发 (默认 false)
	 * @property {Array}		refresherStates							Array | 下拉刷新状态文案
	 * @property {Boolean}  	loadmoreEnabled = [true|false]  		Boolean | 开启上拉加载 (默认 false)
	 * @property {Array}		loadmoreStates							Array | 上拉加载状态文案
	 * @property {Boolean}		showScrollbar = [true|false]			Boolean | 控制是否出现滚动条 (默认 false)
	 * @property {Boolean}		placeholder = [true|false]				Boolean | 底部导航栏高度占位
	 * @property {Boolean}		disabled = [true|false]					Boolean | 禁止滚动 (默认 false)
	 * @property {String}		background								String | 背景颜色 (默认 transparent)
	 * @property {Array}		xstyle									Array<any> | 自定义样式
	 * @event {Function}		refresherpulling						Function | 下拉刷新控件被下拉
	 * @event {Function}		refresherrefresh						Function | 下拉刷新被触发
	 * @event {Function}		refresherrestore						Function | 下拉刷新被复位
	 * @event {Function}		refresherabort							Function | 下拉刷新被中止
	 * @event {Function}		loadmore								Function | 上拉加载触发
	 * @event {Function}		scrolltoupper							Function | 滚动到顶部/左边，会触发 scrolltoupper 事件
	 * @event {Function}		scrolltolower							Function | 滚动到底部/右边，会触发 scrolltolower 事件
	 * @event {Function}		scroll									Function | 滚动时触发，event.detail = {scrollLeft, scrollTop, scrollHeight, scrollWidth, deltaX, deltaY}
	 * @event {Function}		scrollend								Function | 滚动结束时触发，event.detail = {scrollLeft, scrollTop, scrollHeight, scrollWidth, deltaX, deltaY}
	 * <AUTHOR>
	 * @date 2023-11-04 01:12:28
	 */
	
	import { ref, computed, watch, PropType, provide, nextTick, onMounted, Component, toRef, proxyRefs, getCurrentInstance } from 'vue'
	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	
	defineOptions({
		name: 'ux-list',
		mixins: [xstyleMixin]
	})
	
	const emit = defineEmits([
		'refresherpulling',
		'refresherrefresh', 
		'refresherrestore', 
		'refresherabort',
		'loadmore',
		'scrolltoupper', 
		'scrolltolower', 
		'scroll',
		'scrollend'])
	
	const props = defineProps({
		list: {
			type: Object,
			default: []
		},
		pageSize: {
			type: Number,
			default: 10
		},
		cachePages: {
			type: Number,
			default: 1
		},
		bounces: {
			type: Boolean,
			default: true
		},
		upperThreshold: {
			type: Number,
			default: 50
		},
		lowerThreshold: {
			type: Number,
			default: 50
		},
		scrollTop: {
			type: Number,
			default: 0
		},
		scrollLeft: {
			type: Number,
			default: 0
		},
		scrollIntoView: {
			type: String,
			default: '',
		},
		scrollWithAnimation: {
			type: Boolean,
			default: true,
		},
		enableBackToTop: {
			type: Boolean,
			default: false,
		},
		offsetAccuracy: {
			type: Number,
			default: 10,
		},
		pagingEnabled: {
			type: Boolean,
			default: false,
		},
		renderReverse: {
			type: Boolean,
			default: false,
		},
		refresherEnabled: {
			type: Boolean,
			default: false,
		},
		refresherThreshold: {
			type: Number,
			default: 45,
		},
		refresherBackground: {
			type: String,
			default: 'transparent',
		},
		refresherTriggered: {
			type: Boolean,
			default: false,
		},
		refresherStates: {
			type: Array as PropType<Array<string>>,
			default: (): string[] => {
				return ['下拉刷新', '释放刷新', '刷新中...', '刷新成功']
			},
		},
		loadmoreEnabled: {
			type: Boolean,
			default: false,
		},
		loadmoreStates: {
			type: Array as PropType<Array<string>>,
			default: (): string[] => {
				return ['加载中...', '-- 我也是有底线的 --']
			},
		},
		showScrollbar: {
			type: Boolean,
			default: false,
		},
		backtop: {
			type: Boolean,
			default: false
		},
		placeholder: {
			type: Boolean,
			default: false
		},
		background: {
			type: String,
			default: 'transparent',
		}
	})
	
	const instance = getCurrentInstance().proxy
	const myId = `ux-list-${$ux.Random.uuid()}`
	const scrollY = ref(0)
	const scrollHeight = ref(0)
	const headerHeight = ref(0)
	const refresherState = ref(0)
	const loadmoreState = ref(0)
	const uxBacktopRef = ref(null)
	const pullingDis = ref(0)
	const _scrollTop = ref(0)
	const _scrollLeft = ref(0)
	
	const _list = ref([])
	
	provide('scrollY', scrollY)
	
	const __scrollTop = computed(() => {
		return props.scrollTop
	})
	
	watch(__scrollTop, () => {
		_scrollTop.value = __scrollTop.value
	})
	
	const __scrollLeft = computed(() => {
		return props.scrollLeft
	})
	
	watch(__scrollLeft, () => {
		_scrollLeft.value = __scrollLeft.value
	})
	
	const style = computed(() => {
		let css = {}
		
		css['background-color'] = props.background
		
		css = $ux.Util.xStyle(css, props.margin, props.mt, props.mr, props.mb, props.ml, props.padding, props.pt, props.pr, props.pb, props.pl)
		
		return css
	})
	
	const refresherStyle = computed(() => {
		let css = {}
		
		css['height'] = `${pullingDis.value}px`
		
		return css
	})
	
	const refresherTriggered = computed((): boolean => {
		return props.refresherTriggered
	})
	
	watch(refresherTriggered, (a: boolean, b: boolean) => {
		if(a == false && b == true) {
			// 刷新完成
			refresherState.value = 3
		}
	})
	
	function virtualList() {
		const _y = scrollY.value - headerHeight.value
		const _h = scrollHeight.value
		
		const indexs = props.list.map((e, index) => index)
		const chunks = $ux.Toolkit.chunk(indexs, props.pageSize)
		
		const pages = Math.floor(_y / _h)
		const min = Math.max(pages - props.cachePages, 0)
		const max = Math.min(pages + props.cachePages, chunks.length)
		
		let items = []
		if(chunks.length > 0) {
			for (let i = min; i <= max; i++) {
				if(i < chunks.length) {
					items.push(...chunks[i])
				}
			}
		}
		
		props.list.forEach((e, index) => {
			const el = instance?.$refs[`cell-${index}`][0]
			el.render(items.indexOf(index) != -1)
		})
		
		console.log(props.list.length, pages, min, max, [items[0], items[items.length - 1]]);
	}
	
	function scroll(e: ScrollEvent) {
		
		scrollY.value = e.detail.scrollTop
		
		// #ifndef APP-NVUE
		virtualList()
		// #endif
		
		// 显示置顶不卡顿
		if(props.backtop) {
			uxBacktopRef.value?.setShow(scrollY.value > 300)
		}
		
		emit('scroll', e)
	}
	
	function scrollend(e: ScrollEvent) {
		emit('scrollend', e)
	}
	
	function refresherpulling(e: RefresherEvent) {
		// 释放刷新
		if(refresherState.value == 0 || refresherState.value == 1) {
			if(e.detail.dy > props.refresherThreshold / 2) {
				refresherState.value = 1
			} else {
				refresherState.value = 0
			}
		}
		
		pullingDis.value = e.detail.dy > props.refresherThreshold ? props.refresherThreshold : e.detail.dy
		
		emit('refresherpulling', e)
	}
	
	function refresherrefresh(e: RefresherEvent) {
		// 刷新中
		refresherState.value = 2
		
		emit('refresherrefresh', e)
	}
	
	function refresherrestore(e: RefresherEvent) {
		emit('refresherrestore', e)
		
		// 刷新结束
		setTimeout(() => {
			pullingDis.value = 0
			refresherState.value = 0
		}, 50);
	}
	
	function refresherabort(e: RefresherEvent) {
		emit('refresherabort', e)
		
		// 刷新结束
		setTimeout(() => {
			pullingDis.value = 0
			refresherState.value = 0
		}, 50);
	}
	
	function scrolltoupper(e: ScrollToUpperEvent) {
		emit('scrolltoupper', e)
	}
	
	function scrolltolower(e: ScrollToLowerEvent) {
		// 上拉触底加载
		if(props.loadmoreEnabled) {
			loadmoreState.value = 1
			emit('loadmore', e)
		}
		
		emit('scrolltolower', e)
	}
	
	function loadSuccess(lastPage: boolean) {
		loadmoreState.value = lastPage ? 2 : 0
	}
	
	function toTop() {
		_scrollTop.value = -1
		nextTick(() => {
			_scrollTop.value = 0
		})
	}
	
	async function calcHeight() {
		const rect = await $ux.Util.getBoundingClientRect(`#${myId}`, instance)
		scrollHeight.value = rect.height
		
		const rect2 = await $ux.Util.getBoundingClientRect('#cell-header', instance)
		headerHeight.value = rect2.height
	}
	
	onMounted(() => {
		calcHeight()
	})
	
	defineExpose({
		loadSuccess
	})
</script>

<style lang="scss" scoped>
	.ux-list {
		flex: 1;
		overflow: hidden;
	}
	
	.ux-refresher {
		overflow: hidden;
		transition-property: height;
		transition-duration: 200ms;
	}
</style>