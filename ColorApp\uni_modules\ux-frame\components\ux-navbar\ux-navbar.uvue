<template>
	<view class="ux-navbar" :class="fixed?'ux-navbar__fixed':''" :style="style">
		<ux-placeholder :statusbar="true"></ux-placeholder>
		<image v-if="background != ''" class="ux-navbar__background" :src="background" :mode="backgroundMode"></image>
		<ux-blur v-if="showBlur" class="ux-navbar__blur" :style="blurStyle" :radius="blurRadius" :color="blurColor"></ux-blur>
		
		<view class="ux-navbar__content" :style="bodyHeight">
			
			<view v-if="alignLeft" class="ux-navbar__title_left" :style="bodyHeight">
				<view v-if="$slots['left'] == null && goback" id="goback" class="ux-navbar__goback" :style="bodyHeight" @click="onBack()">
					<ux-icon type="arrowleft" :size="22" :color="color"></ux-icon>
					<text v-if="gobackText != ''" class="ux-navbar__goback__text" :style="gobackStyle">{{ gobackText }}</text>
				</view>
				<view v-if="$slots['left'] != null" class="ux-navbar__goback" :style="bodyHeight">
					<slot name="left"></slot>
				</view>

				<slot name="title">
					<text class="ux-navbar__title__content" :style="[titleStyle]">{{ title }}</text>
				</slot>
			</view>
			<view v-else class="ux-navbar__title" :style="bodyHeight">
				<view v-if="$slots['left'] == null && goback" id="goback" class="ux-navbar__goback" :style="bodyHeight" @click="onBack()">
					<ux-icon type="arrowleft" :size="22" :color="color" :xstyle="['margin-left: 14px']"></ux-icon>
					<text v-if="gobackText != ''" class="ux-navbar__goback__text" :style="gobackStyle">{{ gobackText }}</text>
				</view>
				<view v-if="$slots['left'] != null" class="ux-navbar__goback" :style="bodyHeight">
					<slot name="left"></slot>
				</view>

				<slot name="title">
					<text class="ux-navbar__title__content" :style="[titleStyle]">{{ title }}</text>
				</slot>
			</view>
			
			<view class="ux-navbar__btn" :style="bodyHeight">
				<slot name="right"></slot>
				<slot name="global"></slot>
			</view>
			
		</view>
	</view>
</template>

<script setup>
	/**
	 * 顶部导航栏
	 * @description 支持左右插槽，更多自定义样式
	 * @demo pages/component/navbar.uvue
	 * @tutorial https://www.uxframe.cn/component/navbar.html
	 * @property {Slot}				left											Slot | 左侧插槽
	 * @property {Slot}				title											Slot | 标题插槽
	 * @property {Slot}				right											Slot | 右侧插槽
	 * @property {String}			title											String | 标题
	 * @property {Boolean}			alignLeft = [true|false]						Boolean | 标题居左 (默认 false)
	 * @property {String}			color											String | 标题颜色 (默认 $ux.Conf.titleColor)
	 * @property {String} 			darkColor=[none|auto|color]						String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示				
	 * @value auto 自动适配深色模式				
	 * @value color 其他颜色
	 * @property {Any}				size											Any | 字体大小 (默认 $ux.Conf.fontSize)
	 * @property {Boolean}			bold = [true|false]								Boolean | 字体加粗 (默认 false)
	 * @property {Any}				height											Any | 导航栏高度 (默认 44)
	 * @property {Boolean}			border = [true|false]							Boolean | 显示下边框 (默认 true)
	 * @property {String}			borderColor										String | 下边框颜色 (默认 $ux.Conf.borderColor)
	 * @property {Array}			colors											String[] | 背景色 多个渐变
	 * @property {String} 			gradientDirection=[top|bottom|left|right]		String | 渐变色方向 (默认 bottom)
	 * @value top 向上
	 * @value bottom 向下
	 * @value left 向左
	 * @value right 向右
	 * @property {String}			background										String | 背景图
	 * @property {String}			backgroundMode=[scaleToFill|aspectFit|aspectFill|widthFix|heightFix|top|bottom|center|left|right|top left|top right|bottom left|bottom right] 	String | 背景图裁剪模式 （默认 aspectFill）
	 * @value scaleToFill		不保持纵横比缩放图片，使图片的宽高完全拉伸至填满 image 元素
	 * @value aspectFit			保持纵横比缩放图片，使图片的长边能完全显示出来。也就是说，可以完整地将图片显示出来
	 * @value aspectFill		保持纵横比缩放图片，只保证图片的短边能完全显示出来。也就是说，图片通常只在水平或垂直方向是完整的，另一个方向将会发生截取
	 * @value widthFix			宽度不变，高度自动变化，保持原图宽高比不变
	 * @value heightFix			高度不变，宽度自动变化，保持原图宽高比不变
	 * @value top				不缩放图片，只显示图片的顶部区域
	 * @value bottom			不缩放图片，只显示图片的底部区域
	 * @value center			不缩放图片，只显示图片的中间区域
	 * @value left				不缩放图片，只显示图片的左边区域
	 * @value right				不缩放图片，只显示图片的右边区域
	 * @value top left			不缩放图片，只显示图片的左上边区域
	 * @value top right			不缩放图片，只显示图片的右上边区域
	 * @value bottom left		不缩放图片，只显示图片的左下边区域
	 * @value bottom right		不缩放图片，只显示图片的右下边区域
	 * @property {Boolean}			showBlur = [true|false]							Boolean | 开启模糊背景 (默认 false )
	 * @property {Number}			blurRadius										Number | 模糊半径 (默认 10 )
	 * @property {Number}			blurAlpha										Number | 模糊透明度 (默认 0.5 )
	 * @property {Boolean}			fixed = [true|false]							Boolean | 固定定位 (默认 false)
	 * @property {Number}			zIndex											Number | 层级z-index (默认 10000)
	 * @property {Boolean}			goback = [true|false]							Boolean | 显示返回按钮 (默认 true)
	 * @property {String}			gobackText										String | 返回按钮文字
	 * <AUTHOR>
	 * @date 2023-10-03 20:31:11
	 */
	
	import { $ux } from '../../index'
	import { useTitleColor, useFontSize } from '../../libs/use/style.uts'
	
	defineOptions({
		name: 'ux-navbar'
	})

	const props = defineProps({
		title: {
			type: String,
			default: ''
		},
		alignLeft: {
			type: Boolean,
			default: false
		},
		color: {
			type: String,
			default: ''
		},
		darkColor: {
			type: String,
			default: ''
		},
		size: {
			default: 0
		},
		bold: {
			type: Boolean,
			default: true
		},
		height: {
			default: 44
		},
		border: {
			type: Boolean,
			default: true
		},
		borderColor: {
			type: String,
			default: ''
		},
		colors: {
			type: Array as PropType<Array<string>>,
			default: ():string[] => {
				return [] as string[]
			}
		},
		gradientDirection: {
			type: String,
			default: 'bottom'
		},
		background: {
			type: String,
			default: ''
		},
		backgroundMode: {
			type: String,
			default: 'aspectFill'
		},
		showBlur: {
			type: Boolean,
			default: false
		},
		blurRadius: {
			type: Number,
			default: 10
		},
		blurAlpha: {
			type: Number,
			default: 0.5
		},
		goback: {
			type: Boolean,
			default: true
		},
		gobackText: {
			type: String,
			default: ''
		},
		zIndex: {
			type: Number,
			default: 10000
		},
		fixed: {
			type: Boolean,
			default: false
		},
	})
	
	const statusBarHeight = uni.getWindowInfo().statusBarHeight
	
	const height = computed(() => {
		return statusBarHeight + $ux.Util.getPx(props.height)
	})

	const titleColor = computed(() : string => {
		return useTitleColor(props.color, props.darkColor)
	})
	
	const backgroundColor = computed(() : string => {
		return $ux.Conf.darkMode.value ? $ux.Conf.backgroundColor.dark.value : $ux.Conf.backgroundColor.color.value
	})
	
	const borderColor = computed(() : string => {
		if(props.borderColor != '') {
			return props.borderColor
		} else {
			return $ux.Conf.darkMode.value ? $ux.Conf.borderColor.darkColor.value : $ux.Conf.borderColor.color.value
		}
	})
	
	const blurColor = computed(() : string => {
		return $ux.Color.getRgba(backgroundColor.value, props.blurAlpha)
	})
	
	const fontSize = computed(() : number => {
		return useFontSize($ux.Util.getPx(props.size), 2)
	})
	
	const bodyHeight = computed(() => {
		let css = new Map<string, any>()
		
		css.set('height', `${$ux.Util.getPx(props.height)}px`)
		
		return css
	})
	
	const style = computed(() => {
		let css = new Map<string, any>()
	
		css.set('height', `${height.value}px`)
		css.set('z-index', props.zIndex)
	
		if (props.border) {
			css.set('border-bottom', `1px solid ${borderColor.value}`)
		}
	
		if(!props.showBlur) {
			let colors = [] as string[]
				
			props.colors.forEach((e : any | null) => {
				colors.push(e! as string)
			})
				
			if (colors.length == 0) {
				css.set('background-color', backgroundColor.value)
			} else if (colors.length == 1) {
				css.set('background-color', colors[0])
			} else {
				css.set('background', `linear-gradient(to ${props.gradientDirection}, ${colors.join(',')})`)
			}
		}
		
		return css
	})
	
	const blurStyle = computed(() => {
		let css = new Map<string, any>()
		
		css.set('position', 'absolute')
		css.set('top', 0)
		css.set('left', 0)
		css.set('right', 0)
		css.set('bottom', 0)
		css.set('width', '100%')
		css.set('height', `${height.value}px`)
		
		return css
	})
	
	const titleStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
	
		css.set('color', titleColor.value)
		css.set('font-size', $ux.Util.addUnit(fontSize.value))
		css.set('height', `${$ux.Util.addUnit(fontSize.value + 4)}`)
		css.set('font-weight', props.bold ? 'bold' : 'normal')
	
		return css
	})
	
	const gobackStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
	
		css.set('color', titleColor.value)
	
		return css
	})
	
	function onBack() {
		if(getCurrentPages().length == 1) {
			// 路由栈只剩当前页面 返回首页
			// #ifndef APP
			uni.reLaunch({
				url: '/'
			})
			// #endif
			
			// #ifdef APP
			// TODO app端无法返回首页
			// #endif
		} else {
			uni.navigateBack({
				delta : 1
			})
		}
	}
	
	const title = computed(():string => {
		return props.title
	})
	
	watch(title, () => {
		// #ifdef WEB
		// document.title = props.title
		// #endif
	}, {immediate: true})
</script>

<style lang="scss">
	.ux-navbar {
		width: 100%;
		height: 44px;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: flex-start;
		z-index: 100000;
		position: relative;
		
		&__blur {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			width: 100%;
			height: 100%;
		}
		
		&__background {
			position: absolute;
			width: 100%;
			height: 100%;
		}

		&__fixed {
			position: fixed;
			top: 0;
		}

		.ux-navbar__content {
			flex: 1;
			width: 100%;
			height: 44px;

			.ux-navbar__title {
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				height: 44px;
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: center;

				.ux-navbar__goback {
					position: absolute;
					left: 0;
					min-width: 40px;
					height: 40px;
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: flex-start;

					.ux-navbar__goback__text {
						font-size: 14px;
						margin-left: 8px;
						color: black;
						padding-bottom: 1px;
					}
				}

				.ux-navbar__title__content {
					max-width: 200px;
					height: 18px;
					font-size: 16px;
					color: #333333;
					font-weight: bold;
					text-overflow: ellipsis;
					text-align: center;
				}
			}

			.ux-navbar__title_left {
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				height: 44px;
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: flex-start;

				.ux-navbar__goback {
					padding-left: 14px;
					padding-right: 5px;
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: flex-start;

					.ux-navbar__goback__text {
						font-size: 14px;
						margin-left: 8px;
						color: black;
						padding-bottom: 1px;
					}
				}

				.ux-navbar__title__content {
					margin-left: 5px;
					width: 150px;
					height: 18px;
					font-size: 16px;
					color: #333333;
					font-weight: bold;
					text-overflow: ellipsis;
				}
			}

			.ux-navbar__btn {
				position: absolute;
				right: 0;
				width: 50%;
				height: 44px;
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: flex-end;
			}
		}
	}
</style>