<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/floatbutton?title=Floatbutton"></Mobile>

# Floatbutton
> 组件类型：UxFloatbuttonComponentPublicInstance

支持四角定位，自动靠边吸咐，支持阻尼效果，支持按钮展开

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| value | Array | 右下角 | 位置值 |
| [pos](#pos) | String | rb | 默认位置 |
| adsorb | Boolean | true | 自动吸附 |
| offset | Any | 15 | 距边偏移 |
| offsetX | Any | 0 | X轴偏移优先级高于offset |
| offsetY | Any | 0 | Y轴偏移优先级高于offset |
| duration | Number | 600 | 过渡时间 |
| width | Any | 50 | 宽度 |
| height | Any | 50 | 高度 |
| radius | Any | 25 | 圆角 |
| background | String | #fff | 背景色 |
| [backgroundDark](#backgroundDark) | String | #333 | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| shadow | Boolean | true | 显示阴影 |
| disabled | Boolean | false | 禁止滑动 |

### [pos](#pos)

| 值   | 说明 |
|:------:|:----:|
| rt右上
 |  |
| rc右中
 |  |
| rb右下
 |  |
| lt左上
 |  |
| lc左中
 |  |
| lb左下
 |  |
| ct中上
 |  |
| c中心
 |  |
| cb中下
 |  |

### [backgroundDark](#backgroundDark)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

