
export type UxShareWithSystemType = 'text' | 'image' | 'file'

export type UxShareWithSystemOptions = {
	type : UxShareWithSystemType, 
	title ?: string,
	summary ?: string,
	href ?: string,
	imageUrl ?: string, 
	success ?: (res : UxShareCallback) => void,
	fail ?: (res : UxShareCallback) => void,
	complete ?: (res : UxShareCallback) => void
}

export type ShareWithSystem = (options: UxShareWithSystemOptions) => void

export interface UxShareCallback extends IUniError {
	
}