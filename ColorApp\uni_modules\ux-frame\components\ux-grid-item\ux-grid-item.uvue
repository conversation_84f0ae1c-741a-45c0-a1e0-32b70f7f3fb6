<template>
	<view class="ux-grid-item" :style="style">
		<slot></slot>
	</view>
</template>

<script setup>
	/**
	 * GridItem 网格项
	 * @demo pages/component/grid.uvue
	 * @tutorial https://www.uxframe.cn/component/grid.html
	 * <AUTHOR>
	 * @date 2023-12-31 20:49:56
	 */
	
	import { $ux } from '../../index'
	import { useBorderColor } from '../../libs/use/style.uts'
	
	defineOptions({
		name: 'ux-grid-item'
	})
	
	const instance = getCurrentInstance()?.proxy
		
	const col = ref(3)
	const border = ref(false)
	
	const borderColor = computed((): string => {
		return useBorderColor('', '')
	})
	
	const style = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('width', `${100 / col.value}%`)
		css.set('max-width', `150px`)
		
		if(border.value) {
			css.set('border', `1px solid ${borderColor.value}`)
		}
		
		return css
	})
	
	function setData(data: UTSJSONObject) {
		col.value = data.getNumber('col') ?? 3
		border.value = data.getBoolean('border') ?? false
	}
	
	onMounted(() => {
		$ux.Util.$dispatch(instance!, 'ux-grid', 'register', instance)
	})
	
	defineExpose({
		setData
	})
</script>

<style lang="scss" scoped>
	
	.ux-grid-item {
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		box-sizing: border-box;
	}
</style>