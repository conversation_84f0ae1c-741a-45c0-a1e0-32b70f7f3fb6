<template>
	<view class="ux-sprite" :style="style">
		<canvas class="ux-sprite__canvas" :id="canvasId" :canvas-id="canvasId"></canvas>
	</view>
</template>

<script setup lang="ts">
	/**
	* 精灵动画组件
	* @demo pages/component/sprite.uvue
	* @tutorial https://www.uxframe.cn/component/sprite.html
	* @property {Array} 			frames					string[] | 精灵图集
	* @property {Number} 			duration				Number | 每帧周期 (默认 100)
	* @property {Boolean} 			play					Boolean | 是否播放 (默认 false)
	* @property {Boolean} 			loop					Boolean | 循环播放 (默认 false)
	* @property {Boolean} 			autoplay				Boolean | 自动播放 (默认 false)
	* @event {Function} 			start 					Function | 开始时触发
	* @event {Function} 			end 					Function | 结束时触发
	* <AUTHOR>
	* @date 2025-04-07 21:10:21
	*/
   
	import { ref, computed, watch, onBeforeUnmount, PropType, onMounted, getCurrentInstance } from 'vue'
	
	defineOptions({
		name: 'ux-sprite'
	})
	
	const emit = defineEmits(['start', 'end'])
	
	const props = defineProps({
		frames: {
			type: Array as PropType<string[]>,
			default: []
		},
		duration: {
			type: Number,
			default: 100
		},
		play: {
			type: Boolean,
			default: false
		},
		loop: {
			type: Boolean,
			default: false
		},
		autoplay: {
			type: Boolean,
			default: false
		}
	})
	
	const canvasId = 'ux-sprite-' + new Date().getTime()
	let ctx: CanvasRenderingContext2D | null = null
	let context: CanvasContext | null = null
	let frameIndex = 0
	let frameWidth = 0
	let frameHeight = 0 
	let intervalId = 0
	
	const instance = getCurrentInstance()?.proxy
	
	const frames = computed(() => {
		return props.frames
	})
	
	const play = computed(() => {
		return props.play
	})
	
	watch(frames, () => {
		stopAnim()
		playAnim()
	})
	
	watch(play, () => {
		if (play.value) {
			playAnim()
		} else {
			stopAnim()
		}
	})
	
	const style = computed(() => {
		let css = {}
		
		if(frameWidth > 0) {
			css['width'] = `${frameWidth}px`
		}
		
		if(frameHeight > 0) {
			css['height'] = `${frameHeight}px`
		}
		
		return css
	})
	
	function playAnim() {
		if(ctx == null) {
			return
		}
		
		const frameCount = props.frames.length
		if(frameCount == 0) {
			stopAnim()
			return
		}
		
		emit('start')
		
		intervalId = setInterval(() => {
			ctx!.clearRect(0, 0, frameWidth, frameHeight)
			
			const img = context!.createImage()
			img.src = props.frames[frameIndex]
			img.onload = () => {
				ctx!.drawImage(img, 0, 0, frameWidth, frameHeight)
				
				frameIndex = (frameIndex + 1) % frameCount
				
				if(!props.loop && frameIndex == 0) {
					stopAnim()
				}
			}
		}, props.duration)
	}
	
	function stopAnim() {
		ctx!.clearRect(0, 0, frameWidth, frameHeight)
		
		clearInterval(intervalId)
		emit('end')
	}
	
	function init() {
		const dpr = uni.getWindowInfo().pixelRatio
		
		// #ifdef UNI-APP-X
		uni.createCanvasContextAsync({
			id: canvasId,
			component: instance,
			success: (_context : CanvasContext) => {
				context = _context
				ctx = context.getContext('2d')
				
				frameWidth = ctx!.canvas.offsetWidth
				frameHeight = ctx!.canvas.offsetHeight
				
				ctx!.canvas.width = frameWidth * dpr
				ctx!.canvas.height = frameHeight * dpr
				ctx!.scale(dpr, dpr)
				
				if (props.autoplay) {
					playAnim()
				}
			},
			fail: (err) => {
				console.log(err);
			}
		})
		// #endif
		
		// #ifndef UNI-APP-X
		let ctx = uni.createCanvasContext(canvasId, instance)
		// frameWidth = ctx!.canvas.offsetWidth
		// frameHeight = ctx!.canvas.offsetHeight
		
		// ctx!.canvas.width = frameWidth * dpr
		// ctx!.canvas.height = frameHeight * dpr
		// ctx!.scale(dpr, dpr)
		
		// if (props.autoplay) {
		// 	playAnim()
		// }
		// #endif
	}
	
	onMounted(() => {
		init()
	})
	
	onBeforeUnmount(() => {
		clearInterval(intervalId)
	})
	
</script>

<style lang="scss" scoped>
	.ux-sprite {
		width: 100%;
		height: 100%;
		
		&__canvas {
			width: 100%;
			height: 100%;
		}
	}
</style>