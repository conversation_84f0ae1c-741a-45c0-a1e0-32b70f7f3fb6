import { UxMqttOptions, UxMqttConnectOptions, UxMqttSubscribeOptions, UxMqttPublishOptions, UxMqttStatusListener, UxMqttMessageListener } from '../interface.uts'
import Context from 'android.content.Context'
import Intent from 'android.content.Intent'
import MqttAndroidClient from 'info.mqtt.android.service.MqttAndroidClient'
import IMqttActionListener from 'org.eclipse.paho.client.mqttv3.IMqttActionListener'
import IMqttMessageListener from "org.eclipse.paho.client.mqttv3.IMqttMessageListener";
import IMqttToken from 'org.eclipse.paho.client.mqttv3.IMqttToken'
import MqttConnectOptions from 'org.eclipse.paho.client.mqttv3.MqttConnectOptions'
import MqttException from 'org.eclipse.paho.client.mqttv3.MqttException'
import MqttMessage from 'org.eclipse.paho.client.mqttv3.MqttMessage'
import Build from 'android.os.Build';
import IMqttDeliveryToken from 'org.eclipse.paho.client.mqttv3.IMqttDeliveryToken';
import MqttCallback from 'org.eclipse.paho.client.mqttv3.MqttCallback';

export type MqttServerStatus = 'offline' | 'online'

export class UxMqtt implements MqttCallback {

	opts : UxMqttOptions
	client : MqttAndroidClient
	status : MqttServerStatus = 'offline'
	statusListener ?: UxMqttStatusListener = null
	messageListener ?: UxMqttMessageListener = null

	constructor(opts : UxMqttOptions) {
		this.opts = opts
		this.client = MqttAndroidClient(UTSAndroid.getAppContext()!, this.opts.brokerUrl, this.opts.clientId)
	}
	
	override connectionLost(err : kotlin.Throwable | null) {
		this.status = 'offline'
		this.statusListener?.(false, err.toString())
	}
	
	override messageArrived(topic : string, message : MqttMessage) {
		this.messageListener?.(topic, String(message.getPayload()))
	}
	
	override deliveryComplete(token : IMqttDeliveryToken) {
		
	}

	/**
	 * 连接
	 */
	@UTSJS.keepAlive
	connect(opts: UxMqttConnectOptions) {
		this.statusListener = opts.statusListener
		this.messageListener = opts.messageListener
		
		let options = new MqttConnectOptions();
		
		// 清除会话
		options.setCleanSession(this.opts.cleanSession ?? true);
		// 自动重连
		options.setAutomaticReconnect(this.opts.automaticReconnect ?? true);
		// 超时
		if(this.opts.connectionTimeout != null) {
			options.setConnectionTimeout(this.opts.connectionTimeout!.toInt());
		}
		// 心跳周期
		if(this.opts.keepAliveInterval != null) {
			options.setKeepAliveInterval(this.opts.keepAliveInterval!.toInt());
		}
		// 账号
		if ((this.opts.username ?? '') != '') {
			options.setUserName(this.opts.username!)
		}
		// 密码
		if ((this.opts.password ?? '') != '') {
			options.setPassword(this.opts.password!.toCharArray())
		}

		try {
			this.client.setCallback(this)
			this.client.connect(options, null, new UxMqttListener(this))
		} catch (e) {
			console.error(e)
		}
	}

	/**
	 * 断开连接
	 */
	disconnect() {
		this.client.disconnect()
	}

	/**
	 * 订阅主题
	 */
	subscribe(opts: UxMqttSubscribeOptions) {
		try {
			const qos = (opts.qos ?? 2).toInt()
			this.client.subscribe(opts.topic, qos, UTSAndroid.getAppContext()!, new UxMqttSubscribeListener(opts));
		} catch (e) {
			console.log(e);
			opts.fail?.(e.toString())
		}
	}

	/**
	 * 取消订阅
	 */
	unsubscribe(topics: string[]) {
		this.client.unsubscribe(topics.toTypedArray())
	}

	/**
	 * 发布消息
	 */
	publish(opts: UxMqttPublishOptions) {
		try {
			let msg = new MqttMessage(opts.msg.toByteArray())
			// 消息id
			if(opts.id != null) {
				msg.setId(opts.id! as Int)
			}
			// 服务质量
			msg.setQos((opts.qos ?? 2) as Int)
			// 服务器持久化数据
			msg.setRetained(opts.retained ?? false)
			// 消息负载
			if(opts.payload != null) {
				msg.setPayload(opts.payload!.toByteArray())
			}
			
			this.client.publish(opts.topic, msg, null, new UxMqttPublishListener(opts));
		} catch (e) {
			console.log(e);
			opts.fail?.(e.toString())
		}
	}
}

class UxMqttListener extends IMqttActionListener {

	mqtt : UxMqtt

	constructor(mqtt : UxMqtt) {
		this.mqtt = mqtt
	}

	override onSuccess(asyncActionToken : IMqttToken) {
		this.mqtt.status = 'online'
		this.mqtt.statusListener?.(true, '')
	}

	override onFailure(asyncActionToken : IMqttToken, err : kotlin.Throwable) {
		this.mqtt.status = 'offline'
		this.mqtt.statusListener?.(false, err.toString())
		console.error(err)
	}
}

class UxMqttSubscribeListener implements IMqttActionListener {
	options : UxMqttSubscribeOptions
	
	constructor(options : UxMqttSubscribeOptions) {
		this.options = options
	}
	
	override onSuccess(asyncActionToken : IMqttToken) {
		this.options.success?.()
	}
	
	override onFailure(asyncActionToken : IMqttToken, err : kotlin.Throwable) {
		this.options.fail?.(err.toString())
		console.error(err)
	}
}

class UxMqttPublishListener implements IMqttActionListener {
	options : UxMqttPublishOptions

	constructor(options : UxMqttPublishOptions) {
		this.options = options
	}

	override onSuccess(asyncActionToken : IMqttToken) {
		this.options.success?.()
	}

	override onFailure(asyncActionToken : IMqttToken, err : kotlin.Throwable) {
		this.options.fail?.(err.toString())
		console.error(err)
	}
}