import { SVGKFastImageView, SVGKImage } from 'SVGKit'

export class UxSvg {

	$element : UniNativeViewElement; 
	imageView : SVGKFastImageView | null = null
	
	constructor(element : UniNativeViewElement) {
		this.$element = element
		
		let placeholderImage = SVGKImage(contentsOf = URL(fileURLWithPath = "")) 
		this.imageView = new SVGKFastImageView(svgkImage = placeholderImage)
		this.$element.bindIOSView(this.imageView!);
	}
	
	load(src: string, color: string) {
		if(src == "") {
			return
		}
		
		try {
			let svgImage = SVGKImage(contentsOf = URL(fileURLWithPath = UTSiOS.getResourcePath(src)))
			this.imageView!.image = svgImage

			if(color != '') {
				// this.modifySVGColor(this.imageView!.layer, color)
			}
		} catch (e) {}
	}
	
	modifySVGColor(layer ?: CALayer, color: string) {
		if(layer == null) {
			return
		}
		
		let _color = UTSiOS.colorWithString(color)
		let shapeLayer = layer! as CAShapeLayer
		shapeLayer.fillColor = _color.cgColor
		shapeLayer.strokeColor = _color.cgColor
		
		if(layer!.sublayers != null) {
			for (var i = 0; i < layer!.sublayers!.length; i++) {
				this.modifySVGColor(layer!.sublayers![i], color)
			}
		}
	}
}