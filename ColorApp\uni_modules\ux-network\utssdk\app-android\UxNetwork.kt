import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import android.net.Network
import android.net.NetworkRequest
import androidx.annotation.RequiresApi
import io.dcloud.uts.UTSAndroid

class UxNetworkMonitor(private val context: Context) {

    private val connectivityManager: ConnectivityManager by lazy {
        context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    }

    // 旧版广播监听
    private val networkReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            notifyNetworkChange(isNetworkAvailable())
        }
    }

    // 新版 NetworkCallback
    @RequiresApi(Build.VERSION_CODES.N)
    private val networkCallback = object : ConnectivityManager.NetworkCallback() {
        override fun onAvailable(network: Network) {
            notifyNetworkChange(true)
        }
        override fun onLost(network: Network) {
            notifyNetworkChange(false)
        }
    }

    // 网络状态回调
    var onNetworkChange: ((Boolean) -> Unit)? = null

    // 开始监听
    fun startMonitoring() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            // Android 7.0+ 使用 NetworkCallback
            val request = NetworkRequest.Builder()
                .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
                .build()
            connectivityManager.registerNetworkCallback(request, networkCallback)
        } else {
            // 旧版本使用广播
            val filter = IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION)
            context.registerReceiver(networkReceiver, filter)
        }
    }

    // 停止监听
    fun stopMonitoring() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            connectivityManager.unregisterNetworkCallback(networkCallback)
        } else {
            context.unregisterReceiver(networkReceiver)
        }
    }

    // 检查当前网络状态
    fun isNetworkAvailable(): Boolean {
        val network = connectivityManager.activeNetwork ?: return false
        val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
        return capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
    }

    private fun notifyNetworkChange(isConnected: Boolean) {
        onNetworkChange?.invoke(isConnected)
    }
}

var networkMonitor : UxNetworkMonitor? = null
object UxNetwork {
	
	fun on(callback: (isConnected: Boolean) -> Unit){
		networkMonitor?.stopMonitoring()
		networkMonitor = null
		
		networkMonitor = UxNetworkMonitor(UTSAndroid.getAppContext()!!)
		networkMonitor?.startMonitoring()
		networkMonitor?.onNetworkChange = { isConnected ->
		    callback(isConnected)
		}
	}
	
	fun off(){
		networkMonitor?.stopMonitoring()
	}
}