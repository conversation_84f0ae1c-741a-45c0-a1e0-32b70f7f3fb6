<template>
	<view class="ux-table">
		<scroll-view :id="myId" class="ux-table__row" :style="style" direction="horizontal" @scroll="scrollX">
			<list-view ref="scrollRef" 
				class="ux-table__vertical" 
				:style="vstyle" 
				direction="vertical" 
				@touchmove="onPress('1')"
				@touchend="onPress('')"
				@scroll="scrollY" 
				@scrolltolower="scrolltolower" >
				
				<ux-table-header
					:columns="columns"
					:totalWidth="totalWidth"
					:tableWidth="tableWidth"
					:cellHeight="cellHeight"
					:headerColor="headerColor"
					:headerSize="headerSize"
					:headerBold="headerBold"
					:headerBackground="headerBackground"
					:borderColor="borderColor"
					:border="border"
					:selection="selection"
					:isAll="isAll"
					@sort="onSort">
				</ux-table-header>
		
				<ux-table-item
					:index="index" 
					:cell="cell"
					:columns="columns"
					:totalWidth="totalWidth"
					:tableWidth="tableWidth"
					:cellHeight="cellHeight"
					:color="color"
					:fontSize="fontSize"
					:background="background"
					:highlight="highlight"
					:borderColor="borderColor"
					:border="border"
					:ripple="ripple"
					:rippleColor="rippleColor"
					:inputColor="inputColor"
					:nums="nums"
					:selection="selection"
					:edit="edit"
					:len="len"
					@input="onInput"
					@click="onClick"
					v-for="(cell,index) in tables" :key="index">
					<!-- #ifdef MP -->
					<slot :name="`default-${index}`" :cols="columns">
						<!-- <template v-for="(col, i) in columns" :key="i">
							<template v-slot:[`mp-edit-${index}-${i}`]>
								<slot :name="`edit-${index}-${i}`" :row="index"></slot>
							</template>
						</template>
						<template v-for="(col, i) in columns" :key="i">
							<slot :name="`${col.key}-${index}-${i}`" :row="index" :col="i - len"></slot>
						</template> -->
					</slot>
					<!-- #endif -->
					<!-- #ifndef MP -->
					<template #edit>
						<slot name="edit" :row="index"></slot>
					</template>
					<template #[col.key] v-for="(col, i) in columns" :key="i">
						<slot :name="col.key" :row="index" :col="i - len"></slot>
					</template>
					<!-- #endif -->
				</ux-table-item>
				
				<ux-table-total
					v-if="total"
					:tables="tables" 
					:columns="columns"
					:totalWidth="totalWidth"
					:tableWidth="tableWidth"
					:cellHeight="cellHeight"
					:color="color"
					:fontSize="fontSize"
					:background="background"
					:highlight="highlight"
					:borderColor="borderColor"
					:border="border"
					:ripple="ripple"
					:rippleColor="rippleColor">
				</ux-table-total>
			</list-view>
		</scroll-view>
		
		<view v-if="fixedColumns.length > 0" class="ux-table__fixed">
			<list-view ref="fixedRef" 
				class="ux-table__vertical" 
				:style="vFixedStyle" 
				direction="vertical" 
				:scroll-with-animation="false" 
				:show-scrollbar="false" 
				@touchmove="onPress('2')"
				@touchend="onPress('')"
				@scroll="fixedScroll">
				
				<ux-table-header
					:fixed="true"
					:columns="fixedColumns"
					:totalWidth="totalWidth"
					:tableWidth="tableWidth"
					:cellHeight="cellHeight"
					:headerColor="headerColor"
					:headerSize="headerSize"
					:headerBold="headerBold"
					:headerBackground="headerBackground"
					:borderColor="borderColor"
					:border="border"
					:selection="selection"
					:isAll="isAll"
					@sort="onSort">
				</ux-table-header>
				
				<ux-table-item
					:fixed="true"
					:index="index" 
					:cell="cell"
					:columns="fixedColumns"
					:totalWidth="totalWidth"
					:tableWidth="tableWidth"
					:cellHeight="cellHeight"
					:color="color"
					:fontSize="fontSize"
					:background="background"
					:highlight="highlight"
					:borderColor="borderColor"
					:border="border"
					:ripple="ripple"
					:rippleColor="rippleColor"
					:inputColor="inputColor"
					:nums="nums"
					:selection="selection"
					:edit="edit"
					:len="len"
					@input="onInput"
					@click="onClick"
					v-for="(cell,index) in tables" :key="index">
					<!-- #ifdef MP -->
					<template v-slot:[`mp-edit-${index}`]>
						<slot :name="`edit-${index}`" :row="index"></slot>
					</template>
					<template v-for="(col, i) in fixedColumns" :key="i">
						<slot :name="`${col.key}-${index}`" :row="index" :col="i - len"></slot>
					</template>
					<!-- #endif -->
					<!-- #ifndef MP -->
					<template #edit>
						<slot name="edit" :row="index"></slot>
					</template>
					<template #[col.key] v-for="(col, i) in fixedColumns" :key="i">
						<slot :name="col.key" :row="index" :col="i - len"></slot>
					</template>
					<!-- #endif -->
				</ux-table-item>
				
				<ux-table-total
					v-if="total"
					:fixed="true"
					:tables="tables" 
					:columns="fixedColumns"
					:totalWidth="totalWidth"
					:tableWidth="tableWidth"
					:cellHeight="cellHeight"
					:color="color"
					:fontSize="fontSize"
					:background="background"
					:highlight="highlight"
					:borderColor="borderColor"
					:border="border"
					:ripple="ripple"
					:rippleColor="rippleColor">
				</ux-table-total>
			</list-view>
		</view>
		
		<view v-if="loading" class="ux-table__loading">
			<ux-loading>加载中...</ux-loading>
		</view>
	</view>
	
</template>

<script setup>
	/**
	 * Table 表格
	 * @description 支持多种主题自定义、字段插槽、格式化、高亮、边框、斑马纹、序号、全选、单选、编辑、合计、加载状态等多种功能
	 * @demo pages/component/table.uvue
	 * @tutorial https://www.uxframe.cn/component/table.html
	 * @property {Slot}				columnkey							Slot | 列字段插槽
	 * @property {Slot}				edit								Slot | 编辑插槽
	 * @property {Array}			datas								UTSJSONObject[] | 数据源
	 * @property {Array}			columns								UxTableColumn[] | 列数据
	 * @property {Any}				cellHeight							Any | 单元格高度 (默认 40)
	 * @property {Any}				height								Any | 表格高度
	 * @property {Any}				maxHeight							Any | 表格最大高度
	 * @property {String}			headerColor							String | 头部内容颜色
	 * @property {Any}				headerSize							Any | 头部字体大小 (默认 $ux.Conf.fontSize.small)
	 * @property {Boolean}			headerBold = [true|false]			Boolean | 头部字体加粗 (默认 true)
	 * @property {String}			headerBackground					String | 头部背景色 (默认 #f5f5f5)
	 * @property {String}			color								String | 内容颜色 (默认 #333333)
	 * @property {Any}				fontSize							Any | 内容字体大小 (默认 $ux.Conf.fontSize.small)
	 * @property {String}			background							String | 背景色 (默认 #fafafa)
	 * @property {String}			highlight							String | 高亮色 (默认 #ecf5ff)
	 * @property {Boolean}			border = [true|false]				Boolean | 显示边框 (默认 true)
	 * @property {Boolean}			ripple = [true|false]				Boolean | 显示斑马纹 (默认 true)
	 * @property {String}			rippleColor							String | 斑马纹背景色 (默认 #f5f5f5)
	 * @property {Boolean}			nums = [true|false]					Boolean | 显示序号 (默认 false)
	 * @property {Boolean}			numSort = [true|false]				Boolean | 开启序号排序 (默认 false)
	 * @property {Boolean}			selection = [true|false]			Boolean | 开启选择 (默认 false)
	 * @property {Boolean}			edit = [true|false]					Boolean | 开启编辑 (默认 false)
	 * @property {Boolean}			total = [true|false]				Boolean | 合计 (默认 false)
	 * @property {Boolean}			loading = [true|false]				Boolean | 加载中... (默认 false)
	 * @event {Function}			click								Function | 点击时触发
	 * @event {Function}			input								Function | 输入时触发
	 * @event {Function}			scrolltolower						Function | 滚动触底时触发
	 * <AUTHOR>
	 * @date 2024-05-11 21:08:24
	 */
	
	import { $ux } from '../../index'
	import { useFontSize, useFontColor, useForegroundColor, useThemeColor } from '../../libs/use/style.uts'
	import { useResize } from '../../libs/use/resize'
	import { UxTableColumn, UxTableEvent } from '../../libs/types/types.uts'
	
	defineOptions({
		name: 'ux-table'
	})
	
	// defineSlots<{
	// 	edit(props : { row: number }) : any
	// }>()
	
	// defineSlots<{
	// 	name(props : { cols : UxTableColumn[] }) : any,
	// }>()

	const emit = defineEmits(['click', 'input', 'scrolltolower'])
	
	const props = defineProps({
		datas: {
			type: Array as PropType<UTSJSONObject[]>,
			default: () : UTSJSONObject[] => [] as UTSJSONObject[]
		},
		columns: {
			type: Array as PropType<UxTableColumn[]>,
			default: () : UxTableColumn[] => [] as UxTableColumn[]
		},
		cellHeight: {
			default: 40
		},
		height: {
			default: 0
		},
		maxHeight: {
			default: 0
		},
		headerColor: {
			type: String,
			default: "#333333"
		},
		headerSize: {
			default: 0
		},
		headerBold: {
			type: Boolean,
			default: true
		},
		headerBackground: {
			type: String,
			default: "#f5f5f5"
		},
		color: {
			type: String,
			default: "#333333"
		},
		fontSize: {
			default: 0
		},
		background: {
			type: String,
			default: "#fafafa"
		},
		highlight: {
			type: String,
			default: "#ecf5ff"
		},
		border: {
			type: Boolean,
			default: false
		},
		ripple: {
			type: Boolean,
			default: false
		},
		rippleColor: {
			type: String,
			default: "#f5f5f5"
		},
		inputColor: {
			type: String,
			default: "transparent"
		},
		nums: {
			type: Boolean,
			default: false
		},
		numSort: {
			type: Boolean,
			default: false
		},
		selection: {
			type: Boolean,
			default: false
		},
		edit: {
			type: Boolean,
			default: false
		},
		loading: {
			type: Boolean,
			default: false
		},
		total: {
			type: Boolean,
			default: false
		},
	})
	
	const myId = `ux-table-${$ux.Random.uuid()}`
	const tables = ref<UTSJSONObject[]>([] as UTSJSONObject[])
	const columns = ref<UxTableColumn[]>([] as UxTableColumn[])
	const fixedColumns = ref<UxTableColumn[]>([] as UxTableColumn[])
	const fixedRef = ref<UniScrollViewElement | null>(null)
	const scrollRef = ref<UniScrollViewElement | null>(null)
	const tableWidth = ref(0)
	const tableHeight = ref(0)
	const totalWidth = ref(0)
	const selectKey = '__SELECT__'
	const numKey = '__NUM__'
	const editKey = '__EDIT__'
	const isAll = ref(false)
	let pressed = ''
	let instance = getCurrentInstance()?.proxy
	
	const loading = computed((): boolean => {
		return props.loading
	})
	
	const borderColor = computed((): string => {
		return '#e8e8e8'
	})
	
	const datas = computed((): UTSJSONObject[] => {
		let _datas = props.datas.slice(0)
		
		for (let i = 0; i < _datas.length; i++) {
			_datas[i][selectKey] = ''
			_datas[i][numKey] = i + 1
		}
		
		return _datas
	})
	
	const len = computed((): number => {
		let l = 0
		
		if(props.nums) {
			l++
		}
		
		if(props.selection) {
			l++
		}
		
		return l
	})
	
	const style = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		if(props.border) {
			css.set('border-left', `1px solid ${borderColor.value}`)
			css.set('border-bottom', `1px solid ${borderColor.value}`)
		}
		
		return css
	})
	
	const vstyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		if($ux.Util.getPx(props.height) > 0) {
			css.set('height', $ux.Util.addUnit(props.height))
		}
		
		if($ux.Util.getPx(props.maxHeight) > 0) {
			css.set('max-height', $ux.Util.addUnit(props.maxHeight))
		}
		
		return css
	})
	
	const vFixedStyle = computed((): Map<string, any> => {
		let w = 0
		fixedColumns.value.forEach((e) => {
			w += (e._width ?? 0)
		})
		
		let css = new Map<string, any>()
		
		css.set('width', `${w}px`)
		
		if(tableHeight.value > 0) {
			css.set('height', `${tableHeight.value}px`)
		}
		
		return css
	})
	
	function selectAll() {
		isAll.value = !isAll.value
		
		for (let i = 0; i < tables.value.length; i++) {
			tables.value[i][selectKey] = isAll.value ? selectKey : ''
		}
	}
	
	function unselectAll() {
		isAll.value = false
		
		for (let i = 0; i < tables.value.length; i++) {
			tables.value[i][selectKey] = ''
		}
	}
	
	function selectCell(cell: UTSJSONObject) {
		let all = selectKey
		for (let i = 0; i < tables.value.length; i++) {
			if(tables.value[i][selectKey] == '') {
				all = ''
				break
			}
		}
		
		isAll.value = all == selectKey
	}
	
	function select(row: number) {
		let cell = tables.value[row]
		if(cell[selectKey] == selectKey) {
			cell[selectKey] = ''
		} else {
			cell[selectKey] = selectKey
		}
		
		selectCell(cell)
	}
	
	function onClick(cell : UTSJSONObject, key : string, row: number, col: number) {
		if(props.edit && key == editKey) {
			return
		}
		
		if(props.selection && key == selectKey) {
			if(cell[key] == key) {
				cell[key] = ''
			} else {
				cell[key] = key
			}
			selectCell(cell)
		} else {
			emit('click', {
				row: row,
				col: col - len.value,
				key: key,
				data: cell
			} as UxTableEvent)
		}
	}
	
	function getSortValue(col: UxTableColumn, data: UTSJSONObject): number {
		let value = 0
		
		let v = data[col.key]
		if(v == null) {
			return 0
		}
		
		try{
			value = parseInt(v.toString())
		}catch(e){
			try{
				value = parseFloat(v.toString())
			} catch(e){
				if(col.date == true) {
					value = $ux.Date.toDate(v.toString()).getTime()
				}
			}
		}
		
		return value
	}
	
	function onSort(col: UxTableColumn) {
		if(props.selection && col.key == selectKey) {
			selectAll()
			return
		}
		
		if(col.sorted != true) {
			return
		}
		
		let desc = col.desc ?? ''
		
		if (desc == '') {
			col.desc = 'desc'
			
			let _datas = props.datas.slice(0) as UTSJSONObject[]
			_datas.sort((a : UTSJSONObject, b : UTSJSONObject) : number => {
				return getSortValue(col, a) - getSortValue(col, b)
			})
			
			tables.value = _datas
		} else if (desc == 'desc') {
			col.desc = 'asc'
			
			let _datas = props.datas.slice(0) as UTSJSONObject[]
			_datas.sort((a : UTSJSONObject, b : UTSJSONObject) : number => {
				return getSortValue(col, b) - getSortValue(col, a)
			})
			
			tables.value = _datas
		} else if (desc == 'asc') {
			col.desc = ''
			
			tables.value = props.datas.slice(0)
		}
	}
	
	function onInput(e: InputEvent, row: number, key: string) {
		emit('input', e, row, key)
	}
	
	function scrolltolower(e: ScrollToLowerEvent) {
		emit('scrolltolower', e)
	}
	
	function scrollX(e: ScrollEvent) {
		let x = e.detail.scrollLeft
		
		for (let i = 0; i < columns.value.length; i++) {
			let el = columns.value[i]
			if((el.fixed ?? false) && el._fixedLeft != null) {
				el._fixed = x >= el._fixedLeft!
			}
		}
		
		fixedColumns.value = columns.value.filter((e): boolean => (e.fixed ?? false) && (e._fixed ?? false))
	}
	
	function scrollY(e: ScrollEvent) {
		// #ifdef WEB
		// web端只能在竖向scroll里拿到left，原因未知
		scrollX(e)
		// #endif
		
		if(pressed == '' || pressed == '1') {
			if(fixedRef.value != null) {
				// #ifdef APP
				fixedRef.value!.setAttribute('scroll-top', `${e.detail.scrollTop}px`)
				// #endif
				// #ifdef WEB
				fixedRef.value!.scrollTop = e.detail.scrollTop
				// #endif
			}
		}
	}
	
	function fixedScroll(e: ScrollEvent) {
		if(pressed == '' || pressed == '2') {
			if(scrollRef.value != null) {
				// #ifdef APP
				scrollRef.value!.setAttribute('scroll-top', `${e.detail.scrollTop}px`)
				// #endif
				// #ifdef WEB
				scrollRef.value!.scrollTop = e.detail.scrollTop
				// #endif
			}
		}
	}
	
	function onPress(type: string) {
		pressed = type
	}
	
	function calcWidth() {
		uni.createSelectorQuery()
			.in(instance)
			.select(`#${myId}`)
			.boundingClientRect()
			.exec((rect) => {
				if(rect.length == 0) {
					return
				}
				
				let node = rect[0] as NodeInfo
				tableWidth.value = node.width!
				tableHeight.value = node.height!
				
				setTimeout(() => {
					uni.createSelectorQuery()
						.in(instance)
						.selectAll('.ux-table__cell')
						.boundingClientRect()
						.exec((rect) => {
							let nodes = rect[0] as NodeInfo[]
							
							let totalW = 0
							nodes.forEach((el, i) => {
								if(columns.value.length == nodes.length) {
									if(columns.value[i].fixed ?? false) {
										columns.value[i]._fixedLeft = totalW
									}
								}
								
								columns.value[i]._width = el.width!
								totalW += el.width!
							})
	
							totalWidth.value = totalW
						})
				}, 100);
			});
	}
	
	function initColumns() {
		let _columns = props.columns
		
		if(props.nums) {
			_columns = [{key: numKey, title: '序号', width: props.numSort ? '75px' : '55px', sorted: props.numSort} as UxTableColumn, ..._columns] as UxTableColumn[]
		}
		
		if(props.selection) {
			_columns = [{key: selectKey, title: '', width: '55px'} as UxTableColumn, ..._columns] as UxTableColumn[]
		}
		
		if(props.edit) {
			_columns.push({key: editKey, title: ''} as UxTableColumn)
		}
		
		columns.value = _columns
	}
	
	const pcolumns = computed((): UxTableColumn[] => {
		return props.columns
	})
	
	watch(pcolumns, () => {
		initColumns()
		calcWidth();
	})
	
	watch(datas, () => {
		tables.value = datas.value
	}, {immediate: true})
	
	onMounted(() => {
		initColumns()
		setTimeout(function() {
			calcWidth();
		}, 50);
		
		useResize(uni.getElementById(myId), () => {
			calcWidth()
		})
	})
	
	defineExpose({
		select,
		selectAll,
		unselectAll
	})
</script>

<style lang="scss">
	.ux-table {
		width: 100%;
		position: relative;
		
		&__row {
			display: flex;
			flex-direction: row;
		}
		
		&__vertical {
			display: flex;
		}
		
		&__loading {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background-color: rgba(20, 20, 20, 0.4);
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			pointer-events: none;
			z-index: 10000;
		}
		
		&__fixed {
			position: absolute;
			top: 0px;
			left: 1px;
			bottom: 1px;
			z-index: 1000;
		}
	}
	
</style>