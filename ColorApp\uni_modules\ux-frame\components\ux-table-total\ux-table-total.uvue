<template>
	<!-- #ifdef APP -->
	<list-item type="2">
		<view class="ux-table__body" :style="bodyStyle(null, tables.length)">
			<view class="ux-table__wrap" :style="[cellStyle(col, i), col.style]" v-for="(col, i) in columns" :key="i">
				<text class="ux-table__text" :style="[cellTextStyle, col.textStyle]">{{ totals(col) }}</text>
			</view>
		</view>
	</list-item>
	<!-- #endif -->
	<!-- #ifndef APP -->
	<list-item type="2" class="ux-table__body" :style="bodyStyle(null, tables.length)">
		<view class="ux-table__wrap" :style="[cellStyle(col, i), col.style]" v-for="(col, i) in columns" :key="i">
			<text class="ux-table__text" :style="[cellTextStyle, col.textStyle]">{{ totals(col) }}</text>
		</view>
	</list-item>
	<!-- #endif -->
</template>

<script setup>
	/**
	 * Table Total
	 * @demo pages/component/table.uvue
	 * @tutorial https://www.uxframe.cn/component/table.html
	 * <AUTHOR>
	 * @date 2024-07-26 01:08:19
	 */
	
	import { $ux } from '../../index'
	import { useFontSize, useFontColor, useForegroundColor } from '../../libs/use/style.uts'
	import { UxTableColumn } from '../../libs/types/types.uts'
	
	defineOptions({
		name: 'ux-table-total'
	})
	
	const props = defineProps({
		fixed: {
			type: Boolean,
			default: false
		},
		tables: {
			type: Array as PropType<UTSJSONObject[]>,
			default: () : UxTableColumn[] => [] as UxTableColumn[]
		},
		columns: {
			type: Array as PropType<UxTableColumn[]>,
			default: () : UxTableColumn[] => [] as UxTableColumn[]
		},
		tableWidth: {
			type: Number,
			default: 0
		},
		totalWidth: {
			type: Number,
			default: 0
		},
		cellHeight: {
			default: 40
		},
		color: {
			type: String,
			default: "#333333"
		},
		fontSize: {
			default: 0
		},
		background: {
			type: String,
			default: "#fafafa"
		},
		highlight: {
			type: String,
			default: "#ecf5ff"
		},
		borderColor: {
			type: String,
			default: "#e8e8e8"
		},
		border: {
			type: Boolean,
			default: false
		},
		ripple: {
			type: Boolean,
			default: false
		},
		rippleColor: {
			type: String,
			default: "#f5f5f5"
		},
	})
	
	const selectKey = '__SELECT__'
	
	const fontSize = computed((): number => {
		return useFontSize($ux.Util.getPx(props.fontSize), 1)
	})
	
	const color = computed((): string => {
		return useFontColor(props.color, 'auto')
	})
	
	const background = computed((): string => {
		return useForegroundColor(props.background, 'auto')
	})
	
	const rippleColor = computed((): string => {
		return useForegroundColor(props.rippleColor, 'auto')
	})
	
	const highlight = computed((): string => {
		return useForegroundColor(props.highlight, 'auto')
	})
	
	const cellTextStyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('fontSize', $ux.Util.addUnit(fontSize.value))
		css.set('color', color.value)
		
		return css
	})
	
	function getCellWidth(col: UxTableColumn) : string {
		let w = col.width ?? '20%'
		if (w == '') {
			return 'auto'
		}
		
		if (w.indexOf('%') != -1) {
			w = `${parseFloat(w.replace('%', '')) * props.tableWidth / 100}px`
		}
		
		return w
	}
	
	function bodyStyle(cell: UTSJSONObject | null, i: number): Map<string, any> {
		let css = new Map<string, any>()
		
		if(!props.fixed && props.totalWidth > 0) {
			css.set('width', $ux.Util.addUnit(props.totalWidth))
		}
		
		css.set('height', $ux.Util.addUnit(props.cellHeight))
		
		if(cell != null && cell[selectKey] == selectKey) {
			css.set('background-color', highlight.value)
		} else {
			if((i+1)%2 == 0 && props.ripple){
				css.set('background-color', rippleColor.value)
			} else {
				css.set('background-color', background.value)
			}
		}
		
		return css
	}
	
	function cellStyle(col: UxTableColumn, i: number): Map<string, any> {
		let cellWidth = getCellWidth(col)
		
		let css = new Map<string, any>()
		
		if(cellWidth == 'auto') {
			css.set('flex', '1')
		} else {
			css.set('width', cellWidth)
		}
		
		css.set('height', $ux.Util.addUnit(props.cellHeight))
		
		if(props.border) {
			css.set('border-top', `1px solid ${props.borderColor}`)
			css.set('border-right', `1px solid ${props.borderColor}`)
		}
		
		if(col.align == 'left') {
			css.set('align-items', 'flex-start')
		} else if(col.align == 'right') {
			css.set('align-items', 'flex-end')
		} else {
			css.set('align-items', 'center')
		}
		
		return css
	}
	
	function totals(col: UxTableColumn): string {
		if(col.total ?? false) {
			let all = 0
			props.tables.forEach((e: UTSJSONObject) => {
				try{
					all += parseInt(e[col.key]!.toString())
				}catch(err){
					try{
						all += parseFloat(e[col.key]!.toString())
					}catch(err){}
				}
			})
			
			return `合计：${all}`
		}
		
		return ''
	}
	
	
</script>

<style lang="scss">
	.ux-table {
		
		&__body {
			display: flex;
			flex-direction: row;
			flex-wrap: nowrap;
			/* #ifdef WEB */
			cursor: pointer;
			/* #endif */
		}
		
		&__body--dark {
			display: flex;
			flex-direction: row;
			flex-wrap: nowrap;
			/* #ifdef WEB */
			cursor: pointer;
			/* #endif */
		}
		
		&__hover {
			background-color: #f0f0f0 !important;
		}
		
		&__hover--dark {
			background-color: #666666 !important;
		}
		
		/* #ifdef WEB */
		&__body:hover {
			background-color: #f0f0f0 !important;
		}
		
		&__body--dark:hover {
			background-color: #666666 !important;
		}
		/* #endif */
		
		&__wrap {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
		}
		
		&__text {
			text-align: center;
			padding: 4px 8px;
			/* #ifdef WEB */
			word-break: break-all;
			/* #endif */
		}
	}
</style>