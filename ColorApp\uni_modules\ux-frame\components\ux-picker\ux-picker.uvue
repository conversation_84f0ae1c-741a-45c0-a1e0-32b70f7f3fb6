<template>
	<ux-drawer ref="uxDrawerRef" direction="top" :height="`${pickerHeight}px`" :touchable="touchable" :opacity="opacity" :maskClose="maskClose" :border="false" @close="onMaskClose">
		<view class="ux-picker__slot" @click="click()">
			<slot></slot>
		</view>

		<template v-slot:drawer>
			<view v-if="isShow" class="ux-picker" :style="[style]">
				<view class="ux-picker__title" :style="titleBorder">
					<text v-if="title != ''" class="ux-picker__title--text" :style="titleStyle">{{ title }}</text>
					<view v-if="btnPositon == 'top'" :class="btnType == 'normal' || title != '' ? 'ux-picker__btn--cancel' : 'ux-picker__btn--cancel--bigger'"
						hover-class="ux-picker__hover" :hover-start-time="20" :hover-stay-time="200" @click="close()">
						<text class="ux-picker__btn--text" :style="[cancelStyle]">取消</text>
					</view>
					<view v-if="btnPositon == 'top'" :class="btnType == 'normal' || title != '' ? 'ux-picker__btn--confirm' : 'ux-picker__btn--confirm--bigger'"
						:style="confirmBorder" hover-class="ux-picker__hover" :hover-start-time="20" :hover-stay-time="200" @click="onConfirm()">
						<text class="ux-picker__btn--text" :style="[confirmStyle]">{{ confirm }}</text>
					</view>
					<view v-if="btnPositon == 'bottom'" class="ux-picker__close" @click="close()">
						<ux-icon type="close"></ux-icon>
					</view>
				</view>
				<!-- #ifdef APP -->
				<picker-view style="flex: 1;" 
					:indicator-style="indicatorStyle" 
					:mask-top-style="maskTopStyle" 
					:mask-bottom-style="maskBottomStyle"
					:value="values"
					@change="change">
				<!-- #endif -->
				<!-- #ifndef APP -->
				<picker-view style="flex: 1;" 
					indicator-class="ux-picker__indicator"
					:indicator-style="indicatorStyle" 
					:mask-style="maskStyle"
					:value="values"
					@change="change">
				<!-- #endif -->
					<picker-view-column v-for="(items, i) in datas" :key="i">
						<!-- #ifdef APP-ANDROID -->
						<list-view :show-scrollbar="false" :bounces="false">
							<list-item v-for="(item, index) in items" :key="index">
								<ux-picker-item :text="item.name" :size="size" :color="color" :selectColor="selectColor" :selectStyle="selectStyle" :index="index" :selectedIndex="values[i]"></ux-picker-item>
							</list-item>
						</list-view>
						<!-- #endif -->
						<!-- #ifdef APP-HARMONY -->
						<text class="ux-picker__text" :style="[textStyle, {'font-weight': index == values[i] ? 'bold' : 'normal', 'color': index == values[i] ? selectColor : color}]" v-for="(item, index) in items" :key="index">{{ item.name }}</text>
						<!-- #endif -->
						<!-- #ifdef APP-IOS || WEB || MP -->
						<view v-for="(item, index) in items" :key="index">
							<ux-picker-item :text="item.name" :size="size" :color="color" :selectColor="selectColor" :selectStyle="selectStyle" :index="index" :selectedIndex="values[i]"></ux-picker-item>
						</view>
						<!-- #endif -->
					</picker-view-column>
				</picker-view>
				<view v-if="btnPositon == 'bottom'" class="ux-picker__btns">
					<view class="ux-picker__btns--cancel" :style="cancelBtnStyle" hover-class="ux-picker__hover" :hover-start-time="20" :hover-stay-time="200" @click="close()">
						<text class="ux-picker__btns--text" :style="cancelTextStyle">取消</text>
					</view>
					<view class="ux-picker__btns--confirm" :style="confirmBtnStyle" hover-class="ux-picker__hover" :hover-start-time="20" :hover-stay-time="200" @click="onConfirm()">
						<text class="ux-picker__btns--text" :style="confirmTextStyle">{{ confirm }}</text>
					</view>
				</view>
			</view>
		</template>
	</ux-drawer>
</template>

<script setup>
	
	/**
	 * Picker 选择器
	 * @description 支持单列、多列、联动选择，主题样式可配置
	 * @demo pages/component/picker.uvue
	 * @tutorial https://www.uxframe.cn/component/picker.html
	 * @property {String} 			name												String | 标识符，在回调事件中返回
	 * @property {String} 			theme=[primary|warning|success|error|info]			String | 主题颜色
	 * @value primary 	主色
	 * @value warning 	警告
	 * @value success 	成功
	 * @value error 	错误
	 * @value info 		文本
	 * @property {Array}		data								UxPickerData[][] | 二维数组
	 * @property {Array}		value								any[] | 值
	 * @property {Boolean}		link=[true|false]					Boolean | 是否联动 (默认 false)
	 * @value true
	 * @value false
	 * @property {String}		title								String | 标题
	 * @property {String}		titleColor							String | 标题颜色 (默认 $ux.Conf.titleColor)
	 * @property {Any}			size								Any | 内容大小 (默认 $ux.Conf.fontSize)
	 * @property {String}		color								String | 内容颜色 (默认 $ux.Conf.fontColor)
	 * @property {String} 		darkColor=[none|auto|color]			String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {String}		selectColor							String | 内容选中颜色 (默认 $ux.Conf.fontColor)
	 * @property {String} 		selectDarkColor=[none|auto|color]	String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Boolean}		selectStyle=[border|box]			Boolean | 选择区域遮罩样式 (默认 border)
	 * @value border 边框 
	 * @value box 盒子
	 * @property {String}		confirm								String | 确定文字 (默认 确定)
	 * @property {String}		confirmColor						Strng | 确定文字颜色 (默认 $ux.Conf.primaryColor)
	 * @property {Any}			tnSize								Any | 按钮大小 (默认 $ux.Conf.fontSize)
	 * @property {String}		btnType=[normal|bigger]				String | 按钮类型 (默认 normal)
	 * @value normal 正常
	 * @value bigger 大按钮
	 * @property {String}		btnPositon=[top|bottom]				String | 按钮位置 (默认 bottom)
	 * @value top 顶部按钮
	 * @value bottom 底部按钮
	 * @property {Any}			radius								Any | 圆角 (默认 $ux.Conf.radius)
	 * @property {Number}		opacity								Number | 遮罩透明度 0-1 (默认 $ux.Conf.maskAlpha)
	 * @property {Boolean}		touchable=[true|false]				Boolean | 允许滑动关闭 (默认 false)
	 * @value true
	 * @value false
	 * @property {Boolean}		maskClose=[true|false]				Boolean | 遮罩层关闭 (默认 true)
	 * @value true
	 * @value false
	 * @property {Boolean}		disabled=[true|false]				Boolean | 是否禁用 (默认 false)
	 * @value true
	 * @value false
	 * @event {Function}		change								Function(UxPickerEvent) | 确定选择时触发
	 * @event {Function}		close								Function | 关闭时触发
	 * <AUTHOR>
	 * @date 2023-11-08 18:00:46
	 */
	
	import { $ux } from '../../index'
	import { UxPickerData, UxPickerEvent } from '../../libs/types/types.uts'
	import { useThemeColor, useForegroundColor, useBorderColor, useFontColor, useFontSize, useSubTitleColor, useCancelColor, useRadius } from '../../libs/use/style.uts'
	
	defineOptions({
		name: 'ux-picker'
	})
	
	const emit = defineEmits(['change', 'update:modelValue', 'update:value', 'close'])
	
	const props = defineProps({
		modelValue: {
			type: Array as PropType<any[]>,
			default: () : any[] => {
				return [] as any[]
			}
		},
		name: {
			type: String,
			default: ''
		},
		theme: {
			type: String,
			default: 'primary',
		},
		data: {
			type: Array as PropType<UxPickerData[][]>,
			default: () : UxPickerData[][] => {
				return [] as UxPickerData[][]
			}
		},
		value: {
			type: Array as PropType<number[]>,
			default: () : number[] => {
				return [] as number[]
			}
		},
		link: {
			type: Boolean,
			default: false
		},
		title: {
			type: String,
			default: ''
		},
		titleColor: {
			type: String,
			default: ''
		},
		size: {
			default: 0
		},
		color: {
			type: String,
			default: '#999999'
		},
		darkColor: {
			type: String,
			default: '#c5c5c5'
		},
		selectColor: {
			type: String,
			default: ''
		},
		selectDarkColor: {
			type: String,
			default: ''
		},
		selectStyle: {
			type: String,
			default: 'border'
		},
		btnSize: {
			default: 0
		},
		btnType: {
			type: String,
			default: 'normal'
		},
		btnPositon: {
			type: String,
			default: 'bottom'
		},
		confirm: {
			type: String,
			default: '确定'
		},
		confirmColor: {
			type: String,
			default: ''
		},
		radius: {
			default: -1
		},
		opacity: {
			type: Number,
			default: -1
		},
		touchable: {
			type: Boolean,
			default: false
		},
		maskClose: {
			type: Boolean,
			default: true
		},
		disabled: {
			type: Boolean,
			default: false
		}
	})
	
	const uxDrawerRef = ref<UxDrawerComponentPublicInstance | null>(null)
	const values = ref<number[]>([] as number[])
	const datas = ref<UxPickerData[][]>([] as UxPickerData[][])
	const isShow = ref(false)
	
	const pickerHeight = computed(():number => {
		const itemH = 50
		const itemLen = 5
		const topH = 50
		const bottomH = 70
		
		if(props.btnPositon == 'bottom') {
			return itemH * itemLen + topH + bottomH
		} else {
			return itemH * itemLen + topH + 20
		}
	})
	
	const fontSize = computed(():number => {
		return useFontSize($ux.Util.getPx(props.size), 0)
	})
	
	const btnSize = computed(():number => {
		return useFontSize($ux.Util.getPx(props.btnSize), 0)
	})
	
	const titleColor = computed(():string => {
		return useSubTitleColor(props.titleColor, 'auto')
	})
	
	const color = computed(():string => {
		return useFontColor(props.color, props.darkColor)
	})
	
	const selectColor = computed(():string => {
		return useFontColor(props.selectColor, props.selectDarkColor)
	})
	
	const borderColor = computed(():string => {
		return useBorderColor('', '')
	})
	
	const radius = computed(():number => {
		return useRadius($ux.Util.getPx(props.radius), 2)
	})
	
	const confirmColor = computed(():string => {
		if(props.confirmColor == '') {
			return useThemeColor('primary')
		} else {
			return props.confirmColor
		}
	})
	
	const cancelColor = computed(():string => {
		return useCancelColor('', 'auto')
	})
	
	const backgroundColor = computed(() : string => {
		return useForegroundColor('#ffffff', 'auto')
	})
	
	const maskTopStyle = computed(() : string => {
		// #ifdef APP-HARMONY
		return 'background-image: linear-gradient(to bottom, transparent, transparent);'
		// #endif
		
		// #ifndef APP-HARMONY
		return `
			background-color: rgba(0,0,0,0);
			background-image: none;
			`
		// #endif
	})
	
	const maskBottomStyle = computed(() : string => {
		// #ifdef APP-HARMONY
		return 'background-image: linear-gradient(to bottom, transparent, transparent);'
		// #endif
		
		// #ifndef APP-HARMONY
		return `
			background-color: rgba(0,0,0,0);
			background-image: none;
			`
		// #endif
	})
	
	const maskStyle = computed(() : string => {
		return `
			background-image: linear-gradient(
				to top,
				rgba(0, 0, 0, 0),
				rgba(0, 0, 0, 0)
			);
			`
	})
	
	const indicatorStyle = computed(() : string => {
		if(props.selectStyle == 'box') {
			// #ifdef WEB || MP
			return `height: 50px;background-color: rgba(190,190,190,0.2);margin:0 5px;border-radius: 10px;width: ${uni.getWindowInfo().windowWidth/datas.value.length-10}px;`
			// #endif
			// #ifdef APP-HARMONY
			return `height: 50px;background:rgba(182, 179, 255, 0.4);margin:0 5px;border-radius: 10px;`
			// #endif
			// #ifdef APP-ANDROID || APP-IOS
			return `height: 50px;background-color: rgba(190,190,190,0.2);margin:0 5px;border-radius: 10px;`
			// #endif
		} else {
			return `height: 50px;border-top: 1rpx solid ${borderColor.value};border-bottom: 1rpx solid ${borderColor.value};`
		}
	})
	
	const style = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('border-top-left-radius', $ux.Util.addUnit(radius.value))
		css.set('border-top-right-radius', $ux.Util.addUnit(radius.value))
		css.set('height', `${pickerHeight.value}px`)
		css.set('background-color', backgroundColor.value)
		
		// #ifdef WEB
		css.set('cursor', props.disabled? 'not-allowed' : 'pointer')
		// #endif
		
		return css
	})
	
	const textStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('font-size', $ux.Util.addUnit(fontSize.value))
		
		return css
	})
	
	const titleStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('color', titleColor.value)
		
		return css
	})
	
	const titleBorder = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		if(props.btnPositon == 'bottom' || props.btnType == 'normal' || props.title != '') {
			
		} else {
			css.set('border-bottom', `1rpx solid ${borderColor.value}`)
		}
		
		return css
	})
	
	const cancelStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('font-size', $ux.Util.addUnit(btnSize.value))
		css.set('color', cancelColor.value)
		
		return css
	})
	
	const confirmStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('font-size', $ux.Util.addUnit(btnSize.value))
		css.set('color', confirmColor.value)
		
		return css
	})
	
	const confirmBorder = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		if(props.btnPositon == 'bottom' || props.btnType == 'normal' || props.title != '') {
			
		} else {
			css.set('border-left', `1rpx solid ${borderColor.value}`)
		}
		
		return css
	})
	
	const confirmBtnStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('background-color', confirmColor.value)
		
		return css
	})
	
	const confirmTextStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('color', '#ffffff')
		
		return css
	})
	
	const cancelBtnStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('background-color', $ux.Color.getRgba(confirmColor.value, 0.05))
		css.set('border', `1px solid ${$ux.Color.getRgba(confirmColor.value, 0.8)}`)
		
		return css
	})
	
	const cancelTextStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('color', confirmColor.value)
		
		return css
	})
	
	function click() {
		
		if (props.disabled) {
			return
		}
	
		if (props.data.length == 0) {
			return
		}
		
		isShow.value = true
	
		// 初始数据
		if (datas.value.length == 0) {
			setTimeout(() => {
				// 联动结构
				if (props.link) {
					datas.value = [props.data[0]]
					
					// 递归出联动链表
					let f = (_ : UxPickerData[] | null) : void => { }
					f = (items : UxPickerData[] | null) : void => {
						if (items == null) {
							return
						}
					
						let index = items.findIndex(function (e : UxPickerData) : boolean {
							return e.children != null
						})
					
						if (index != -1) {
							datas.value.push(items[0].children == null ? [] as UxPickerData[] : items[0].children!)
							f(items[0].children)
						}
					}
					
					f(props.data[0])
				} else {
					datas.value = props.data
				}
					
				values.value = []
				for (let i = 0; i < datas.value.length; i++) {
					values.value.push(0)
				}
				
				if(props.value.length == values.value.length && props.value.length == datas.value.length) {
					for (let i = 0; i < values.value.length; i++) {
						values.value[i] = props.value[i] < datas.value[i].length ? props.value[i] : 0
					}
				}
				
				setTimeout(() => {
					uxDrawerRef.value!.open()
				}, 50);
			}, 100);
		} else {
			if(props.value.length == values.value.length && props.value.length == datas.value.length) {
				for (let i = 0; i < values.value.length; i++) {
					values.value[i] = props.value[i] < datas.value[i].length ? props.value[i] : 0
				}
			}
			
			let _datas = datas.value
			let _values = values.value
			
			datas.value = []
			values.value = []
			
			setTimeout(() => {
				datas.value = _datas
				values.value = _values
				
				uxDrawerRef.value!.open()
			}, 100);
		}
	}
	
	function change(ev : PickerViewChangeEvent) {
		values.value = ev.detail.value
	
		// 更新联动链表
		if (props.link) {
			values.value.forEach((_ : number, i : number) => {
				if (i > 0) {
					datas.value[i] = datas.value[i - 1][values.value[i - 1]].children ?? [] as UxPickerData[]
				}
			})
		}
	}
	
	function open() {
		click()
	}
	
	function close() {
		uxDrawerRef.value!.close()
		emit('close')
	}
	
	function onMaskClose() {
		emit('close')
	}
	
	function onConfirm() {
		let _indexs = values.value.map((i : number, _ : number) : number => {
			return i
		})
	
		let _names = values.value.map((i : number, index : number) : string => {
			return datas.value[index].length == 0 ? '' : datas.value[index][i].name
		}).filter((val : string) : boolean => val != '')
	
		let _values = values.value.map((i : number, index : number) : any => {
			return datas.value[index].length == 0 ? '' : datas.value[index][i].value
		}).filter((val : any) : boolean => val.toString() != '')
		
		emit('update:modelValue', _values)
		emit('update:value', _indexs)
		
		emit('change', {
			name: props.name,
			index: _indexs,
			label: _names,
			value: _values,
		} as UxPickerEvent)
	
		close()
	}
	
	watch((): UxPickerData[][] => props.data, () => {
		datas.value = []
	}, {deep: true})
	
	defineExpose({
		open,
		close
	})
</script>

<style lang="scss">
	.ux-picker__slot {
		/* #ifdef MP */
		width: 100%;
		/* #endif */
	}
	
	.ux-picker {
		width: 100%;
		position: relative;

		&__title {
			width: 100%;
			height: 50px;
			padding: 0 15px;
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;

			&--text {
				font-size: 13px;
				color: #333;
			}
		}
		
		&__close {
			position: absolute;
			width: 50px;
			height: 40px;
			right: 0;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		&__btn--cancel {
			position: absolute;
			left: 8px;
			padding: 8px 13px;
		}

		&__btn--cancel--bigger {
			position: absolute;
			left: 0;
			width: 50%;
			height: 100%;
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
		}

		&__btn--confirm {
			position: absolute;
			right: 8px;
			padding: 8px 13px;
		}

		&__btn--confirm--bigger {
			position: absolute;
			right: 0;
			width: 50%;
			height: 100%;
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
		}

		&__btn--text {
			font-size: 14px;
		}
		
		&__btns {
			width: 100%;
			height: 70px;
			padding: 0 15px;
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
			
			&--cancel {
				margin: 0 8px;
				flex: 1;
				height: 42px;
				display: flex;
				flex-direction: row;
				justify-content: center;
				align-items: center;
				border-radius: 42px;
			}
			
			&--confirm {
				margin: 0 8px;
				flex: 1;
				height: 42px;
				display: flex;
				flex-direction: row;
				justify-content: center;
				align-items: center;
				border-radius: 42px;
			}
			
			&--text {
				font-size: 14px;
			}
		}

		&__hover {
			opacity: 0.6;
		}
	}
	
	.ux-picker__item {
		height: 50px;
	}
	
	.ux-picker__text {
		line-height: 50px;
		text-align: center;
		color: black;
	}
	
	/* #ifndef APP */
	.ux-picker__indicator::before,
	.ux-picker__indicator::after {
		border-top: 0px solid #fff;
		border-bottom: 0px solid #fff;
	}
	
	.uni-picker-view-indicator::before,
	.uni-picker-view-indicator::after, {
		visibility: hidden;
	}
	/* #endif */
</style>