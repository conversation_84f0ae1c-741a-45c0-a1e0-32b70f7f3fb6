<template>
	<view class="ux-virtual-item">
		<slot></slot>
	</view>
</template>

<script>
	
	/**
	 * 虚拟列表子项
	 * @demo pages/component/virtual-list.uvue
	 * @tutorial https://www.uxframe.cn/component/virtual-list.html
	 * @property {Any}				item								Any | 当前组件渲染的列表项
	 * @property {Number}			height								Number | 子项高度，如果指定，内部不再计算高度，提升性能
	 * <AUTHOR>
	 * @date 2025-04-03 20:31:11
	 */

	export default {
		name: "ux-virtual-item",
		props: {
			item: {
				type: Object as PropType<any | null>,
			},
			height: {
				type: Number,
				default: 0
			}
		},
		inject: {
			setCachedSize: {
				type: Function as PropType<(item : any | null, size : number) => void>
			},
		},
		mounted() {
			if(this.height > 0) {
				this.setCachedSize(this.item, this.height)
			} else {
				uni.createSelectorQuery().in(this).select('.ux-virtual-item').boundingClientRect().exec((ret) => {
					this.setCachedSize(this.item, (ret[0] as NodeInfo).height!)
				})
			}
		}
	}
</script>

<style lang="scss">

</style>