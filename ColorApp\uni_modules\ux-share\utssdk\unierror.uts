
import { UxShareCallback } from "./interface.uts"

// #ifndef UNI-APP-X
class UniError {
	
}
// #endif

export class UxShareSuccessCallbackImpl extends UniError implements UxShareCallback {

	constructor(errMsg : string) {
		super();

		this.errSubject = 'ux-share'
		this.errCode = 0
		this.errMsg = errMsg
	}
}

export class UxShareErrorCallbackImpl extends UniError implements UxShareCallback {

	constructor(errMsg : string) {
		super();

		this.errSubject = 'ux-share'
		this.errCode = 1
		this.errMsg = errMsg
	}
}