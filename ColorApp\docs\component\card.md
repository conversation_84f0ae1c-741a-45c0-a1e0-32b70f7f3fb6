<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/card?title=Card"></Mobile>

# Card
> 组件类型：UxCardComponentPublicInstance

支持图标、标题、副标题及样式自定义，支持内边距和外边距设置，可显示阴影和边框

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| [direction](#direction) | String |  | 布局方向 |
| title | String |  | 标题 |
| size | Any | `$ux.Conf.fontSize` | 标题大小 |
| color | String | `$ux.Conf.fontColor` | 标题颜色 |
| [darkColor](#darkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| bold | Boolean | false | 标题加粗 |
| titleStyle | String |  | 标题样式 |
| subtitle | String |  | 副标题 |
| subtitleColor | String | `$ux.Conf.secondaryColor` | 副标题颜色 |
| subtitleSize | Any | `$ux.Conf.fontColor.small` | 副标题大小 |
| subtitleStyle | String |  | 副标题样式 |
| icon | String |  | 标题图标 |
| iconColor | String | `$ux.Conf.fontColor` | 标题图标颜色 |
| iconSize | Any | `$ux.Conf.fontSize` | 标题图标大小 |
| iconStyle | String |  | 标题图标样式 |
| customFamily | String |  | 自定义字体family |
| background | String | `$ux.Conf.backgroundColor` | 背景色 |
| [backgroundDark](#backgroundDark) | String | auto | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| showBlur | Boolean | false | 开启模糊背景 |
| blurRadius | Number | 10 | 模糊半径 |
| blurColor | String | rgba(255,255,255,0.3) | 模糊颜色 |
| shadow | Boolean | false | 显示阴影 |
| border | Boolean | false | 显示边框 |
| borderColor | String | `$ux.Conf.borderColor` | 边框颜色 |
| [borderDarkColor](#borderDarkColor) | String | auto | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| radius | Any | `$ux.Conf.radius` | 圆角 |
| padding | Any | `$ux.Conf.padding` | 内部水平边距 |
| margin | Any | `$ux.Conf.margin` | 边距 |
| xstyle | Array |  | 自定义样式 |

### [direction](#direction)

| 值   | 说明 |
|:------:|:----:|
| row水平
 |  |
| column垂直
 |  |

### [darkColor](#darkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [backgroundDark](#backgroundDark)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [borderDarkColor](#borderDarkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

