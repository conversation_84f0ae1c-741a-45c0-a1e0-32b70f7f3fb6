<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FormBuilder 组件预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f8f8;
            min-height: 100vh;
        }
        
        .main {
            width: 100%;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .title {
            color: white;
            font-size: 20px;
            font-weight: bold;
        }
        
        .content {
            flex: 1;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
        }
        
        .preview-note {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            border-left: 4px solid #667eea;
        }
        
        .preview-note h3 {
            margin-bottom: 10px;
            color: #333;
            font-size: 18px;
        }
        
        .preview-note p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 8px;
        }
        
        .feature-list {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }
        
        .feature-list h4 {
            margin-bottom: 15px;
            color: #333;
            font-size: 16px;
        }
        
        .feature-list ul {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            color: #666;
            position: relative;
            padding-left: 20px;
        }
        
        .feature-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #4CAF50;
            font-weight: bold;
        }
        
        .demo-section {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }
        
        .demo-section h4 {
            margin-bottom: 15px;
            color: #333;
            font-size: 16px;
        }
        
        .form-preview {
            border: 2px dashed #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            background: #fafafa;
            text-align: center;
            color: #666;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
        
        .form-preview .icon {
            font-size: 48px;
            margin-bottom: 10px;
            opacity: 0.5;
        }
        
        .code-block {
            background: #f5f5f5;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            overflow-x: auto;
            color: #333;
        }
        
        .usage-example {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }
        
        .usage-example h4 {
            margin-bottom: 15px;
            color: #333;
            font-size: 16px;
        }
        
        .back-link {
            display: inline-block;
            padding: 10px 20px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin-top: 20px;
            transition: background 0.3s ease;
        }
        
        .back-link:hover {
            background: #5a6fd8;
        }
        
        @media (max-width: 768px) {
            .content {
                padding: 15px;
            }
            
            .preview-note,
            .feature-list,
            .demo-section,
            .usage-example {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="main">
        <div class="header">
            <div class="title">📝 FormBuilder 动态表单构建器</div>
        </div>
        
        <div class="content">
            <div class="preview-note">
                <h3>🚀 组件介绍</h3>
                <p>FormBuilder 是一个基于 FieldConfig-README.md 规范的动态表单构建器组件。</p>
                <p>它能够根据 JSON 配置自动生成表单，支持多种字段类型、验证规则和数据源配置。</p>
                <p>完全兼容项目中的 ux-frame 组件库，提供一致的用户体验。</p>
            </div>
            
            <div class="feature-list">
                <h4>✨ 主要特性</h4>
                <ul>
                    <li>支持多种字段类型：文本、数字、选择器、开关、日期等</li>
                    <li>灵活的验证规则：必填、长度、数值范围、正则表达式</li>
                    <li>动态数据源：支持静态数据和 JSON 格式数据源</li>
                    <li>字段分组：支持将字段按组织结构分组显示</li>
                    <li>响应式设计：自适应不同屏幕尺寸</li>
                    <li>事件支持：字段变化、表单提交等事件回调</li>
                    <li>双向数据绑定：支持 v-model 数据绑定</li>
                    <li>样式定制：支持自定义样式和主题</li>
                </ul>
            </div>
            
            <div class="demo-section">
                <h4>📱 表单预览</h4>
                <div class="form-preview">
                    <div class="icon">📝</div>
                    <p><strong>动态表单渲染区域</strong></p>
                    <p>在实际应用中，这里会根据 FieldConfig 配置</p>
                    <p>动态生成相应的表单字段和布局</p>
                </div>
            </div>
            
            <div class="usage-example">
                <h4>💻 使用示例</h4>
                <p>在页面中引入并使用 FormBuilder 组件：</p>
                <div class="code-block">&lt;template&gt;
  &lt;FormBuilder 
    :fields="formFields" 
    v-model="formData"
    @submit="handleSubmit"
    @change="handleFieldChange" /&gt;
&lt;/template&gt;

&lt;script setup&gt;
import FormBuilder from '@/components/FormBuilder.uvue'

const formFields = [
  {
    "Id": "username",
    "Name": "username", 
    "Title": "用户名",
    "Type": "Text",
    "Required": true,
    "Placeholder": "请输入用户名"
  },
  {
    "Id": "email",
    "Name": "email",
    "Title": "邮箱", 
    "Type": "InputEditor",
    "InputType": "Email",
    "Required": true,
    "Validation": "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$"
  }
]

const formData = ref({})

const handleSubmit = (result) => {
  console.log('表单提交:', result)
}
&lt;/script&gt;</div>
            </div>
            
            <div class="demo-section">
                <h4>📋 支持的字段类型</h4>
                <div class="code-block">• Text - 单行文本输入
• TextArea - 多行文本输入  
• InputEditor - 带类型的输入框（邮箱、电话等）
• Switch - 开关选择器
• Select/Picker - 下拉选择器
• Date/DateTime - 日期时间选择器
• RadioGroup - 单选按钮组
• CheckboxGroup - 复选框组
• Checkbox - 单个复选框</div>
            </div>
            
            <div class="demo-section">
                <h4>🔧 配置说明</h4>
                <p>FormBuilder 组件完全基于 <code>FieldConfig-README.md</code> 中定义的 JSON 格式进行配置。</p>
                <p>每个字段支持以下主要配置项：</p>
                <div class="code-block">• 基本信息：Id, Name, Title, Type
• 显示控制：Visible, Disabled, Readonly  
• 验证规则：Required, MinLength, MaxLength, Validation
• 数据源：DataSource, DefaultDataSource
• 样式配置：Class, Style, GroupClass
• 分组排序：Group, Order</div>
            </div>
            
            <a href="index-preview.html" class="back-link">← 返回首页</a>
        </div>
    </div>
</body>
</html>