export * from '../interface.uts' 

import { HideKeyboard } from '../interface.uts'
import { SetGray } from '../interface.uts'
import { OpenURL, UxOpenURLOptions } from '../interface.uts'
import { OpenWeb, UxOpenWebOptions } from '../interface.uts'
import { MakePhoneCall, UxMakePhoneCallOptions } from '../interface.uts'
import { Vibrate, UxVibrateOptions } from '../interface.uts'
import { SetClipboardData, GetClipboardData } from '../interface.uts'
import { Exit } from '../interface.uts'

/**
 * 公共函数
 */
import Common from "./Common.uts"
const common = new Common()

/**
 * 隐藏键盘
 */
export const hideKeyboard : HideKeyboard = function () {
	common.hideKeyboard()
}

/**
 * 置灰
 * @param Number gray 灰度 0 正常 1 灰色
 */
export const setGray : SetGray = function (gray : number) {
	common.setGray(gray)
}

/**
 * 打开链接
 * @param UxOpenURLOptions options 参数
 */
export const openURL : OpenURL = function (options : UxOpenURLOptions) {
	common.openURL(options)
}

/**
 * 打开Web页面
 * @param UxOpenWebOptions options 参数
 */
export const openWeb = function (options : UxOpenWebOptions) {
	common.openWeb(options)
}

/**
 * 拨打电话
 * @param UxMakePhoneCallOptions options 参数
 */
export const makePhoneCall : MakePhoneCall = function (options : UxMakePhoneCallOptions) {
	common.makePhoneCall(options)
}

/**
 * 震动
 * @param UxVibrateOptions options 参数
 */
export const vibrate : Vibrate = function (options : UxVibrateOptions) {
	common.vibrate(options)
}

/**
 * 设置粘贴板内容
 * @param string data 内容
 */
export const setClipboardData : SetClipboardData = function (data : string) {
	
}

/**
 * 获取粘贴板内容
 */
export const getClipboardData : GetClipboardData = function (): string {
	let data = ''
	
	return data
}

/**
 * 退出APP
 */
export const exit : Exit = function () {
	
}