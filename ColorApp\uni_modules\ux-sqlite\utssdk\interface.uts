
export type UxSqlWhere = {
	name: string
	value: any | null
	operator: string
}

export type OpenDatabaseOptions = {
	/**
	 * 数据库名称
	 */
	database ?: string
	/**
	 * 打印sql语句
	 */
	log?: boolean
	
	success ?: (res : UxSqlSuccess) => void
	fail ?: (err : UxSqlFail) => void
	complete ?: () => void
}

export type DeleteDatabaseOptions = {
	/**
	 * 数据库名称
	 */
	database ?: string
}

export type ExecQueryOptions = {
	/**
	 * 表名称
	 */
	table ?: string
	/**
	 * 去重
	 */
	distinct ?: boolean
	/**
	 * 选择字段
	 */
	selects ?: string[]
	/**
	 * 条件 SQL（Android 使用）
	 */
	whereSql : string | null
	/**
	 * and 条件（iOS 使用）
	 */
	wheres : UxSqlWhere[] | null
	/**
	 * or 条件（iOS 使用）
	 */
	ors : UxSqlWhere[] | null
	/**
	 * 分组
	 */
	groupBy ?: string
	/**
	 * 分组条件
	 */
	having ?: string
	/**
	 * 排序
	 */
	orderBy ?: string
	/**
	 * 分页
	 */
	limit ?: string
	/**
	 * 表字段
	 */
	columns: UTSJSONObject
}

export type ExecRawQueryOptions = {
	/**
	 * sql语句
	 */
	sql : string
	/**
	 * 表字段
	 */
	columns ?: UTSJSONObject
}

export type ExecInsertOptions = {
	/**
	 * 表名称
	 */
	table : string
	/**
	 * 内容
	 */
	content ?: UTSJSONObject
	/**
	 * 表字段
	 */
	columns: UTSJSONObject
}

export type ExecUpdateOptions = {
	/**
	 * 表名称
	 */
	table : string
	/**
	 * 更新内容
	 */
	content: UTSJSONObject | null
	/**
	 * 条件 SQL（Android 使用）
	 */
	whereSql : string | null
	/**
	 * and 条件（iOS 使用）
	 */
	wheres : UxSqlWhere[] | null
	/**
	 * or 条件（iOS 使用）
	 */
	ors : UxSqlWhere[] | null
	/**
	 * 表字段
	 */
	columns: UTSJSONObject
}

export type ExecDeleteOptions = {
	/**
	 * 表名称
	 */
	table : string
	/**
	 * 条件 SQL（Android 使用）
	 */
	whereSql : string | null
	/**
	 * and 条件（iOS 使用）
	 */
	wheres : UxSqlWhere[] | null
	/**
	 * or 条件（iOS 使用）
	 */
	ors : UxSqlWhere[] | null
}

export type ExecSQLOptions = {
	/**
	 * SQL语句
	 */
	sql : string
}

export type ExecTransactionOptions = {
	/**
	 * 操作事务
	 */
	operations : () => boolean
}

export type ExecCountOptions = {
	/**
	 * 表名称
	 */
	table : string
	/**
	 * 条件 SQL（Android 使用）
	 */
	whereSql : string | null
	/**
	 * and 条件（iOS 使用）
	 */
	wheres : UxSqlWhere[] | null
	/**
	 * or 条件（iOS 使用）
	 */
	ors : UxSqlWhere[] | null
}

export type GetColumnsOptions = {
	/**
	 * 表名称
	 */
	table : string
}

export type UxSqlSuccess = {
	code: number
	count : number
	datas : any[]
}

export type UxSqlErrorCode = -1 | -2;

export interface UxSqlFail extends IUniError {
	errCode : UxSqlErrorCode
}

export type OpenDatabase = (options : OpenDatabaseOptions) => void

export type CloseDatabase = () => boolean

export type DeleteDatabase = (options: DeleteDatabaseOptions) => boolean

// #ifdef APP-ANDROID
export type ExecQuery = (options: ExecQueryOptions) => Array<Map<string, any | null>>

export type ExecRawQuery = (options: ExecRawQueryOptions) => Array<Map<string, any | null>>
// #endif

// #ifndef APP-ANDROID
export type ExecQuery = (options: ExecQueryOptions) => Array<Map<string, any>>

export type ExecRawQuery = (options: ExecRawQueryOptions) => Array<Map<string, any>>
// #endif

export type ExecInsert = (options: ExecInsertOptions) => number

export type ExecUpdate = (options: ExecUpdateOptions) => number

export type ExecDelete = (options: ExecDeleteOptions) => number

export type ExecSQL = (options: ExecSQLOptions) => boolean

export type ExecTransaction = (options: ExecTransactionOptions) => boolean

export type ExecCount = (options: ExecCountOptions) => number

export type GetColumns = (options: GetColumnsOptions) => string[]

export type GetPath = () => string

export type IsOpen = () => boolean