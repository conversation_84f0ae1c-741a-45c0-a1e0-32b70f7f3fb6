<template>
	<view>
        
	</view>
</template>

<script lang="uts">
	/**
	* 精灵动画组件
	* @demo pages/component/sprite.uvue
	* @tutorial https://www.uxframe.cn/component/sprite.html
	* @property {Array} 			frames					string[] | 精灵图集
	* @property {Number} 			duration				Number | 每帧周期 (默认 100)
	* @property {Boolean} 			play					Boolean | 是否播放 (默认 false)
	* @property {Boolean} 			loop					Boolean | 循环播放 (默认 false)
	* @property {Boolean} 			autoplay				Boolean | 自动播放 (默认 false)
	* @event {Function} 			start 					Function | 开始时触发
	* @event {Function} 			end 					Function | 结束时触发
	* <AUTHOR>
	* @date 2025-04-07 21:10:21
	*/
   
	import { UIView, UIImage, UIImageView } from "UIKit"
	import { CGRect } from "CoreGraphics"
	
	import { PropType } from "vue";
	
	export default {
		name: 'ux-sprite',
		emits: ['start', 'end'],
		props: {
			frames: {
				type: Array as PropType<Array<string>>,
				default: () : Array<string> => {
					return [] as Array<string>
				}
			},
			duration: {
				type: Number,
				default: 100
			},
			play: {
				type: Boolean,
				default: false
			},
			loop: {
				type: Boolean,
				default: false
			},
			autoplay: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				myView: new UIView() as UIView,
				imageView: new UIImageView() as UIImageView,
				durations: 0,
				datas: [] as string[]
			}
		},
		watch: {
			frames: {
				handler(newVal : string[], oldVal : string[]) {
					this.datas = this.frames
					this.setResource()
				},
				immediate: true,
				deep: true
			},
			play: {
				immediate: true,
				handler(newVal : boolean, oldVal : boolean) {
					if (newVal) {
						this.playAnim()
					} else {
						this.stopAnim()
					}
				},
			},
			autoplay: {
				immediate: false,
				handler(newVal : boolean, oldVal : boolean) {
					if (newVal) {
						this.playAnim()
					}
				}
			},
			loop: {
				immediate: true,
				handler(newVal : boolean, oldVal : boolean) {
					this.imageView.animationRepeatCount = this.loop ? 0 : 1
				}
			}
		},
		expose: ['setDatas'],
		methods: {
			setDatas(datas: string[]) {
				this.datas = datas
				this.setResource()
			},
			// 播放
			playAnim() {
				this.$emit('start')
				this.imageView.startAnimating()
				this.listenPlayEnd()
			},
			// 停止
			stopAnim() {
				this.imageView.stopAnimating()
			},
			// 监听播放结束
			listenPlayEnd() {
				setTimeout(() => {
					this.$emit('end')
				}, this.durations as Int)
			},
			// 设置动画资源
			setResource() {
				try {
					let frames = this.datas
					
					if (frames.length == 0) {
						return
					}
					
					let images = [] as UIImage[]
					
					frames.forEach((path: string) => {
						let filePath: string
						if(path.indexOf('http://') != -1 || path.indexOf('https://') != -1) {
							filePath = path
						} else {
							filePath = UTSiOS.getResourcePath(path)
						}
						
						let image = new UIImage(contentsOfFile = filePath)
						if(image != null) {
							images.add(image!)
						}
					})
					
					this.durations = (images.length as number) * this.duration
					
					this.imageView.frame = CGRect(x = 0, y = 0, width = this.myView.bounds.width, height = this.myView.bounds.width)
					this.imageView.contentMode = UIView.ContentMode.scaleAspectFit
					this.imageView.animationImages = images
					this.imageView.animationDuration = (this.durations / 1000) as TimeInterval
					this.imageView.animationRepeatCount = this.loop ? 0 : 1
					
					if(this.autoplay) {
						this.playAnim()
					}
				} catch (e) {
					//TODO handle the exception
				}
			}
		},
		NVLoad() : UIView {
			this.myView.addSubview(this.imageView)
			return this.myView
		},
		NVLayouted() {
			this.setResource()
		},
		NVMeasure(size : UTSSize) : UTSSize {
			return size
		},
		NVUpdateStyles(styles : Map<string, any>) {
			this.setResource()
		}
	}
</script>

