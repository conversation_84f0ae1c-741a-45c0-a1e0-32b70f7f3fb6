import { UIImage, UIActivityViewController } from 'UIKit';
import { URL , FileManager } from 'Foundation';
import { ShareWithSystem, UxShareWithSystemOptions } from '../interface.uts';
import { UxShareSuccessCallbackImpl, UxShareErrorCallbackImpl } from '../unierror.uts'

/**
 * 系统分享
 * @param UxShareWithSystemOptions options 参数
 */
export const shareWithSystem: ShareWithSystem = function(options: UxShareWithSystemOptions) {
	let items = [] as any[];
	if(options.type == 'image'){
		if (options.imageUrl == null) {
			const err = new UxShareErrorCallbackImpl('shareWithSystem:imageUrl is null')
			options.fail?.(err)
			options.complete?.(err)
			return;
		}
		
		let path = options.imageUrl!
		if(path.startsWith('/static/')) {
			path = UTSiOS.getResourcePath(options.imageUrl!)
		}
		
		items = [new UIImage(contentsOfFile = path)]
	} else if(options.type == 'file'){
		if (options.href == null) {
			const err = new UxShareErrorCallbackImpl('shareWithSystem:href is null')
			options.fail?.(err)
			options.complete?.(err)
			return;
		}
		
		items = [FileManager.default.contents(atPath = options.href!), new URL.init(fileURLWithPath = options.href!)]
	} else{
		if(options.imageUrl != null) {
			let path = options.imageUrl!
			if(path.startsWith('/static/')) {
				path = UTSiOS.getResourcePath(options.imageUrl!)
			}
			
			items.push(new UIImage(contentsOfFile = path))
		}
		
		if(options.href != null) {
			items.push(URL(string = options.href!))
		}
		
		if(options.title != null || options.summary != null) {
			let title = options.title ?? ''
			let summary = options.summary ?? ''
			
			if(summary != '') {
				if(title == '') {
					title = summary
				} else {
					title += '\n' + summary
				}
			}
			
			items.push(title)
		}
	}
	
	DispatchQueue.main.async(execute = ():void => {
		let activityViewController = new UIActivityViewController(activityItems = items, applicationActivities = nil)
		UTSiOS.getCurrentViewController().present(activityViewController, animated = true, completion = () => {
			const res = new UxShareErrorCallbackImpl('shareWithSystem:success')
			options.success?.(res)
			options.complete?.(res)
		})
	})
}