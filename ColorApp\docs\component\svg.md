<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/svg?title=Svg"></Mobile>

# Svg
> 组件类型：UxSvgComponentPublicInstance



## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| src | String |  | 路径 |
| color | String |  | 颜色 |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| loaded | 加载完成时触发 |  |
  
  