import Foundation
import DCloudUTSFoundation
import SQLite3

var db: OpaquePointer? = nil
var databaseName: String? = nil

public class UxDB {
  
  static func openDatabase(name: String) -> Bool {
      let documentsDirectory = FileManager.default.urls(for:.documentDirectory, in:.userDomainMask).first!
      let databaseURL = documentsDirectory.appendingPathComponent(name)
      databaseName = name
	  
      if sqlite3_open(databaseURL.path, &db) == SQLITE_OK {
          return true
      } else {
          return false
      }
  }
  
  static func closeDatabase() -> <PERSON>ol {
	return sqlite3_close(db) == SQLITE_OK
  }
  
  
 static func deleteDatabase(name: String) -> Bool {
      let fileManager = FileManager.default
      do {
          let documentDirectory = try fileManager.urls(for:.documentDirectory, in:.userDomainMask).first!
          let databaseURL = documentDirectory.appendingPathComponent(name)
          try fileManager.removeItem(at: databaseURL)
          return true
      } catch {
          return false
      }
  }
  
  static func isOpen() -> <PERSON><PERSON> {
    return db != nil
  }
  
  static func getPath() -> String? {
	if databaseName == nil {
		return nil
	}
	
    let documentsDirectory = FileManager.default.urls(for:.documentDirectory, in:.userDomainMask).first!
    let databaseURL = documentsDirectory.appendingPathComponent(databaseName!)
    return databaseURL.path
  }

  static func execSQL(sql: String) -> Bool {
	  var statement: OpaquePointer?
	  guard sqlite3_prepare_v2(db, sql, -1, &statement, nil) == SQLITE_OK else {
	      return false
	  }
	  
	  defer {
	      sqlite3_finalize(statement)
	  }
	  
	  guard sqlite3_step(statement) == SQLITE_DONE else {
	      return false
	  }
	  
	  return true
  }
  
  static func transaction(operations: () -> Bool) -> Bool {
	  sqlite3_exec(db, "BEGIN TRANSACTION;", nil, nil, nil)
	  
	  let success = operations()
	  
	  if success {
	      sqlite3_exec(db, "COMMIT;", nil, nil, nil)
	  } else {
	      sqlite3_exec(db, "ROLLBACK;", nil, nil, nil)
	  }
	
  	  return success
  }
  
  static func bind(values: [Any?], statement: OpaquePointer?) {
	for (index, parameter) in values.enumerated() {
	    let idx = Int32(index + 1)
		
	    if let parameter = parameter {
	         switch parameter {
	    	 case let value as Int64:
	    	     sqlite3_bind_int64(statement, idx, Int64(value))
	         case let value as Double:
	             sqlite3_bind_double(statement, idx, value)
	         case let value as Int:
	             sqlite3_bind_int(statement, idx, Int32(value))
			 case let value as Int16:
			    sqlite3_bind_int(statement, idx, Int32(value))
			case let value as Float:
			    sqlite3_bind_double(statement, idx, Double(value))
	         case let value as String:
	             sqlite3_bind_text(statement, idx, (value as NSString).utf8String, -1, nil)
	    	 case let value as Bool:
	    		 sqlite3_bind_int(statement, idx, (value as Bool) ? 1 : 0)
			 // case let value as Data:
				//  sqlite3_bind_blob(statement, idx, (value as NSData).bytes, Int32(value.count), nil)
	         default:
	             sqlite3_bind_null(statement, idx)
	         }
	     } else {
	        sqlite3_bind_null(statement, idx)
	    }
	}
  }
  
  static func format(statement: OpaquePointer?, index: Int32) -> Any? {
      switch sqlite3_column_type(statement, index) {
      case SQLITE_INTEGER:
          return sqlite3_column_int64(statement, index)
      case SQLITE_FLOAT:
          return sqlite3_column_double(statement, index)
      case SQLITE_TEXT:
          return String(cString: sqlite3_column_text(statement, index))
      case SQLITE_BLOB:
          let data = sqlite3_column_blob(statement, index)
          let size = sqlite3_column_bytes(statement, index)
          return Data(bytes: data!, count: Int(size))
      case SQLITE_NULL:
          return nil
      default:
          return nil
      }
  }
  
  static func query(sql: String, values: [Any?]) -> [[String: Any]] {
	var results = [[String: Any]]()
	  
  	var statement: OpaquePointer?
  	guard sqlite3_prepare_v2(db, sql, -1, &statement, nil) == SQLITE_OK else {
  	    return results
  	}
  	
  	bind(values: values, statement: statement)
	
	while sqlite3_step(statement) == SQLITE_ROW {
	    var row = [String: Any]()
	    for i in 0..<sqlite3_column_count(statement) {
	        let columnName = String(cString: sqlite3_column_name(statement, i))
	        let columnValue = format(statement: statement, index: i)
	        row[columnName] = columnValue
	    }
	    results.append(row)
	}
  	
  	defer {
  	    sqlite3_finalize(statement)
  	}
  	
  	return results
  }
  
  static func insert(sql: String, values: [Any?]) -> Int64 {
  	var statement: OpaquePointer?
  	guard sqlite3_prepare_v2(db, sql, -1, &statement, nil) == SQLITE_OK else {
  	    return 0
  	}
  	
  	bind(values: values, statement: statement)
  	
  	defer {
  	    sqlite3_finalize(statement)
  	}
  	
  	guard sqlite3_step(statement) == SQLITE_DONE else {
  	    return 0
  	}
  	
  	return sqlite3_last_insert_rowid(db)
  }
  
  static func update(sql: String, values: [Any?]) -> Int64 {
  	var statement: OpaquePointer?
  	guard sqlite3_prepare_v2(db, sql, -1, &statement, nil) == SQLITE_OK else {
  	    return 0
  	}
  	
  	bind(values: values, statement: statement)
  	
  	defer {
  	    sqlite3_finalize(statement)
  	}
  	
  	guard sqlite3_step(statement) == SQLITE_DONE else {
  	    return 0
  	}
  	
  	return Int64(sqlite3_changes(db))
  }
  
  static func delete(sql: String, values: [Any?]) -> Int64 {
  	var statement: OpaquePointer?
  	guard sqlite3_prepare_v2(db, sql, -1, &statement, nil) == SQLITE_OK else {
  	    return 0
  	}
  	
  	bind(values: values, statement: statement)
  	
  	defer {
  	    sqlite3_finalize(statement)
  	}
  	
  	guard sqlite3_step(statement) == SQLITE_DONE else {
  	    return 0
  	}
  	
  	return Int64(sqlite3_changes(db))
  }
  
  static func count(sql: String, values: [Any?]) -> Int64 {
  	var statement: OpaquePointer?
  	guard sqlite3_prepare_v2(db, sql, -1, &statement, nil) == SQLITE_OK else {
  	    return 0
  	}
  	
  	bind(values: values, statement: statement)
  	
  	defer {
  	    sqlite3_finalize(statement)
  	}
  	
  	guard sqlite3_step(statement) == SQLITE_ROW else {
  	    return 0
  	}
  	
  	return Int64(sqlite3_column_int(statement, 0))
  }
  
  static func columns(tableName: String) -> [String] {
	var columns = [String]()
	  
	var statement: OpaquePointer?
	guard sqlite3_prepare_v2(db, "PRAGMA table_info(\(tableName));", -1, &statement, nil) == SQLITE_OK else {
	    return columns
	}
	
	while sqlite3_step(statement) == SQLITE_ROW {
	    let columnName = sqlite3_column_text(statement, 1)
	    if let columnName = columnName {
	        let columnNameString = String(cString: columnName)
	        columns.append(columnNameString)
	    }
	}
	
	defer {
	    sqlite3_finalize(statement)
	}
	
	return columns
  }
}
