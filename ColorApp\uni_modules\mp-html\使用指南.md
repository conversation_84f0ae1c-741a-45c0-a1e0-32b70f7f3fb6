# mp-html 组件使用指南

## 🔧 最新修复 (v1.0.3)

### 自闭合标签检测逻辑优化
- **问题**：自闭合标签修复导致复杂内容无法正确渲染
- **原因**：检测 `/` 字符时没有验证下一个字符是否为 `>`，导致误判
- **修复**：严格检查 `/>` 组合，确保只有真正的自闭合标签才被识别
- **影响**：现在复杂内容和自闭合标签都能正确解析渲染

### 自闭合标签识别优化 (v1.0.2)
- **问题**：HtmlParser 未能正确识别以 `/>` 结尾的自闭合标签
- **修复**：优化解析逻辑，支持标准的自闭合标签语法
- **影响**：现在可以正确解析 `<img />`, `<br/>`, `<hr/>`, `<input />` 等标准自闭合标签

### StyleUtils.mergeStyles 错误修复 (v1.0.1)

### 修复内容
- **修复 StyleUtils.mergeStyles 错误**：解决了 `defaultStyle.keys is not a function` 的运行时错误
- **UTS 语法优化**：将 `UTSJSONObject.keys()` 方法替换为标准的 `for...in` 循环
- **兼容性提升**：确保所有代码符合 UTS 语言规范

### 影响范围
此修复解决了组件在解析和应用样式时的关键错误，现在组件可以正常运行。

---

## 🚀 快速开始

### 1. 基础使用

```vue
<template>
  <mp-html :content="htmlContent" />
</template>

<script setup lang="uts">
const htmlContent = '<p>Hello <strong>World</strong>!</p>'
</script>
```

### 2. 完整示例

```vue
<template>
  <mp-html 
    :content="htmlContent"
    :selectable="true"
    :show-img-menu="true"
    @link-tap="onLinkTap"
    @img-tap="onImgTap"
    @error="onError"
  />
</template>

<script setup lang="uts">
import { LinkTapEventType, ImgTapEventType, ErrorEventType } from '@/uni_modules/mp-html/libs/types.uts'

const htmlContent = `
  <div style="padding: 15px;">
    <h2>标题</h2>
    <p>段落内容，包含<a href="https://example.com">链接</a></p>
    <img src="https://example.com/image.jpg" style="width: 200px;" />
  </div>
`

const onLinkTap = (event: LinkTapEventType) => {
  console.log('链接点击:', event.href)
}

const onImgTap = (event: ImgTapEventType) => {
  console.log('图片点击:', event.src)
}

const onError = (event: ErrorEventType) => {
  console.error('解析错误:', event.error)
}
</script>
```

## 运行项目

1. 使用HBuilderX打开项目
2. 选择运行到浏览器或模拟器
3. 在首页点击"mp-html 组件"进入示例页面

## 测试组件

可以在控制台运行以下代码来测试组件功能：

```javascript
import { MpHtmlTest } from '@/uni_modules/mp-html/test/mp-html-test.uts'
MpHtmlTest.runAllTests()
```

## 支持的功能

- ✅ HTML标签解析
- ✅ CSS样式解析
- ✅ 图片显示
- ✅ 链接点击
- ✅ 列表渲染
- ✅ 文本样式
- ✅ 事件处理

## 注意事项

1. 确保HTML内容格式正确
2. 图片链接需要是有效的URL
3. 复杂的CSS样式可能不完全支持
4. 建议在真机上测试完整功能