import { Review, AppStore } from '../interface.uts'
import Intent from 'android.content.Intent'
import Uri from 'android.net.Uri'
import Build from 'android.os.Build'

/**
 * 跳转应用市场
 * @param id appstore appid
 */
export const appStore: AppStore = (id: string): boolean => {
	let marketId = ''
	let market = `market://details?id=${id}`
	
	let brand = Build.MANUFACTURER.toLowerCase()
	if (brand == 'huawei' || brand == 'honor') {
		marketId = "com.huawei.appmarket"
		market = `market://details?id=${id}`
	} else if (brand == 'xiaomi') {
		marketId = "com.xiaomi.market"
		market = `mimarket://details?id=${id}`
	} else if (brand == 'oppo') {
		marketId = "com.heytap.appmarket"
		market = `oppomarket://details?packagename=${id}`
	} else if (brand == 'vivo') {
		marketId = "com.bbk.appstore"
		market = `vivomarket://details?id=${id}`
	} else if (brand == 'meizu') {
		marketId = "com.meizu.mstore"
		market = `market://details?id=${id}`
	} else if (brand == 'samsung') {
		marketId = "com.sec.android.app.samsungapps"
		market = `samsungapps://ProductDetail/${id}`
	}
	
	const uri = Uri.parse(market)
	const intent = new Intent(Intent.ACTION_VIEW, uri)
	intent.setData(uri)
	UTSAndroid.getUniActivity()!.startActivity(intent)
	
	return false
}

/**
 * 打开应用评分
 */
export const review: Review = (): boolean => {
	return false
}