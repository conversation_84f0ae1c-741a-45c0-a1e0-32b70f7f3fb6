<template>
	<view class="ux-loadmore" @click="click">
		<ux-loading v-if="state == 1" type="icon"></ux-loading>
		<text class="ux-loadmore__text">{{ states[state] }}</text>
	</view>
</template>

<script setup>
	
	/**
	 * 上拉加载
	 * @description 支持自定义文案
	 * @demo pages/component/loadmore.uvue
	 * @tutorial https://www.uxframe.cn/component/loadmore.html
	 * @property {Number}		state  				Number | 状态下标 (默认 [0)
	 * @property {Array} 		states 				string[] | 状态文案 (默认 ['已加载', '加载中...', '-- 我也是有底线的 --'])
	 * @event {Function}		click				Function | 点击时触发
	 * <AUTHOR>
	 * @date 2023-11-03 14:28:11
	 */
	
	defineOptions({
		name: 'ux-loadmore'
	})
	
	const emit = defineEmits(['click'])
	
	defineProps({
		state: {
			type: Number,
			default: 0
		},
		states: {
			type: Array,
			default: (): string[] => {
				return ['已加载', '加载中...', '-- 我也是有底线的 --'] as string[]
			}
		},
	})
	
	function click(e: MouseEvent) {
		emit('click', e)
	}
	
</script>

<style lang="scss">

	.ux-loadmore {
		width: 100%;
		height: 50px;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		
		&__text {
			color: gray;
			font-size: 13px;
			margin-left: 5px;
		}
	}

</style>