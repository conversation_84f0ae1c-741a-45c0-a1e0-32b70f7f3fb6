<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" xmlns:tools="http://schemas.android.com/tools"
	package="uts.sdk.modules.uxShare">
	
	<uses-permission android:name="android.permission.INTERNET" />
	<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
	<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
	<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
	
	<application>
		<meta-data android:name="ScopedStorage" android:value="true"/>
	
		<provider
		    android:name="uts.sdk.modules.uxShare.ShareFileProvider"
		    android:authorities="${applicationId}.uxShare.fileprovider"
		    android:exported="false"
		    android:grantUriPermissions="true"
			tools:replace="android:authorities">
		    <meta-data
		        android:name="android.support.FILE_PROVIDER_PATHS"
		        android:resource="@xml/uxshare_file_provider" />
		</provider>
	</application>
</manifest>
