
<p align="center">
    <img alt="logo" src="https://www.uxframe.cn/logo/logo.png" width="120" height="120" style="margin-bottom: 10px;">
</p>
<h3 align="center" style="margin: 30px 0 30px;font-weight: bold;font-size:40px;">UxFrame 系统分享SDK 1.0.1</h3>

## 特别说明

如果您已经购买了[UxFrame 低代码高性能UI框架](https://ext.dcloud.net.cn/plugin?id=16148), 则无需再次购买本插件，请点击上方`进入交流群`联系我免费获取离线版插件！

### 使用方法

``` ts

import * as share from "@/uni_modules/ux-share"

function shareText() {
	share.shareWithSystem({
		type: 'text',
		title: 'UxFrame低代码高性能UI框架',
		summary: 'UxFrame是基于UNI-APP-X开发的低代码高性能原生UI框架',
		href: 'https://www.uxframe.cn',
		imageUrl: '/static/logo.png'
	} as share.UxShareWithSystemOptions)
}

function shareImage() {
	share.shareWithSystem({
		type: 'image',
		imageUrl: '/static/logo.png'
	} as share.UxShareWithSystemOptions)
}

function shareFile() {
	share.shareWithSystem({
		type: 'file',
		href: '/static/demo.pdf'
	} as share.UxShareWithSystemOptions)
}

```

## 文档教程 ⬇️

[https://www.uxframe.cn](https://www.uxframe.cn)
