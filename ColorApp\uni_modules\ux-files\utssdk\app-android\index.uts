
import { UxFile, UxMimeType } from '../interface.uts'
import { OpenFileManager, UxOpenFileManagerOptions } from '../interface.uts'
import { SearchFiles, UxSearchFilesOptions } from '../interface.uts'
import { StopSearch } from '../interface.uts'

/**
 * 打开文件管理器
 * @param {UxOpenFileManagerOptions} options 参数
 */
export const openFileManager: OpenFileManager = (options: UxOpenFileManagerOptions) => {
	let mimeTypes = options.mimeTypes ?? []
	let multiple = options.multiple ?? false
	let ignore: string[] = []
	
	let callback = (files: MutableList<MutableMap<string, any | null>>) => {
		let _files: UxFile[] = []
		
		files.forEach((file: MutableMap<string, any | null>) => {
			_files.push({
				name: file.get('name') as string,
				path: file.get('path') as string,
				extension: file.get('extension') as string,
				blob: file.get('blob'),
				size: file.get('size') as number,
				date: file.get('date') as string,
			})
		})
		
		options.success?.(_files)
	}
	
	UxFiles.openFileManager(getMimeTypes(false, mimeTypes, ignore), multiple, callback)
}

/**
 * 搜索文件
 * @param {UxSearchFilesOptions} options 参数
 */
export const searchFiles: SearchFiles = (options: UxSearchFilesOptions) => {
	let mimeTypes = options.mimeTypes ?? []
	let ignore = options.ignore ?? []
	
	let callback = (file: MutableMap<string, any | null>) => {
		options.success?.({
			name: file.get('name') as string,
			path: file.get('path') as string,
			extension: file.get('extension') as string,
			blob: file.get('blob'),
			size: file.get('size') as number,
			date: file.get('date') as string,
		} as UxFile)
	}
	
	UxFiles.searchFiles(getMimeTypes(true, mimeTypes, ignore), callback)
}

/**
 * 停止搜索文件
 */
export const stopSearch: StopSearch = () => {
	UxFiles.stopSearch()
}

const ImageTypes = {
	"jpg": "image/jpeg",
	"jpeg": "image/jpeg",
	"png": "image/png",
	"gif": "image/gif",
	"bmp": "image/bmp",
	"webp": "image/webp",
	"heic": "image/heic",
	"tiff": "image/tiff",
	"svg": "image/svg+xml",
	"ico": "image/x-icon",
}

const VideoTypes = {
	"mp4": "video/mp4",
	"webm": "video/webm",
	"ogv": "video/ogg",
	"mov": "video/quicktime",
	"avi": "video/x-msvideo",
	"wmv": "video/x-ms-wmv",
	"mkv": "video/x-matroska",
}

const AudioTypes = {
	"mp3": "audio/mpeg",
	"wav": "audio/wav",
	"ogg": "audio/ogg",
	"flac": "audio/flac",
	"aac": "audio/aac",
	"m4a": "audio/mp4",
	"wma": "audio/x-ms-wma",
	"opus": "audio/opus",
}

const TxtTypes = {
	"txt": "text/plain",
	"html": "text/html",
	"css": "text/css",
	"js": "text/javascript",
	"xml": "text/xml",
	"csv": "text/csv",
	"markdown": "text/markdown",
	"json": "application/json",
}

const PdfTypes = {
	"pdf": "application/pdf"
}

const DocTypes = {
	"doc": "application/msword",
	"docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
}

const XlsTypes = {
	"xls": "application/vnd.ms-excel",
	"xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
}

const PptTypes = {
	"ppt": "application/vnd.ms-powerpoint",
	"pptx": "application/vnd.openxmlformats-officedocument.presentationml.presentation",
}

const ZipTypes = {
	"zip": "application/zip",
	"rar": "application/vnd.rar",
	"7z": "application/x-7z-compressed",
	"epub": "application/epub+zip",
	"exe": "application/octet-stream",
	"bin": "application/octet-stream",
}

function handleTypes(types: UTSJSONObject, ignore: string[]): string[] {
	let mimeTypes: string[] = []
	
	let keys = UTSJSONObject.keys(types)
	keys.filter(e => ignore.indexOf(e) == -1).forEach(k => {
		mimeTypes.push(types.getString(k)!)
	})
	
	return mimeTypes
}
	
function getMimeTypes(search: boolean, types: UxMimeType[], ignore: string[]): kotlin.Array<string> {
	let mimeTypes: string[] = []
	
	for (let i = 0; i < types.length; i++) {
		let t = types[i]
		
		if(t == 'image') {
			mimeTypes = mimeTypes.concat(handleTypes(ImageTypes, ignore))
			if(!search) {
				mimeTypes.add('image/*')
			}
		} else if(t == 'video') {
			mimeTypes = mimeTypes.concat(handleTypes(VideoTypes, ignore))
			if(!search) {
				mimeTypes.add('video/*')
			}
		} else if(t == 'audio') {
			mimeTypes = mimeTypes.concat(handleTypes(AudioTypes, ignore))
			if(!search) {
				mimeTypes.add('audio/*')
			}
		} else if(t == 'txt') {
			mimeTypes = mimeTypes.concat(handleTypes(TxtTypes, ignore))
			if(!search) {
				mimeTypes.add('text/*')
			}
		} else if(t == 'pdf') {
			mimeTypes = mimeTypes.concat(handleTypes(PdfTypes, ignore))
			if(!search) {
				mimeTypes.add('application/pdf')
			}
		} else if(t == 'doc') {
			mimeTypes = mimeTypes.concat(handleTypes(DocTypes, ignore))
			if(!search) {
				mimeTypes.add('application/*')
			}
		} else if(t == 'xls') {
			mimeTypes = mimeTypes.concat(handleTypes(XlsTypes, ignore))
			if(!search) {
				mimeTypes.add('application/*')
			}
		} else if(t == 'ppt') {
			mimeTypes = mimeTypes.concat(handleTypes(PptTypes, ignore))
			if(!search) {
				mimeTypes.add('application/*')
			}
		} else if(t == 'zip') {
			mimeTypes = mimeTypes.concat(handleTypes(ZipTypes, ignore))
			if(!search) {
				mimeTypes.add('application/*')
			}
		}
	}
	
	return mimeTypes.toTypedArray()
}