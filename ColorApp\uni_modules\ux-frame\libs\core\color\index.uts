/**
 * 颜色工具类 - 提供颜色解析、转换和渐变生成功能
 */

import { RGBA, HSL, RGB } from "../../types/color";
import { predefinedColors } from './predefined-colors.uts';

export class Color {

	// 颜色格式正则表达式常量
	private static readonly HEX_PATTERN =
		/^#([a-f0-9]{3}|[a-f0-9]{6}|[a-f0-9]{8})$/i;
	private static readonly RGB_PATTERN =
		/^rgb\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*\)$/i;
	private static readonly RGBA_PATTERN =
		/^rgba\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d*\.?\d+)\s*\)$/i;
	private static readonly HSL_PATTERN =
		/^hsl\(\s*(\d{1,3})\s*,\s*(\d{1,3})%\s*,\s*(\d{1,3})%\s*\)$/i;
	private static readonly HSLA_PATTERN =
		/^hsla\(\s*(\d{1,3})\s*,\s*(\d{1,3})%\s*,\s*(\d{1,3})%\s*,\s*(0|1|0?\.\d+)\s*\)$/i;

	// 方向映射常量
	private static readonly DIRECTION_MAP = new Map<string, string>([
		["top", "to top"],
		["bottom", "to bottom"],
		["left", "to left"],
		["right", "to right"],
	]);

	/**
	 * 判断是否是颜色相关的样式
	 */
	public isColorRelatedStyle(value : string) : boolean {
		return value.includes("background") || value.includes("color");
	}

	/**
	 * 解析颜色值 - 支持预定义颜色名称和标准颜色格式
	 */
	public parseColorValue(value : string) : string {
		if (value == "") return "";

		// 先检查原始值是否为有效颜色格式
		const trimmedValue = value.trim();
		if (this.isValidColorFormat(trimmedValue)) {
			return trimmedValue;
		}
		const formattedValue = value.toLowerCase().trim().replace(/\s+/g, "");
		// 检查是否匹配标准颜色格式
		if (this.isValidColorFormat(formattedValue)) {
			return formattedValue;
		}
		// 检查预定义颜色
		const predefinedColor = predefinedColors.get(formattedValue);
		if (predefinedColor != null && predefinedColor != "") {
			return predefinedColor;
		}

		// 默认返回主色
		return predefinedColors.get("primary") as string;
	}

	/**
	 * 检查是否为有效的颜色
	 */
	public isValid(color : string) : boolean {
		return this.isValidColorFormat(color)
	}
	
	/**
	 * 检查是否为有效的颜色格式
	 */
	private isValidColorFormat(value : string) : boolean {
		return (
			Color.HEX_PATTERN.test(value) ||
			Color.RGB_PATTERN.test(value) ||
			Color.RGBA_PATTERN.test(value) ||
			Color.HSL_PATTERN.test(value) ||
			Color.HSLA_PATTERN.test(value)
		);
	}

	// 贝塞尔曲线计算
	private calculateBezier(t : number, p0 : number, p1 : number) : number {
		return (1 - t) * p0 + t * p1;
	}

	// 贝塞尔曲线导数计算
	private calculateBezierDerivative(p0 : number, p1 : number) : number {
		return p1 - p0;
	}

	/**
	 * 贝塞尔插值
	 * @param startX
	 * @param endX
	 * @param startY
	 * @param endY
	 * @returns
	 */
	public bezierInterpolation(
		startX : number,
		endX : number,
		startY : number,
		endY : number
	) : ((input : number) => number) | null {
		// 验证输入参数范围是否正确
		if (!(0 <= startX && startX <= 1 && 0 <= startY && startY <= 1)) {
			return null;
		}
		const stepSize = 0.1;

		// 存储中间计算值
		let bezierValues : number[] = [];

		// 如果曲线的两个点不同，预计算贝塞尔曲线上的点
		if (startX !== endX || startY !== endY) {
			for (let i = 0; i <= 10; ++i) {
				bezierValues.push(
					this.calculateBezier(i * stepSize, startX, startY) as number
				);
			}
		}

		// 内部函数：用于查找给定 x 值对应的 t 参数
		const findTForX = (x : number) : number => {
			let lowerBound = 0;
			let upperBoundIndex = 1;

			// 通过预计算的点找到对应的区间
			while (upperBoundIndex < 10 && bezierValues[upperBoundIndex] <= x) {
				lowerBound += stepSize;
				upperBoundIndex++;
			}
			upperBoundIndex--;

			// 线性插值初步估计 t 值
			let tEstimate =
				lowerBound +
				((x - bezierValues[upperBoundIndex]) /
					(bezierValues[upperBoundIndex + 1] - bezierValues[upperBoundIndex])) *
				stepSize;
			let slope = this.calculateBezierDerivative(startX, startY);

			// 如果斜率较大，则使用 Newton-Raphson 法进一步逼近 t 值
			if (slope >= 0.001) {
				const newtonRaphsonMethod = (
					x : number,
					t : number,
					startX : number,
					startY : number
				) : number => {
					let correctedT = t;
					for (let i = 0; i < 4; i++) {
						let derivative = this.calculateBezierDerivative(startX, startY);
						if (derivative == 0) return correctedT;
						correctedT -=
							(this.calculateBezier(correctedT, startX, startY) - x) /
							derivative;
					}
					return correctedT;
				};
				return newtonRaphsonMethod(x, tEstimate, startX, startY);
			}

			// 如果斜率为 0，则直接返回 t 值
			if (slope == 0) {
				return tEstimate;
			}
			const bisectionMethod = (
				x : number,
				lower_bound : number,
				upper_bound : number,
				startX : number,
				startY : number
			) : number => {
				let midPoint : number;
				let iterations : number = 0;
				let error : number;
				do {
					midPoint = (lower_bound + upper_bound) / 2;
					error = this.calculateBezier(midPoint, startX, startY) - x;
					if (error > 0) {
						upper_bound = midPoint;
					} else {
						lower_bound = midPoint;
					}
				} while (Math.abs(error) > 1e-7 && ++iterations < 10);
				return midPoint;
			};
			return bisectionMethod(
				x,
				lowerBound,
				lowerBound + stepSize,
				startX,
				startY
			);
		};

		return function (input : number) : number {
			// 如果起始点和终点相同或输入为 0 或 1，直接返回输入值
			if ((startX == endX && startY == endY) || input == 0 || input == 1) {
				return input;
			}
			// 根据插值计算
			return 1 - findTForX(input) * endX + findTForX(input) * endY;
		};
	}

	/**
	 * 解析颜色字符串为RGBA对象
	 */
	public parseColorString(color : string) : RGBA {
		if (color == "" || color.length == 0) {
			return { r: 0, g: 0, b: 0, a: 0 };
		}

		const colorStr = color.toLowerCase().trim();
		
		// 检查预定义颜色
		const predefinedColor = predefinedColors.get(colorStr);
		if (predefinedColor != null && predefinedColor != "") {
			return this.parseHexColor(predefinedColor);
		}

		// 解析十六进制颜色
		if (colorStr.startsWith("#")) {
			return this.parseHexColor(colorStr);
		}

		// 解析RGB/RGBA颜色
		if (colorStr.startsWith("rgb")) {
			return this.parseRgbColor(colorStr);
		}

		// 默认返回黑色
		return { r: 0, g: 0, b: 0, a: 1 };
	}

	/**
	 * 解析十六进制颜色
	 */
	private parseHexColor(hex : string) : RGBA {
		const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3}|[A-Fa-f0-9]{8})$/;
		if (!hexRegex.test(hex)) {
			return { r: 0, g: 0, b: 0, a: 1 };
		}

		let colorStr = hex;

		// 处理三位十六进制 (#RGB -> #RRGGBB)
		if (colorStr.length == 4) {
			colorStr =
				"#" +
				colorStr[1] +
				colorStr[1] +
				colorStr[2] +
				colorStr[2] +
				colorStr[3] +
				colorStr[3];
		}

		const r = parseInt(colorStr.substring(1, 3), 16);
		const g = parseInt(colorStr.substring(3, 5), 16);
		const b = parseInt(colorStr.substring(5, 7), 16);
		const a =
			colorStr.length == 9 ? parseInt(colorStr.substring(7, 9), 16) / 255 : 1;

		return { r, g, b, a };
	}

	/**
	 * 解析RGB/RGBA颜色
	 */
	private parseRgbColor(rgb : string) : RGBA {
		const values = rgb
			.replace(/(?:\(|\)|rgba|rgb|RGB|RGBA)*/g, "")
			.split(",")
			.map((v) => parseFloat(v.trim()));

		if (values.length < 3) {
			return { r: 0, g: 0, b: 0, a: 1 };
		}

		return {
			r: values.length > 0 ? values[0] : 0,
			g: values.length > 1 ? values[1] : 0,
			b: values.length > 2 ? values[2] : 0,
			a: values.length >= 4 ? values[3] : 1,
		};
	}

	/**
	 * 生成线性渐变CSS字符串
	 */
	public generateLinearGradient(direction : string, colors : string[]) : string {
		if (direction == "") return "";
		if (colors.length == 0) return "";
		const mappedDirection = Color.DIRECTION_MAP.get(direction);

		const finalDirection =
			mappedDirection != null && mappedDirection != ""
				? mappedDirection
				: direction;

		const parsedColors = colors.map((color) => this.parseColorValue(color));

		return `linear-gradient(${finalDirection}, ${parsedColors.join(", ")})`;
	}

	/**
	 * 生成线性渐变 - 从字符串解析
	 * @param gradientString   渐变字符串
	 * @returns   渐变字符串
	 */
	generateLinearGradientFromString(gradientString : string) : string {
		if (gradientString == "") return "";

		// 拆分字符串为数组，移除多余的空格
		const gradientArray : string[] = gradientString.split(",");

		// 如果拆分后的数组长度不足 3，返回空字符串
		if (gradientArray.length < 3) return "";

		// 定义常见方向的映射
		const directionMap = new Map<string, string>([
			["top", "to top"],
			["bottom", "to bottom"],
			["left", "to left"],
			["right", "to right"],
		]);

		// 获取渐变方向并检查是否为预定义方向
		const direction = directionMap.get(gradientArray[0]);
		// 返回线性渐变的 CSS 字符串
		return `linear-gradient(${direction}, ${gradientArray[1]}, ${gradientArray[2]})`;
	}

	/**
	 * 创建RGBA颜色字符串
	 */
	public createRgbaString(
		r : number,
		g : number,
		b : number,
		a : number = 1
	) : string {
		return `rgba(${Math.round(r)}, ${Math.round(g)}, ${Math.round(b)}, ${a})`;
	}

	/**
	 * 解析后直接获取rgba值
	 */
	public getRgba(color : string, opacity : number = 1) : string {
		if (opacity > 1) return "";
		const RGBA = this.parseColorString(color);
		return this.createRgbaString(RGBA.r, RGBA.g, RGBA.b, opacity);
	}
	/**
	 * 创建十六进制颜色字符串
	 */
	public createHexString(r : number, g : number, b : number) : string {
		const toHex = (n : number) : string => {
			const hex = Math.round(Math.max(0, Math.min(255, n))).toString(16);
			return hex.length == 1 ? "0" + hex : hex;
		};
		return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
	}
	/**
	 * 获取随机颜色（十六进制格式）
	 */
	public getRandomColor() : string {
		const r = Math.floor(Math.random() * 256);
		const g = Math.floor(Math.random() * 256);
		const b = Math.floor(Math.random() * 256);
		return this.createHexString(r, g, b);
	}

	/**
	 * 获取随机柔和颜色（HSL方式生成）
	 */
	public getRandomSoftColor() : string {
		const h = Math.random(); // 随机色相
		const s = 0.3 + Math.random() * 0.4; // 饱和度30%-70%
		const l = 0.4 + Math.random() * 0.3; // 亮度40%-70%

		const rgb = this.hslToRgb(h, s, l);
		return this.createHexString(rgb.r, rgb.g, rgb.b);
	}
	/**
	 * 获取颜色的亮度值 (0-255)
	 */
	public getBrightness(color : string) : number {
		const rgba = this.parseColorString(color);
		const r = rgba.r;
		const g = rgba.g;
		const b = rgba.b;

		return Math.round(0.299 * r + 0.587 * g + 0.114 * b);
	}

	/**
	 * 判断颜色是否为深色
	 */
	public isDarkColor(color : string) : boolean {
		return this.getBrightness(color) < 128;
	}

	/**
	 * 获取预定义颜色列表
	 */
	public getPredefinedColors() : Map<string, string> {
		return predefinedColors;
	}
	/**
	 * 颜色反转（取反色）
	 * @param color 颜色值
	 * @returns 反转后的颜色字符串
	 */
	public invertColor(color : string) : string {
		const rgba = this.parseColorString(color);
		const invertedR = 255 - rgba.r;
		const invertedG = 255 - rgba.g;
		const invertedB = 255 - rgba.b;

		return this.createRgbaString(invertedR, invertedG, invertedB, rgba.a);
	}

	/**
	 * 获取禁用状态的颜色（通常是灰色调）
	 * @param color 原始颜色
	 * @param opacity 透明度，默认0.4
	 * @returns 禁用状态颜色字符串
	 */
	public getDisabledColor(color : string, opacity : number = 0.4) : string {
		const rgba = this.parseColorString(color);
		// 转换为灰度值
		const gray = Math.round(0.299 * rgba.r + 0.587 * rgba.g + 0.114 * rgba.b);
		return this.createRgbaString(gray, gray, gray, opacity);
	}
	/**
	 * 深色变浅色，浅色变深色
	 * @param color 颜色值
	 * @param lightAmount 变浅程度，默认80
	 * @param darkAmount 变深程度，默认80
	 * @returns 反转后的颜色字符串
	 */
	public invertColors(color : string, lightAmount : number = 100, darkAmount : number = 100) : string {
		if (this.isDarkColor(color)) {
			return this.lightenColor(color, lightAmount);
		} else {
			return this.darkenColor(color, darkAmount);
		}
	}
	/**
	 * 获取深色版本的颜色
	 * @param color 颜色值
	 * @param amount 变深程度，默认40
	 * @returns 深色版本的颜色字符串
	 */
	public getDarkColor(color : string, amount : number = 40) : string {
		return this.darkenColor(color, amount);
	}
	/**
	 * 获取浅色版本的颜色
	 * @param color 颜色值
	 * @param amount 变浅程度，默认40
	 * @returns 浅色版本的颜色字符串
	 */
	public getLightColor(color : string, amount : number = 40) : string {
		return this.lightenColor(color, amount);
	}

	/**
	 * 根据背景色自动选择合适的文字颜色（黑色或白色）
	 * @param backgroundColor 背景颜色
	 * @param lightTextColor 浅色文字颜色，默认白色
	 * @param darkTextColor 深色文字颜色，默认黑色
	 * @returns 合适的文字颜色
	 */
	public getContrastTextColor(
		backgroundColor : string,
		lightTextColor : string = "#FFFFFF",
		darkTextColor : string = "#000000"
	) : string {
		if (this.isDarkColor(backgroundColor)) {
			return lightTextColor;
		} else {
			return darkTextColor;
		}
	}
	/**
	 * 将颜色变浅指定的量
	 * @param color 颜色值
	 * @param amount 变浅程度，范围1-100，100为纯白色
	 * @returns 变浅后的颜色字符串
	 */
	public lightenColor(color : string, amount : number = 10) : string {
		const rgba = this.parseColorString(color);
		const hsl = this.rgbToHsl(rgba.r, rgba.g, rgba.b);

		// 增加亮度
		hsl.l = Math.min(1, hsl.l + amount / 100);

		const rgb = this.hslToRgb(hsl.h, hsl.s, hsl.l);
		return this.createRgbaString(rgb.r, rgb.g, rgb.b, rgba.a);
	}

	/**
	 * 将颜色变暗指定的量
	 * @param color 颜色值  
	 * @param amount 变暗程度，范围1-100，100为纯黑色
	 * @returns 变暗后的颜色字符串
	 */
	public darkenColor(color : string, amount : number = 10) : string {
		const rgba = this.parseColorString(color);
		const hsl = this.rgbToHsl(rgba.r, rgba.g, rgba.b);

		// 减少亮度
		hsl.l = Math.max(0, hsl.l - amount / 100);

		const rgb = this.hslToRgb(hsl.h, hsl.s, hsl.l);
		return this.createRgbaString(rgb.r, rgb.g, rgb.b, rgba.a);
	}

	/**
	 * 将颜色变亮指定的量（直接调整RGB值）
	 * @param color 颜色值
	 * @param amount 变亮程度，范围1-100
	 * @returns 变亮后的颜色字符串
	 */
	public brightenColor(color : string, amount : number = 10) : string {
		const rgba = this.parseColorString(color);
		const adjustment = Math.round(255 * (amount / 100));

		const r = Math.max(0, Math.min(255, rgba.r + adjustment));
		const g = Math.max(0, Math.min(255, rgba.g + adjustment));
		const b = Math.max(0, Math.min(255, rgba.b + adjustment));

		return this.createRgbaString(r, g, b, rgba.a);
	}

	/**
	 * 调整颜色饱和度
	 * @param color 颜色值
	 * @param amount 饱和度调整量，正数增加饱和度，负数减少饱和度
	 * @returns 调整后的颜色字符串
	 */
	public saturateColor(color : string, amount : number = 10) : string {
		const rgba = this.parseColorString(color);
		const hsl = this.rgbToHsl(rgba.r, rgba.g, rgba.b);

		// 调整饱和度
		hsl.s = Math.max(0, Math.min(1, hsl.s + amount / 100));

		const rgb = this.hslToRgb(hsl.h, hsl.s, hsl.l);
		return this.createRgbaString(rgb.r, rgb.g, rgb.b, rgba.a);
	}

	/**
	 * 获取颜色的互补色
	 * @param color 颜色值
	 * @returns 互补色字符串
	 */
	public getComplementaryColor(color : string) : string {
		const rgba = this.parseColorString(color);
		const hsl = this.rgbToHsl(rgba.r, rgba.g, rgba.b);

		const complementaryH = (hsl.h + 0.5) % 1;

		const rgb = this.hslToRgb(complementaryH, hsl.s, hsl.l);
		return this.createRgbaString(rgb.r, rgb.g, rgb.b, rgba.a);
	}

	/**
	 * 混合两种颜色
	 * @param color1 第一种颜色
	 * @param color2 第二种颜色
	 * @param ratio 混合比例，0-1之间，0完全是color1，1完全是color2
	 * @returns 混合后的颜色字符串
	 */
	public mixColors(color1 : string, color2 : string, ratio : number = 0.5) : string {
		const rgba1 = this.parseColorString(color1);
		const rgba2 = this.parseColorString(color2);

		const clampedRatio = Math.max(0, Math.min(1, ratio));

		const r = Math.round(rgba1.r * (1 - clampedRatio) + rgba2.r * clampedRatio);
		const g = Math.round(rgba1.g * (1 - clampedRatio) + rgba2.g * clampedRatio);
		const b = Math.round(rgba1.b * (1 - clampedRatio) + rgba2.b * clampedRatio);
		const a = rgba1.a * (1 - clampedRatio) + rgba2.a * clampedRatio;

		return this.createRgbaString(r, g, b, a);
	}

	// 辅助函数：RGB转HSL
	private rgbToHsl(r : number, g : number, b : number) : HSL {
		// 创建新的变量来存储计算值
		const rNorm = r / 255;
		const gNorm = g / 255;
		const bNorm = b / 255;

		const max = Math.max(rNorm, gNorm, bNorm);
		const min = Math.min(rNorm, gNorm, bNorm);

		// 先声明变量
		let h : number;
		let s : number;
		const l = (max + min) / 2;

		if (max == min) {
			h = 0;
			s = 0; // 无色相
		} else {
			const d = max - min;
			s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

			switch (max) {
				case rNorm:
					h = (gNorm - bNorm) / d + (gNorm < bNorm ? 6 : 0);
					break;
				case gNorm:
					h = (bNorm - rNorm) / d + 2;
					break;
				case bNorm:
					h = (rNorm - gNorm) / d + 4;
					break;
				default:
					h = 0;
			}
			h /= 6;
		}

		return { h, s, l };
	}

	// 辅助函数：HSL转RGB
	private hslToRgb(h : number, s : number, l : number) : RGB {
		let r : number;
		let g : number;
		let b : number;

		if (s == 0) {
			r = l; // 无饱和度
			g = l;
			b = l;
		} else {
			const hue2rgb = (p : number, q : number, t : number) : number => {
				let tNorm = t;
				if (tNorm < 0) tNorm += 1;
				if (tNorm > 1) tNorm -= 1;
				if (tNorm < 1 / 6) return p + (q - p) * 6 * tNorm;
				if (tNorm < 1 / 2) return q;
				if (tNorm < 2 / 3) return p + (q - p) * (2 / 3 - tNorm) * 6;
				return p;
			};

			const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
			const p = 2 * l - q;

			r = hue2rgb(p, q, h + 1 / 3);
			g = hue2rgb(p, q, h);
			b = hue2rgb(p, q, h - 1 / 3);
		}

		return {
			r: Math.round(r * 255),
			g: Math.round(g * 255),
			b: Math.round(b * 255)
		};
	}
}