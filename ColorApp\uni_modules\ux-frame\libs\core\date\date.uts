import type { UxDateUnit, UxDateInput, UxDateConfig } from '../../types/date'

/**
 * 全新的时间处理类
 */
export class UxDate {
	private _timestamp : number
	private _config : UxDateConfig

	constructor(input : UxDateInput = '', config : UxDateConfig = {
		locale: 'zh-CN',
		weekStart: 1
	}) {
		this._config = config;
		this._timestamp = this.parseToTimestamp(input)
	}

	/**
	 * 解析输入为时间戳
	 */
	private parseToTimestamp(input ?: UxDateInput) : number {
		// 空值处理
		if (input == null || input == '' || input == 0) {
			return Date.now()
		}

		// 已经是Time实例
		if (input instanceof UxDate) {
			return input._timestamp
		}

		// Date对象
		if (input instanceof Date) {
			return input.getTime()
		}

		// 数字时间戳
		if (typeof input == 'number') {
			// 10位秒级时间戳转毫秒
			return input.toString().length == 10 ? input * 1000 : input
		}

		// 字符串解析
		if (typeof input == 'string') {
			return this.parseStringToTimestamp(input.trim())
		}

		return Date.now()
	}

	/**
	 * 解析字符串为时间戳
	 */
	private parseStringToTimestamp(str : string) : number {
		if (str == '') return Date.now()

		// 移除中文描述
		const cleanStr = str.replace(/[周星期礼拜][一二三四五六七日天]/g, '').trim()
		if (cleanStr == '') return Date.now()

		// 纯数字时间戳
		if (/^\d{10,13}$/.test(cleanStr)) {
			const num = parseInt(cleanStr)
			return cleanStr.length == 10 ? num * 1000 : num
		}

		// 标准化字符串
		let normalized = cleanStr
			.replace(/[年]/g, '-')
			.replace(/[月]/g, '-')
			.replace(/[日号]/g, '')
			.replace(/[时点]/g, ':')
			.replace(/[分]/g, ':')
			.replace(/[秒]/g, '')
			.replace(/\//g, '-')
			.replace(/\\/g, '-')
			.replace(/\s+/g, ' ')
			.trim()

		// 智能补全
		normalized = this.smartComplete(normalized)

		const date = new Date(normalized)
		return isNaN(date.getTime()) ? Date.now() : date.getTime()
	}

	/**
	 * 智能补全日期字符串
	 */
	private smartComplete(str : string) : string {
		if (str == '') return new Date().toISOString()

		const now = new Date()
		const parts = str.split(' ')
		let datePart = parts.length > 0 ? parts[0] : ''
		let timePart = parts.length > 1 ? parts[1] : '00:00:00'

		const dateSegs = datePart.split('-').filter(s => s !== '')
		if (dateSegs.length == 0) {
			datePart = now.toISOString().split('T')[0]
		} else if (dateSegs.length == 1) {
			datePart = `${dateSegs[0]}-01-01`
		} else if (dateSegs.length == 2) {
			datePart = `${dateSegs[0]}-${dateSegs[1].padStart(2, '0')}-01`
		} else {
			datePart = `${dateSegs[0]}-${dateSegs[1].padStart(2, '0')}-${dateSegs[2].padStart(2, '0')}`
		}

		// 补全时间
		const timeSegs = timePart.split(':').filter(s => s !== '')
		if (timeSegs.length == 1) {
			timePart = `${timeSegs[0].padStart(2, '0')}:00:00`
		} else if (timeSegs.length == 2) {
			timePart = `${timeSegs[0].padStart(2, '0')}:${timeSegs[1].padStart(2, '0')}:00`
		} else if (timeSegs.length >= 3) {
			timePart = `${timeSegs[0].padStart(2, '0')}:${timeSegs[1].padStart(2, '0')}:${timeSegs[2].padStart(2, '0')}`
		}

		return `${datePart}T${timePart}`
	}


	/**
	 * 创建当前时间
	 */
	create() : UxDate {
		return new UxDate()
	}

	/**
	 * 从输入创建时间
	 */
	from(input ?: UxDateInput) : UxDate {
		return new UxDate(input);
	}

	/**
	 * 创建今天开始时间
	 */
	today() : UxDate {
		return new UxDate().startOfDay()
	}

	/**
	 * 创建明天开始时间
	 */
	tomorrow() : UxDate {
		return this.today().plusDays(1)
	}

	/**
	 * 创建昨天开始时间
	 */
	yesterday() : UxDate {
		return this.today().minusDays(1)
	}

	/**
	 * 创建本周开始时间
	 */
	thisWeek() : UxDate {
		return this.startOfWeek()
	}

	/**
	 * 创建本月开始时间
	 */
	thisMonth() : UxDate {
		return this.startOfMonth()
	}

	/**
	 * 创建本年开始时间
	 */
	thisYear() : UxDate {
		return new UxDate().startOfYear()
	}

	/**
	 * 获取年份
	 */
	getYear() : number {
		return new Date(this._timestamp).getFullYear()
	}

	/**
	 * 获取月份 (1-12)
	 */
	getMonth() : number {
		return new Date(this._timestamp).getMonth() + 1
	}

	/**
	 * 获取日期 (1-31)
	 */
	getDay() : number {
		return new Date(this._timestamp).getDate()
	}

	/**
	 * 获取星期几 (1-7, 1=周一)
	 */
	getWeekday() : number {
		const day = new Date(this._timestamp).getDay()
		return day == 0 ? 7 : day
	}

	/**
	 * 获取小时 (0-23)
	 */
	getHour() : number {
		return new Date(this._timestamp).getHours()
	}

	/**
	 * 获取分钟 (0-59)
	 */
	getMinute() : number {
		return new Date(this._timestamp).getMinutes()
	}

	/**
	 * 获取秒数 (0-59)
	 */
	getSecond() : number {
		return new Date(this._timestamp).getSeconds()
	}

	/**
	 * 获取毫秒 (0-999)
	 */
	getMillisecond() : number {
		return new Date(this._timestamp).getMilliseconds()
	}

	/**
	 * 获取时间戳 (毫秒)
	 */
	getTimestamp() : number {
		return this._timestamp
	}

	/**
	 * 获取Unix时间戳 (秒)
	 */
	getUnixTimestamp() : number {
		return Math.floor(this._timestamp / 1000)
	}

	/**
	 * 获取本月天数
	 */
	getDaysInMonth() : number {
		const date = new Date(this._timestamp)
		return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate()
	}

	/**
	 * 获取本年天数
	 */
	getDaysInYear() : number {
		return this.isLeapYear() ? 366 : 365
	}

	/**
	 * 获取季度 (1-4)
	 */
	getQuarter() : number {
		return Math.floor((this.getMonth() - 1) / 3) + 1
	}


	/**
	 * 设置年份
	 */
	setYear(year : number) : UxDate {
		if (year == 0) return this.copy()
		const date = new Date(this._timestamp)
		date.setFullYear(year)
		return new UxDate(date.getTime(), this._config)
	}

	/**
	 * 设置月份 (1-12)
	 */
	setMonth(month : number) : UxDate {
		if (month == 0) return this.copy()
		const date = new Date(this._timestamp)
		date.setMonth(month - 1)
		return new UxDate(date.getTime(), this._config)
	}

	/**
	 * 设置日期 (1-31)
	 */
	setDay(day : number) : UxDate {
		if (day == 0) return this.copy()
		const date = new Date(this._timestamp)
		date.setDate(day)
		return new UxDate(date.getTime(), this._config)
	}

	/**
	 * 设置小时 (0-23)
	 */
	setHour(hour : number) : UxDate {
		const date = new Date(this._timestamp)
		date.setHours(hour)
		return new UxDate(date.getTime(), this._config)
	}

	/**
	 * 设置分钟 (0-59)
	 */
	setMinute(minute : number) : UxDate {
		const date = new Date(this._timestamp)
		date.setMinutes(minute)
		return new UxDate(date.getTime(), this._config)
	}

	/**
	 * 设置秒数 (0-59)
	 */
	setSecond(second : number) : UxDate {
		const date = new Date(this._timestamp)
		date.setSeconds(second)
		return new UxDate(date.getTime(), this._config)
	}

	/**
	 * 设置毫秒 (0-999)
	 */
	setMillisecond(millisecond : number) : UxDate {
		const date = new Date(this._timestamp)
		date.setMilliseconds(millisecond)
		return new UxDate(date.getTime(), this._config)
	}


	/**
	 * 增加年份
	 */
	plusYears(years : number) : UxDate {
		if (years == 0) return this.copy()
		return this.setYear(this.getYear() + years)
	}

	/**
	 * 减少年份
	 */
	minusYears(years : number) : UxDate {
		return this.plusYears(-years)
	}

	/**
	 * 增加月份
	 */
	plusMonths(months : number) : UxDate {
		if (months == 0) return this.copy()
		const date = new Date(this._timestamp)
		date.setMonth(date.getMonth() + months)
		return new UxDate(date.getTime(), this._config)
	}

	/**
	 * 减少月份
	 */
	minusMonths(months : number) : UxDate {
		return this.plusMonths(-months)
	}

	/**
	 * 增加天数
	 */
	plusDays(days : number) : UxDate {
		if (days == 0) return this.copy()
		return new UxDate(this._timestamp + days * 24 * 60 * 60 * 1000, this._config)
	}

	/**
	 * 减少天数
	 */
	minusDays(days : number) : UxDate {
		return this.plusDays(-days)
	}

	/**
	 * 增加周数
	 */
	plusWeeks(weeks : number) : UxDate {
		return this.plusDays(weeks * 7)
	}

	/**
	 * 减少周数
	 */
	minusWeeks(weeks : number) : UxDate {
		return this.plusWeeks(-weeks)
	}

	/**
	 * 增加小时
	 */
	plusHours(hours : number) : UxDate {
		if (hours == 0) return this.copy()
		return new UxDate(this._timestamp + hours * 60 * 60 * 1000, this._config)
	}

	/**
	 * 减少小时
	 */
	minusHours(hours : number) : UxDate {
		return this.plusHours(-hours)
	}

	/**
	 * 增加分钟
	 */
	plusMinutes(minutes : number) : UxDate {
		if (minutes == 0) return this.copy()
		return new UxDate(this._timestamp + minutes * 60 * 1000, this._config)
	}

	/**
	 * 减少分钟
	 */
	minusMinutes(minutes : number) : UxDate {
		return this.plusMinutes(-minutes)
	}

	/**
	 * 增加秒数
	 */
	plusSeconds(seconds : number) : UxDate {
		if (seconds == 0) return this.copy()
		return new UxDate(this._timestamp + seconds * 1000, this._config)
	}

	/**
	 * 减少秒数
	 */
	minusSeconds(seconds : number) : UxDate {
		return this.plusSeconds(-seconds)
	}

	/**
	 * 增加毫秒
	 */
	plusMilliseconds(milliseconds : number) : UxDate {
		if (milliseconds == 0) return this.copy()
		return new UxDate(this._timestamp + milliseconds, this._config)
	}

	/**
	 * 减少毫秒
	 */
	minusMilliseconds(milliseconds : number) : UxDate {
		return this.plusMilliseconds(-milliseconds)
	}

	/**
	 * 年初
	 */
	startOfYear() : UxDate {
		const date = new Date(this._timestamp)
		date.setMonth(0)
		date.setDate(1)
		date.setHours(0)
		date.setMinutes(0)
		date.setSeconds(0)
		date.setMilliseconds(0)
		return new UxDate(date.getTime(), this._config)
	}

	/**
	 * 年末
	 */
	endOfYear() : UxDate {
		const date = new Date(this._timestamp)
		date.setMonth(11)
		date.setDate(31)
		date.setHours(23)
		date.setMinutes(59)
		date.setSeconds(59)
		date.setMilliseconds(999)
		return new UxDate(date.getTime(), this._config)
	}

	/**
	 * 月初
	 */
	startOfMonth() : UxDate {
		const date = new Date(this._timestamp)
		date.setDate(1)
		date.setHours(0)
		date.setMinutes(0)
		date.setSeconds(0)
		date.setMilliseconds(0)
		return new UxDate(date.getTime(), this._config)
	}

	/**
	 * 月末
	 */
	endOfMonth() : UxDate {
		const date = new Date(this._timestamp)
		date.setMonth(date.getMonth() + 1)
		date.setDate(0)
		date.setHours(23)
		date.setMinutes(59)
		date.setSeconds(59)
		date.setMilliseconds(999)
		return new UxDate(date.getTime(), this._config)
	}

	/**
	 * 周初
	 */
	startOfWeek() : UxDate {
		const date = new Date(this._timestamp)
		const day = date.getDay()
		const diff = day == 0 ? 6 : day - 1
		date.setDate(date.getDate() - diff)
		date.setHours(0)
		date.setMinutes(0)
		date.setSeconds(0)
		date.setMilliseconds(0)
		return new UxDate(date.getTime(), this._config)
	}

	/**
	 * 周末
	 */
	endOfWeek() : UxDate {
		const date = new Date(this._timestamp)
		const day = date.getDay()
		const diff = day == 0 ? 0 : 7 - day
		date.setDate(date.getDate() + diff)
		date.setHours(23)
		date.setMinutes(59)
		date.setSeconds(59)
		date.setMilliseconds(999)
		return new UxDate(date.getTime(), this._config)
	}

	/**
	 * 日初
	 */
	startOfDay() : UxDate {
		const date = new Date(this._timestamp)
		date.setHours(0)
		date.setMinutes(0)
		date.setSeconds(0)
		date.setMilliseconds(0)
		return new UxDate(date.getTime(), this._config)
	}

	/**
	 * 日末
	 */
	endOfDay() : UxDate {
		const date = new Date(this._timestamp)
		date.setHours(23)
		date.setMinutes(59)
		date.setSeconds(59)
		date.setMilliseconds(999)
		return new UxDate(date.getTime(), this._config)
	}

	/**
	 * 小时初
	 */
	startOfHour() : UxDate {
		const date = new Date(this._timestamp)
		date.setMinutes(0)
		date.setSeconds(0)
		date.setMilliseconds(0)
		return new UxDate(date.getTime(), this._config)
	}

	/**
	 * 小时末
	 */
	endOfHour() : UxDate {
		const date = new Date(this._timestamp)
		date.setMinutes(59)
		date.setSeconds(59)
		date.setMilliseconds(999)
		return new UxDate(date.getTime(), this._config)
	}

	/**
	 * 分钟初
	 */
	startOfMinute() : UxDate {
		const date = new Date(this._timestamp)
		date.setSeconds(0)
		date.setMilliseconds(0)
		return new UxDate(date.getTime(), this._config)
	}

	/**
	 * 分钟末
	 */
	endOfMinute() : UxDate {
		const date = new Date(this._timestamp)
		date.setSeconds(59)
		date.setMilliseconds(999)
		return new UxDate(date.getTime(), this._config)
	}

	/**
	 * 是否在指定时间之前
	 */
	isBefore(other : UxDateInput) : boolean {
		if (other == null) return false
		const otherTime = new UxDate(other)
		return this._timestamp < otherTime._timestamp
	}

	/**
	 * 是否在指定时间之后
	 */
	isAfter(other : UxDateInput) : boolean {
		if (other == null) return false
		const otherTime = new UxDate(other)
		return this._timestamp > otherTime._timestamp
	}

	/**
	 * 是否等于指定时间
	 */
	isEqual(other : UxDateInput) : boolean {
		if (other == null) return false
		const otherTime = new UxDate(other)
		return this._timestamp === otherTime._timestamp
	}

	/**
	 * 是否在指定时间之前或等于
	 */
	isBeforeOrEqual(other : UxDateInput) : boolean {
		return this.isBefore(other) || this.isEqual(other)
	}

	/**
	 * 是否在指定时间之后或等于
	 */
	isAfterOrEqual(other : UxDateInput) : boolean {
		return this.isAfter(other) || this.isEqual(other)
	}

	/**
	 * 是否在两个时间之间
	 */
	isBetween(start : UxDateInput, end : UxDateInput) : boolean {
		if (start == null || end == null) return false
		return this.isAfterOrEqual(start) && this.isBeforeOrEqual(end)
	}

	/**
	 * 是否是同一天
	 */
	isSameDay(other : UxDateInput) : boolean {
		if (other == null) return false
		const otherTime = new UxDate(other)
		return this.toDateString() === otherTime.toDateString()
	}

	/**
	 * 是否是同一周
	 */
	isSameWeek(other : UxDateInput) : boolean {
		if (other == null) return false
		const otherTime = new UxDate(other)
		return this.startOfWeek().toDateString() === otherTime.startOfWeek().toDateString()
	}

	/**
	 * 是否是同一月
	 */
	isSameMonth(other : UxDateInput) : boolean {
		if (other == null) return false
		const otherTime = new UxDate(other)
		return this.getYear() === otherTime.getYear() && this.getMonth() === otherTime.getMonth()
	}

	/**
	 * 是否是同一年
	 */
	isSameYear(other : UxDateInput) : boolean {
		if (other == null) return false
		const otherTime = new UxDate(other)
		return this.getYear() === otherTime.getYear()
	}

	/**
	 * 是否是今天
	 */
	isToday() : boolean {
		return this.isSameDay(this.create())
	}

	/**
	 * 是否是昨天
	 */
	isYesterday() : boolean {
		return this.isSameDay(this.yesterday())
	}

	/**
	 * 是否是明天
	 */
	isTomorrow() : boolean {
		return this.isSameDay(this.tomorrow())
	}

	/**
	 * 是否是本周
	 */
	isThisWeek() : boolean {
		return this.isSameWeek(this.create())
	}

	/**
	 * 是否是本月
	 */
	isThisMonth() : boolean {
		return this.isSameMonth(this.create())
	}

	/**
	 * 是否是本年
	 */
	isThisYear() : boolean {
		return this.isSameYear(this.create())
	}

	/**
	 * 是否是闰年
	 */
	isLeapYear() : boolean {
		const year = this.getYear()
		return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)
	}

	/**
	 * 是否是周末
	 */
	isWeekend() : boolean {
		const day = this.getWeekday()
		return day == 6 || day == 7  // 周六或周日
	}

	/**
	 * 是否是工作日
	 */
	isWeekday() : boolean {
		return !this.isWeekend()
	}

	/**
	 * 计算与另一时间的差值 (毫秒)
	 */
	diffInMilliseconds(other : UxDateInput) : number {
		if (other == null) return 0
		const otherTime = new UxDate(other)
		return this._timestamp - otherTime._timestamp
	}

	/**
	 * 计算与另一时间的差值 (秒)
	 */
	diffInSeconds(other : UxDateInput) : number {
		return Math.floor(this.diffInMilliseconds(other) / 1000)
	}

	/**
	 * 计算与另一时间的差值 (分钟)
	 */
	diffInMinutes(other : UxDateInput) : number {
		return Math.floor(this.diffInSeconds(other) / 60)
	}

	/**
	 * 计算与另一时间的差值 (小时)
	 */
	diffInHours(other : UxDateInput) : number {
		return Math.floor(this.diffInMinutes(other) / 60)
	}

	/**
	 * 计算与另一时间的差值 (天)
	 */
	diffInDays(other : UxDateInput) : number {
		return Math.floor(this.diffInHours(other) / 24)
	}

	/**
	 * 计算与另一时间的差值 (周)
	 */
	diffInWeeks(other : UxDateInput) : number {
		return Math.floor(this.diffInDays(other) / 7)
	}

	/**
	 * 计算与另一时间的差值 (月)
	 */
	diffInMonths(other : UxDateInput) : number {
		if (other == null) return 0
		const otherTime = new UxDate(other)
		const yearDiff = this.getYear() - otherTime.getYear()
		const monthDiff = this.getMonth() - otherTime.getMonth()
		return yearDiff * 12 + monthDiff
	}

	/**
	 * 计算与另一时间的差值 (年)
	 */
	diffInYears(other : UxDateInput) : number {
		return Math.floor(this.diffInMonths(other) / 12)
	}

	/**
	 * 格式化为字符串
	 * 支持: yyyy年 MM月 dd日 HH时 mm分 ss秒 SSS毫秒 Q季度 W周 d星期
	 */
	format(pattern ?: string | null) : string {
		if (pattern == null || pattern === '') {
			pattern = 'yyyy-MM-dd HH:mm:ss'
		}

		const date = new Date(this._timestamp)
		const year = date.getFullYear()
		const month = date.getMonth() + 1
		const day = date.getDate()
		const hour = date.getHours()
		const minute = date.getMinutes()
		const second = date.getSeconds()
		const millisecond = date.getMilliseconds()
		const quarter = this.getQuarter()
		const weekday = this.getWeekday()

		// 创建替换映射
		const replacements : UTSJSONObject = {
			'yyyy': year.toString(),
			'yy': year.toString().slice(-2),
			'MM': month.toString().padStart(2, '0'),
			'M': month.toString(),
			'dd': day.toString().padStart(2, '0'),
			'd': day.toString(),
			'HH': hour.toString().padStart(2, '0'),
			'H': hour.toString(),
			'hh': (hour % 12).toString().padStart(2, '0'),
			'h': (hour % 12).toString(),
			'mm': minute.toString().padStart(2, '0'),
			'm': minute.toString(),
			'ss': second.toString().padStart(2, '0'),
			's': second.toString(),
			'SSS': millisecond.toString().padStart(3, '0'),
			'Q': quarter.toString(),
			'W': this.getWeekOfYear().toString(),
			'wd': weekday.toString(),
			'A': hour >= 12 ? 'PM' : 'AM',
			'a': hour >= 12 ? 'pm' : 'am'
		}

		let result = `${pattern}`

		const keys = UTSJSONObject.keys(replacements)
		for (let i = 0; i < keys.length; i++) {
			const key = keys[i]
			const value = replacements.getString(key, '')
			if (value !== '' && result != '') {
				result = result.replace(new RegExp(key, 'g'), value)
			}
		}

		return result
	}
	
	/**
	 * 格式化指定时间
	 * @param input 要格式化的时间输入
	 * @param pattern 格式化模式，默认 'yyyy-MM-dd'
	 */
	formatTime(input : UxDateInput, pattern ?: string) : string {
		try {
			const time = new UxDate(input)
			return time.format(pattern ?? 'yyyy-MM-dd');
		} catch (error) {
			return ''
		}
	}
	
	/**
	 * 格式化为日期字符串
	 * @param pattern 格式化模式，默认 'yyyy-MM-dd'
	 */
	toDateString(pattern : string = 'yyyy-MM-dd') : string {
		return this.format(pattern)
	}

	/**
	 * 格式化为时间字符串 HH:mm:ss
	 */
	toTimeString() : string {
		return this.format('HH:mm:ss')
	}

	/**
	 * 格式化为日期时间字符串 yyyy-MM-dd HH:mm:ss
	 */
	toDateTimeString() : string {
		return this.format('yyyy-MM-dd HH:mm:ss')
	}

	/**
	 * 格式化为ISO字符串
	 */
	toISOString() : string {
		return new Date(this._timestamp).toISOString()
	}

	/**
	 * 转换为原生Date对象
	 */
	toDate() : Date {
		return new Date(this._timestamp)
	}

	/**
	 * 转换为JSON字符串
	 */
	toJSON() : string {
		return this.toISOString()
	}

	/**
	 * 获取中文星期名
	 */
	getWeekdayName() : string {
		const names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
		return names[this.getWeekday() - 1]
	}

	/**
	 * 获取中文月份名
	 */
	getMonthName() : string {
		const names = ['一月', '二月', '三月', '四月', '五月', '六月',
			'七月', '八月', '九月', '十月', '十一月', '十二月']
		return names[this.getMonth() - 1]
	}

	/**
	 * 获取一年中的第几周
	 */
	getWeekOfYear() : number {
		const start = this.startOfYear()
		const diff = this.diffInDays(start)
		return Math.floor(diff / 7) + 1
	}

	/**
	 * 获取一年中的第几天
	 */
	getDayOfYear() : number {
		const start = this.startOfYear()
		return this.diffInDays(start) + 1
	}

	/**
	 * 相对时间描述
	 */
	fromNow() : string {
		const diff = this.create().diffInSeconds(this)

		if (Math.abs(diff) < 60) {
			return diff > 0 ? '刚刚' : '即将'
		}

		const absDiff = Math.abs(diff)
		const suffix = diff > 0 ? '前' : '后'

		if (absDiff < 3600) {
			return `${Math.floor(absDiff / 60)}分钟${suffix}`
		}

		if (absDiff < 86400) {
			return `${Math.floor(absDiff / 3600)}小时${suffix}`
		}

		if (absDiff < 2592000) {
			return `${Math.floor(absDiff / 86400)}天${suffix}`
		}

		if (absDiff < 31536000) {
			return `${Math.floor(absDiff / 2592000)}个月${suffix}`
		}

		return `${Math.floor(absDiff / 31536000)}年${suffix}`
	}

	/**
	 * 复制当前实例
	 */
	copy() : UxDate {
		return new UxDate(this._timestamp, this._config)
	}

	/**
	 * 克隆当前实例
	 */
	clone() : UxDate {
		return this.copy()
	}

	/**
	 * 是否有效时间
	 */
	isValid() : boolean {
		return !isNaN(this._timestamp) && this._timestamp > 0
	}

	/**
	 * 获取本周所有日期
	 */
	getWeekDays() : UxDate[] {
		const start = this.startOfWeek()
		const days : UxDate[] = []
		for (let i = 0; i < 7; i++) {
			days.push(start.plusDays(i))
		}
		return days
	}

	/**
	 * 获取本月所有日期
	 */
	getMonthDays() : UxDate[] {
		const start = this.startOfMonth()
		const end = this.endOfMonth()
		const days : UxDate[] = []
		let current = start

		while (current.isBeforeOrEqual(end)) {
			days.push(current.copy())
			current = current.plusDays(1)
		}

		return days
	}

	/**
	 * 比较两个时间
	 */
	compare(other : UxDateInput) : number {
		if (other == null) return 1
		const otherTime = new UxDate(other)
		return this._timestamp - otherTime._timestamp
	}

	/**
	 * 最小时间
	 */
	static min(...times : UxDateInput[]) : UxDate {
		if (times.length == 0) return new UxDate()

		let minTime = new UxDate(times[0])
		for (let i = 1; i < times.length; i++) {
			const time = new UxDate(times[i])
			if (time.isBefore(minTime)) {
				minTime = time
			}
		}
		return minTime
	}

	/**
	 * 最大时间
	 */
	static max(...times : UxDateInput[]) : UxDate {
		if (times.length == 0) return new UxDate()

		let maxTime = new UxDate(times[0])
		for (let i = 1; i < times.length; i++) {
			const time = new UxDate(times[i])
			if (time.isAfter(maxTime)) {
				maxTime = time
			}
		}
		return maxTime
	}
}