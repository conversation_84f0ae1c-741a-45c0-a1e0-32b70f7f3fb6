import { UxOpenAppAuthorizeSettingOptions, OpenAppAuthorizeSetting, UxRequestPermissionTipsOptions, SetRequestPermissionTips, RequestPermissions, UxRequestPermissionsOptions } from '../interface.uts';

/**
 * 设置请求权限的提示
 * @param UxRequestPermissionTipsOptions[] options 参数
 */
export const setRequestPermissionTips : SetRequestPermissionTips = function (options : UxRequestPermissionTipsOptions[]) : void {
	
}

/**
 * 请求权限
 * @param UxRequestPermissionsOptions options 参数
 */
export const requestPermissions : RequestPermissions = function (options : UxRequestPermissionsOptions) : void {
	
}

/**
 * 打开app权限设置
 * @param UxOpenAppAuthorizeSettingOptions options 参数
 */
export const openAppAuthorizeSetting : OpenAppAuthorizeSetting = function (options : UxOpenAppAuthorizeSettingOptions) : void {
	
}