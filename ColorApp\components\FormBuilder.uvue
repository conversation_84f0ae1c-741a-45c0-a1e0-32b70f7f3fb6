<template>
	<!-- 表单 -->
	<ux-form ref="formRef" :model-value="dataModel" :rules="formRules">
		<!-- 表单顶部内容 -->
		<slot name="top_form_item"></slot>
		<!-- 表单分组 -->
		<FormGroup
			v-for="(group, index) in fieldGroups"
			:key="index"
			:group="group"
			:modelValue="dataModel"
			:rules="formRules"
			:title-class="titleClass"
			:cols="cols"
			@change="handleChange"
			@field-change="handleFieldChange"
		/>
		<!-- 表单底部内容 -->
		<slot></slot>
	</ux-form>
</template>

<script setup lang="uts">
	import { ref, computed, watch, nextTick } from "vue"
	import FormGroup from './FormGroup.uvue'
	
	// 定义组件选项
	defineOptions({
		name: 'FormBuilder'
	})
	
	// 定义事件
	const emit = defineEmits<{
		change: [data: UTSJSONObject]
		fieldChange: [data: UTSJSONObject]
	}>()
	
	// 定义props - 严格按照Vue版本
	const props = defineProps({
		// 表单绑定数据
		modelValue: {
			type: Object as PropType<UTSJSONObject>,
			default: (): UTSJSONObject => ({} as UTSJSONObject)
		},
		// 表单字段配置
		fields: {
			type: Array as PropType<UTSJSONObject[]>,
			default: (): UTSJSONObject[] => []
		},
		// 表单项标题宽度，按24网格计算，24表示100%宽度
		labelCell: {
			type: Number,
			default: 24
		},
		// 表单字段配置
		rules: {
			type: Object as PropType<UTSJSONObject>,
			default: (): UTSJSONObject => {
				return { required: [] } as UTSJSONObject
			}
		},
		// 表单标题样式
		titleClass: {
			type: String,
			default: "h-form-item"
		},
		// 表单分栏数量，1表示单栏，2表示双栏，3表示三栏
		cols: {
			type: Number,
			default: 3
		},
		// 是否启用分组折叠功能
		fold: {
			type: Boolean,
			default: false
		}
	})
	
	// 响应式数据
	const formRef = ref(null)
	const dataModel = ref<UTSJSONObject>({} as UTSJSONObject)
	const formRules = ref<UTSJSONObject>(props.rules)
	const currFields = ref<UTSJSONObject[]>(props.fields)
	
	// 计算属性 - 按分组整理字段
	const fieldGroups = computed((): UTSJSONObject[] => {
		let groups: UTSJSONObject[] = []
		let items: UTSJSONObject[] = []
		let lastGroup: string | null = null
		let lastGroupClass: string | null = null
		
		for (let i = 0; i < props.fields.length; i++) {
			const item = props.fields[i]
			const group = (item['Group'] as string) || ""
			if (lastGroup != group) {
				if (lastGroup != null) {
					groups.push({
						// 分组标题
						Title: lastGroup,
						// 分组样式
						Class: lastGroupClass,
						// 该分组下的字段列表
						Fields: items,
						// 所有分组都显示，不再根据fold属性控制
						show: true
					} as UTSJSONObject)
				}
				items = []
				lastGroup = group
				lastGroupClass = (item['GroupClass'] as string) || ""
			}
			items.push(item)
		}
		if (items.length > 0) {
			groups.push({
				Title: lastGroup,
				Fields: items,
				show: true, // 所有分组都显示
				Class: lastGroupClass
			} as UTSJSONObject)
		}
		return groups
	})
	
	// 监听器
	watch(
		() => props.rules,
		(val: UTSJSONObject) => {
			formRules.value = val
			checkRules()
		}
	)
	
	watch(
		() => props.modelValue,
		(val: UTSJSONObject) => {
			updateModel(val)
			checkFieldRules()
		}
	)
	
	watch(
		() => props.fields,
		(val: UTSJSONObject[]) => {
			currFields.value = val
		}
	)
	
	// 方法
	const updateModel = (model: UTSJSONObject) => {
		const newDataModel = model || ({} as UTSJSONObject)
		props.fields.forEach((field: UTSJSONObject) => {
			const fieldId = field['Id'] as string
			if (newDataModel[fieldId] === undefined) {
				newDataModel[fieldId] = (field['DefaultValue'] as string) || ""
			}
		})
		dataModel.value = newDataModel
	}
	
	const checkFieldRules = () => {
		if (!dataModel.value) return
		// props.fields.forEach((field: UTSJSONObject) => checkFormRules(field))
		checkRules()
	}
	
	const handleChange = (data: UTSJSONObject) => {
		console.log("###change：", data)
		const field = data['field'] as UTSJSONObject
		const fieldData = data['data']
		
		if ((data['type'] as string) === "sendCode") {
			// 处理发送验证码逻辑
			return
		}
		
		const fieldType = field['Type'] as string
		if (fieldType == "Image" || fieldType == "File" || fieldType == "Face") {
			// formRef.value.validField(field['Id'] as string)
		}
		
		const emitData = { field: field, data: fieldData } as UTSJSONObject
		// if (checkFormRules(field, emitData.data)) {
		checkRules()
		// }
		emit("change", emitData)
	}
	
	const handleFieldChange = (data: UTSJSONObject) => {
		emit("fieldChange", data)
	}
	
	const checkRules = () => {
		const newFormRules = props.rules ? JSON.parse(JSON.stringify(props.rules)) : ({} as UTSJSONObject)
		if (!newFormRules['required']) newFormRules['required'] = []
		if (!newFormRules['rules']) newFormRules['rules'] = {} as UTSJSONObject
		if (!newFormRules['email']) newFormRules['email'] = []
		if (!newFormRules['tel']) newFormRules['tel'] = []
		if (!newFormRules['url']) newFormRules['url'] = []
		if (!newFormRules['mobile']) newFormRules['mobile'] = []
		if (!newFormRules['number']) newFormRules['number'] = []
		if (!newFormRules['combineRules']) newFormRules['combineRules'] = []
		
		for (let i = 0; i < props.fields.length; i++) {
			const field = props.fields[i]
			const fieldId = field['Id'] as string
			const required = field['Required'] as boolean
			const inputType = field['InputType'] as string
			const validationType = field['ValidationType'] as string
			
			if (required) (newFormRules['required'] as string[]).push(fieldId)
			if (inputType === "email") (newFormRules['email'] as string[]).push(fieldId)
			if (inputType === "mobile") (newFormRules['mobile'] as string[]).push(fieldId)
			if (inputType === "number") (newFormRules['number'] as string[]).push(fieldId)
			if (inputType === "url") (newFormRules['url'] as string[]).push(fieldId)
			
			if (validationType === "equal" || validationType === "greaterThan" || validationType === "lessThan") {
				(newFormRules['combineRules'] as UTSJSONObject[]).push({
					refs: [fieldId, field['ValidField'] as string],
					valid: {
						valid: validationType,
						message: field['ValidMesasge'] as string || field['ValidMesasgeEN'] as string
					}
				} as UTSJSONObject)
			} else if (validationType === "pattern") {
				const rules = newFormRules['rules'] as UTSJSONObject
				rules[fieldId] = {
					valid: {
						pattern: field['Validation'] as string,
						message: (field['ValidMesasge'] as string) || (field['ValidMesasgeEN'] as string) || "请输入正确的格式！"
					}
				} as UTSJSONObject
			}
			
			const val = dataModel.value[fieldId]
			if (!val && field['DefaultValue']) {
				dataModel.value[fieldId] = field['DefaultValue'] as string
			}
		}
		formRules.value = newFormRules
	}
	
	const isValid = (): boolean => {
		// 表单验证逻辑
		return true
	}
	
	const valid = () => {
		// return formRef.value.valid()
		return { result: true }
	}
	
	// 暴露方法给父组件
	defineExpose({
		isValid,
		valid
	})
	
	// 初始化
	if (props.fields) {
		updateModel(props.modelValue)
		nextTick(() => {
			checkFieldRules()
		})
	}
</script>

<style lang="scss">
	.form-builder {
		padding: 20rpx;
	}
	
	.form-group {
		margin-bottom: 40rpx;
	}
	
	.form-group-title {
		padding: 20rpx 0;
		border-bottom: 2rpx solid #e5e5e5;
		margin-bottom: 20rpx;
	}
	
	.group-title-text {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}
	
	.form-field {
		margin-bottom: 20rpx;
	}
	
	.field-tooltip {
		margin-top: 10rpx;
	}
	
	.tooltip-text {
		font-size: 24rpx;
		color: #999;
		line-height: 1.4;
	}
	
	.form-actions {
		padding: 40rpx 20rpx;
		display: flex;
		flex-direction: row;
		justify-content: center;
		gap: 20rpx;
	}
	
	.form-actions ux-button {
		flex: 1;
		max-width: 200rpx;
	}
</style>