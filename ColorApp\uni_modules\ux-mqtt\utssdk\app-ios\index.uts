import { UxMqttOptions, UxMqttConnectOptions, UxMqttSubscribeOptions, UxMqttPublishOptions } from '../interface.uts';
import { CocoaMQTT, CocoaMQTTConnState, CocoaMQTTQoS, CocoaMQTTMessage } from "CocoaMQTT";

export type MqttServerStatus = 'offline' | 'online'

export class UxMqtt {
	opts : UxMqttOptions
	client : CocoaMQTT
	status : MqttServerStatus = 'offline'

	constructor(opts : UxMqttOptions) {
		this.opts = opts
		
		let strs = this.opts.brokerUrl.split('://')
		let str = strs.length == 0 ? '' : strs[1]
		strs = str.split(':')
		let host = strs[0]
		let port = strs.length == 0 ? 0 : parseInt(strs[1])
		
		this.client = CocoaMQTT(clientID = this.opts.clientId, host = host, port = port.toUInt16())
	}

	/**
	 * 连接
	 */
	@UTSJS.keepAlive
	connect(opts: UxMqttConnectOptions) {
		// 如果客户端意外断开连接​（例如网络故障、客户端崩溃等），MQTT 服务器会将这条消息发布到/will主题
		this.client.willMessage = CocoaMQTTMessage(topic = "/will", string = "dieout")
		
		// 清除会话
		this.client.cleanSession = this.opts.cleanSession ?? true
		
		// 自动重连
		this.client.autoReconnect = this.opts.automaticReconnect ?? true
		
		// 超时
		if (this.opts.connectionTimeout != null) {
			// this.client.connectionTimeout = this.opts.connectionTimeout!.toUInt16()
		}
		
		// 心跳周期
		if (this.opts.keepAliveInterval != null) {
			this.client.keepAlive = this.opts.keepAliveInterval!.toUInt16()
		}
		
		// 账号
		if ((this.opts.username ?? '') != '') {
			this.client.username = this.opts.username!
		}
		
		// 密码
		if ((this.opts.password ?? '') != '') {
			this.client.password = this.opts.password!
		}
		
		// 允许使用不受信任的 CA 证书进行 TLS/SSL 加密连接
		this.client.allowUntrustCACertificate = true
		
		// 连接成功
		this.client.didConnectAck = (mqtt : CocoaMQTT, cack ?: any) => {
			this.status = 'online'
			opts.statusListener?.(true, '')
		}
		
		// 连接失败
		this.client.didDisconnect = (mqtt : CocoaMQTT, errorcode ?: any) => {
			this.status = 'offline'
			opts.statusListener?.(false, '连接失败')
		}
		
		// 连接状态
		this.client.didChangeState = (mqtt : CocoaMQTT, state : CocoaMQTTConnState) => {
			if (state == CocoaMQTTConnState.connecting) {
		
			} else if (state == CocoaMQTTConnState.connected) {
				this.status = 'online'
				opts.statusListener?.(true, '')
			} else if (state == CocoaMQTTConnState.disconnected) {
				this.status = 'offline'
				opts.statusListener?.(false, '连接断开')
			}
		}
		
		// 接收消息
		this.client.didReceiveMessage = (mqtt : CocoaMQTT, message : CocoaMQTTMessage, id : UInt16) => {
			opts.messageListener?.(message.topic, message.string ?? '')
		}
		
		this.client.connect()
	}

	/**
	 * 断开连接
	 */
	disconnect() {
		this.client.disconnect()
	}

	/**
	 * 订阅主题
	 */
	subscribe(opts : UxMqttSubscribeOptions) {
		let qos : CocoaMQTTQoS
		if (opts.qos == 1) {
			qos = CocoaMQTTQoS.qos1
		} else if (opts.qos == 2) {
			qos = CocoaMQTTQoS.qos2
		} else {
			qos = CocoaMQTTQoS.qos0
		}

		this.client.subscribe(opts.topic, qos = qos)
		opts.success?.()
	}

	/**
	 * 取消订阅
	 */
	unsubscribe(topics : string[]) {
		for (let i = 0; i < topics.length; i++) {
			this.client.unsubscribe(topics[i])
		}
	}

	/**
	 * 发布消息
	 */
	publish(opts : UxMqttPublishOptions) {
		let qos : CocoaMQTTQoS
		if (opts.qos == 1) {
			qos = CocoaMQTTQoS.qos1
		} else if (opts.qos == 2) {
			qos = CocoaMQTTQoS.qos2
		} else {
			qos = CocoaMQTTQoS.qos0
		}

		let retained = opts.retained ?? false

		this.client.publish(new CocoaMQTTMessage(topic = opts.topic, string = opts.msg, qos = qos, retained = retained))
		opts.success?.()
	}
}