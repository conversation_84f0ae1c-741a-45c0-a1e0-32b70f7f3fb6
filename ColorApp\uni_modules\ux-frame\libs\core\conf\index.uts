// #ifndef UNI-APP-X
import { ref } from 'vue'
// #endif

import { defConf } from "./def"
import { ColorBase } from "./color"
import { SizeBase } from "./size"
import { RadiusBase } from "./radiu"
import { SpacingBase } from "./spacing"
import type { UxConf } from "./def"

/**
 * UxConf 全局配置
 */
export class Conf {
	
	/**
	 * 开启debug打印
	 */
	debugLog = ref(true)
	
	/**
	 * 深色模式
	 */
	darkMode = ref(false)
	
	/**
	 * 灰色模式（哀悼模式）
	 */
	grayMode = ref(false)
	
	/**
	 * 跟随系统
	 */
	followSys = ref(false)
	
	/**
	 * 强调色
	 */
	primaryColor: ColorBase
	
	/**
	 * 次要色
	 */
	secondaryColor: ColorBase
	
	/**
	 * 成功提示色
	 */
	successColor: ColorBase
	
	/**
	 * 错误提示色
	 */
	errorColor: ColorBase
	
	/**
	 * 警告提示色
	 */
	warningColor: ColorBase
	
	/**
	 * 信息提示色
	 */
	infoColor: ColorBase
	
	/**
	 * 背景色
	 */
	backgroundColor: ColorBase
	
	/**
	 * 前景色
	 */
	foregroundColor: ColorBase
	
	/**
	 * tabbar背景色
	 */
	tabbarBackgroundColor: ColorBase
	
	/**
	 * tabbar文本色
	 */
	tabbarTextColor: ColorBase
	
	/**
	 * 文字色
	 */
	fontColor: ColorBase
	
	/**
	 * 占位色
	 */
	placeholderColor: ColorBase
	
	/**
	 * 取消色
	 */
	cancelColor: ColorBase
	
	/**
	 * 边框色
	 */
	borderColor: ColorBase
	
	/**
	 * 按压色
	 */
	hoverColor: ColorBase
	
	/**
	 * 标题色
	 */
	titleColor: ColorBase
	
	/**
	 * 二级标题色
	 */
	subtitleColor: ColorBase
	
	/**
	 * 段落色
	 */
	paragraphColor: ColorBase
	
	/**
	 * 分割线色
	 */
	lineColor: ColorBase
	
	/**
	 * 背景透明度
	 */
	maskAlpha = ref(0.6)
	
	/**
	 * 文字大小
	 */
	fontSize: SizeBase
	
	/**
	 * 圆角
	 */
	radius: RadiusBase
	
	/**
	 * 水平外边距
	 */
	hmargin: SpacingBase
	
	/**
	 * 垂直外边距
	 */
	vmargin: SpacingBase
	
	/**
	 * 水平内边距
	 */
	hpadding: SpacingBase
	
	/**
	 * 垂直内边距
	 */
	vpadding: SpacingBase
	
	/**
	 * 主题改变回调
	 */
	themeChangeEvent ?: (dark: boolean) => void = null
	
	/**
	 * 哀悼模式改变回调
	 */
	grayModeChangeEvent ?: (gray: boolean) => void = null
	
	/**
	 * 缓存key
	 */
	private cacheKey: string = 'UxFrameConf'
	
	constructor() {
		
		// 初始化配置
		this.debugLog.value = defConf.debugLog!
		this.darkMode.value = defConf.darkMode!
		this.grayMode.value = defConf.grayMode!
		this.followSys.value = defConf.followSys!
		this.primaryColor = new ColorBase(defConf.primaryColor!, '')
		this.secondaryColor = new ColorBase(defConf.secondaryColor!, '')
		this.successColor = new ColorBase(defConf.successColor!, '')
		this.errorColor = new ColorBase(defConf.errorColor!, '')
		this.warningColor = new ColorBase(defConf.warningColor!, '')
		this.infoColor = new ColorBase(defConf.infoColor!, '')
		this.backgroundColor = new ColorBase(defConf.backgroundColor!, defConf.backgroundColorDark!)
		this.foregroundColor = new ColorBase(defConf.foregroundColor!, defConf.foregroundColorDark!)
		this.tabbarBackgroundColor = new ColorBase(defConf.tabbarBackgroundColor!, defConf.tabbarBackgroundColorDark!)
		this.tabbarTextColor = new ColorBase(defConf.tabbarTextColor!, defConf.tabbarTextColorDark!)
		this.fontColor = new ColorBase(defConf.fontColor!, defConf.fontColorDark!)
		this.placeholderColor = new ColorBase(defConf.placeholderColor!, defConf.placeholderColorDark!)
		this.cancelColor = new ColorBase(defConf.cancelColor!, defConf.cancelColorDark!)
		this.hoverColor = new ColorBase(defConf.hoverColor!, defConf.hoverColorDark!)
		this.borderColor = new ColorBase(defConf.borderColor!, defConf.borderColorDark!)
		this.titleColor = new ColorBase(defConf.titleColor!, defConf.titleColorDark!)
		this.subtitleColor = new ColorBase(defConf.subtitleColor!, defConf.subtitleColorDark!)
		this.paragraphColor = new ColorBase(defConf.paragraphColor!, defConf.paragraphColorDark!)
		this.lineColor = new ColorBase(defConf.lineColor!, defConf.lineColorDark!)
		this.maskAlpha.value = defConf.maskAlpha!
		this.fontSize = new SizeBase(defConf.fontSize!)
		this.radius = new RadiusBase(defConf.radius!)
		this.hmargin = new SpacingBase(defConf.hmargin!)
		this.vmargin = new SpacingBase(defConf.vmargin!)
		this.hpadding = new SpacingBase(defConf.hpadding!)
		this.vpadding = new SpacingBase(defConf.vpadding!)
		
		// 加载缓存配置
		this.loadCached()
		
		// 初始系统深色模式
		this.setSysDarkMode()
		
		// 监听系统深色模式
		this.onDarkModeListener()
	}
	
	/**
	 * 初始化
	 */
	init(options ?: UxConf) {
		if(options != null) {
			this.setConf(options)
		}
		
		if(this.debugLog.value) {
			console.log('[UxFrame] 感谢使用UxFrameUI框架！如有任何问题可以加我们QQ群[450704209]或者访问我们的官网[https://www.uxframe.cn]进行反馈！配置[debugLog=false]可关闭此日志。');
		}
	}
	
	/**
	 * 加载缓存配置
	 */
	private loadCached() {
		// this.clearCache()
		let data = uni.getStorageSync(this.cacheKey)
		
		// #ifdef UNI-APP-X
		if(data instanceof UTSJSONObject) {
			let conf = JSON.parseObject<UxConf>(JSON.stringify(data))
			if(conf != null) {
				this.setConf(conf)
			}
		}
		// #endif
		
		// #ifndef UNI-APP-X
		if(typeof data == JSON) {
			let conf = JSON.parseObject<UxConf>(JSON.stringify(data))
			if(conf != null) {
				this.setConf(conf)
			}
		}
		// #endif
	}
	
	/**
	 * 设置缓存
	 */
	private setCache() {
		uni.setStorageSync(this.cacheKey, {
			debugLog: this.debugLog.value,
			darkMode: this.darkMode.value,
			grayMode: this.grayMode.value,
			followSys: this.followSys.value,
			primaryColor: this.primaryColor.color.value,
			secondaryColor: this.secondaryColor.color.value,
			successColor: this.successColor.color.value,
			errorColor: this.errorColor.color.value,
			warningColor: this.warningColor.color.value,
			infoColor: this.infoColor.color.value,
			backgroundColor: this.backgroundColor.color.value,
			backgroundColorDark: this.backgroundColor.darkColor.value,
			foregroundColor: this.foregroundColor.color.value,
			foregroundColorDark: this.foregroundColor.darkColor.value,
			tabbarBackgroundColor: this.tabbarBackgroundColor.color.value,
			tabbarBackgroundColorDark: this.tabbarBackgroundColor.darkColor.value,
			tabbarTextColor: this.tabbarTextColor.color.value,
			tabbarTextColorDark: this.tabbarTextColor.darkColor.value,
			fontColor: this.fontColor.color.value,
			fontColorDark: this.fontColor.darkColor.value,
			placeholderColor: this.placeholderColor.color.value,
			placeholderColorDark: this.placeholderColor.darkColor.value,
			cancelColor: this.cancelColor.color.value,
			cancelColorDark: this.cancelColor.darkColor.value,
			hoverColor: this.hoverColor.color.value,
			hoverColorDark: this.hoverColor.darkColor.value,
			borderColor: this.borderColor.color.value,
			borderColorDark: this.borderColor.darkColor.value,
			titleColor: this.titleColor.color.value,
			titleColorDark: this.titleColor.darkColor.value,
			subtitleColor: this.subtitleColor.color.value,
			subtitleColorDark: this.subtitleColor.darkColor.value,
			paragraphColor: this.paragraphColor.color.value,
			paragraphColorDark: this.paragraphColor.darkColor.value,
			lineColor: this.lineColor.color.value,
			lineColorDark: this.lineColor.darkColor.value,
			maskAlpha: this.maskAlpha.value,
			fontSize: this.fontSize.normal.value,
			radius: this.radius.normal.value,
			hmargin: this.hmargin.normal.value,
			vmargin: this.vmargin.normal.value,
			hpadding: this.hpadding.normal.value,
			vpadding: this.vpadding.normal.value,
		} as UTSJSONObject)
	}
	
	/**
	 * 清理缓存
	 */
	clearCache() {
		uni.removeStorageSync(this.cacheKey)
	}
	
	/**
	 * 设置配置
	 */
	setConf(conf: UxConf) {
		if(conf.debugLog != null) this.setDebugLog(conf.debugLog!)
		if(conf.followSys != null) this.setFollowSys(conf.followSys!)
		if(conf.darkMode != null) this.setDarkMode(conf.darkMode!)
		if(conf.grayMode != null) this.setGrayMode(conf.grayMode!)
		if(conf.primaryColor != null) this.setPrimaryColor(conf.primaryColor!)
		if(conf.secondaryColor != null) this.setSecondaryColor(conf.secondaryColor!)
		if(conf.successColor != null) this.setSuccessColor(conf.successColor!)
		if(conf.errorColor != null) this.setErrorColor(conf.errorColor!)
		if(conf.warningColor != null) this.setWarningColor(conf.warningColor!)
		if(conf.infoColor != null) this.setInfoColor(conf.infoColor!)
		if(conf.backgroundColor != null) this.setBackgroundColor(conf.backgroundColor!, conf.backgroundColorDark)
		if(conf.foregroundColor != null) this.setForegroundColor(conf.foregroundColor!, conf.foregroundColorDark)
		if(conf.tabbarBackgroundColor != null) this.setTabbarBackgroundColor(conf.tabbarBackgroundColor!, conf.tabbarBackgroundColorDark)
		if(conf.tabbarTextColor != null) this.setTabbarTextColor(conf.tabbarTextColor!, conf.tabbarTextColorDark)
		if(conf.fontColor != null) this.setFontColor(conf.fontColor!, conf.fontColorDark)
		if(conf.placeholderColor != null) this.setPlaceholderColor(conf.placeholderColor!, conf.placeholderColorDark)
		if(conf.cancelColor != null) this.setCancelColor(conf.cancelColor!, conf.cancelColorDark)
		if(conf.hoverColor != null) this.setHoverColor(conf.hoverColor!, conf.hoverColorDark)
		if(conf.borderColor != null) this.setBorderColor(conf.borderColor!, conf.borderColorDark)
		if(conf.titleColor != null) this.setTitleColor(conf.titleColor!, conf.titleColorDark)
		if(conf.subtitleColor != null) this.setSubtitleColor(conf.subtitleColor!, conf.subtitleColorDark)
		if(conf.paragraphColor != null) this.setParagraphColor(conf.paragraphColor!, conf.paragraphColorDark)
		if(conf.lineColor != null) this.setLineColor(conf.lineColor!, conf.lineColorDark)
		if(conf.maskAlpha != null) this.setMaskAlpha(conf.maskAlpha!)
		if(conf.fontSize != null) this.setFontSize(conf.fontSize!)
		if(conf.radius != null) this.setRadius(conf.radius!)
		if(conf.hmargin != null) this.setHmargin(conf.hmargin!)
		if(conf.vmargin != null) this.setVmargin(conf.vmargin!)
		if(conf.hpadding != null) this.setHpadding(conf.hpadding!)
		if(conf.vpadding != null) this.setVpadding(conf.vpadding!)
		
		this.setCache()
	}
	
	/**
	 * 开启打印日志
	 */
	setDebugLog(debugLog: boolean) {
		this.debugLog.value = debugLog
		this.setCache()
	}
	
	/**
	 * 设置深色模式
	 */
	setDarkMode(dark: boolean) {
		this.darkMode.value = dark
		this.themeChangeEvent?.(dark)
		this.setGlobalBackgroundColor()
		this.setCache()
		
		// #ifdef APP-ANDROID || APP-IOS
		uni.setAppTheme({
			theme: dark ? 'dark' : 'light',
			fail: (e: IAppThemeFail) => {
				console.log('[ux-frame]设置主题失败', e.errMsg)
			}
		})
		// #endif
	}
	
	/**
	 * 设置灰色模式（哀悼模式）
	 */
	setGrayMode(gray: boolean) {
		this.grayMode.value = gray
		this.grayModeChangeEvent?.(this.grayMode.value)
		this.setCache()
	}
	
	/**
	 * 设置跟随系统
	 */
	setFollowSys(followSys: boolean) {
		this.followSys.value = followSys
		
		this.setSysDarkMode()
		this.setCache()
		
		// #ifdef APP-ANDROID || APP-IOS
		let theme = uni.getSystemInfoSync().osTheme
		let isDark = theme != null && theme == 'dark'
		uni.setAppTheme({
			theme: followSys ? 'auto' : (isDark ? 'dark' : 'light'),
			fail: (e: IAppThemeFail) => {
				console.log('[ux-frame]设置主题失败', e.errMsg)
			}
		})
		// #endif
	}
	
	/**
	 * 设置主色调
	 */
	setPrimaryColor(color: string) {
		this.primaryColor.reset(color, '')
		this.setCache()
	}
	
	/**
	 * 设置次要色
	 */
	setSecondaryColor(color: string) {
		this.secondaryColor.reset(color, '')
		this.setCache()
	}
	
	/**
	 * 设置成功提示色
	 */
	setSuccessColor(color: string) {
		this.successColor.reset(color, '')
		this.setCache()
	}
	
	/**
	 * 设置错误提示色
	 */
	setErrorColor(color: string) {
		this.errorColor.reset(color, '')
		this.setCache()
	}
	
	/**
	 * 设置警告提示色
	 */
	setWarningColor(color: string) {
		this.warningColor.reset(color, '')
		this.setCache()
	}
	
	/**
	 * 设置信息提示色
	 */
	setInfoColor(color: string) {
		this.infoColor.reset(color, '')
		this.setCache()
	}
	
	/**
	 * 设置背景色
	 */
	setBackgroundColor(color: string, darkColor: string | null) {
		this.backgroundColor.reset(color, darkColor ?? '')
		this.setCache()
	}
	
	/**
	 * 设置前景色
	 */
	setForegroundColor(color: string, darkColor: string | null) {
		this.foregroundColor.reset(color, darkColor ?? '')
		this.setCache()
	}
	
	/**
	 * 设置tabbar背景色
	 */
	setTabbarBackgroundColor(color: string, darkColor: string | null) {
		this.tabbarBackgroundColor.reset(color, darkColor ?? '')
		this.setCache()
	}
	
	/**
	 * 设置tabbar文本色
	 */
	setTabbarTextColor(color: string, darkColor: string | null) {
		this.tabbarTextColor.reset(color, darkColor ?? '')
		this.setCache()
	}
	
	/**
	 * 设置文本色
	 */
	setFontColor(color: string, darkColor: string | null) {
		this.fontColor.reset(color, darkColor ?? '')
		this.setCache()
	}
	
	/**
	 * 设置占位色
	 */
	setPlaceholderColor(color: string, darkColor: string | null) {
		this.placeholderColor.reset(color, darkColor ?? '')
		this.setCache()
	}
	
	/**
	 * 设置取消色
	 */
	setCancelColor(color: string, darkColor: string | null) {
		this.cancelColor.reset(color, darkColor ?? '')
		this.setCache()
	}
	
	/**
	 * 设置按压色
	 */
	setHoverColor(color: string, darkColor: string | null) {
		this.hoverColor.reset(color, darkColor ?? '')
		this.setCache()
	}
	
	/**
	 * 设置边框色
	 */
	setBorderColor(color: string, darkColor: string | null) {
		this.borderColor.reset(color, darkColor ?? '')
		this.setCache()
	}
	
	/**
	 * 设置标题色
	 */
	setTitleColor(color: string, darkColor: string | null) {
		this.titleColor.reset(color, darkColor ?? '')
		this.setCache()
	}
	
	/**
	 * 设置二级标题色
	 */
	setSubtitleColor(color: string, darkColor: string | null) {
		this.subtitleColor.reset(color, darkColor ?? '')
		this.setCache()
	}
	
	/**
	 * 设置段落色
	 */
	setParagraphColor(color: string, darkColor: string | null) {
		this.paragraphColor.reset(color, darkColor ?? '')
		this.setCache()
	}
	
	/**
	 * 设置分割线色
	 */
	setLineColor(color: string, darkColor: string | null) {
		this.lineColor.reset(color, darkColor ?? '')
		this.setCache()
	}
	
	/**
	 * 设置背景透明度
	 */
	setMaskAlpha(alpha: number) {
		this.maskAlpha.value = alpha == 0 ? 0.6 : alpha
		this.setCache()
	}
	
	/**
	 * 设置文字大小
	 */
	setFontSize(fontSize: number) {
		this.fontSize.reset(fontSize)
		this.setCache()
	}
	
	/**
	 * 设置圆角
	 */
	setRadius(radius: number) {
		this.radius.reset(radius)
		this.setCache()
	}
	
	/**
	 * 水平外边距
	 */
	setHmargin(margin: number) {
		this.hmargin.reset(margin)
		this.setCache()
	}
	
	/**
	 * 垂直外边距
	 */
	setVmargin(margin: number) {
		this.vmargin.reset(margin)
		this.setCache()
	}
	
	/**
	 * 水平内边距
	 */
	setHpadding(padding: number) {
		this.hpadding.reset(padding)
		this.setCache()
	}
	
	/**
	 * 垂直内边距
	 */
	setVpadding(padding: number) {
		this.vpadding.reset(padding)
		this.setCache()
	}
	
	/**
	 * 设置主题改变回调
	 */
	setOnThemeChangeEvent(callback: (dark: boolean) => void) {
		this.themeChangeEvent = callback
		this.themeChangeEvent?.(this.darkMode.value)
	}
	
	/**
	 * 设置哀悼模式改变回调
	 */
	setOnGrayModeChangeEvent(callback: (gray: boolean) => void) {
		this.grayModeChangeEvent = callback
		this.grayModeChangeEvent?.(this.grayMode.value)
	}
	
	/**
	 * 设置系统主题
	 */
	setSysDarkMode() {
		let setOsTheme = (dark: boolean) => {
			if(this.followSys.value) {
				this.setDarkMode(dark)
			} else {
				this.themeChangeEvent?.(dark)
			}
		}
		
		// #ifdef APP || MP
		let theme = uni.getSystemInfoSync().osTheme
		setOsTheme(theme != null && theme == 'dark')
		// #endif
		
		// #ifdef WEB
		const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
		setOsTheme(isDarkMode)
		// #endif
	}
	
	/**
	 * 监听深色模式
	 */
	onDarkModeListener() {
		let setOsTheme = (dark: boolean) => {
			if(this.followSys.value) {
				this.setDarkMode(dark)
			} else {
				this.themeChangeEvent?.(dark)
			}
		}
		
		// #ifdef UNI-APP-X
		
		// #ifdef APP-ANDROID || APP-IOS
		uni.onOsThemeChange((res: OsThemeChangeResult)=> {
		    setOsTheme(res.osTheme == 'dark')
		})
		// #endif
		
		// #ifdef WEB
		const themeChange = (e) => {
			setOsTheme(e.matches)
		}
		window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', themeChange)
		// #endif
		
		// #ifdef MP
		uni.onHostThemeChange((res)=> {
		    setOsTheme(res.osTheme == 'dark')
		})
		// #endif
		
		// #endif
		
		// #ifndef UNI-APP-X
		uni.onThemeChange((res)=> {
		    setOsTheme(res.theme == 'dark')
		})
		// #endif
		
		this.setGlobalBackgroundColor()
	}
	
	/**
	 * 设置全局背景色
	 */
	setGlobalBackgroundColor() {
		// #ifdef WEB
		const color = this.darkMode.value ? '#ffffff' : '#000000'
		const backgroundColor = this.darkMode.value ? this.backgroundColor.dark.value : this.backgroundColor.color.value
		
		document.documentElement.style.color = color
		document.documentElement.style.backgroundColor = backgroundColor
		// #endif
	}
}