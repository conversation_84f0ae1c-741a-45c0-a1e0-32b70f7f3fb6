<template>
	<view class="ux-text" :style="xstyle" @click="click">
		<template v-if="prefixIcon != ''">
			<ux-icon
				:type="prefixIcon" 
				:size="iconSize" 
				:color="fontColor" 
				:custom-family="customFamily"
				:xstyle="['margin-right: 4px;margin-top: 1px;']">
			</ux-icon>
		</template>
		
		<view class="ux-text__value">
			<text :selectable="selectable" :space="space" :decode="decode" :style="style">{{ value }}</text>
		</view>
		
		<template v-if="suffixIcon != ''">
			<ux-icon
				:type="suffixIcon" 
				:size="iconSize" 
				:color="fontColor" 
				:custom-family="customFamily"
				:xstyle="['margin-left: 4px;margin-top: 1px;']">
			</ux-icon>
		</template>
	</view>
</template>

<script setup>
	/**
	* Text 文本
	* @description 扩展支持拨打电话，格式化日期，脱敏，超链接...等功能
	* @demo pages/component/text.uvue
	* @tutorial https://www.uxframe.cn/component/text.html
	* @property {String} 			name												String | 标识符/网页名称
	* @property {String} 			theme=[primary|warning|success|error|info]			String | 主题颜色
	* @value primary 	主色
	* @value warning 	警告
	* @value success 	成功
	* @value error 		错误
	* @value info 		文本
	* @property {String} 			text												String | 文本内容
	* @property {String} 			color												String | 文本颜色 优先级高于主题
	* @property {String} 			darkColor=[none|auto|color]							String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	* @value none 不显示
	* @value auto 自动适配深色模式
	* @value color 其他颜色
	* @property {Any} 				size												Any | 字体大小（默认 $ux.Conf.fontSize ）
	* @property {String}			sizeType=[normal|small|big]							String | 字体大小类型（默认 small ）
	* @value normal 正常
	* @value small 较小
	* @value big 较大
	* @property {Number} 			lines												Number | 文本显示的行数，若大于0，超出此行数，将会显示省略号（默认 0 ）
	* @property {Boolean} 			bold=[true|false]									Boolean | 字体加粗（默认 false ）
	* @value true 加粗
	* @value false 正常
	* @property {String} 			align=[left|center|right]							String | 文本对齐方式（默认 left ）
	* @value left 左对齐
	* @value center 居中对齐
	* @value right 右对齐
	* @property {String} 			decoration=[none|underline|line-through] 			String | 文字装饰（默认 none ）
	* @value none 无
	* @value underline 下划线
	* @value line-through 中划线
	* @property {Number} 			lineHeight											Number | 文本行高
	* @property {String}			prefixIcon											String | 前置图标
	* @property {String} 			suffixIcon											String | 后置图标
	* @property {Any}				iconSize											Any | 图标字体大小 (默认 $ux.Conf.fontSize)
	* @property {String}			customFamily										String | 字体family
	* @property {Boolean} 			selectable=[true|false]								Boolean | 文本是否可选（默认 false ）
	* @property {String} 			space=[ensp|emsp|nbsp]								String | 显示连续空格
	* @value ensp 中文字符空格一半大小
	* @value emsp 中文字符空格大小
	* @value nbsp 根据字体设置的空格大小
	* @property {Boolean} 			decode=[true|false]									Boolean | 是否解码（默认 false ）
	* @property {String} 			mode=[text|phone|name|date|link|money]				String | 文本处理的匹配模式 (默认 text)
	* @value text 普通文本
	* @value phone 手机号
	* @value name 姓名
	* @value date 日期
	* @value link 超链接
	* @value money 金额
	* @property {String} 			format=[encrypt|verify|cmoney|qmoney|wmoney|yyyy-MM-dd|yyyy-MM-dd HH:mm:ss|yyyy/MM/dd|yyyy/MM/dd HH:mm:ss|yyyy年MM月dd日]		String | 格式化规则
	* @value encrypt 脱敏加密
	* @value verify 合法性校验
	* @value cmoney 大写金额
	* @value qmoney 金额千分制
	* @value wmoney 金额万分制
	* @property {String} 			path												String | 页面跳转地址
	* @property {Boolean} 			call=[true|false]									Boolean | 是否拨打电话（默认 false ）
	* @property {Array}				margin												Array | 边距 [上 右 下 左] [上下 左右] [上下左右]
	* @property {Number}			mt													Number | 距上 单位px
	* @property {Number}			mr													Number | 距右 单位px
	* @property {Number}			mb													Number | 距下 单位px
	* @property {Number}			ml													Number | 距左 单位px
	* @property {Array}				padding												Array | 填充 [上 右 下 左] [上下 左右] [上下左右]
	* @property {Number}			pt													Number | 上内边距 单位px
	* @property {Number}			pr													Number | 右内边距 单位px
	* @property {Number}			pb													Number | 下内边距 单位px
	* @property {Number}			pl													Number | 左内边距 单位px
	* @property {Array}				xstyle												Array<any> | 自定义样式
	* @event {Function} 			click 												Function |  被点击时触发
	* <AUTHOR>
	* @date 2023-11-19 22:56:28
	*/
	
	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	import { useFontColor, useFontSize, useThemeColor, UxThemeType } from '../../libs/use/style.uts'
	
	defineOptions({
		name: 'ux-text',
		mixins: [xstyleMixin]
	})
	
	const emit = defineEmits(['click'])
	
	const props = defineProps({
		theme: {
			type: String,
			default: ''
		},
		text: {
			type: String,
			default: '',
		},
		color: {
			type: String,
			default: '',
		},
		darkColor: {
			type: String,
			default: '',
		},
		size: {
			default: 0
		},
		sizeType: {
			type: String,
			default: 'small'
		},
		bold: {
			type: Boolean,
			default: false
		},
		lines: {
			type: Number,
			default: 0
		},
		align: {
			type: String,
			default: 'left',
		},
		decoration: {
			type: String,
			default: 'none',
		},
		lineHeight: {
			type: Number,
			default: 0
		},
		prefixIcon: {
			type: String,
			default: '',
		},
		suffixIcon: {
			type: String,
			default: '',
		},
		iconSize: {
			default: 0
		},
		customFamily: {
			type: String,
			default: ''
		},
		selectable: {
			type: Boolean,
			default: false
		},
		space: {
			type: String,
			default: ''
		},
		decode: {
			type: Boolean,
			default: false
		},
		mode: {
			type: String,
			default: 'text',
		},
		format: {
			type: String,
			default: '',
		},
		path: {
			type: String,
			default: '',
		},
		call: {
			type: Boolean,
			default: false
		},
	})
	
	const fontColor = computed((): string => {
		if(props.color != '' || props.theme == '') {
			return useFontColor(props.color, props.darkColor)
		} else {
			return useThemeColor(props.theme as UxThemeType) 
		}
	})
	
	const fontSize = computed(() : number => {
		let type = props.sizeType == 'big' ? 2 : (props.sizeType == 'small' ? 1 : 0) 
		return useFontSize($ux.Util.getPx(props.size), type)
	})
	
	const style = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('font-size', $ux.Util.addUnit(fontSize.value))
		css.set('color', props.mode == 'link' ? '#4281FF' : fontColor.value)
		css.set('font-weight', props.bold ? 'bold' : 'normal')
		css.set('text-align', props.align)
		
		if(props.mode == 'link') {
			css.set('text-decoration-line', 'underline')
		} else {
			css.set('text-decoration-line', props.decoration)
		}
		
		if (props.lines > 0) {
			// #ifdef APP
			css.set('lines', props.lines)
			css.set('text-overflow', 'ellipsis')
			// #endif
			// #ifndef APP
			css.set('display', '-webkit-box')
			css.set('-webkit-line-clamp', props.lines)
			css.set('-webkit-box-orient', 'vertical')
			css.set('overflow', 'hidden')
			// #endif
		}
		
		if(props.lineHeight > 0) {
			css.set('line-height', props.lineHeight)
		}
		
		// #ifdef WEB
		css.set('cursor', props.call || props.mode == 'link' ? 'pointer' : '')
		// #endif
		
		css = $ux.Util.xStyle(css, props.margin, props.mt, props.mr, props.mb, props.ml, props.padding, props.pt, props.pr, props.pb, props.pl)
		
		return css
	})
	
	const value = computed((): string => {
		let text = props.text
		
		if (props.mode == 'text') {
			
		} else if (props.mode == 'phone') {
			// 合法性校验
			if(props.format == 'verify' || props.format == 'encrypt') {
				if(!$ux.Verify.isPhone(text)) {
					console.error(`[ux-text]手机号验证失败`);
				}
			}
			
			// 脱敏加密
			if(props.format == 'encrypt') {
				text = `${text.substring(0, 3)}****${text.substring(7)}`
			}
		} else if (props.mode == 'name') {
			// 合法性校验
			if(props.format == 'verify' || props.format == 'encrypt') {
				if(text.length <= 1) {
					console.error(`[ux-text]姓名验证失败`);
				}
			}
			
			// 脱敏加密
			if(props.format == 'encrypt') {
				text = $ux.Fmt.encryptText(text)
			}
		} else if (props.mode == 'date') {
			if(props.format != '') {
				// 合法性校验
				if(!$ux.Verify.isDate(text)) {
					console.error(`[ux-text]日期验证失败`);
				}
				
				// 日期格式化
				text = $ux.Date.fmtDate(text, props.format)
			}
		} else if (props.mode == 'link') {
			// 合法性校验
			if(props.format == 'verify') {
				if(!$ux.Verify.isURL(text)) {
					console.error(`[ux-text]超链接验证失败`);
				}
			}
		} else if (props.mode == 'money') {
			// 合法性校验
			if(props.format == 'verify') {
				if (!/^\d+(\.\d+)?$/.test(text)) {
					console.error(`[ux-text]金额验证失败`);
				}
			}
			
			if(props.format == 'cmoney') {
				// 大写金额
				text = $ux.Fmt.upperMoney(text)
			} else if(props.format == 'qmoney') {
				// 金额千分制
				text = $ux.Fmt.fmtMoney(text, false)
			} else if(props.format == 'wmoney') {
				// 金额万分制
				text = $ux.Fmt.fmtMoney(text, true)
			}
		}
		
		return text
	})
	
	function click(e: MouseEvent) {
		// 拨打电话
		if (props.call && props.mode == 'phone') {
			// TODO 等uniapi
		}
		
		if(props.mode == 'link') {
			if(props.path == '') {
				console.warn('[ux-text]请配置页面跳转路径 path ');
			} else {
				uni.navigateTo({
					url: `${props.path}?title=${props.name}&url=${props.text}`
				})
			}
		}
		
		emit('click', e)
	}
</script>

<style lang="scss">
	.ux-text {
		display: flex;
		flex-direction: row;
		align-items: center;
		
		&__value {
			flex: 1;
		}
	}
</style>