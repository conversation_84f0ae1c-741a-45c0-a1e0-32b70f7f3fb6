<template>
	<movable-area :scale-area="scaleArea" class="ux-movable-area">
		<slot></slot>
	</movable-area>
</template>

<script setup lang="ts">
	/**
	 * MovableArea 拖动区域
	 * @description 拖动或双指缩放区域
	 * @demo pages/component/movable-area.uvue
	 * @tutorial https://www.uxframe.cn/component/movable-area.html
	 * @property {Boolean}			scaleArea=[true|false]	Boolean | 是否禁用 (默认 false)
	 * @value true
	 * @value false
	 * <AUTHOR>
	 * @date 2024-12-20 17:27:15
	 */
	
	import { $ux } from '../../index'
	
	defineOptions({
		name: 'ux-movable-area'
	})
	
	defineProps({
		scaleArea: {
			type: Boolean,
			default: false
		},
	})
	
</script>

<style lang="scss">
	.ux-movable-area {
		position: relative;
		width: 100%;
		height: 100%;
		overflow: hidden;
	}
</style>