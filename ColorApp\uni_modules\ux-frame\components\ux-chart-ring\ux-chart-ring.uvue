<template>
	<view class="ux-chart-ring" :style="style">
		<canvas id="ring" :style="style" />
		<view class="ux-chart-ring__content" :style="cStyle">
			<slot>
				<text class="ux-chart-ring__value" :style="valueStyle">{{ progress }}%</text>
			</slot>
		</view>
	</view>
</template>

<script setup>
	
	/**
	 * 环形进度条图表
	 * @description canvas绘制的环形进度条图表
	 * @demo pages/component/chart-ring.uvue
	 * @tutorial https://www.uxframe.cn/component/chart-ring.html
	 * @property {String}				mode=[ring|dashboard]			String | 模式 (默认 ring)
	 * @value ring 圆环
	 * @value dashboard 仪表盘
	 * @property {UxChartRingType}		data							UxChartRingType | 数据
	 * @property {Number}				delay							Number | 延迟初始化 (默认 0)
	 * @event {Function}				changing						Function | 进度发生改变时触发
	 * <AUTHOR>
	 * @date 2025-02-27 16:35:45
	 */
	
	import { $ux } from '../../index'
	import { useFontSize, useFontColor, useThemeColor, useColor } from '../../libs/use/style.uts'
	import { UxChartRingType } from '../../libs/types/chart.uts'
	
	defineOptions({
		name: 'ux-chart-ring'
	})
	
	const emit = defineEmits(['changing'])

	const props = defineProps({
		mode: {
			type: String,
			default: 'ring'
		},
		data: {
			type: Object as PropType<UxChartRingType>,
			required: true
		},
		delay: {
			type: Number,
			default: 0
		}
	})

	let ctx: CanvasRenderingContext2D | null = null
	
	const lastProgress = ref(0)
	const progress = ref(0)
	
	let width = 0
	let height = 0
	
	const data = computed((): UxChartRingType => {
		return props.data as UxChartRingType
	})
	
	const step = computed(() => {
		return data.value.step ?? 1
	})
	
	const radiusArr = computed(() => {
		return data.value.radius ?? [36, 40] as number[]
	})
	
	const radius = computed(() => {
		return $ux.Util.getPx(radiusArr.value[1])
	})
	
	const lineWidth = computed(() => {
		return $ux.Util.getPx(radiusArr.value[1] - radiusArr.value[0])
	})
	
	const fontSize = computed((): number => {
		return useFontSize($ux.Util.getPx(data.value.fontSize ?? 18), 0)
	})
	
	const fontColor = computed((): string => {
		return useFontColor(data.value.color ?? '', data.value.darkColor ?? '')
	})
	
	const backgroundColor = computed((): string => {
		let bc = data.value.backgroundColor ?? ''
		let dc = data.value.backgroundColorDark ?? ''
		let color = bc == '' ? '#f6f6f6' : bc
		let darkColor = dc == '' ? 'auto' : dc
		return useColor(color, darkColor)
	})
	
	const progressColor = computed((): string => {
		let theme = useThemeColor('primary')
		let bc = data.value.progressColor ?? ''
		let dc = data.value.progressColorDark ?? ''
		let color = bc == '' ? theme : bc
		let darkColor = dc == '' ? 'auto' : dc
		return useColor(color, darkColor)
	})
	
	const style = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('width', `${radius.value * 2}px`)
		
		if(props.mode == 'ring') {
			css.set('height', `${radius.value * 2}px`)
		} else {
			css.set('height', `${radius.value}px`)
		}
		
		return css
	})
	
	const cStyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		if((data.value.bottom ?? 0) > 0) {
			css.set('bottom', $ux.Util.addUnit(data.value.bottom!))
		}
		
		return css
	})
	
	const valueStyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('font-size', $ux.Util.addUnit(fontSize.value))
		css.set('color', fontColor.value)
		css.set('font-weight', (data.value.bold ?? false) ? 'bold' : 'normal')
		
		return css
	})

	function drawRing() {
		if (ctx == null) return
		ctx!.clearRect(0, 0, width, height)
		
		// 起始角度
		const startAngle = props.mode == 'ring' ? (-Math.PI / 2) : -Math.PI
		// 结束角度
		const endAngle = props.mode == 'ring' ? (2 * Math.PI) : Math.PI
		
		const _radius = radius.value - lineWidth.value
		const x = radius.value
		const y = radius.value
		
		ctx!.beginPath();
		ctx!.arc(x, y, _radius, startAngle, endAngle)
		ctx!.lineWidth = lineWidth.value
		ctx!.strokeStyle = backgroundColor.value
		ctx!.lineCap = 'round'
		ctx!.stroke()
	}

	function drawProgress(progress : number) {
		if (ctx == null) return
		// 起始角度
		const startAngle = props.mode == 'ring' ? (-Math.PI / 2) : -Math.PI
		// 结束角度
		const endAngle = props.mode == 'ring' ? (2 * Math.PI) : Math.PI
		
		const _radius = radius.value - lineWidth.value
		const x = radius.value
		const y = radius.value
		
		ctx!.clearRect(0, 0, width, height)
		
		ctx!.beginPath();
		ctx!.arc(x, y, _radius, startAngle, endAngle)
		ctx!.lineWidth = lineWidth.value
		ctx!.strokeStyle = backgroundColor.value
		ctx!.lineCap = 'round'
		ctx!.stroke()
		
		ctx!.beginPath()
		ctx!.arc(x, y, _radius, startAngle, startAngle + (endAngle * (progress / 100))) // 动态更新进度
		ctx!.lineWidth = lineWidth.value
		ctx!.strokeStyle = progressColor.value
		ctx!.lineCap = 'round'
		ctx!.stroke()
	}
	
	function draw() {
		// 结束值 目标位置
		const endVal = Math.max(0, data.value.value)
		// 记录上一次结束的位置
		const lastVal = lastProgress.value
		// 动画起始值继承上一次结束值，从上次位置继续动画
		const startVal = lastVal
		
		$ux.Anim.run({
			startVal: startVal,
			endVal: endVal,
			lastVal: lastVal,
			speed: step.value,
			max: 100,
			min: 0,
			// offset: 2,
			// offsetSpeed: 0.2,
			frame: (value: number) => {
				progress.value = value
				drawProgress(value)
				emit('changing', value)
			},
			end: (value: number) => {
				lastProgress.value = value
			}
		})
	}

	function init() {
		uni.createCanvasContextAsync({
			id: 'ring',
			component: getCurrentInstance()?.proxy!,
			success: (context : CanvasContext) => {
				ctx = context.getContext('2d')!;
				const canvas = ctx!.canvas;

				// 处理高清屏逻辑
				const dpr = uni.getDeviceInfo().devicePixelRatio ?? 1;
				canvas.width = canvas.offsetWidth * dpr;
				canvas.height = canvas.offsetHeight * dpr;
				ctx!.scale(dpr, dpr)
				
				width = canvas.offsetWidth
				height = canvas.offsetHeight
				
				setTimeout(function() {
					drawRing()
					setTimeout(function() {
						draw()
					}, props.delay);
				}, 100);
			}
		})
	}

	watch(() : number => data.value.value, () => {
		draw()
	})
	
	watch(backgroundColor, () => {
		draw()
	})
	
	watch(progressColor, () => {
		draw()
	})

	onMounted(() => {
		init()
	})
</script>

<style lang="scss">
	
	.ux-chart-ring {
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;

		&__content {
			position: absolute;
			z-index: 0;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
		}
			
		&__value {
			font-size: 18px;
		}
	}
</style>