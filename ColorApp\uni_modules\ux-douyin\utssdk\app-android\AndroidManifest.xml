<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" xmlns:tools="http://schemas.android.com/tools" package="uts.sdk.modules.uxDouyin">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <queries>
        <package android:name="com.ss.android.ugc.aweme" />
        <package android:name="com.ss.android.ugc.aweme.lite" />
    </queries>
	
	<!-- ${applicationId} -->

    <application>
		<activity
		    android:name="uts.sdk.modules.uxDouyin.DouYinEntryActivity"
		    android:theme="@android:style/Theme.Translucent.NoTitleBar"
		    android:exported="true"
		    android:taskAffinity="com.chunge.suanmi"
		    android:launchMode="singleTask">
		</activity>
		
		<activity-alias
		    android:name="com.chunge.suanmi.douyinapi.DouYinEntryActivity"
		    android:targetActivity="uts.sdk.modules.uxDouyin.DouYinEntryActivity"
		    android:exported="true"
			tools:replace="android:targetActivity">
		</activity-alias>

        <provider
            android:name="uts.sdk.modules.uxDouyin.DouyinFileProvider"
            android:authorities="com.chunge.suanmi.uxDouyin.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true"
			tools:replace="android:authorities">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/uxdy_file_provider" />
        </provider>
    </application>

</manifest>