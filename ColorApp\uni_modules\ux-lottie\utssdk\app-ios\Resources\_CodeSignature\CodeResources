<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/unimoduleUxLottie-Swift.h</key>
		<data>
		IFndRdR9AqP6mw/JZA+eWJvjcnw=
		</data>
		<key>Headers/unimoduleUxLottie.h</key>
		<data>
		FH2SRnQAHIO8AbDZPa5/Bz/CcdA=
		</data>
		<key>Info.plist</key>
		<data>
		v6lc+37Pq3BdPLKt7hbPIJzqeTc=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		okeJXcSA3RXD+neK3qMgMlX22tg=
		</data>
		<key>Modules/unimoduleUxLottie.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		OiyT8mVzFvsJNjJWbS+vZjYDvMA=
		</data>
		<key>Modules/unimoduleUxLottie.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		QVlF3J1p9DcEEnSCBeD9zVSNPJE=
		</data>
		<key>Modules/unimoduleUxLottie.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		ZYKus+qFFjbIsFCyywaZQDI/hsM=
		</data>
		<key>Modules/unimoduleUxLottie.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		cI662qz5lxmNYwzoiXR3aUYIQOM=
		</data>
		<key>Modules/unimoduleUxLottie.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		ZYKus+qFFjbIsFCyywaZQDI/hsM=
		</data>
		<key>Modules/unimoduleUxLottie.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		gVrEFeEP5NMq7y13koKJjEQPySE=
		</data>
		<key>config.json</key>
		<data>
		gKa3m1s5nLnwXquReMt4g+kVsWo=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/unimoduleUxLottie-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ErGd19HaWey+R6W2NCDS4Rkx194wsXLGqTgRihKhGoE=
			</data>
		</dict>
		<key>Headers/unimoduleUxLottie.h</key>
		<dict>
			<key>hash2</key>
			<data>
			XAsze1m7Z4PhyI2gj+RafZVS4xyqWjgvqraTawdVPMY=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			R2111XFe2w+mcN5a0n/1xYz4kWnVed1MZ9qUbB3CCs0=
			</data>
		</dict>
		<key>Modules/unimoduleUxLottie.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			SXv6T3mmu1sjIDjAs+Fj2R2v7JqTlHsOuY6kH/EA4W0=
			</data>
		</dict>
		<key>Modules/unimoduleUxLottie.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			TZpPTfAS7g79L7sQ/08emOBbv9yvyqMtln9FkDt1CsE=
			</data>
		</dict>
		<key>Modules/unimoduleUxLottie.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			05GS9SfMZpUUd5QW/i7mB3mHrk7+8o+KW+d96yC3ld0=
			</data>
		</dict>
		<key>Modules/unimoduleUxLottie.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			JTmSbcYcw61e8K9zl4U8yFwMofnPsg9QMqFiN1N2QK8=
			</data>
		</dict>
		<key>Modules/unimoduleUxLottie.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			05GS9SfMZpUUd5QW/i7mB3mHrk7+8o+KW+d96yC3ld0=
			</data>
		</dict>
		<key>Modules/unimoduleUxLottie.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			PzWJ/s+AmMea0+37R/WQkHcIDRuZCMWTdYmdbUAPiE4=
			</data>
		</dict>
		<key>config.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zf1gUAFsJ4osXiTTA57mMNvMKMw7OOYoHGMne1Hb0+8=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
