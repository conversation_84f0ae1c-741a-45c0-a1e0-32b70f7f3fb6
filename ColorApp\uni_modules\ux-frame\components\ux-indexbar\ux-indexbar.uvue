<template>
	<view ref="uxIndexbarRef" class="ux-indexbar" :style="style">
		<!-- #ifndef MP -->
		<list-view class="ux-indexbar__list" :style="listStyle"
			direction="vertical"
			:bounces="false"
			:scroll-top="scrollTop"
			:scroll-with-animation="true" 
			:show-scrollbar="false"
			@scroll="scroll">
			
			<slot></slot>
			
		</list-view>
		<!-- #endif -->
		
		<!-- #ifdef MP -->
		<scroll-view class="ux-indexbar__list" :style="listStyle"
			direction="vertical"
			:scroll-top="scrollTop"
			:scroll-with-animation="true"
			:show-scrollbar="false"
			@scroll="scroll">
			
			<slot></slot>
		
		</scroll-view>
		<!-- #endif -->
	
		<view class="ux-indexbar__slider" :style="sliderStyle">
			<view ref="sliderRef" class="ux-indexbar__slider--warp" 
				@touchmove="slidermove" 
				@mousedown="isPressed = true" 
				@mousemove="slidermovePc" 
				@mouseup="isPressed = false" 
				@mouseleave="isPressed = false" 
				@mousecancel="isPressed = false">
				<view class="ux-indexbar__slider--item" :style="[itemStyle, {'background-color': activeIndex == index? activeColor : 'transparent'}]" v-for="(item,index) in items" :key="index" @click="(e: MouseEvent) => click(e, item, index)" >
					<ux-icon v-if="item.name == 'OTHER' && otherIcon != ''" :type="otherIcon" :color="activeIndex == index? activeFontColor : '#fff'" :size="13"></ux-icon>
					<text v-else class="ux-indexbar__slider--text" :style="{color: activeIndex == index? activeFontColor : '#fff'}">{{item.name == 'OTHER' ? '#' : item.name}}</text>
				</view>
			</view>
		</view>
		
		<view v-if="showIndicate" ref="indicateRef" class="ux-indexbar__indicate" :style="indicateStyle">
			<ux-icon v-if="items[activeIndex].name == 'OTHER' && otherIcon != ''" style="transform: rotate(45deg)" :type="otherIcon" color="#fff"></ux-icon>
			<text v-else class="ux-indexbar__indicate--text">{{ items[activeIndex].name == 'OTHER' ? '#' : items[activeIndex].name }}</text>
		</view>
	</view>
</template>

<script setup>
	
	/**
	 * IndexBar 索引导航栏
	 * @description 支持右侧索引联动滚动到锚点
	 * @tutorial https://www.uxframe.cn/component/tab/indexbar.html
	 * @property {Boolean}			fixed								Boolean | fixed定位 (默认 true)
	 * @property {Any}				width								Any | 宽度 (默认 auto)
	 * @property {Any}				height								Any | 高度 (默认 auto)
	 * @property {Any}				right								Any | slider右偏移距离 (默认 10px)
	 * @property {String}			activeColor							String | 选中颜色 (默认 $ux.Conf.primaryColor)
	 * @property {String}			indicateColor						String | 未选中颜色 (默认 $ux.Conf.primaryColor)
	 * @property {Number}			opacity								Number | 侧边栏背景透明度 (默认 0.2)
	 * @property {String}			otherIcon							String | 其他项图标
	 * @event {Function}			click								Function | 点击时触发
	 * @event {Function}			change								Function | 当前选择下标改变时触发
	 * <AUTHOR>
	 * @date 2024-05-26 11:25:54
	 */
	
	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	import { UxIndexbarItem } from '../../libs/types/types.uts'
	import { useThemeColor } from '../../libs/use/style.uts'
	
	defineOptions({
		name: 'ux-indexbar',
		mixins: [xstyleMixin]
	})
	
	const emit = defineEmits(['change', 'click'])
	
	const props = defineProps({
		fixed: {
			type: Boolean,
			default: true
		},
		width: {
			default: 0
		},
		height: {
			default: 0
		},
		right: {
			default: 0
		},
		activeColor: {
			type: String,
			default: ''
		},
		indicateColor: {
			type: String,
			default: ''
		},
		activeFontColor: {
			type: String,
			default: '#ffffff'
		},
		indicateSize: {
			type: Number,
			default: 18
		},
		opacity: {
			type: Number,
			default: 0.2
		},
		otherIcon: {
			type: String,
			default: ''
		}
	})
	
	const indicateRef = ref<Element | null>(null)
	const sliderRef = ref<Element | null>(null)
	const uxIndexbarRef = ref<Element | null>(null)
	const tabs = ref<UxIndexbarItem[]>([] as UxIndexbarItem[])
	const scrollTop = ref(0)
	const scrollY = ref(0)
	const isMoving = ref(false)
	let isPressed = false
	const isClicked = ref(false)
	const activeIndex = ref(0)
	const indicateTop = ref(0)
	const indicateRight = ref(0)
	let sliderTop = 0
	let sliderLeft = 0
	let sliderHeight = 0
	let timer = 0
	let ctimer = 0
	const windowTop = ref(0)
	const windowHeight = ref(0)
	
	provide('scrollY', scrollY)
	
	const items = computed(() : UxIndexbarItem[] => {
		return tabs.value.filter((el : UxIndexbarItem) : boolean => el.name != "");
	})
	
	const primaryColor = computed(() => {
		return useThemeColor('primary')
	})
	
	const activeColor = computed(():string => {
		if(props.activeColor == '') {
			return primaryColor.value
		} else {
			return props.activeColor
		}
	})
	
	const indicateColor = computed(():string => {
		if(props.indicateColor == '') {
			return activeColor.value
		} else {
			return props.indicateColor
		}
	})
	
	const style = computed(():Map<string, any> => {
		let css = new Map<string, any>()
		
		if($ux.Util.getPx(props.height) > 0) {
			css.set('height', `${$ux.Util.addUnit(props.height)}`)
		}
		
		return css
	})
	
	const listStyle = computed(():Map<string, any> => {
		let css = new Map<string, any>()
		
		if($ux.Util.getPx(props.width) > 0) {
			css.set('width', `${$ux.Util.addUnit(props.width)}`)
		}
		
		if($ux.Util.getPx(props.height) > 0) {
			css.set('height', `${$ux.Util.addUnit(props.height)}`)
		}
		
		return css
	})
	
	const sliderStyle = computed(():Map<string, any> => {
		let h = props.indicateSize * tabs.value.length
		let css = new Map<string, any>()
		
		css.set('position', props.fixed ? 'fixed' : 'absolute')
		css.set('background-color', `rgba(0, 0, 0, ${props.opacity})`)
		css.set('border-radius', '20px')
		css.set('height', `${h}px`)
		if(props.fixed) {
			css.set('top', `${(windowHeight.value - h) / 2}px`)
		} else {
			css.set('top', `${($ux.Util.getPx(props.height) - h) / 2}px`)
		}
		css.set('right', `${$ux.Util.addUnit(props.right)}`)
		
		return css
	})
	
	const itemStyle = computed(():Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('width', `${props.indicateSize}px`)
		css.set('height', `${props.indicateSize}px`)
		
		return css
	})
	
	const indicateStyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('top', `${indicateTop.value}px`)
		css.set('left', `${indicateRight.value}px`)
		css.set('background-color', indicateColor.value)
		
		return css
	})

	const showIndicate = computed(() : boolean => {
		return isMoving.value && items.value.length > 0
	})
	
	function scroll(e : UniScrollEvent) {
		let y = e.detail.scrollTop
		scrollY.value = y
		
		if(isClicked.value || isMoving.value) {
			return
		}
		
		for (let i = 0; i < tabs.value.length; i++) {
			let tab = tabs.value[i]
			if (y >= tab.startY && y <= tab.endY) {
				activeIndex.value = i
				break
			}
		}
	}
	
	function setPos() {
		let top = 0
		for (let i = 0; i < items.value.length; i++) {
			if(i < activeIndex.value) {
				top += items.value[i].height
			}
		}
		scrollTop.value = top
		
		indicateRight.value = sliderLeft - 65
		indicateTop.value = windowTop.value + sliderTop + activeIndex.value * props.indicateSize - props.indicateSize / 2 - 5
	}
	
	function click(e: MouseEvent, item : UxIndexbarItem, index : number) {
		e.stopPropagation()
		
		clearTimeout(timer)
		clearTimeout(ctimer)
		
		isClicked.value = true
		ctimer = setTimeout(() => {
			isClicked.value = false
		}, 1000)
		
		activeIndex.value = index
		setPos()
		
		emit('click', index, item.name)
		emit('change', index, item.name)
		
		isMoving.value = true
		timer = setTimeout(() => {
			isMoving.value = false
		}, 300);
	}
	
	function _touchmove(y: number) {
		clearTimeout(timer)
		
		isMoving.value = true
		timer = setTimeout(() => {
			isMoving.value = false
		}, 300);
		
		let dis = Math.abs(y) - sliderTop
		dis = Math.max(0, Math.min(dis, sliderHeight))
		
		let index = Math.floor(dis / props.indicateSize)
		index = Math.max(0, Math.min(index, items.value.length-1))
		
		if(activeIndex.value == index) {
			return
		}
		
		activeIndex.value = index
		setPos()
		
		emit('change', index, items.value[index].name)
	}
	
	function slidermove(e : TouchEvent) {
		e.stopPropagation()
		e.preventDefault()
		
		_touchmove(e.changedTouches[0].clientY)
	}
	
	function slidermovePc(e: MouseEvent) {
		if(!isPressed) {
			return
		}
		
		e.stopPropagation()
		e.preventDefault()
		
		_touchmove(e.clientY)
	}
	
	async function calcSize() {
		let top = 0
		tabs.value.forEach(e => {
			e.startY = top
			e.endY = e.startY + e.height
			top = e.endY
		})
		
		let rect = await sliderRef.value?.getBoundingClientRectAsync()!
		sliderTop = rect.top
		sliderLeft = rect.left
		sliderHeight = rect.height
	}
	
	function register(item : UxIndexbarItem) {
		let index = tabs.value.findIndex((el : UxIndexbarItem) : boolean => el.id == item.id)
		if (index != -1) {
			tabs.value.splice(index, 1, item)
		} else {
			tabs.value.push(item)
		}
		
		calcSize()
	}
	
	function unregister(id : string) {
		let index = tabs.value.findIndex((el : UxIndexbarItem) : boolean => el.id == id)
		if (index != -1) {
			tabs.value.splice(index, 1)
		}
		
		calcSize()
	}
	
	onMounted(() => {
		windowTop.value =  uni.getWindowInfo().windowTop
		windowHeight.value = uni.getWindowInfo().windowHeight
		
		setTimeout(() => {
			calcSize()
		}, 50)
	})
	
	defineExpose({
		register,
		unregister
	})
</script>


<style lang="scss">
	
	.ux-indexbar {
		width: 100%;
		position: relative;
		
		&__list {
			width: 100%;
		}
		
		&__slider {
			position: fixed;
			top: 10%;
			right: 0;
			width: 20px;
			height: 80%;
			z-index: 100;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			background-color: rgba(0, 0, 0, 0.2);
			border-radius: 2px;
		}
		
		&__slider--warp {
			width: 100%;
			height: 100%;
			position: relative;
			display: flex;
			flex-direction: column;
			align-items: center;
		}
		
		&__slider--item {
			width: 18px;
			height: 18px;
			border-radius: 18px;
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
			transition-property: top;
			transition-duration: 50ms;
		}
		
		&__slider--text {
			font-size: 10px;
			color: white;
		}
		
		&__indicate{
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
			position: fixed;
			left: 0;
			top: 0;
			width: 45px;
			height: 45px;
			border-radius: 45px 45px 5px 45px;
			transform: rotate(-45deg);
			z-index: 100000;
			transition-property: top;
			transition-duration: 50ms;
			
			&--text{
				font-size: 20px;
				color: white;
				font-weight: bold;
				transform: rotate(45deg);
			}
		}
	}
</style>