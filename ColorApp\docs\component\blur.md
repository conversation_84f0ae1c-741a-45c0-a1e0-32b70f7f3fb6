<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/blur?title=Blur"></Mobile>

# Blur
> 组件类型：UxBlurComponentPublicInstance

实时计算毛玻璃效果 支持android、ios、web

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| view | Any |  | 指定模糊的视图通过el.getAndroidView()、el.getIOSView()获取 |
| radius | Number | 10 | 模糊半径 |
| color | String | white | 颜色 |
| cornerRadius | Number | 0 | 圆角 |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| click | 被点击时触发 |  |
  
  