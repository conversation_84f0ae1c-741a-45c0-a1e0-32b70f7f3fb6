<template>
	<view class="ux-popover" :style="{'z-index': zIndex}">
		<ux-overlay v-if="showMask" :show="show" :show-blur="showBlur" :blur-radius="blurRadius" :blur-color="blurColor" :z-index="zIndex" :opacity="opacity" @close="close"></ux-overlay>
		<view v-if="opened"
			:id="myId"
			:class="['ux-popover__content', `ux-popover__content__${arrowPosition}`, 'transform']"
			:style="[style]">
			<view v-if="arrowPosition == 'left' || arrowPosition == 'top'" 
				ref="arrowRef" 
				class="ux-popover__arrow" 
				:style="[arrowStyle]">
					<!-- #ifndef APP -->
					<canvas class="ux-popover__arrow__canvas" :id="arrowId" :canvas-id="arrowId"></canvas>
					<!-- #endif -->
			</view>
			<view ref="slotRef" class="ux-popover__slot" :style="[slotStyle]">
				<slot></slot>
			</view>
			<view v-if="arrowPosition == 'right' || arrowPosition == 'bottom'" 
				ref="arrowRef" 
				class="ux-popover__arrow" 
				:style="[arrowStyle]">
					<!-- #ifndef APP -->
					<canvas class="ux-popover__arrow__canvas" :id="arrowId" :canvas-id="arrowId"></canvas>
					<!-- #endif -->
			</view>
		</view>
	</view>
</template>

<script setup>
	
	/**
	 * Popup 弹出层
	 * @description 支持任何位置弹出并且自动计算在适当的位置显示
	 * @demo pages/component/popover.uvue
	 * @tutorial https://www.uxframe.cn/component/popover.html
	 * @property {String}			targetId					String | 目标id
	 * @property {Any}				width						Any | 宽度
	 * @property {Any}				offset						Any | 偏移 (默认 10)
	 * @property {Boolean}			align = [true|false]		Boolean | 与目标水平对齐 (默认 false)
	 * @property {String}			backgroundColor				String | 背景色 (默认 #000000)
	 * @property {Boolean}			showMask = [true|false]		Boolean | 显示遮罩 (默认 true)
	 * @property {Boolean}			showBlur = [true|false]		Boolean | 开启模糊背景 (默认 false )
	 * @property {Number}			blurRadius					Number | 模糊半径 (默认 10 )
	 * @property {String}			blurColor					String | 模糊颜色  (默认 rgba(10, 10, 10, 0.3) )
	 * @property {Number}			opacity						Number | 遮罩透明度 0-1 (默认 $ux.Conf.maskAlpha)
	 * @property {Number}			zIndex						Number | 层级z-index (默认 10001)
	 * @event {Function}			change						Function | 显示状态改变时触发
	 * <AUTHOR>
	 * @date 2023-10-03 20:31:11
	 */
	
	import { $ux } from '../../index'
	
	defineOptions({
		name: 'ux-popover'
	})
	
	const emit = defineEmits(['change'])
	
	const props = defineProps({
		targetId: {
			type: String,
			default: ''
		},
		width: {
			default: 150
		},
		offset: {
			default: 10
		},
		align: {
			type: Boolean,
			default: false,
		},
		backgroundColor: {
			type: String,
			default: '#000000'
		},
		showMask: {
			type: Boolean,
			default: true,
		},
		showBlur: {
			type: Boolean,
			default: false
		},
		blurRadius: {
			type: Number,
			default: 10
		},
		blurColor: {
			type: String,
			default: 'rgba(10, 10, 10, 0.3)'
		},
		opacity: {
			type: Number,
			default: 0.2
		},
		zIndex: {
			type: Number,
			default: 10001
		},
	})
	
	const opened = ref(false)
	const show = ref(false)
	
	const popoverTop = ref(0)
	const popoverLeft = ref(0)
	const popoverHeight = ref(0)
	
	const arrowTop = ref(0)
	const arrowLeft = ref(0)
	const arrowWidth = ref(12)
	const arrowHeight = ref(8)
	const arrowPosition = ref('') // right left top bottom
	
	const origin = ref('right top')
	
	const slotRef = ref<Element | null>(null)
	const arrowRef = ref<Element | null>(null)
	
	const arrowId = `ux-popover-${$ux.Random.uuid()}`
	const dpr = uni.getWindowInfo().pixelRatio
	
	const instance = getCurrentInstance()?.proxy
	
	const myId = computed(():string => {
		return `ux-popover__${props.targetId}`
	})
	
	const width = computed(() => {
		return $ux.Util.getPx(props.width)
	})
	
	const offset = computed(() => {
		return $ux.Util.getPx(props.offset)
	})
	
	const style = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('z-index', props.zIndex + 1)
		
		if (arrowPosition.value == 'top' || arrowPosition.value == 'bottom') {
			css.set('width', `${width.value}px`)
			css.set('height', `${popoverHeight.value + arrowHeight.value}px`)
		} else {
			css.set('width', `${width.value + arrowWidth.value}px`)
			css.set('height', `${popoverHeight.value}px`)
		}
		
		css.set('top', `${popoverTop.value}px`)
		css.set('left', `${popoverLeft.value}px`)
		css.set('transform-origin', origin.value)
		css.set('opacity', show.value ? 1 : 0)
		
		return css
	})
	
	const slotStyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('width', `${width.value}px`)
		css.set('height', `${popoverHeight.value}px`)
		css.set('background-color', props.backgroundColor)
		
		return css
	})
	
	const arrowStyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		if (arrowPosition.value == 'top' || arrowPosition.value == 'bottom') {
			css.set('width', `${arrowWidth.value}px`)
			css.set('height', `${arrowHeight.value}px`)
		} else {
			css.set('width', `${arrowHeight.value}px`)
			css.set('height', `${arrowWidth.value}px`)
		}
		
		if (arrowPosition.value == 'top') {
			css.set('margin-left', `${arrowLeft.value}px`)
		} else if (arrowPosition.value == 'right') {
			css.set('margin-top', `${arrowTop.value}px`)
		} else if (arrowPosition.value == 'bottom') {
			css.set('margin-left', `${arrowLeft.value}px`)
		} else if (arrowPosition.value == 'left') {
			css.set('margin-top', `${arrowTop.value}px`)
		}
		
		return css
	})
	
	async function calcPos() {
		const windowWidth = uni.getWindowInfo().windowWidth
		const windowHeight = uni.getWindowInfo().windowHeight
		
		// target 坐标
		let el = uni.getElementById(props.targetId)!
		let rect = await el.getBoundingClientRectAsync()!
		let top = rect?.top ?? 0
		let left = rect?.left ?? 0
		let _width = rect?.width ?? 0
		let height = rect?.height ?? 0
		
		// 高度
		// #ifdef APP-ANDROID || APP-HARMONY || MP
		let _height = 0
		// #endif
		// #ifndef APP-ANDROID || APP-HARMONY || MP
		let _height = 10
		// #endif
		
		// #ifdef MP
		let children: Element[] = slotRef.value?.$vm.$children ?? [] as Element[]
		for (let i = 0; i < children.length; i++) {
			_height += children[i].height ?? 0
		}
		// #endif
		// #ifndef MP
		let children: Element[] = slotRef.value?.children ?? [] as Element[]
		for (let i = 0; i < children.length; i++) {
			let _rect = children[i].getBoundingClientRect()
			_height += _rect.height
		}
		// #endif
		
		if (_height > windowHeight * (3 / 5)) {
			_height = windowHeight * (3 / 5)
		}
		
		popoverHeight.value = _height
		
		// 相对中点位置方向
		let isLeft = left + _width / 2 < windowWidth / 2
		let isTop = top + height / 2 < windowHeight / 2
		
		// 水平对齐
		if (props.align) {
			if (isLeft) {
				// left = 目标left + 目标宽度 + 偏移
				popoverLeft.value = left + _width + offset.value
			} else {
				// left = 目标left - 偏移 - 本身宽
				popoverLeft.value = left - offset.value - width.value
			}
		
			if (isTop) {
				// top = 目标top
				popoverTop.value = top
		
				// 箭头top
				arrowTop.value = 8
			} else { // 相对目标下方向
				// top = 目标top + 目标高度 - 偏移 - 本身高度
				popoverTop.value = top + height - offset.value - popoverHeight.value
		
				// 箭头top
				arrowTop.value = popoverHeight.value - arrowWidth.value - 8
			}
		
			// 箭头方向
			arrowPosition.value = isLeft ? 'left' : 'right'
		
			// 动画方向
			origin.value = `${isLeft ? 'left' : 'right'} ${isTop ? 'bottom' : 'top'}`
		} else {
			if (isLeft) {
				// left = 目标left
				popoverLeft.value = left
		
				// 越界处理
				if (popoverLeft.value < offset.value) {
					popoverLeft.value = offset.value
				}
			} else {
				// left = 目标left + 目标宽度 - 本身宽度
				popoverLeft.value = left + _width - width.value
		
				// 越界处理
				if (popoverLeft.value + width.value + offset.value > windowWidth) {
					popoverLeft.value = windowWidth - offset.value - width.value
				}
			}
		
			if (isTop) {
				// top = 目标top + 目标高 + 偏移
				popoverTop.value = top + height + offset.value
			} else {
				// top = 目标top - 偏移 - 本身高度
				popoverTop.value = top - offset.value - popoverHeight.value
			}
		
			// 箭头left
			arrowLeft.value = isLeft ? 8 : (width.value - arrowWidth.value - 8)
		
			// 箭头
			arrowPosition.value = isTop ? 'top' : 'bottom'
		
			// 动画方向
			origin.value = `${isLeft ? 'left' : 'right'} ${isTop ? 'top' : 'bottom'}`
		}
	}
	
	async function darwArrow() {
		// #ifdef APP
		const ctx = arrowRef.value?.getDrawableContext()!
		// #endif
		// #ifndef APP
		const context = await $ux.Util.createCanvasContext(arrowId, instance)
		const ctx = context.getContext('2d')!
		ctx.canvas.width = ctx.canvas.offsetWidth * dpr
		ctx.canvas.height = ctx.canvas.offsetHeight * dpr
		ctx.scale(dpr, dpr)
		// #endif
		
		ctx.beginPath()
			
		if (arrowPosition.value == 'top') {
			ctx.moveTo(0, arrowHeight.value)
			ctx.lineTo(arrowWidth.value, arrowHeight.value)
			ctx.lineTo(arrowWidth.value / 2, 0)
			ctx.lineTo(0, arrowHeight.value)
		} else if (arrowPosition.value == 'bottom') {
			ctx.moveTo(0, 0)
			ctx.lineTo(arrowWidth.value, 0)
			ctx.lineTo(arrowWidth.value / 2, arrowHeight.value)
			ctx.lineTo(0, 0)
		} else if (arrowPosition.value == 'right') {
			ctx.moveTo(0, 0)
			ctx.lineTo(0, arrowWidth.value)
			ctx.lineTo(arrowHeight.value, arrowWidth.value / 2)
			ctx.lineTo(0, 0)
		} else if (arrowPosition.value == 'left') {
			ctx.moveTo(arrowHeight.value, 0)
			ctx.lineTo(arrowHeight.value, arrowWidth.value)
			ctx.lineTo(0, arrowWidth.value / 2)
			ctx.lineTo(arrowHeight.value, 0)
		}
			
		ctx.closePath()
		ctx.fillStyle = props.backgroundColor
		ctx.fill()
		
		// #ifdef APP
		ctx.update()
		// #endif
	}
	
	function animTo(_open : boolean) {
		if (uni.getElementById(myId.value) == null) {
			return
		}
		
		let el = uni.getElementById(myId.value)
		
		if (_open) {
			show.value = true
			emit('change', show.value)
		
			setTimeout(() => {
				darwArrow()
		
				el?.style?.setProperty('opacity', 1)
				el?.style?.setProperty('transform', 'scale(1)')
			}, 50)
		} else {
			el?.style?.setProperty('opacity', 0)
			el?.style?.setProperty('transform', 'scale(0.9)')
		
			setTimeout(() => {
				show.value = false
				opened.value = false
				arrowPosition.value = ''
				emit('change', show.value)
			}, 120)
		}
	}
	
	function open() {
		let target = uni.getElementById(props.targetId)
		
		if (target == null) {
			console.error(`[ux-popover]配置错误: targetId[${props.targetId}]未找到`)
			return
		}
		
		opened.value = true
		setTimeout(() => {
			calcPos()
			animTo(true)
		}, 100)
	}
	
	function close() {
		animTo(false)
	}
	
	defineExpose({
		open,
		close
	})
</script>

<style lang="scss">
	.ux-popover {
		position: fixed;
		z-index: 10000;
		
		.ux-popover__content {
			position: fixed;
			display: flex;
			align-items: flex-start;
			z-index: 10000;
			opacity: 0;
			transform: scale(0.9);
			background-color: transparent;

			&__top {
				flex-direction: column;
				align-items: flex-start;
			}

			&__bottom {
				flex-direction: column;
				align-items: flex-start;
			}

			&__left {
				flex-direction: row;
				align-items: flex-start;
			}

			&__right {
				flex-direction: row;
				align-items: flex-start;
			}

			.ux-popover__slot {
				border-radius: 5px;
			}

			.ux-popover__arrow {
				background-color: transparent;
				
				&__canvas {
					width: 100%;
					height: 100%;
				}
			}
		}
	}

	.transform {
		transform-origin: right top;
		transition-property: transform, opacity;
		transition-duration: 0.1s;
		transition-timing-function: ease-out;
	}
</style>