import { ref } from 'vue'
import { Conf } from './conf/index'
import { Store } from './store/index'
import { Locale } from './i18n/index'
import { Animation } from './anim/index'
import { Color } from './color/index'
import { Dates } from './date/index'
import { Format } from './format/index'
import { Verify } from './verify/index'
import { Random } from './random/index'
import { Encrypt } from './encrypt/index'
import { Objs } from './objs/index'
import { Util } from './util/index'
import { Toolkit } from './toolkit/index'

/**
 * UxFrame Core
 */
export class Core {
	
	/**
	 * 状态管理
	 */
	Store = new Store()
	
	/**
	 * 国际化
	 */
	Locale = new Locale()
	
	/**
	 * 颜色库
	 */
	Color = new Color()
	
	/**
	 * 日期库
	 */
	Date = new Dates()
	
	/**
	 * 动画库
	 */
	Anim = new Animation()
	
	// /**
	//  * Canvas画布绘制函数
	//  */
	// Canvas = new useCanvas(this)
	
	// /**
	//  * Socket函数
	//  */
	// Socket = new useSocket(this)
	
	/**
	 * 格式化函数
	 */
	Fmt = new Format()
	
	/**
	 * 对象操作函数
	 */
	Objs = new Objs()
	
	/**
	 * 随机数函数
	 */
	Random = new Random()
	
	/**
	 * 校验函数
	 */
	Verify = new Verify()
	
	/**
	 * 数据加解密
	 */
	Encrypt = new Encrypt()
	
	/**
	 * 常用工具函数
	 */
	Util = new Util(this)
	
	/**
	 * 工具集
	 */
	Toolkit = new Toolkit()
	
	/**
	 * 配置
	 */
	Conf = new Conf()
}