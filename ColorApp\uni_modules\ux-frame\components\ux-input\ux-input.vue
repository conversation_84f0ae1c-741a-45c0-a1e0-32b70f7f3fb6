<template>
	<view class="ux-input" :style="[style, xstyle]">
		<view v-if="prefix != '' || $slots['prefix'] != null" class="ux-input__prefix">
			<slot name="prefix">
				<ux-icon :type="prefix" :size="iconSize" :custom-family="customFamily" :xstyle="[prefixStyle]"></ux-icon>
			</slot>
		</view>
		<input class="ux-input__field" 
				:style="[inputStyle]"
				:name="name" 
				:type="type"
				:value="value"
				v-model="innerValue"
				:password="password" 
				:placeholder="placeholder" 
				:placeholder-style="placeholderStyle" 
				:placeholder-class="placeholderClass" 
				:maxlength="maxlength" 
				:cursor-spacing="cursorSpacing" 
				:auto-focus="autoFocus" 
				:focus="focus"
				:confirm-type="confirmType" 
				:confirm-hold="confirmHold" 
				:cursor="cursor" 
				:fixed="fixed"
				:selection-start="selectionStart" 
				:selection-end="selectionEnd" 
				:adjust-position="adjustPosition" 
				:hold-keyboard="holdKeyboard"
				:disabled="disabled || readonly" 
				@input="inputChange" 
				@focus="focusChange" 
				@blur="blurChange" 
				@confirm="confirmChange" 
				@keyboardheightchange="keyboardheightChange"/>
		
		<view v-if="suffix != '' || $slots['suffix'] != null" class="ux-input__suffix">
			<slot name="suffix">
				<ux-icon :type="suffix" :size="iconSize" :custom-family="customFamily" :xstyle="[suffixStyle]"></ux-icon>
			</slot>
		</view>
		
		<!-- #ifdef H5 -->
		<view v-if="disabled || readonly" class="ux-input__mask"></view>
		<!-- #endif -->
		
		<view v-if="clearable && focused && innerValue != ''" class="ux-input__clearable" @click="onClear">
			<ux-icon type="closecircle-outline" color="#acacac" :size="16"></ux-icon>
		</view>
	</view>
</template>

<script setup lang="ts">

	/**
	* Input 输入框
	* @description 扩展支持聚焦高亮、前后置图标、边框类型、输入框形状、可清空
	* @demo pages/component/input.uvue
	* @tutorial https://www.uxframe.cn/component/input.html
	* @property {String} 			theme=[primary|warning|success|error|info]	String | 主题颜色 (默认 primary)
	* @value primary 	主色
	* @value warning 	警告
	* @value success 	成功
	* @value error 		错误
	* @value info 		文本
	* @property {String} 			name													String | 表单的控件名称，作为键值对的一部分与表单(form组件)一同提交
	* @property {String} 			value													String | 输入框的初始内容
	* @property {String} 			type=[text|number|digit|tel]				String | 输入类型（默认 text)
	* @value text 文本输入键盘
	* @value number 数字输入键盘
	* @value digit 带小数点数字输入键盘
	* @value tel 电话输入键盘
	* @property {Boolean} 			password=[true|false]						Boolean | 是否是密码类型（默认 false)
	* @property {Boolean}			clearable=[true|false]						Boolean | 是否显示清除控件 (默认 false)
	* @property {String} 			placeholder									String | 输入框为空时占位符
	* @property {String} 			placeholderStyle							String | 指定 placeholder 的样式
	* @property {String} 			placeholderClass							String | 指定 placeholder 的样式类
	* @property {Number} 			maxlength									Number | 最大输入长度，设置为 -1 的时候不限制最大长度（默认 140)
	* @property {Number}			cursorSpacing								Number | 指定光标与键盘的距离，单位 px 。取 input 距离底部的距离和 cursor-spacing 指定的距离的最小值作为光标与键盘的距离（默认 0)
	* @property {Boolean} 			autoFocus=[true|false]						Boolean | 自动获取焦点，与focus属性对比，此属性只会首次生效（默认 false)
	* @property {Boolean}			focus=[true|false]							Boolean | 获取焦点（默认 false)
	* @property {String}			confirmType=[send|search|next|go|done]		String | 设置键盘右下角按钮的文字（默认 done)
	* @value send 发送
	* @value search 搜索
	* @value next 下一个
	* @value go 前往
	* @value done 完成
	* @property {Boolean}			confirmHold=[true|false]					Boolean | 点击键盘右下角按钮时是否保持键盘不收起（默认 false)
	* @property {Number} 			cursor										Number | 指定focus时的光标位置（默认 0)
	* @property {Number} 			selection-start								Number | 光标起始位置，自动聚集时有效，需与selection-end搭配使用（默认 -1)
	* @property {Number} 			selection-end								Number | 光标结束位置，自动聚集时有效，需与selection-satrt搭配使用（默认 -1)
	* @property {Boolean} 			adjustPosition=[true|false]					Boolean | 键盘弹起时，是否自动上推页面（默认 true)
	* @property {Boolean} 			holdKeyboard=[true|false]					Boolean | focus时，点击页面的时候不收起键盘（默认 false)
	* @property {String}			align=[left|center|right]					String | 输入框内容对齐方式 (默认 left)
	* @value left 左对齐
	* @value center 居中
	* @value right 右对齐
	* @property {Any}				size										Any | 输入框字体的大小 (默认 $ux.Conf.fontSize)
	* @property {String}			sizeType=[normal|small|big]					String | 字体大小类型（默认 small ）
	* @value normal 正常
	* @value small 较小
	* @value big 较大
	* @property {String}			color										String | 输入框字体颜色 (默认 $ux.Conf.fontColor)
	* @property {String} 			darkColor=[none|auto|color]					String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	* @value none 不显示
	* @value auto 自动适配深色模式
	* @value color 其他颜色
	* @property {String}			background									String | 输入框背景颜色 (默认 #f0f0f0)
	* @property {String} 			backgroundDark=[none|auto|color]			String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色 (默认 #333333)
	* @value none 不显示
	* @value auto 自动适配深色模式
	* @value color 其他颜色
	* @property {String}			disabledColor								String | 输入框禁用颜色
	* @property {String} 			disabledDark=[none|auto|color]				String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	* @value none 不显示
	* @value auto 自动适配深色模式
	* @value color 其他颜色
	* @property {String}			prefix										String | 输入框前置图标
	* @property {String}			prefixStyle									String | 前置图标样式，对象或字符串
	* @property {String}			suffix										String | 输入框后置图标
	* @property {String}			suffixStyle									String | 后置图标样式，对象或字符串
	* @property {Any}				iconSize									Any | 图标大小 (默认 20)
	* @property {String}			customFamily								String | 字体family
	* @property {String}			border=[surround|bottom|none]				String | 边框类型 (默认 surround)
	* @value surround 四周边框
	* @value bottom 底部边框
	* @value none 无边框
	* @property {String}			borderColor									String | 输入框边框颜色
	* @property {String} 			borderColorDark=[none|auto|color]			String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	* @value none 不显示
	* @value auto 自动适配深色模式
	* @value color 其他颜色
	* @property {Number}			borderWidth									Number | 边框宽 (默认 1)
	* @property {Any}				borderRadius								Any | 圆角 (默认 10)
	* @property {Boolean}			focusBorder=[true|false]					Boolean | 聚焦时显示边框 (默认 true)
	* @property {Boolean}			fixed=[true|false]							Boolean | 如果input是在一个position:fixed的区域，需要显示指定属性fixed为true (默认 false)
	* @property {Boolean}			readonly=[true|false]						Boolean | 是否只读，与disabled不同之处在于disabled会置灰组件，而readonly则不会 (默认 false)
	* @property {String}			shape=[circle|square]						String | 输入框形状 (默认 square)
	* @value circle 圆形
	* @value square 方形
	* @property {Any}				hPadding									Any | 水平内边距 单位px
	* @property {Any}				vPadding									Any | 垂直内边距 单位px
	* @property {Boolean} 			disabled=[true|false]						Boolean | 是否禁用（默认 false)
	* @property {Array}				margin										Array | 边距 [上 右 下 左] [上下 左右] [上下左右]
	* @property {Any}				mt											Any | 距上 单位px
	* @property {Any}				mr											Any | 距右 单位px
	* @property {Any}				mb											Any | 距下 单位px
	* @property {Any}				ml											Any | 距左 单位px
	* @property {Array}				padding										Array | 填充 [上 右 下 左] [上下 左右] [上下左右]
	* @property {Any}				pt											Any | 上内边距 单位px
	* @property {Any}				pr											Any | 右内边距 单位px
	* @property {Any}				pb											Any | 下内边距 单位px
	* @property {Any}				pl											Any | 左内边距 单位px
	* @property {Array}				xstyle										Array<any> | 自定义样式
	* @event {Function} 			clear 										Function |  点击清空时触发
	* @event {Function} 			input 										Function |  当键盘输入时，触发input事件，处理函数可以直接 return 一个字符串，将替换输入框的内容
	* @event {Function} 			focus 										Function |  输入框聚焦时触发
	* @event {Function} 			blur 										Function |  输入框失去焦点时触发
	* @event {Function} 			confirm 									Function |  点击完成按钮时触发
	* @event {Function} 			keyboardheightchange 						Function |  键盘高度发生变化的时候触发此事件
	* <AUTHOR>
	* @date 2024-12-01 14:32:28
	*/
	
	import { ref, computed, watch, getCurrentInstance } from 'vue'
	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	import { useForegroundColor, useFontColor, useFontSize, useThemeColor, useBorderColor, UxThemeType } from '../../libs/use/style.uts'
	
	defineOptions({
		name: 'ux-input',
		mixins: [xstyleMixin]
	})
	
	const emit = defineEmits(['update:modelValue', 'input', 'focus', 'blur', 'confirm', 'clear', 'keyboardheightchange'])
	
	const props = defineProps({
		theme: {
			type: String,
			default: 'primary'
		},
		name: {
			type: String,
			default: ''
		},
		value: {
			type: String,
			default: ''
		},
		modelValue: {
			type: String,
			default: ''
		},
		type: {
			type: String,
			default: 'text'
		},
		password: {
			type: Boolean,
			default: false
		},
		clearable: {
			type: Boolean,
			default: false
		},
		showWords: {
			type: Boolean,
			default: false
		},
		placeholder: {
			type: String,
			default: ''
		},
		placeholderStyle: {
			type: String,
			default: 'color: #999'
		},
		placeholderClass: {
			type: String,
			default: ''
		},
		maxlength: {
			type: Number,
			default: 140
		},
		cursorSpacing: {
			type: Number,
			default: 0
		},
		autoFocus: {
			type: Boolean,
			default: false
		},
		focus: {
			type: Boolean,
			default: false
		},
		confirmType: {
			type: String,
			default: 'done'
		},
		confirmHold: {
			type: Boolean,
			default: false
		},
		cursor: {
			type: Number,
			default: 0
		},
		fixed: {
			type: Boolean,
			default: false
		},
		selectionStart: {
			type: Number,
			default: 0
		},
		selectionEnd: {
			type: Number,
			default: 0
		},
		adjustPosition: {
			type: Boolean,
			default: true
		},
		holdKeyboard: {
			type: Boolean,
			default: false
		},
		align: {
			type: String,
			default: 'left'
		},
		size: {
			default: 0
		},
		sizeType: {
			type: String,
			default: 'small'
		},
		color: {
			type: String,
			default: ''
		},
		darkColor: {
			type: String,
			default: ''
		},
		background: {
			type: String,
			default: '#f0f0f0'
		},
		backgroundDark: {
			type: String,
			default: '#333333'
		},
		disabledColor: {
			type: String,
			default: ''
		},
		disabledDark: {
			type: String,
			default: ''
		},
		prefix: {
			type: String,
			default: ''
		},
		prefixStyle: {
			type: String,
			default: ''
		},
		suffix: {
			type: String,
			default: ''
		},
		suffixStyle: {
			type: String,
			default: ''
		},
		iconSize:{
			default: 0
		},
		customFamily: {
			type: String,
			default: ''
		},
		border: {
			type: String,
			default: 'surround'
		},
		borderWidth: {
			type: Number,
			default: 1
		},
		borderColor: {
			type: String,
			default: '#d9d9d9'
		},
		borderColorDark: {
			type: String,
			default: '#111111'
		},
		borderRadius: {
			default: 10
		},
		focusBorder: {
			type: Boolean,
			default: true
		},
		readonly: {
			type: Boolean,
			default: false
		},
		shape: {
			type: String,
			default: 'square'
		},
		hPadding:{
			default: 14
		},
		vPadding:{
			default: 12
		},
		disabled: {
			type: Boolean,
			default: false
		},
	})
	
	const innerValue = ref('')
	const focused = ref(false)
	
	const value = computed(() : string => {
		return props.value
	})
	
	const modelValue = computed(() : string => {
		return props.modelValue
	})
	
	const fontColor = computed((): string => {
		return useFontColor(props.color, props.darkColor)
	})
	
	const fontSize = computed(() : number => {
		let type = props.sizeType == 'big' ? 2 : (props.sizeType == 'small' ? 1 : 0)
		return useFontSize($ux.Util.getPx(props.size), type)
	})
	
	const backgroundColor = computed((): string => {
		return useForegroundColor(props.background, props.backgroundDark)
	})
	
	const borderColor = computed((): string => {
		if(!focused.value || !props.focusBorder || props.disabled || props.readonly) {
			return useBorderColor(props.borderColor, props.borderColorDark) 
		} else {
			return useThemeColor(props.theme as UxThemeType) 
		}
	})
	
	const fontDisabledColor = computed(() : string => {
		return $ux.Color.getLightColor(fontColor.value, 40)
	})
	
	const backgroundDisabledColor = computed(() : string => {
		if(props.disabledColor != '') {
			return useForegroundColor(props.disabledColor, props.disabledDark)
		} else {
			return $ux.Color.getLightColor(backgroundColor.value, 40)
		}
	})

	const style = computed(() => {
		let css = {}
		
		css['background-color'] = props.disabled ? backgroundDisabledColor.value : backgroundColor.value
		
		if(props.border == 'bottom') {
			css['border-bottom'] = `${props.borderWidth}px solid ${css['background-color']}`
		} else if(props.border == 'surround') {
			css['border'] = `${props.borderWidth}px solid ${css['background-color']}`
		}
		
		if(focused.value && props.focusBorder) {
			if(props.border == 'surround') {
				css['border'] = `${props.borderWidth}px solid ${borderColor.value}`
			} else if(props.border == 'bottom') {
				css['border-bottom'] = `${props.borderWidth}px solid ${borderColor.value}`
			}
		}
		
		css['border-radius'] = `${props.shape == 'circle' ? $ux.Util.addUnit(100) : $ux.Util.addUnit(props.borderRadius)}`
		
		css = $ux.Util.xStyle(css, props.margin, props.mt, props.mr, props.mb, props.ml, props.padding, props.pt, props.pr, props.pb, props.pl)
		
		return css
	})
	
	const inputStyle = computed(() => {
		
		let slots = getCurrentInstance()?.slots
		
		let paddingLeft = props.prefix == '' && slots != null && slots['prefix'] == null ? props.hPadding : 0
		let paddingRight = props.suffix == '' && slots != null && slots['suffix'] == null ? props.hPadding : 0
		
		let css = {}
		
		css['text-align'] = props.align
		css['color'] = props.disabled ? fontDisabledColor.value : fontColor.value
		css['font-size'] = $ux.Util.addUnit(fontSize.value)
		css['padding'] = `${$ux.Util.addUnit(props.vPadding)} ${$ux.Util.addUnit(paddingRight)} ${$ux.Util.addUnit(props.vPadding)} ${$ux.Util.addUnit(paddingLeft)}`
		
		// #ifdef WEB
		css['cursor'] = props.disabled? 'not-allowed' : 'pointer'
		// #endif
		
		return css
	})
	
	watch(value, () => {
		if(modelValue.value == '') {
			innerValue.value = value.value
		}
	}, {immediate: true})
	
	watch(modelValue, () => {
		if(value.value == '') {
			innerValue.value = modelValue.value
		}
	}, {immediate: true})
	
	function inputChange(e: InputEvent) {
		innerValue.value = e.detail.value
		
		emit('update:modelValue',innerValue.value)
		emit('input', innerValue.value)
	}
	
	function focusChange(e: InputFocusEvent) {
		focused.value = true
		innerValue.value = e.detail.value
		
		emit('focus', innerValue.value)
	}
	
	function blurChange(e: InputBlurEvent) {
		innerValue.value = e.detail.value
			
		setTimeout(function() {
			focused.value = false
			emit('blur', innerValue.value)
		}, 100);
	}
	
	function confirmChange(e: InputConfirmEvent) {
		innerValue.value = e.detail.value
		
		emit('confirm', innerValue.value)
	}
	
	function keyboardheightChange(e: InputKeyboardHeightChangeEvent) {
		emit('keyboardheightchange', e)
		
		focused.value = e.detail.height as number != 0
	}
	
	function onClear(e: MouseEvent) {
		innerValue.value = ''
		
		emit('clear', '')
		emit('update:modelValue', '')
		emit('input', '')
		
		e.stopPropagation()
	}
</script>

<style lang="scss" scoped>
	/* #ifdef H5 */
	uni-view {
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: stretch;
		align-content: stretch;
		justify-content: flex-start;
		box-sizing: border-box;
		min-height: 0px;
		min-width: 0px;
		overflow: hidden;
	}
	/* #endif */
	
	.ux-input {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		position: relative;
		
		&__field {
			flex: 1;
		}

		&__prefix {
			min-width: 30px;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
		}

		&__suffix {
			min-width: 30px;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
		}
		
		&__clearable {
			position: absolute;
			right: 5px;
			width: 18px;
			height: 18px;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
		}
		
		&__mask {
			position: absolute;
			width: 100%;
			height: 100%;
		}
	}
</style>