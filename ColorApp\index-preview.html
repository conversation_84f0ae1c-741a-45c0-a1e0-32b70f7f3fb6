<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ColorApps - 首页预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f8f8;
            min-height: 100vh;
        }
        
        .main {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
            background-color: #f8f8f8;
        }
        
        .header {
            padding: 20px;
            background-color: #007AFF;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .title {
            color: white;
            font-size: 18px;
            font-weight: bold;
        }
        
        .content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .demo-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .demo-item {
            background-color: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            display: flex;
            flex-direction: row;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid #f0f0f0;
            text-decoration: none;
            color: inherit;
        }
        
        .demo-item:hover {
            background-color: #f8f9ff;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.15);
        }
        
        .demo-item:active {
            transform: scale(0.98);
        }
        
        .demo-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 6px;
        }
        
        .demo-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            line-height: 1.4;
        }
        
        .demo-desc {
            font-size: 14px;
            color: #666;
            line-height: 1.4;
        }
        
        .demo-tags {
            display: flex;
            flex-direction: row;
            gap: 8px;
            margin-top: 4px;
        }
        
        .demo-tag {
            background-color: #e8f4fd;
            color: #007AFF;
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            border: 1px solid #d1e9ff;
        }
        
        .demo-arrow {
            font-size: 18px;
            color: #007AFF;
            margin-left: 15px;
            font-weight: bold;
        }
        
        .preview-note {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .preview-note h3 {
            margin-bottom: 8px;
            font-size: 16px;
        }
        
        .preview-note p {
            font-size: 14px;
            opacity: 0.9;
        }
        
        @media (max-width: 768px) {
            .content {
                padding: 15px;
            }
            
            .demo-item {
                padding: 15px;
            }
            
            .demo-tags {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <div class="main">
        <div class="header">
            <div class="title">ColorApps - uni-app x</div>
        </div>
        
        <div class="content">
            <div class="preview-note">
                <h3>📱 首页预览</h3>
                <p>这是 index.uvue 页面的预览效果，已添加 PageRender 组件演示入口</p>
            </div>
            
            <div class="demo-list">
                <a href="page-render-preview.html" class="demo-item">
                    <div class="demo-content">
                        <div class="demo-title">🚀 PageRender 组件</div>
                        <div class="demo-desc">基于 JSON 配置的动态页面渲染器</div>
                        <div class="demo-tags">
                            <span class="demo-tag">动态渲染</span>
                            <span class="demo-tag">JSON配置</span>
                        </div>
                    </div>
                    <div class="demo-arrow">></div>
                </a>
                
                <a href="form-builder-preview.html" class="demo-item">
                    <div class="demo-content">
                        <div class="demo-title">📝 FormBuilder 组件</div>
                        <div class="demo-desc">基于 FieldConfig 的动态表单构建器</div>
                        <div class="demo-tags">
                            <span class="demo-tag">动态表单</span>
                            <span class="demo-tag">字段配置</span>
                            <span class="demo-tag">验证规则</span>
                        </div>
                    </div>
                    <div class="demo-arrow">></div>
                </a>
                
                <a href="mp-html-preview.html" class="demo-item">
                    <div class="demo-content">
                        <div class="demo-title">📄 mp-html 组件</div>
                        <div class="demo-desc">HTML解析与原生渲染组件</div>
                    </div>
                    <div class="demo-arrow">></div>
                </a>
                
                <a href="component-test-preview.html" class="demo-item">
                    <div class="demo-content">
                        <div class="demo-title">🧪 组件动态测试</div>
                        <div class="demo-desc">动态加载和测试项目中的组件</div>
                    </div>
                    <div class="demo-arrow">></div>
                </a>
                
                <div class="demo-item" style="opacity: 0.6; cursor: not-allowed;">
                    <div class="demo-content">
                        <div class="demo-title">🔮 更多组件</div>
                        <div class="demo-desc">敬请期待...</div>
                    </div>
                    <div class="demo-arrow">></div>
                </div>
            </div>
            
            <div style="margin-top: 30px; padding: 20px; background: #f0f8ff; border-radius: 8px; border-left: 4px solid #007AFF;">
                <h4 style="color: #007AFF; margin-bottom: 10px;">✨ 更新内容</h4>
                <ul style="color: #666; line-height: 1.6; padding-left: 20px;">
                    <li>添加了 PageRender 组件演示入口</li>
                    <li>优化了页面布局和视觉效果</li>
                    <li>增加了组件标签和图标</li>
                    <li>改进了交互动画效果</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>