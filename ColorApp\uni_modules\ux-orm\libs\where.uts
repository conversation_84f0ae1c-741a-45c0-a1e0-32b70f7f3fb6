import { UxSqlWhere } from '@/uni_modules/ux-sqlite'

export class WhereWrap {
	
	private name: string
	private value ?: any
	private operator: string
	
	constructor(name: string, value: any | null, oper: string) {
	    this.name = name
		this.value = value
		this.operator = oper
	}
	
	getWhereSql(): string {
		let _where: string
		
		if(this.operator == 'between' || this.operator == 'not between') {
			let vals = this.value as any[]
			_where = `(${this.name} ${this.operator} ${vals[0]} AND ${vals[1]})`
		} else if(this.operator == 'in' || this.operator == 'not in') {
			let _vals = this.value as any[]
			let vals: string[] = []
			for (let i = 0; i < _vals.length; i++) {
				vals.add(vals[i].toString())
			}
			_where = `${this.name} ${this.operator} (${vals.join(', ')})`
		} else if(this.operator == 'is' || this.operator == 'is not') {
			_where = `${this.name} ${this.operator} ${this.value}`
		} else {
			_where = `${this.name} ${this.operator} '${this.value}'`
		}
		
		return _where
	}
	
	getWhere(): UxSqlWhere {
		return {
			name: this.name,
			value: this.value,
			operator: this.operator
		} as UxSqlWhere
	}
}
