<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/guide?title=Guide"></Mobile>

# Guide
> 组件类型：UxGuideComponentPublicInstance

支持动画效果，支持跳过和插槽自定义

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| default | Slot |  | 默认插槽 |
| datas | Array |  | 数据 |
| width | Any | 300 | 宽度 |
| skip | Boolean | true | 跳过 |
| offset | Any | 5 | 偏移 |
| opacity | Number | 0.8 | 遮罩透明度0-1 |
| confirmText | String | '完成' | 完成按钮文字 |
| autoscroll | Boolean | true | 自动滚动到锚点 |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| scroll | 锚点无法触达需要滚动时触发 |  |
| change | 步骤改变时触发 |  |
| skip | 跳过时触发 |  |
| end | 结束时触发 |  |
  
  