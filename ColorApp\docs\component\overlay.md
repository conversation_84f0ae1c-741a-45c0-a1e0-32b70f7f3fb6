<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/overlay?title=Overlay"></Mobile>

# Overlay
> 组件类型：UxOverlayComponentPublicInstance

支持透明度、遮罩点击关闭

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| show | Boolean | fasle | 显隐状态 |
| showBlur | Boolean | false | 开启模糊背景 |
| blurRadius | Number | 10 | 模糊半径 |
| blurColor | String | rgba(10,10,10,0.3) | 模糊颜色 |
| maskClose | Boolean | true | 点击遮罩是否关闭 |
| opacity | Number | `$ux.Conf.maskAlpha` | 遮罩透明度0-1 |
| fixedOpacity | Boolean | true | 固定透明度 |
| zIndex | Number | 10001 | 层级z-index |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| close | 点击遮罩关闭时触发 |  |
  
  