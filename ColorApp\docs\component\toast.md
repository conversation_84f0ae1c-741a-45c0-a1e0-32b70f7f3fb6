<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/toast?title=Toast"></Mobile>

# Toast
> 组件类型：UxToastComponentPublicInstance

支持上下左右位置显示，支持拖拽关闭、自动关闭，内置多种关闭动画

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| [position](#position) | String | top | 显示位置 |
| [anim](#anim) | String | fade | 动画效果 |
| max | Number | 0不限制 | 最大存在个数 |
| offset | Any | 15 | 偏移 |
| threshold | Any | 100 | 移动阈值 |
| logoWidth | Any | 30 | Logo宽 |
| logoHeight | Any | 30 | Logo高 |
| logoRadius | Any | 10 | Logo圆角 |
| duration | Number | 700 | 关闭过渡时间 |
| autoclose | Boolean | true | 自动关闭 |
| clickclose | Boolean | true | 点击关闭 |
| touchclose | Boolean | false | 长按拖拽关闭暂只支持Web |

### [position](#position)

| 值   | 说明 |
|:------:|:----:|
| center居中显示
 |  |
| top居上显示
 |  |
| right居右上显示
 |  |
| bottom居下显示
 |  |
| left居左上显示
 |  |

### [anim](#anim)

| 值   | 说明 |
|:------:|:----:|
| fade渐变过渡
 |  |
| none无动画
 |  |

