
/**
 * 本插件由UxFrame授权予插件购买者个人/公司单个项目专用，请尊重知识产权，勿私下传播，违者追究法律责任。
 * <AUTHOR>
 * @date 2023-12-01 00:00:00
 */

import { Core } from './libs/core/core'
import type { UxConf } from './libs/core/conf/def'
import type { UxPage } from './libs/types/types'
import type { UxChart } from './libs/types/chart'

/**
 * 导出组件类型
 */
export type * from './libs/types/types'

/**
 * 导出图表类型
 */
export type * from './libs/types/chart'

/**
 * 导出日期类型
 */
export type * from './libs/types/date'

/**
 * 导出颜色类型
 */
export type * from './libs/types/color'

/**
 * 导出use函数
 */
export * from './libs/use/style'
export * from './libs/use/resize'
export * from './libs/use/darkmode'

/**
 * 导出核心类
 */
export * from './libs/core/core'
export const $ux: Core = new Core()

// 安装参数
let opts: UxFrameInstallOptions | null = null

/**
 * 安装器
 */
// #ifdef APP
export const uxframe = (options: UxFrameInstallOptions): VuePlugin => {
// #endif
// #ifndef APP
export const uxframe = (options: UxFrameInstallOptions) => {
// #endif
	opts = options
	
	if(opts!.conf != null) {
		$ux.Conf.init(opts!.conf!)
	}
	
	const _install = (app: VueApp) => {
		
		// 全局挂载$ux
		app.config.globalProperties.$ux = $ux
		
		// 国际化
		if(opts!.i18n != null) {
			app.use($ux.Locale.install(opts!.i18n!))
		}
	}
	
	// #ifdef UNI-APP-X
	return definePlugin({
		install: _install
	})
	// #endif
	
	// #ifndef UNI-APP-X
	return _install
	// #endif
}

export type UxFrameInstallOptions = {
	/**
	 *  配置
	 */
	conf ?: UxConf,
	/**
	 * 国际化
	 */
	i18n ?: UTSJSONObject
}