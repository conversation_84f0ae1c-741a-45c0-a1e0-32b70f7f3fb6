import { UxOpenWebOptions } from '../interface.uts'
import { UxPlusSuccessCallbackImpl, UxPlusErrorCallbackImpl } from '../unierror.uts'

let mask: HTMLDivElement | null = null

export function show(options : UxOpenWebOptions) {
	loadFont()
	
	let darkMode =  false
	let data = uni.getStorageSync('UxFrameConf')
	
	// #ifdef UNI-APP-X
	if(data instanceof UTSJSONObject) {
		darkMode = data.getBoolean('darkMode') ?? false
	}
	// #endif
	// #ifndef UNI-APP-X
	if(typeof data == 'object') {
		darkMode = data['darkMode'] ?? false
	}
	// #endif
	
	let _background = document.documentElement.style.backgroundColor
	// #ifdef UNI-APP-X
	// @ts-expect-error
	let _backgroundColorContent = __uniConfig.globalStyle['backgroundColorContent']
	// #endif
	
	let background = darkMode ? '#000' : '#d5d5d5'
	let backgroundColor = darkMode ? '#000' : '#fff'
	let maskColor = darkMode ? 'rgba(0,0,0,0.2)' : 'rgba(0,0,0,0.4)'
	
	if (mask != null) {
		mask!.remove()
	}
	
	mask = document.createElement('div')
	mask!.style.setProperty('opacity', 0)
	mask!.style.setProperty('background-color', maskColor)
	if(options.blur) {
		mask!.style.setProperty('backdrop-filter', `blur(${options.blur}px)`)
	}
	mask!.style.setProperty('position', 'fixed')
	mask!.style.setProperty('left', '0px')
	mask!.style.setProperty('top', '0px')
	mask!.style.setProperty('width', '100%')
	mask!.style.setProperty('height', '100%')
	mask!.style.setProperty('transition-property', 'opacity')
	mask!.style.setProperty('transition-duration', '400ms')
	mask!.style.display = 'flex'
	mask!.style.flexDirection = 'row'
	mask!.style.justifyContent = 'center'
	mask!.style.alignItems = 'center'
	mask!.style.zIndex = 10000
	
	let content = document.createElement('div')
	content.style.setProperty('background-color', backgroundColor)
	content.style.setProperty('position', 'fixed')
	content.style.setProperty('left', '0px')
	content.style.setProperty('bottom', '0px')
	content.style.setProperty('width', '100%')
	content.style.setProperty('height', 'calc(100% - 40px)')
	content.style.setProperty('transform', 'translateY(100%)')
	content.style.setProperty('border-top-left-radius', '20px')
	content.style.setProperty('border-top-right-radius', '20px')
	content.style.setProperty('transition-property', 'transform')
	content.style.setProperty('transition-duration', '300ms')
	
	let top = document.createElement('div')
	top.style.setProperty('position', 'relative')
	top.style.setProperty('width', '100%')
	top.style.setProperty('height', '44px')
	top.style.display = 'flex'
	top.style.flexDirection = 'row'
	top.style.justifyContent = 'center'
	top.style.alignItems = 'center'
	
	let title = document.createElement('div')
	title.style.setProperty('width', '100%')
	title.style.setProperty('line-height', '44px')
	title.style.setProperty('text-align', 'center')
	title.innerHTML = options.title
	top.appendChild(title)
	
	let close = document.createElement('div')
	close.style.setProperty('position', 'absolute')
	close.style.setProperty('right', '0px')
	close.style.setProperty('width', '50px')
	close.style.setProperty('height', '44px')
	close.style.display = 'flex'
	close.style.flexDirection = 'row'
	close.style.justifyContent = 'center'
	close.style.alignItems = 'center'
	close.style.fontFamily = 'ux-iconfont'
	close.style.fontSize = '20px'
	close.style.color = darkMode ? '#fff' : '#000'
	close.innerText = `\uea7d`
	top.appendChild(close)
	
	content.appendChild(top)
	
	let iframe = document.createElement('iframe')
	iframe.style.setProperty('width', '100%')
	iframe.style.setProperty('height', 'calc(100% - 44px)')
	content.appendChild(iframe)
	
	mask!.appendChild(content)
	window.document.body.appendChild(mask)
	
	const res = new UxPlusSuccessCallbackImpl('openWeb:success')
	
	let _close = () => {
		content.style.setProperty('transform', 'translateY(100%)')
		
		setTimeout(() => {
			mask!.style.setProperty('opacity', 0)
			options?.complete?.(res)
			
			setTimeout(() => {
				document.documentElement.style.backgroundColor = _background
				// #ifdef UNI-APP-X
				// @ts-expect-error
				__uniConfig.globalStyle['backgroundColorContent'] = _backgroundColorContent
				// #endif
				mask!.remove()
			}, 400);
		}, 100);
	}
	
	let _open = () => {
		document.documentElement.style.backgroundColor = background
		// #ifdef UNI-APP-X
		// @ts-expect-error
		__uniConfig.globalStyle['backgroundColorContent'] = background
		// #endif
		
		setTimeout(() => {
			mask!.style.setProperty('opacity', 1)
			options?.success?.(res)
		}, 100);
		
		setTimeout(() => {
			content.style.setProperty('transform', 'translateY(0%)')
			
			setTimeout(() => {
				iframe.src = options.url
			}, 400)
		}, 200);
	}
	
	close.addEventListener('click', (event) => {
		_close()
		event.stopPropagation()
	})
	
	mask!.addEventListener('click', (event) => {
		_close()
		event.stopPropagation()
	})
	
	_open()
}

function loadFont() {
	// @ts-ignore
	const font = new FontFace('ux-iconfont', `url('/uni_modules/ux-frame/static/iconfont.ttf')`)
	document.fonts.add(font)
	font.load()
}