<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/rate?title=Rate"></Mobile>

# Rate
> 组件类型：UxRateComponentPublicInstance

支持设置数量、最小选择、半星选择、触摸/鼠标拖动选择

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| name | String |  | 标识符，在回调事件中返回 |
| [theme](#theme) | String |  | 主题颜色 |
| value | Number |  | 分数 |
| count | Number | 5 | 数量 |
| minCount | Number | 0 | 最小选择数量 |
| color | String | `$ux.Conf.placeholderColor` | 颜色 |
| [darkColor](#darkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| activeColor | String | `$ux.Conf.primaryColor` | 选中颜色优先级高于主题 |
| [activeDarkColor](#activeDarkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| size | Any | `$ux.Conf.fontSize` | 字体大小 |
| gutter | Any | 5 | 间距 |
| [type](#type) | String | star | 类型 |
| icon | String |  | 星星图标优先级高于type |
| half | Boolean | false | 允许半星halfIcon配置时有效 |
| halfIcon | String |  | 半星图标half=true时有效 |
| customFamily | String |  | 字体family |
| valueFormat | String | __s1__分 | 格式化文本，有则显示 |
| touchable | Boolean | false | 触摸滑动 |
| disabled | Boolean | false | 禁用 |
| readonly | Boolean | false | 只读 |
| margin | Array |  | 边距[上右下左][上下左右][上下左右] |
| mt | Any |  | 距上单位px |
| mr | Any |  | 距右单位px |
| mb | Any |  | 距下单位px |
| ml | Any |  | 距左单位px |
| padding | Array |  | 填充[上右下左][上下左右][上下左右] |
| pt | Any |  | 上内边距单位px |
| pr | Any |  | 右内边距单位px |
| pb | Any |  | 下内边距单位px |
| pl | Any |  | 左内边距单位px |
| xstyle | Array |  | 自定义样式 |

### [theme](#theme)

| 值   | 说明 |
|:------:|:----:|
| primary | 主色
 |
| warning | 警告
 |
| success | 成功
 |
| error | 错误
 |
| info | 文本
 |

### [darkColor](#darkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [activeDarkColor](#activeDarkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [type](#type)

| 值   | 说明 |
|:------:|:----:|
| star星星
 |  |
| heart爱心
 |  |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| change | 值改变时触发 |  |
| touchstart | 开始触摸时触发 |  |
| touchmove | 正在触摸时触发 |  |
| touchend | 触摸结束时触发 |  |
  
  