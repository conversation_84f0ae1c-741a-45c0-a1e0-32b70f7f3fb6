export type UxStartAuthSuccess = {
	/**
	 * 生物认证方式
	 */
	authMode: string
}

export type AuthMode = 'Fingerprint' | 'PIN'

export type StartAuthOptions = {
	/**
	 * 请求认证方式
	 * STRONG 强生物认证（3D 人脸/指纹）
	 * WEAK 弱生物认证（2D 人脸）
	 */
	mode ?: AuthMode
	/**
	 * 挑战因子。挑战因子为调用者为此次生物鉴权准备的用于签名的字符串关键识别信息，供调用者识别本次请求。例如：如果场景为请求用户对某订单进行授权确认，则可以将订单号填入此参数
	 */
	challenge ?: string
	/**
	 * 验证标题
	 */
	title ?: string
	/**
	 * 验证描述，即识别过程中显示在界面上的对话框提示内容
	 */
	content ?: string
	/**
	 * 接口调用成功的回调函数
	 */
	success ?: (res : UxStartAuthSuccess) => void
	/**
	 * 接口调用失败的回调函数
	 */
	fail ?: (err : UxSoterFail) => void
	/**
	 * 接口调用结束的回调函数（调用成功、失败都会执行）	
	 */
	complete ?: () => void
}

export type StartAuth = (options: StartAuthOptions) => void

export type Cancel = () => void

export type IsSupport = () => boolean

export type IsEnrolled = () => boolean

export type IsFingerprint = () => boolean

export type IsPIN = () => boolean

export type MyErrorCode = 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8
export interface UxSoterFail extends IUniError {
	errCode : MyErrorCode
}