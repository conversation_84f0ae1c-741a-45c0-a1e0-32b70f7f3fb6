<template>
	<view class="ux-transition">
		<view :id="myId" v-if="display" class="ux-transition__body transform" :style="[style, xstyle]" @transitionend="onEnd">
			<slot></slot>
		</view>
	</view>
</template>

<script setup lang="ts">
	
	/**
	 * transition  过渡动画
	 * @description 支持淡入、缩放、缩放淡入、上滑淡入、下滑淡入、左滑淡入、右滑淡入、上滑进入、下滑进入、左滑进入、右滑进入等动画方式
	 * @demo pages/component/transition.uvue
	 * @tutorial https://www.uxframe.cn/component/transition.html
	 * @property {Boolean}			show = [true|false]			Boolean | 显示状态 （默认 false ）
	 * @property {String}			mode = [fade|zoom|fade-zoom|fade-up|fade-down|fade-left|fade-right|slide-up|slide-down|slide-left|slide-right]			String | 动画模式 （默认 fade ）
	 * @value fade 淡入
	 * @value zoom 缩放
	 * @value fade-zoom 缩放淡入
	 * @value fade-up 上滑淡入
	 * @value fade-left 左滑淡入
	 * @value fade-right 右滑淡入
	 * @value fade-down 下滑淡入
	 * @value slide-up 上滑进入
	 * @value slide-left 左滑进入
	 * @value slide-down 下滑进入
	 * @value slide-right 右滑进入
	 * @property {Number}			duration		Number | 动画的执行时间，单位ms （默认 300 ）
	 * @property {String}			timingFunction = [ease|ease-in|ease-out|ease-in-out|linear|cubic-bezier]	String | 使用的动画过渡函数 （默认 ease-out ）
	 * @value ease 开始缓慢，然后逐渐加速，最后减速结束
	 * @value ease-in 过渡开始时较慢，然后逐渐加速
	 * @value ease-out 过渡开始时较快，然后逐渐减速
	 * @value ease-in-out 过渡开始时较慢，然后加速，最后减速
	 * @value linear 恒定速度，没有加速或减速
	 * @value cubic-bezier 用于自定义 CSS 过渡（transition）的时间函数的函数，它允许你精确地定义过渡效果的速度变化
	 * @property {Array}			xstyle			Array<any> | 自定义样式
	 * @event {Function} 			beforeEnter		Function | 进入前触发
	 * @event {Function} 			enter			Function | 进入中触发
	 * @event {Function} 			afterEnter		Function | 进入后触发
	 * @event {Function} 			beforeLeave		Function | 离开前触发
	 * @event {Function} 			leave			Function | 离开中触发
	 * @event {Function} 			afterLeave		Function | 离开后触发
	 * @event {Function} 			transitionend	Function | 动画结束后触发
	 * <AUTHOR>
	 * @date 2024-12-01 11:35:16
	 */
	
	import { ref, computed, watch, PropType, nextTick } from 'vue'
	import { $ux } from '../../index'
	
	defineOptions({
		name: 'ux-transition'
	})
	
	const emit = defineEmits(['beforeEnter', 'enter', 'afterEnter', 'beforeLeave', 'leave', 'afterLeave', 'transitionend'])
	
	const props = defineProps({
		show: {
		    type: Boolean,
		    default: false
		},
		mode: {
		    type: String,
		    default: 'fade'
		},
		duration: {
		    type: Number,
		    default: 300
		},
		timingFunction: {
		    type: String,
		    default: 'ease-out'
		},
		xstyle: {
			type: Array as PropType<Array<any>>,
			default: (): any[] => [] as any[]
		},
	})
	
	const myId = `ux-transition-${$ux.Random.uuid()}`
	const display = ref(false)
	const playing = ref(false)
	const beforeEnd = ref(false)
	const delay = 60
	
	let isFirst = true
	
	const style = computed(()=> {
		let css = {}
		
		css['transition-timing-function'] = props.timingFunction
		
		return css
	})
	
	function fade(open: boolean) {
		if(open) {
			if(playing.value) {
				return
			}
			playing.value = true
			
			emit('beforeEnter')
			uni.getElementById(myId)?.style?.setProperty('transition-duration', `${10}ms`)
			uni.getElementById(myId)?.style?.setProperty('opacity', 0)
			
			setTimeout(() => {
				emit('enter')
				uni.getElementById(myId)?.style?.setProperty('transition-duration', `${props.duration}ms`)
				uni.getElementById(myId)?.style?.setProperty('opacity', 1)
				beforeEnd.value = true
			}, delay);
		} else {
			emit('beforeLeave')
			emit('leave')
			uni.getElementById(myId)?.style?.setProperty('opacity', 0)
		}
	}
	
	function zoom(open: boolean) {
		if(open) {
			if(playing.value) {
				return
			}
			playing.value = true
			
			emit('beforeEnter')
			uni.getElementById(myId)?.style?.setProperty('transition-duration', `${10}ms`)
			uni.getElementById(myId)?.style?.setProperty('opacity', 0)
			uni.getElementById(myId)?.style?.setProperty('transform', 'scale(0)')
			
			setTimeout(() => {
				emit('enter')
				uni.getElementById(myId)?.style?.setProperty('transition-duration', `${props.duration}ms`)
				uni.getElementById(myId)?.style?.setProperty('opacity', 1)
				uni.getElementById(myId)?.style?.setProperty('transform', 'scale(1)')
				beforeEnd.value = true
			}, delay);
		} else {
			emit('beforeLeave')
			emit('leave')
			uni.getElementById(myId)?.style?.setProperty('transform', 'scale(0)')
		}
	}
	
	function fadeZoom(open: boolean) {
		if(open) {
			if(playing.value) {
				return
			}
			playing.value = true
			
			emit('beforeEnter')
			uni.getElementById(myId)?.style?.setProperty('transition-duration', `${10}ms`)
			uni.getElementById(myId)?.style?.setProperty('opacity', 0)
			uni.getElementById(myId)?.style?.setProperty('transform', 'scale(0)')
			
			setTimeout(() => {
				emit('enter')
				uni.getElementById(myId)?.style?.setProperty('transition-duration', `${props.duration}ms`)
				uni.getElementById(myId)?.style?.setProperty('opacity', 1)
				uni.getElementById(myId)?.style?.setProperty('transform', 'scale(1)')
				beforeEnd.value = true
			}, delay);
		} else {
			emit('beforeLeave')
			emit('leave')
			uni.getElementById(myId)?.style?.setProperty('opacity', 0)
			uni.getElementById(myId)?.style?.setProperty('transform', 'scale(0)')
		}
	}
	
	function fadeUp(open: boolean) {
		if(open) {
			if(playing.value) {
				return
			}
			playing.value = true
			
			emit('beforeEnter')
			uni.getElementById(myId)?.style?.setProperty('transition-duration', `${10}ms`)
			uni.getElementById(myId)?.style?.setProperty('opacity', 0)
			uni.getElementById(myId)?.style?.setProperty('transform', `translate(0, 100%)`)
			
			setTimeout(() => {
				emit('enter')
				uni.getElementById(myId)?.style?.setProperty('transition-duration', `${props.duration}ms`)
				uni.getElementById(myId)?.style?.setProperty('opacity', 1)
				uni.getElementById(myId)?.style?.setProperty('transform', `translate(0, 0)`)
				beforeEnd.value = true
			}, delay);
		} else {
			emit('beforeLeave')
			emit('leave')
			uni.getElementById(myId)?.style?.setProperty('opacity', 0)
			uni.getElementById(myId)?.style?.setProperty('transform', `translate(0, 100%)`)
		}
	}
	
	function fadeLeft(open: boolean) {
		if(open) {
			if(playing.value) {
				return
			}
			playing.value = true
			
			emit('beforeEnter')
			uni.getElementById(myId)?.style?.setProperty('transition-duration', `${10}ms`)
			uni.getElementById(myId)?.style?.setProperty('opacity', 0)
			uni.getElementById(myId)?.style?.setProperty('transform', `translate(-100%, 0)`)
			
			setTimeout(() => {
				emit('enter')
				uni.getElementById(myId)?.style?.setProperty('transition-duration', `${props.duration}ms`)
				uni.getElementById(myId)?.style?.setProperty('opacity', 1)
				uni.getElementById(myId)?.style?.setProperty('transform', `translate(0, 0)`)
				beforeEnd.value = true
			}, delay);
		} else {
			emit('beforeLeave')
			emit('leave')
			uni.getElementById(myId)?.style?.setProperty('opacity', 0)
			uni.getElementById(myId)?.style?.setProperty('transform', `translate(-100%, 0)`)
		}
	}
	
	function fadeRight(open: boolean) {
		if(open) {
			if(playing.value) {
				return
			}
			playing.value = true
			
			emit('beforeEnter')
			uni.getElementById(myId)?.style?.setProperty('transition-duration', `${10}ms`)
			uni.getElementById(myId)?.style?.setProperty('opacity', 0)
			uni.getElementById(myId)?.style?.setProperty('transform', `translate(100%, 0)`)
			
			setTimeout(() => {
				emit('enter')
				uni.getElementById(myId)?.style?.setProperty('transition-duration', `${props.duration}ms`)
				uni.getElementById(myId)?.style?.setProperty('opacity', 1)
				uni.getElementById(myId)?.style?.setProperty('transform', `translate(0, 0)`)
				beforeEnd.value = true
			}, delay);
		} else {
			emit('beforeLeave')
			emit('leave')
			uni.getElementById(myId)?.style?.setProperty('opacity', 0)
			uni.getElementById(myId)?.style?.setProperty('transform', `translate(100%, 0)`)
		}
	}
	
	function fadeDown(open: boolean) {
		if(open) {
			if(playing.value) {
				return
			}
			playing.value = true
			
			emit('beforeEnter')
			uni.getElementById(myId)?.style?.setProperty('transition-duration', `${10}ms`)
			uni.getElementById(myId)?.style?.setProperty('opacity', 0)
			uni.getElementById(myId)?.style?.setProperty('transform', `translate(0, -100%)`)
			
			setTimeout(() => {
				emit('enter')
				uni.getElementById(myId)?.style?.setProperty('transition-duration', `${props.duration}ms`)
				uni.getElementById(myId)?.style?.setProperty('opacity', 1)
				uni.getElementById(myId)?.style?.setProperty('transform', `translate(0, 0)`)
				beforeEnd.value = true
			}, delay);
		} else {
			emit('beforeLeave')
			emit('leave')
			uni.getElementById(myId)?.style?.setProperty('opacity', 0)
			uni.getElementById(myId)?.style?.setProperty('transform', `translate(0, -100%)`)
		}
	}
	
	function slideUp(open: boolean) {
		if(open) {
			if(playing.value) {
				return
			}
			playing.value = true
			
			emit('beforeEnter')
			uni.getElementById(myId)?.style?.setProperty('transition-duration', `${10}ms`)
			uni.getElementById(myId)?.style?.setProperty('opacity', 0)
			uni.getElementById(myId)?.style?.setProperty('transform', `translate(0, 100%)`)
			
			setTimeout(() => {
				emit('enter')
				uni.getElementById(myId)?.style?.setProperty('transition-duration', `${props.duration}ms`)
				uni.getElementById(myId)?.style?.setProperty('opacity', 1)
				uni.getElementById(myId)?.style?.setProperty('transform', `translate(0, 0)`)
				beforeEnd.value = true
			}, delay);
		} else {
			emit('beforeLeave')
			emit('leave')
			uni.getElementById(myId)?.style?.setProperty('transform', `translate(0, 100%)`)
			// setTimeout(() => {
			// 	uni.getElementById(myId)?.style?.setProperty('transition-duration', `${10}ms`)
			// 	uni.getElementById(myId)?.style?.setProperty('opacity', 0)
			// }, props.duration);
		}
	}
	
	function slideDown(open: boolean) {
		if(open) {
			if(playing.value) {
				return
			}
			playing.value = true
			
			emit('beforeEnter')
			uni.getElementById(myId)?.style?.setProperty('transition-duration', `${10}ms`)
			uni.getElementById(myId)?.style?.setProperty('opacity', 0)
			uni.getElementById(myId)?.style?.setProperty('transform', `translate(0, -100%)`)
			
			setTimeout(() => {
				emit('enter')
				uni.getElementById(myId)?.style?.setProperty('transition-duration', `${props.duration}ms`)
				uni.getElementById(myId)?.style?.setProperty('opacity', 1)
				uni.getElementById(myId)?.style?.setProperty('transform', `translate(0, 0)`)
				beforeEnd.value = true
			}, delay);
		} else {
			emit('beforeLeave')
			emit('leave')
			uni.getElementById(myId)?.style?.setProperty('transform', `translate(0, -100%)`)
		}
	}
	
	function slideLeft(open: boolean) {
		if(open) {
			if(playing.value) {
				return
			}
			playing.value = true
			
			emit('beforeEnter')
			uni.getElementById(myId)?.style?.setProperty('transition-duration', `${10}ms`)
			uni.getElementById(myId)?.style?.setProperty('opacity', 0)
			uni.getElementById(myId)?.style?.setProperty('transform', `translate(-100%, 0)`)
			
			setTimeout(() => {
				emit('enter')
				uni.getElementById(myId)?.style?.setProperty('transition-duration', `${props.duration}ms`)
				uni.getElementById(myId)?.style?.setProperty('opacity', 1)
				uni.getElementById(myId)?.style?.setProperty('transform', `translate(0, 0)`)
				beforeEnd.value = true
			}, delay);
		} else {
			emit('beforeLeave')
			emit('leave')
			uni.getElementById(myId)?.style?.setProperty('transform', `translate(-100%, 0)`)
		}
	}
	
	function slideRight(open: boolean) {
		if(open) {
			if(playing.value) {
				return
			}
			playing.value = true
			
			emit('beforeEnter')
			uni.getElementById(myId)?.style?.setProperty('transition-duration', `${10}ms`)
			uni.getElementById(myId)?.style?.setProperty('opacity', 0)
			uni.getElementById(myId)?.style?.setProperty('transform', `translate(100%, 0)`)
			
			setTimeout(() => {
				emit('enter')
				uni.getElementById(myId)?.style?.setProperty('transition-duration', `${props.duration}ms`)
				uni.getElementById(myId)?.style?.setProperty('opacity', 1)
				uni.getElementById(myId)?.style?.setProperty('transform', `translate(0, 0)`)
				beforeEnd.value = true
			}, delay);
		} else {
			emit('beforeLeave')
			emit('leave')
			uni.getElementById(myId)?.style?.setProperty('transform', `translate(100%, 0)`)
		}
	}
	
	function enter() {
		if(props.mode == 'fade') {
			fade(true)
		} else if(props.mode == 'zoom') {
			zoom(true)
		} else if(props.mode == 'fade-zoom') {
			fadeZoom(true)
		} else if(props.mode == 'fade-up') {
			fadeUp(true)
		} else if(props.mode == 'fade-left') {
			fadeLeft(true)
		} else if(props.mode == 'fade-right') {
			fadeRight(true)
		} else if(props.mode == 'fade-down') {
			fadeDown(true)
		} else if(props.mode == 'slide-up') {
			slideUp(true)
		} else if(props.mode == 'slide-left') {
			slideLeft(true)
		} else if(props.mode == 'slide-down') {
			slideDown(true)
		} else if(props.mode == 'slide-right') {
			slideRight(true)
		}
	}
	
	function leave() {
		if(props.mode == 'fade') {
			fade(false)
		} else if(props.mode == 'zoom') {
			zoom(false)
		} else if(props.mode == 'fade-zoom') {
			fadeZoom(false)
		} else if(props.mode == 'fade-up') {
			fadeUp(false)
		} else if(props.mode == 'fade-left') {
			fadeLeft(false)
		} else if(props.mode == 'fade-right') {
			fadeRight(false)
		} else if(props.mode == 'fade-down') {
			fadeDown(false)
		} else if(props.mode == 'slide-up') {
			slideUp(false)
		} else if(props.mode == 'slide-left') {
			slideLeft(false)
		} else if(props.mode == 'slide-down') {
			slideDown(false)
		} else if(props.mode == 'slide-right') {
			slideRight(false)
		}
	}
	
	const show = computed((): boolean => {
		return props.show
	})
	
	watch(show, () =>  {
		if(show.value) {
			display.value = true 
			
			nextTick(() => {
				enter()
			})
		} else {
			if(isFirst) {
				isFirst = false
			} else {
				leave()
			}
		}
	}, {
		immediate: true
	})
	
	function onEnd() {
		playing.value = false
		
		if(beforeEnd.value) {
			if(show.value) {
				emit('afterEnter')
			} else {
				emit('afterLeave')
			}
		}
		
		emit('transitionend')
	}
</script>

<style lang="scss" scoped>
	.ux-transition {
		overflow: hidden;
		position: relative;
		width: 100%;
		height: 100%;
	}
	
	.ux-transition__body {
		position: absolute;
		opacity: 0;
	}
	
	.transform {
		transition-property: opacity, transform;
	}
</style>