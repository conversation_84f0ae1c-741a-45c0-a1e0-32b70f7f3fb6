import { HtmlNodeType } from './types.uts'

/**
 * 样式工具类
 */
export class StyleUtils {
  
  /**
   * 转换CSS样式为uni-app样式对象
   */
  static convertStyle(style: UTSJSONObject | null): UTSJSONObject {
    const uniStyle: UTSJSONObject = {}
    
    if (style == null) {
      return uniStyle
    }
    
    // 处理颜色
    const color = style.getString('color')
    if (color != null && color != '') {
      uniStyle['color'] = this.normalizeColor(color)
    }
    
    // 处理字体大小
    const fontSize = style.getString('fontSize') ?? style.getString('font-size')
    if (fontSize != null && fontSize != '') {
      uniStyle['fontSize'] = this.normalizeFontSize(fontSize)
    }
    
    // 处理字体粗细
    const fontWeight = style.getString('fontWeight') ?? style.getString('font-weight')
    if (fontWeight != null && fontWeight != '') {
      uniStyle['fontWeight'] = fontWeight
    }
    
    // 处理文本对齐
    const textAlign = style.getString('textAlign') ?? style.getString('text-align')
    if (textAlign != null && textAlign != '') {
      uniStyle['textAlign'] = textAlign
    }
    
    // 处理背景色
    const backgroundColor = style.getString('backgroundColor') ?? style.getString('background-color')
    if (backgroundColor != null && backgroundColor != '') {
      uniStyle['backgroundColor'] = this.normalizeColor(backgroundColor)
    }
    
    // 处理边距
    const margin = style.getString('margin')
    if (margin != null && margin != '') {
      const margins = this.parseSpacing(margin)
      if (margins != null) {
        uniStyle['marginTop'] = margins.top
        uniStyle['marginRight'] = margins.right
        uniStyle['marginBottom'] = margins.bottom
        uniStyle['marginLeft'] = margins.left
      }
    }
    
    // 处理内边距
    const padding = style.getString('padding')
    if (padding != null && padding != '') {
      const paddings = this.parseSpacing(padding)
      if (paddings != null) {
        uniStyle['paddingTop'] = paddings.top
        uniStyle['paddingRight'] = paddings.right
        uniStyle['paddingBottom'] = paddings.bottom
        uniStyle['paddingLeft'] = paddings.left
      }
    }
    
    // 处理宽度
    const width = style.getString('width')
    if (width != null && width != '') {
      uniStyle['width'] = this.normalizeSize(width)
    }
    
    // 处理高度
    const height = style.getString('height')
    if (height != null && height != '') {
      uniStyle['height'] = this.normalizeSize(height)
    }
    
    return uniStyle
  }
  
  /**
   * 获取标签的默认样式
   */
  static getDefaultStyle(tagName: string): UTSJSONObject {
    const style: UTSJSONObject = {}
    
    switch (tagName) {
      case 'h1':
        style['fontSize'] = '32px'
        style['fontWeight'] = 'bold'
        style['marginTop'] = '16px'
        style['marginBottom'] = '16px'
        break
      case 'h2':
        style['fontSize'] = '28px'
        style['fontWeight'] = 'bold'
        style['marginTop'] = '14px'
        style['marginBottom'] = '14px'
        break
      case 'h3':
        style['fontSize'] = '24px'
        style['fontWeight'] = 'bold'
        style['marginTop'] = '12px'
        style['marginBottom'] = '12px'
        break
      case 'h4':
        style['fontSize'] = '20px'
        style['fontWeight'] = 'bold'
        style['marginTop'] = '10px'
        style['marginBottom'] = '10px'
        break
      case 'h5':
        style['fontSize'] = '18px'
        style['fontWeight'] = 'bold'
        style['marginTop'] = '8px'
        style['marginBottom'] = '8px'
        break
      case 'h6':
        style['fontSize'] = '16px'
        style['fontWeight'] = 'bold'
        style['marginTop'] = '6px'
        style['marginBottom'] = '6px'
        break
      case 'p':
        style['marginTop'] = '8px'
        style['marginBottom'] = '8px'
        break
      case 'strong':
      case 'b':
        style['fontWeight'] = 'bold'
        break
      case 'em':
      case 'i':
        style['fontStyle'] = 'italic'
        break
      case 'u':
        style['textDecoration'] = 'underline'
        break
      case 'del':
      case 's':
        style['textDecoration'] = 'line-through'
        break
      case 'ul':
      case 'ol':
        style['marginTop'] = '8px'
        style['marginBottom'] = '8px'
        style['paddingLeft'] = '20px'
        break
      case 'li':
        style['marginTop'] = '4px'
        style['marginBottom'] = '4px'
        break
      case 'a':
        style['color'] = '#007AFF'
        style['textDecoration'] = 'underline'
        break
      case 'img':
        style['maxWidth'] = '100%'
        style['height'] = 'auto'
        break
    }
    
    return style
  }
  
  /**
   * 合并样式对象
   */
  static mergeStyles(defaultStyle: UTSJSONObject, customStyle: UTSJSONObject): UTSJSONObject {
    const merged: UTSJSONObject = {}
    
    // 先添加默认样式
    for (const key in defaultStyle) {
      merged[key] = defaultStyle[key]
    }
    
    // 再添加自定义样式（覆盖默认样式）
    for (const key in customStyle) {
      merged[key] = customStyle[key]
    }
    
    return merged
  }
  
  /**
   * 标准化颜色值
   */
  private static normalizeColor(color: string): string {
    // 移除空格
    color = color.trim()
    
    // 处理颜色关键字
    const colorMap: UTSJSONObject = {
      'red': '#FF0000',
      'green': '#008000',
      'blue': '#0000FF',
      'white': '#FFFFFF',
      'black': '#000000',
      'gray': '#808080',
      'yellow': '#FFFF00',
      'orange': '#FFA500',
      'purple': '#800080',
      'pink': '#FFC0CB'
    }
    
    const lowerColor = color.toLowerCase()
    const mappedColor = colorMap.getString(lowerColor)
    if (mappedColor != null) {
      return mappedColor
    }
    
    // 处理rgb/rgba
    if (color.startsWith('rgb')) {
      return color
    }
    
    // 处理十六进制颜色
    if (color.startsWith('#')) {
      return color
    }
    
    return color
  }
  
  /**
   * 标准化字体大小
   */
  private static normalizeFontSize(fontSize: string): string {
    fontSize = fontSize.trim()
    
    // 如果已经有单位，直接返回
    if (fontSize.endsWith('px') || fontSize.endsWith('rpx') || fontSize.endsWith('em') || fontSize.endsWith('%')) {
      return fontSize
    }
    
    // 如果是纯数字，添加px单位
    const num = parseFloat(fontSize)
    if (!isNaN(num)) {
      return num + 'px'
    }
    
    return fontSize
  }
  
  /**
   * 标准化尺寸值
   */
  private static normalizeSize(size: string): string {
    size = size.trim()
    
    // 如果已经有单位，直接返回
    if (size.endsWith('px') || size.endsWith('rpx') || size.endsWith('%') || size == 'auto') {
      return size
    }
    
    // 如果是纯数字，添加px单位
    const num = parseFloat(size)
    if (!isNaN(num)) {
      return num + 'px'
    }
    
    return size
  }
  
  /**
   * 解析间距值（margin/padding）
   */
  private static parseSpacing(spacing: string): {top: string, right: string, bottom: string, left: string} | null {
    spacing = spacing.trim()
    const values = spacing.split(/\s+/)
    
    if (values.length == 0) {
      return null
    }
    
    let top: string, right: string, bottom: string, left: string
    
    if (values.length == 1) {
      // 一个值：四个方向相同
      top = right = bottom = left = this.normalizeSize(values[0])
    } else if (values.length == 2) {
      // 两个值：上下 左右
      top = bottom = this.normalizeSize(values[0])
      right = left = this.normalizeSize(values[1])
    } else if (values.length == 3) {
      // 三个值：上 左右 下
      top = this.normalizeSize(values[0])
      right = left = this.normalizeSize(values[1])
      bottom = this.normalizeSize(values[2])
    } else {
      // 四个值：上 右 下 左
      top = this.normalizeSize(values[0])
      right = this.normalizeSize(values[1])
      bottom = this.normalizeSize(values[2])
      left = this.normalizeSize(values[3])
    }
    
    return { top, right, bottom, left }
  }
}