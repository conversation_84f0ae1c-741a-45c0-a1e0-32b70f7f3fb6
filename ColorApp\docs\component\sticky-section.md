<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/sticky-section?title=Sticky-section"></Mobile>

# Sticky-section
> 组件类型：UxStickySectionComponentPublicInstance



## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| sectionId | String |  | id |
| type | String |  | 类型，如果一个页面有多个本组件，可设置type区分 |
| index | Number |  | 下标 |
| pushPinnedHeader | Boolean | true | sticky-section元素重叠时是否继续上推 |
| padding | Number[] |  | 长度为4的数组，按top、right、bottom、left顺序指定内边距 |

