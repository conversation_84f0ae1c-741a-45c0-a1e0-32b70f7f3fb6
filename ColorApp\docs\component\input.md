<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/input?title=Input"></Mobile>

# Input
> 组件类型：UxInputComponentPublicInstance

扩展支持聚焦高亮、前后置图标、边框类型、输入框形状、可清空

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| [theme](#theme) | String | primary | 主题颜色 |
| name | String |  | 表单的控件名称，作为键值对的一部分与表单(form组件)一同提交 |
| value | String |  | 输入框的初始内容 |
| [type](#type) | String | text | 输入类型 |
| password | Boolean | false | 是否是密码类型 |
| clearable | Boolean | false | 是否显示清除控件 |
| placeholder | String |  | 输入框为空时占位符 |
| placeholderStyle | String |  | 指定placeholder的样式 |
| placeholderClass | String |  | 指定placeholder的样式类 |
| maxlength | Number | 140 | 最大输入长度，设置为-1的时候不限制最大长度 |
| cursorSpacing | Number | 0 | 指定光标与键盘的距离，单位px。取input距离底部的距离和cursor-spacing指定的距离的最小值作为光标与键盘的距离 |
| autoFocus | Boolean | false | 自动获取焦点，与focus属性对比，此属性只会首次生效 |
| focus | Boolean | false | 获取焦点 |
| [confirmType](#confirmType) | String | done | 设置键盘右下角按钮的文字 |
| confirmHold | Boolean | false | 点击键盘右下角按钮时是否保持键盘不收起 |
| cursor | Number | 0 | 指定focus时的光标位置 |
| selection-start | Number | -1 | 光标起始位置，自动聚集时有效，需与selection-end搭配使用 |
| selection-end | Number | -1 | 光标结束位置，自动聚集时有效，需与selection-satrt搭配使用 |
| adjustPosition | Boolean | true | 键盘弹起时，是否自动上推页面 |
| holdKeyboard | Boolean | false | focus时，点击页面的时候不收起键盘 |
| [align](#align) | String | left | 输入框内容对齐方式 |
| size | Any | `$ux.Conf.fontSize` | 输入框字体的大小 |
| [sizeType](#sizeType) | String | small | 字体大小类型 |
| color | String | `$ux.Conf.fontColor` | 输入框字体颜色 |
| [darkColor](#darkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| background | String | #f0f0f0 | 输入框背景颜色 |
| [backgroundDark](#backgroundDark) | String | #222222 | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| disabledColor | String |  | 输入框禁用颜色 |
| [disabledDark](#disabledDark) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| prefix | String |  | 输入框前置图标 |
| prefixStyle | String |  | 前置图标样式，对象或字符串 |
| suffix | String |  | 输入框后置图标 |
| suffixStyle | String |  | 后置图标样式，对象或字符串 |
| iconSize | Any | 20 | 图标大小 |
| customFamily | String |  | 字体family |
| [border](#border) | String | none | 边框类型 |
| borderWidth | Number | 1 | 边框宽 |
| borderColor | String |  | 输入框边框颜色 |
| [borderColorDark](#borderColorDark) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| borderRadius | Any | 10 | 圆角 |
| focusBorder | Boolean | true | 聚焦时显示边框 |
| fixed | Boolean | false | 如果input是在一个position:fixed的区域，需要显示指定属性fixed为true |
| readonly | Boolean | false | 是否只读，与disabled不同之处在于disabled会置灰组件，而readonly则不会 |
| [shape](#shape) | String | square | 输入框形状 |
| hPadding | Any |  | 水平内边距 |
| vPadding | Any |  | 垂直内边距 |
| disabled | Boolean | false | 是否禁用 |
| margin | Array |  | 边距[上右下左][上下左右][上下左右] |
| mt | Any |  | 距上单位px |
| mr | Any |  | 距右单位px |
| mb | Any |  | 距下单位px |
| ml | Any |  | 距左单位px |
| padding | Array |  | 填充[上右下左][上下左右][上下左右] |
| pt | Any |  | 上内边距单位px |
| pr | Any |  | 右内边距单位px |
| pb | Any |  | 下内边距单位px |
| pl | Any |  | 左内边距单位px |
| xstyle | Array |  | 自定义样式 |

### [theme](#theme)

| 值   | 说明 |
|:------:|:----:|
| primary | 主色
 |
| warning | 警告
 |
| success | 成功
 |
| error | 错误
 |
| info | 文本
 |

### [type](#type)

| 值   | 说明 |
|:------:|:----:|
| text文本输入键盘
 |  |
| number数字输入键盘
 |  |
| digit带小数点数字输入键盘
 |  |
| tel电话输入键盘
 |  |

### [confirmType](#confirmType)

| 值   | 说明 |
|:------:|:----:|
| send发送
 |  |
| search搜索
 |  |
| next下一个
 |  |
| go前往
 |  |
| done完成
 |  |

### [align](#align)

| 值   | 说明 |
|:------:|:----:|
| left左对齐
 |  |
| center居中
 |  |
| right右对齐
 |  |

### [sizeType](#sizeType)

| 值   | 说明 |
|:------:|:----:|
| normal正常
 |  |
| small较小
 |  |
| big较大
 |  |

### [darkColor](#darkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [backgroundDark](#backgroundDark)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [disabledDark](#disabledDark)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [border](#border)

| 值   | 说明 |
|:------:|:----:|
| surround四周边框
 |  |
| bottom底部边框
 |  |
| none无边框
 |  |

### [borderColorDark](#borderColorDark)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [shape](#shape)

| 值   | 说明 |
|:------:|:----:|
| circle圆形
 |  |
| square方形
 |  |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| clear | 点击清空时触发 |  |
| input | 当键盘输入时，触发input事件，处理函数可以直接return一个字符串，将替换输入框的内容 |  |
| focus | 输入框聚焦时触发 |  |
| blur | 输入框失去焦点时触发 |  |
| confirm | 点击完成按钮时触发 |  |
| keyboardheightchange | 键盘高度发生变化的时候触发此事件 |  |
  
  