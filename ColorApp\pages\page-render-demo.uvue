<template>
	<!-- #ifdef APP -->
	<scroll-view class="container" scroll-y="true">
	<!-- #endif -->
		<view class="page-container">
			<!-- 页面标题 -->
			<view class="header">
				<text class="title">页面渲染器演示</text>
				<text class="subtitle">基于 JSON 配置的动态页面渲染</text>
			</view>

			<!-- 配置选择区域 -->
			<view class="config-section">
				<text class="section-title">选择配置</text>
				<view class="config-buttons">
					<button 
						v-for="(config, index) in configOptions" 
						:key="index"
						class="config-button"
						:class="{ active: selectedConfig === index }"
						@click="selectConfig(index)"
					>
						<text class="config-button-text">{{ config.name }}</text>
					</button>
				</view>
			</view>

			<!-- 页面渲染区域 -->
			<view class="render-section">
				<text class="section-title">页面预览</text>
				<view class="render-container">
					<PageRender 
						v-if="currentConfig"
						:config="currentConfig.data"
						:auto-load="true"
						@loaded="onPageLoaded"
						@error="onPageError"
						@event="onPageEvent"
					/>
					<view v-else class="placeholder">
						<text class="placeholder-text">请选择一个配置进行预览</text>
					</view>
				</view>
			</view>

			<!-- 事件日志区域 -->
			<view class="events-section">
				<view class="events-header">
					<text class="section-title">事件日志</text>
					<button class="clear-button" @click="clearEvents">
						<text class="clear-text">清空</text>
					</button>
				</view>
				<scroll-view class="events-scroll" scroll-y="true">
					<view class="events-list">
						<view v-if="events.length === 0" class="events-empty">
							<text class="empty-text">暂无事件记录</text>
						</view>
						<view 
							v-for="(event, index) in events" 
							:key="index"
							class="event-item"
						>
							<view class="event-header">
								<text class="event-type">{{ event.type }}</text>
								<text class="event-time">{{ event.time }}</text>
							</view>
							<text class="event-data">{{ event.data }}</text>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script setup lang="uts">
	import { ref, computed } from 'vue'
	import PageRender from '../components/PageRender.uvue'
	
	// 响应式数据
	const selectedConfig = ref(1)  // 默认选择"信息展示"配置
	const events = ref<any[]>([])
	
	// 配置选项
	const configOptions = ref([
		{
			name: '登录表单',
			data: {
				version: '1.0',
				pageId: 'loginPage',
				header: {
					title: '用户登录',
					showBackButton: true
				},
				dataSources: {
					loginForm: {
						id: 'loginForm',
						type: 'local',
						options: {},
						data: {
							username: '',
							password: ''
						},
						autoLoad: false
					}
				},
				router: {
					path: '/login'
				},
				pageLayout: {
					id: 'mainLayout',
					component: 'flex-layout',
					props: {
						flexDirection: 'column',
						justifyContent: 'center',
						alignItems: 'center',
						class: 'bg-primary',
						padding: '20px'
					}
				},
				components: [
					{
						id: 'formContainer',
						component: 'view',
						props: {
							class: 'text-white',
							backgroundColor: 'rgba(255,255,255,0.1)',
							borderRadius: '8px',
							padding: '20px',
							width: '100%'
						},
						dataSource: 'loginForm',
						children: [
							{
								id: 'usernameField',
								component: 'input',
								props: {
									type: 'text',
									label: '用户名',
									dataBind: 'username',
									required: true,
									placeholder: '请输入用户名'
								}
							},
							{
								id: 'passwordField',
								component: 'input',
								props: {
									type: 'password',
									label: '密码',
									dataBind: 'password',
									required: true,
									placeholder: '请输入密码'
								}
							},
							{
								id: 'submitBtn',
								component: 'button',
								props: {
									color: 'primary'
								},
								value: {
									'zh-CN': '登录',
									'en-US': 'Login'
								},
								events: {
									click: [
										{
											type: 'validateForm',
											target: 'formContainer'
										},
										{
											type: 'apiRequest',
											options: {
												endpoint: '/api/login',
												method: 'POST',
												params: {}
											},
											payload: 'loginForm',
											onSuccess: {
												type: 'redirect',
												path: '/dashboard'
											}
										}
									]
								}
							}
						]
					}
				]
			}
		},
		{
			name: '信息展示',
			data: {
				version: '1.0',
				pageId: 'infoPage',
				header: {
					title: '信息展示',
					showBackButton: true
				},
				dataSources: {
					userInfo: {
						id: 'userInfo',
						type: 'local',
						options: {},
						data: {
							name: '张三',
							email: '<EMAIL>',
							phone: '13800138000',
							avatar: '/static/avatar.png'
						},
						autoLoad: false
					}
				},
				pageLayout: {
					id: 'infoLayout',
					component: 'flex-layout',
					props: {
						flexDirection: 'column',
						padding: '20px',
						backgroundColor: '#f5f5f5'
					}
				},
				components: [
					{
						id: 'userCard',
						component: 'view',
						props: {
							backgroundColor: '#ffffff',
							borderRadius: '8px',
							padding: '20px',
							marginBottom: '15px'
						},
						dataSource: 'userInfo',
						children: [
							{
								id: 'userName',
								component: 'text',
								props: {
									fontSize: '18px',
									fontWeight: 'bold',
									color: '#333333',
									marginBottom: '10px'
								},
								value: '用户姓名：张三'
							},
							{
								id: 'userEmail',
								component: 'text',
								props: {
									fontSize: '14px',
									color: '#666666',
									marginBottom: '5px'
								},
								value: '邮箱：<EMAIL>'
							},
							{
								id: 'userPhone',
								component: 'text',
								props: {
									fontSize: '14px',
									color: '#666666'
								},
								value: '电话：13800138000'
							}
						]
					},
					{
						id: 'actionButtons',
						component: 'view',
						props: {
							flexDirection: 'row',
							justifyContent: 'space-between'
						},
						children: [
							{
								id: 'editBtn',
								component: 'button',
								props: {
									backgroundColor: '#007aff',
									flex: '1',
									marginRight: '10px'
								},
								value: '编辑',
								events: {
									click: [
										{
											type: 'redirect',
											path: '/edit-profile'
										}
									]
								}
							},
							{
								id: 'deleteBtn',
								component: 'button',
								props: {
									backgroundColor: '#ff3b30',
									flex: '1'
								},
								value: '删除',
								events: {
									click: [
										{
											type: 'confirm',
											message: '确定要删除吗？'
										}
									]
								}
							}
						]
					}
				]
			}
		}
	])
	
	// 计算属性：当前选中的配置
	const currentConfig = computed(() => {
		return configOptions.value[selectedConfig.value] || null
	})
	
	// 选择配置
	const selectConfig = (index: number) => {
		selectedConfig.value = index
		addEvent('config-change', `切换到配置: ${configOptions.value[index].name}`)
	}
	
	// 页面加载完成事件
	const onPageLoaded = (config: any) => {
		addEvent('page-loaded', `页面加载完成: ${config.pageId}`)
	}
	
	// 页面加载错误事件
	const onPageError = (error: string) => {
		addEvent('page-error', `页面加载错误: ${error}`)
	}
	
	// 页面组件事件
	const onPageEvent = (eventData: any) => {
		addEvent(eventData.type, `组件事件 - 目标: ${eventData.target}, 数据: ${JSON.stringify(eventData.data)}`)
	}
	
	// 添加事件记录
	const addEvent = (type: string, data: string) => {
		events.value.unshift({
			type: type,
			data: data,
			time: new Date().toLocaleTimeString()
		})
		
		// 限制事件记录数量
		if (events.value.length > 50) {
			events.value = events.value.slice(0, 50)
		}
	}
	
	// 清空事件记录
	const clearEvents = () => {
		events.value = []
		addEvent('system', '事件日志已清空')
	}
</script>

<style scoped>
	.container {
		flex: 1;
		background-color: #f5f5f5;
	}
	
	.page-container {
		flex: 1;
		flex-direction: column;
	}
	
	.header {
		background-color: #ffffff;
		padding: 20px;
		border-bottom: 1px solid #e5e5e5;
	}
	
	.title {
		font-size: 24px;
		font-weight: bold;
		color: #333333;
		margin-bottom: 5px;
	}
	
	.subtitle {
		font-size: 14px;
		color: #666666;
	}
	
	.config-section {
		background-color: #ffffff;
		margin-top: 10px;
		padding: 15px 20px;
		border-bottom: 1px solid #e5e5e5;
	}
	
	.section-title {
		font-size: 16px;
		font-weight: bold;
		color: #333333;
		margin-bottom: 10px;
	}
	
	.config-buttons {
		flex-direction: row;
		flex-wrap: wrap;
	}
	
	.config-button {
		background-color: #f8f9fa;
		border: 1px solid #e9ecef;
		border-radius: 20px;
		padding: 8px 16px;
		margin-right: 10px;
		margin-bottom: 10px;
	}
	
	.config-button.active {
		background-color: #007aff;
		border-color: #007aff;
	}
	
	.config-button-text {
		font-size: 14px;
		color: #333333;
	}
	
	.config-button.active .config-button-text {
		color: #ffffff;
	}
	
	.render-section {
		background-color: #ffffff;
		margin-top: 10px;
		padding: 15px 20px;
		border-bottom: 1px solid #e5e5e5;
		flex: 1;
	}
	
	.render-container {
		flex: 1;
		border: 1px dashed #dee2e6;
		border-radius: 8px;
		margin-top: 10px;
		overflow: hidden;
	}
	
	.placeholder {
		align-items: center;
		justify-content: center;
		height: 200px;
	}
	
	.placeholder-text {
		font-size: 14px;
		color: #999999;
	}
	
	.events-section {
		background-color: #ffffff;
		margin-top: 10px;
		padding: 15px 20px;
		height: 200px;
	}
	
	.events-header {
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 10px;
	}
	
	.clear-button {
		background-color: #e3f2fd;
		border: none;
		border-radius: 4px;
		padding: 5px 10px;
	}
	
	.clear-text {
		font-size: 14px;
		color: #007aff;
	}
	
	.events-scroll {
		flex: 1;
	}
	
	.events-list {
		flex-direction: column;
	}
	
	.events-empty {
		align-items: center;
		justify-content: center;
		height: 100px;
	}
	
	.empty-text {
		font-size: 14px;
		color: #999999;
	}
	
	.event-item {
		padding: 10px;
		margin-bottom: 5px;
		background-color: #f8f9fa;
		border-radius: 4px;
		border-left: 3px solid #007aff;
	}
	
	.event-header {
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 5px;
	}
	
	.event-type {
		font-size: 12px;
		font-weight: bold;
		color: #007aff;
		background-color: #e3f2fd;
		padding: 2px 6px;
		border-radius: 4px;
	}
	
	.event-time {
		font-size: 12px;
		color: #666666;
	}
	
	.event-data {
		font-size: 12px;
		color: #333333;
	}
</style>