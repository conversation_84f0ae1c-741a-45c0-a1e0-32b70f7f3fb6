{
	// npx @dcloudio/toolkit link --clear ux-shimmer ux-cloud ux-plus ux-soter ux-movable ux-pinyin ux-orm ux-sqlite ux-files ux-share ux-weixin ux-wxwork ux-douyin ux-network ux-permission ux-notification ux-mqtt ux-storekit ux-camera-view ux-lottie ux-svg ux-blur ux-sprite
    "uni_modules": [
		"/Users/<USER>/Documents/work/ux-frame/ux-cloud/uni_modules",
		"/Users/<USER>/Documents/work/ux-frame/ux-plus/uni_modules",
		"/Users/<USER>/Documents/work/ux-frame/ux-soter/uni_modules",
		"/Users/<USER>/Documents/work/ux-frame/ux-movable/uni_modules",
		"/Users/<USER>/Documents/work/ux-frame/ux-pinyin/uni_modules",
		"/Users/<USER>/Documents/work/ux-frame/ux-orm/uni_modules",
		"/Users/<USER>/Documents/work/ux-frame/ux-orm/uni_modules",
		"/Users/<USER>/Documents/work/ux-frame/ux-files/uni_modules",
		"/Users/<USER>/Documents/work/ux-frame/ux-share/uni_modules",
		"/Users/<USER>/Documents/work/ux-frame/ux-weixin/uni_modules",
		"/Users/<USER>/Documents/work/ux-frame/ux-douyin/uni_modules",
		"/Users/<USER>/Documents/work/ux-frame/ux-network/uni_modules",
		"/Users/<USER>/Documents/work/ux-frame/ux-permission/uni_modules",
		"/Users/<USER>/Documents/work/ux-frame/ux-notification/uni_modules",
		"/Users/<USER>/Documents/work/ux-frame/ux-mqtt/uni_modules",
		"/Users/<USER>/Documents/work/ux-frame/ux-storekit/uni_modules",
		"/Users/<USER>/Documents/work/ux-frame/ux-camera-view/uni_modules",
		"/Users/<USER>/Documents/work/ux-frame/ux-lottie/uni_modules",
		"/Users/<USER>/Documents/work/ux-frame/ux-svg/uni_modules",
		"/Users/<USER>/Documents/work/ux-frame/ux-blur/uni_modules",
		"/Users/<USER>/Documents/work/ux-frame/ux-sprite/uni_modules",
		"/Users/<USER>/Documents/work/ux-frame/ux-shimmer/uni_modules"
	]
}
