<template>
	<ux-placeholder v-if="fixed" :height="height"></ux-placeholder>
	<view :id="myId" class="ux-sticky-header" :style="style">
		<slot></slot>
	</view>
</template>

<script setup lang="ts">
	
	/**
	 * StickyHeader 吸顶布局容器, 当一个容器视图设置多个sticky-header时，后一个sticky-header会停靠在前一个sticky-header的末尾处
	 * @demo pages/component/sticky.uvue
	 * @tutorial https://www.uxframe.cn/component/sticky.html
	 * @property {String}		id							String | header id
	 * @property {Number[]}		padding						Number[] | 长度为 4 的数组，按 top、right、bottom、left 顺序指定内边距
	 * <AUTHOR>
	 * @date 2024-12-05 15:20:35
	 */
	
	import { ref, computed, PropType, onMounted, getCurrentInstance, inject } from 'vue'
	import { $ux } from '../../index'

	defineOptions({
		name: 'ux-sticky-header',
	})

	const props = defineProps({
		id: {
			type: String,
			default: ''
		},
		padding: {
			type: Array as PropType<number[]>,
			default: () => [] as number[]
		}
	})
	
	const instance = getCurrentInstance()?.proxy
	const top = ref(0)
	const left = ref(0)
	const width = ref(0)
	const height = ref(0)
	const fixed = ref(false)
	const childIndex = ref(0)
	
	const pushPinnedHeader = inject('pushPinnedHeader', false) as boolean
	const index = inject('index', 0) as number
	const scrollTop = inject('scrollTop', 0)
	
	const myId = computed(() => {
		return props.id == '' ? `ux-sticky-header-${$ux.Random.uuid()}` : props.id
	})
	
	const offset = computed(() => {
		return pushPinnedHeader ? 0 : (index * height.value + childIndex.value * height.value)
	})
	
	const style = computed(() => {
		const css = {}
		
		if(fixed.value) {
			css['position'] = fixed.value ? 'fixed' : 'relative'
			css['top'] = `${scrollTop.value + offset.value}px`
			css['left'] = `${left.value}px`
			css['width'] = `${width.value}px`
			css['height'] = `${height.value}px`
			css['z-index'] = 1
		} else {
			css['position'] = 'relative'
			css['z-index'] = 0
		}
		
		if(props.padding.length > 0) {
			css['padding'] = props.padding.map(e => $ux.Util.addUnit(e))
		}
		
		return css
	})
	
	function uptStatus(y: number, i: number) {
		childIndex.value = i
		fixed.value = Math.max(y, 0) > Math.max(top.value - (scrollTop.value + offset.value), 0)
	}
	
	function register() {
		$ux.Util.$dispatch(instance, 'ux-sticky-section', 'register', instance, top.value)
	}
	
	onMounted(() => {
		let f = async () => {
			const rect = await $ux.Util.getBoundingClientRect(`#${myId.value}`, instance)
			top.value = rect?.top ?? 0
			left.value = rect?.left ?? 0
			width.value = rect?.width ?? 0
			height.value = rect?.height ?? 0
			register()
		}
		
		setTimeout(() => {
			f()
		}, 50);
	})
	
	defineExpose({
		uptStatus
	})
</script>

<style lang="scss" scoped>
	.ux-sticky-header {
		top: 0;
		overflow: hidden;
	}
</style>