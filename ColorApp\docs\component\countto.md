<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/countto?title=Countto"></Mobile>

# Countto
> 组件类型：UxCounttoComponentPublicInstance

支持设置滚动时间，可显示小数

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| [theme](#theme) | String | info | 主题 |
| startVal | Number | 0 | 开始的数值 |
| endVal | Number | 0 | 要滚动的目标数值 |
| duration | Number | 2000 | 滚动到目标数值的动画持续时间，单位为毫秒(ms) |
| autoplay | Boolean | true | 设置数值后是否自动开始滚动 |
| decimals | Number | 0 | 要显示的小数位数 |
| [format](#format) | String |  | 格式化规则 |
| color | String | `$ux.Conf.fontColor` | 字体颜色 |
| [darkColor](#darkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| size | Any | `$ux.Conf.fontSize` | 字体大小 |
| bold | Boolean | false | 字体是否加粗 |
| margin | Array |  | 边距[上右下左][上下左右][上下左右] |
| mt | Any |  | 距上单位px |
| mr | Any |  | 距右单位px |
| mb | Any |  | 距下单位px |
| ml | Any |  | 距左单位px |
| padding | Array |  | 填充[上右下左][上下左右][上下左右] |
| pt | Any |  | 上内边距单位px |
| pr | Any |  | 右内边距单位px |
| pb | Any |  | 下内边距单位px |
| pl | Any |  | 左内边距单位px |
| xstyle | Array |  | 自定义样式 |

### [theme](#theme)

| 值   | 说明 |
|:------:|:----:|
| text | 文字
 |
| info | 默认
 |
| primary | 主要
 |
| success | 成功
 |
| warning | 警告
 |
| error | 错误
 |

### [format](#format)

| 值   | 说明 |
|:------:|:----:|
| cmoney大写金额
 |  |
| qmoney金额千分制
 |  |
| wmoney金额万分制
 |  |

### [darkColor](#darkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| end | 数值滚动到目标值时触发 |  |
  
  