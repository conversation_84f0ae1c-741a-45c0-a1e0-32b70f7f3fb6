<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PageRender 组件演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section h2 {
            color: #333;
            font-size: 1.8rem;
            margin-bottom: 20px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            border-left: 4px solid #667eea;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .feature-card h3 {
            color: #667eea;
            font-size: 1.3rem;
            margin-bottom: 10px;
        }
        
        .feature-card p {
            color: #666;
            line-height: 1.6;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 20px 0;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .json-example {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .json-example pre {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            line-height: 1.4;
            color: #2d3748;
            overflow-x: auto;
        }
        
        .component-demo {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            text-align: center;
            margin: 30px 0;
        }
        
        .component-demo h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
        }
        
        .component-demo p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .file-list {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .file-list ul {
            list-style: none;
        }
        
        .file-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
            color: #4a5568;
        }
        
        .file-list li:last-child {
            border-bottom: none;
        }
        
        .file-list li::before {
            content: "📄";
            margin-right: 10px;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }
        
        .highlight h4 {
            color: #2d3748;
            margin-bottom: 10px;
        }
        
        .highlight p {
            color: #4a5568;
            line-height: 1.6;
        }
        
        .footer {
            background: #2d3748;
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .footer p {
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .content {
                padding: 20px;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 PageRender 组件系统</h1>
            <p>基于 JSON 配置的动态页面渲染解决方案</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>✨ 功能特性</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>🎨 动态页面渲染</h3>
                        <p>根据 JSON 配置动态生成完整的页面布局，支持 flex 布局系统和自定义样式主题。</p>
                    </div>
                    <div class="feature-card">
                        <h3>🧩 组件化架构</h3>
                        <p>支持多种基础组件类型，包括视图、输入框、按钮、文本、图片等，支持组件嵌套和层级结构。</p>
                    </div>
                    <div class="feature-card">
                        <h3>📊 数据源管理</h3>
                        <p>支持多种数据源类型（API、本地数据），支持数据绑定和自动加载，数据源之间可以关联。</p>
                    </div>
                    <div class="feature-card">
                        <h3>⚡ 事件处理系统</h3>
                        <p>支持组件事件配置，包括表单验证、API 请求、页面跳转等事件类型，支持事件链和条件执行。</p>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>🏗️ 组件架构</h2>
                <div class="component-demo">
                    <h3>PageRender + DynamicComponent</h3>
                    <p>PageRender 负责页面级别的渲染和管理，DynamicComponent 负责具体组件的动态渲染</p>
                </div>
                
                <div class="highlight">
                    <h4>🎯 核心组件说明</h4>
                    <p><strong>PageRender.uvue</strong> - 页面渲染器组件，处理页面配置、数据源管理、事件系统等</p>
                    <p><strong>DynamicComponent.uvue</strong> - 动态组件渲染器，根据配置渲染具体的 UI 组件</p>
                </div>
            </div>
            
            <div class="section">
                <h2>📝 配置示例</h2>
                <p>以下是一个登录页面的 JSON 配置示例：</p>
                <div class="json-example">
                    <pre>{
  "version": "1.0",
  "pageId": "loginPage",
  "header": {
    "title": "用户登录",
    "showBackButton": true
  },
  "dataSources": {
    "loginForm": {
      "id": "loginForm",
      "type": "local",
      "data": {
        "username": "",
        "password": ""
      },
      "autoLoad": false
    }
  },
  "pageLayout": {
    "id": "mainLayout",
    "component": "flex-layout",
    "props": {
      "flexDirection": "column",
      "justifyContent": "center",
      "alignItems": "center",
      "class": "bg-primary"
    }
  },
  "components": [
    {
      "id": "formContainer",
      "component": "view",
      "props": {
        "class": "form-container"
      },
      "dataSource": "loginForm",
      "children": [
        {
          "id": "usernameField",
          "component": "input",
          "props": {
            "type": "text",
            "label": "用户名",
            "dataBind": "username",
            "required": true
          }
        },
        {
          "id": "submitBtn",
          "component": "button",
          "value": {
            "zh-CN": "登录",
            "en-US": "Login"
          },
          "events": {
            "click": [
              {
                "type": "validateForm",
                "target": "formContainer"
              },
              {
                "type": "apiRequest",
                "options": {
                  "endpoint": "/api/login",
                  "method": "POST"
                },
                "payload": "loginForm",
                "onSuccess": {
                  "type": "redirect",
                  "path": "/dashboard"
                }
              }
            ]
          }
        }
      ]
    }
  ]
}</pre>
                </div>
            </div>
            
            <div class="section">
                <h2>🎮 支持的组件类型</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>基础组件</h3>
                        <p>view, text, image, button</p>
                    </div>
                    <div class="feature-card">
                        <h3>表单组件</h3>
                        <p>input, textarea, switch, picker</p>
                    </div>
                    <div class="feature-card">
                        <h3>布局组件</h3>
                        <p>scroll-view, list-view, flex-layout</p>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>📁 项目文件结构</h2>
                <div class="file-list">
                    <ul>
                        <li>components/PageRender.uvue - 页面渲染器组件</li>
                        <li>components/DynamicComponent.uvue - 动态组件渲染器</li>
                        <li>pages/page-render-demo.uvue - 使用示例页面</li>
                        <li>docs/page_design/page.json - 配置示例文件</li>
                        <li>docs/PageRenderJson-README.md - 详细使用说明</li>
                    </ul>
                </div>
            </div>
            
            <div class="section">
                <h2>🚀 使用方法</h2>
                <div class="code-block">
&lt;template&gt;
  &lt;PageRender 
    :config="pageConfig"
    :auto-load="true"
    @loaded="onPageLoaded"
    @error="onPageError"
    @event="onPageEvent"
  /&gt;
&lt;/template&gt;

&lt;script setup&gt;
import PageRender from '@/components/PageRender.uvue'

const pageConfig = {
  // 页面配置对象
}

const onPageLoaded = (config) => {
  console.log('页面加载完成:', config)
}
&lt;/script&gt;
                </div>
            </div>
            
            <div class="highlight">
                <h4>💡 开发提示</h4>
                <p>这是一个 uni-app-x 项目，需要使用 HBuilderX 或相应的 uni-app 开发环境来运行。组件已经创建完成并可以在项目中使用。</p>
            </div>
        </div>
        
        <div class="footer">
            <p>🎉 PageRender 组件系统已成功创建！可以开始使用基于 JSON 配置的动态页面渲染功能了。</p>
        </div>
    </div>
</body>
</html>