# 原子化AutoCSS AI说明


## 一、框架简介
原子化AutoCSS是一款基于`vite HMR`技术的即时原子化CSS框架，核心能力是根据页面class属性实时生成行内样式，旨在帮助开发者省去手写样式的繁琐工作，适配低代码开发场景，是高性能原生UI框架的重要组成部分。


## 二、核心功能特色
1. **即时原子化**：实时解析页面class，动态生成对应样式，无需手动编写CSS。  
2. **语法兼容性**：支持大多数主流原子化框架语法规则，降低学习成本。  
3. **深度适配**：专门适配uniapp x编译器，满足跨端开发需求。  
4. **自动化能力**：配置、全局样式可自动生成，支持样式自动导入，提升开发效率。  
5. **高度自定义**：支持自定义样式生成规则、颜色库、快捷方式等，无预设样式束缚。  


## 三、前提配置
使用前需在项目根目录创建`vite.config.ts`配置文件，引入并配置AutoCSS插件，示例如下：  
```typescript
import { defineConfig } from 'vite';
import uni from '@dcloudio/vite-plugin-uni';
import autocss from './uni_modules/ux-frame/libs/plugins/autocss/index.mjs';

export default defineConfig({
  plugins: [
    uni(),
    autocss({
      debug: false, // 调试模式开关
      autoUseSnippets: true, // 自动使用代码片段
      generateGlobalCss: false, // 是否生成全局CSS
      configFile: './autocss/config.js', // 自定义配置文件路径
      cssFile: 'autocss/index.css', // 生成的CSS文件路径
      cssSnippetsFile: 'autocss/snippets.css', // 代码片段CSS路径
      exclude: ['uni_modules/*', 'node_modules/*'] // 排除文件
    })
  ]
});
```


## 四、基础使用方式
通过在HTML/组件标签的`class`属性中写入原子化语法，即可自动生成对应样式，示例：  
```html
<!-- 宽300px、高30px、蓝底、Flex布局居中 -->
<view class="w-300 h-30 bg-blue-65 flex-row justify-content-center align-items-center">
  <!-- 字体大小16px、白色、加粗 -->
  <text class="fs-16 c-fff fw-700">字体大小颜色</text>
</view>
```


## 五、核心语法规则
### 1. 简写约定
- `m`：margin（外边距）  
- `p`：padding（内边距）  
- `h`：height（高度）  
- `w`：width（宽度）  


### 2. 方向约定（trblxy）
- `t`（上）、`r`（右）、`b`（下）、`l`（左）：单方向  
- `x`：左右（lr）、`y`：上下（tb）：双向组合  
- 优先级：单方向（t/r/b/l）> 双向组合（x/y）> 四项组合  


### 3. 伪类与媒体查询
- **伪类**：通过`hover:xxx`、`active:xxx`语法实现，如`hover:c-red`（ hover时文字变红）。  
- **媒体查询**：内置`sm`（≥640px）、`md`（≥768px）、`lg`（≥1024px）、`xl`（≥1280px）等断点，语法如`lg@w-300`（大屏时宽度300px）。  


### 4. 数值与单位
- **数值**：支持正数（如`m-16`）、负数（如`m--16`或`m-m-16`）、小数（如`w-33.33p`）。  
- **单位**：默认单位为`px`，支持`p`（百分比）、`rem`、`vh`、`vw`等，内置单位列表：`cm、mm、in、px、pt、pc、em、ex、ch、rem、vw、vh、vmin、vmax、p、rpx`。  


### 5. 常用属性规则示例
| 功能         | 语法示例                | 生成CSS                          |
|--------------|-------------------------|----------------------------------|
| 宽高         | `w-300`、`h-50p`        | `width:300px`、`height:50%`      |
| 内外边距     | `m-x-20`、`p-b-16rem`   | `margin-left:20px;margin-right:20px`、`padding-bottom:16rem` |
| Flex布局     | `flex-center-center`    | `display:flex;justify-content:center;align-items:center` |
| 颜色         | `c-red-50`、`bg-blue`   | `color:rgba(255,0,0,0.5)`、`background:rgba(0,121,255,1)` |
| 边框         | `border-2`、`br-10`     | `border:2px solid black`、`border-radius:10px` |


# 原子化AutoCSS 语法规则详解


## 一、通用基础约定


### 1. 简写约定  
用于简化常用CSS属性的书写，核心简写对应关系如下：  
- `m`：对应`margin`（外边距）  
- `p`：对应`padding`（内边距）  
- `h`：对应`height`（高度）  
- `w`：对应`width`（宽度）  
- `lh`/`leading`：对应`line-height`（行高）  
- `fs`：对应`font-size`（字体大小）  
- `fw`：对应`font-weight`（字体粗细）  
- `br`/`rounded`：对应`border-radius`（边框圆角）  
- `of`：对应`overflow`（溢出处理）  


### 2. 方向约定（trblxy）  
用于指定方位（如边距、边框的方向），规则如下：  
- 单方向：`t`（上）、`r`（右）、`b`（下）、`l`（左）  
- 双向组合：`x`（左右，等价于`lr`）、`y`（上下，等价于`tb`）  
- 优先级：单方向（`t/r/b/l`）> 双向组合（`x/y`）> 四项组合（未指定方向时默认全方向）  

**示例**：  
- `m-t-10`：`margin-top:10px`（单方向）  
- `p-x-20`：`padding-left:20px;padding-right:20px`（双向组合）  
- `m-16`：`margin:16px`（全方向）  


### 3. 伪类约定  
支持通过伪类前缀指定交互状态样式，语法格式为`[伪类]:[属性]`，支持的伪类包括：  
`hover`（悬停）、`link`（未访问链接）、`visited`（已访问链接）、`active`（激活）、`focus`（聚焦）、`focus-within`（内部聚焦）等。  

**示例**：  
- `hover:c-red`：`hover时文字颜色为红色`，生成CSS：`.hover\:c-red:hover { color: rgba(255,0,0,1); }`  
- `active:w-200`：`active时宽度为200px`，生成CSS：`.active\:w-200:active { width: 200px; }`  


### 4. 媒体查询约定  
支持通过断点前缀适配不同屏幕尺寸，内置断点及对应规则：  
- `sm`：`(min-width: 640px)`（小屏）  
- `md`：`(min-width: 768px)`（中屏）  
- `lg`：`(min-width: 1024px)`（大屏）  
- `xl`：`(min-width: 1280px)`（超大屏）  

语法格式：`[断点]@[属性]`；若与伪类结合，格式为`[断点]@[伪类]:[属性]`。  

**示例**：  
- `sm@bg-fff`：`屏幕≥640px时背景为白色`，生成CSS：`@media (min-width: 640px) { .sm\@bg-fff { background: rgba(255,255,255,1); } }`  
- `lg@hover:w-300`：`屏幕≥1024px且hover时宽度为300px`，生成CSS：`@media (min-width: 1024px) { .lg\@hover\:w-300:hover { width: 300px; } }`  


### 5. 数值约定  
用于表示正数、负数及小数，规则如下：  
- 正数：直接写数值，如`m-16`（`margin:16px`）  
- 负数：通过`m-`或`--`前缀，如`m--16`（`margin:-16px`）、`m-x-m-22`（`margin-left:-22px;margin-right:-22px`）  
- 小数：支持带小数点的数值，如`m-t-0.5vh`（`margin-top:0.5vh`）、`w-33.333p`（`width:33.333%`）  


### 6. 单位约定  
支持多种CSS单位，默认单位为`px`，核心规则：  
- 单位简写：`p`代表`%`（百分比），如`w-50p`（`width:50%`）  
- 可变单位`v`：需在配置文件中通过`vToAny`启用，如`w-10v`（`width:10/16rem=0.625rem`，具体转换规则自定义）  
- 支持的完整单位列表：`cm、mm、in、px、pt、pc、em、ex、ch、rem、vw、vh、vmin、vmax、p、rpx`  

**示例**：  
- `h-100`：默认单位`px`，生成`height:100px`  
- `w-20rem`：指定单位`rem`，生成`width:20rem`  
- `m-x-50p`：单位`p`对应`%`，生成`margin-left:50%;margin-right:50%`  


### 7. 属性约定  
- 基础规则：大多数属性遵循“`key-value`”形式，如`text-center`（`text-align:center`）  
- 简写兼容：支持简写与全写，如`d-flex`与`display-flex`（均对应`display:flex`）  
- 组合属性：提供常用样式组合的简写，如`square-80`（`width:80px;height:80px`）、`flex-center-center`（`display:flex;justify-content:center;align-items:center`）  


## 二、具体属性语法规则  


### 1. 宽高相关  
#### 基础宽高  
- 语法：`(w|h)-(数值)(单位)?`  
- 正则：`^[wh]-(0|[1-9]\\d*)(${UNIT_ENUM_STR})?$`  
- 示例：  
  - `w-10p` → `width:10%`  
  - `h-375` → `height:375px`  
  - `w-20rem` → `width:20rem`  

#### 最大/最小宽高  
- 语法：`(min|max)-(h|w)-(数值)(单位)?`  
- 正则：`^(min|max)-[wh]-(0|[1-9]\\d*)(${UNIT_ENUM_STR})?$`  
- 示例：  
  - `max-w-100` → `max-width:100px`  
  - `min-h-200vh` → `min-height:200vh`  

#### 正方形（宽高相等）  
- 语法：`square-(数值)(单位)?`  
- 正则：`^square-(0|[1-9]\\d*)(${UNIT_ENUM_STR})?$`  
- 示例：  
  - `square-80` → `width:80px;height:80px`  
  - `square-50p` → `width:50%;height:50%`  


### 2. 内外间距（margin/padding）  
- 语法：`(m|margin|p|padding)-(方向-)?(数值)(单位)?`（支持正负值）  
- 正则：`^(?<type>m|margin|p|padding)(-|)((?<direction>[trblxy])-)?((?<auto>auto)|(?<isMinus>(m-|-))?(?<num>${NONNEGATIVE_NUMBER_REGEX_STR})(?<unit>${UNIT_STR})?)$`  
- 方向说明：`t`（上）、`r`（右）、`b`（下）、`l`（左）、`x`（左右）、`y`（上下），省略方向则为全方向  
- 示例：  
  - `m-16` → `margin:16px`  
  - `m-b-32rem` → `margin-bottom:32rem`  
  - `p-x-24` → `padding-left:24px;padding-right:24px`  
  - `m--16` → `margin:-16px`  
  - `mx-20`（简写）→ `margin-left:20px;margin-right:20px`  
  - `px-10`（简写）→ `padding-left:10px;padding-right:10px`  


### 3. 定位与层级  
#### 定位方式  
- 语法：`(position|)-(参数)`  
- 正则：`/^(position-|)(?<value>static|relative|sticky|unset|absolute|fixed|inherit|initial)$/`  
- 示例：  
  - `relative` → `position:relative`  
  - `position-fixed` → `position:fixed`  

#### 方向定位（top/right/bottom/left）  
- 语法：`(inset-)?(方向[trblxy]|top|right|bottom|left)-(数值)(单位)?`（支持正负值）  
- 正则：`^(inset-)?(?<direction>[trblxy]|top|right|bottom|left)-(?<isMinus>m-|-)?(?<num>${NONNEGATIVE_NUMBER_REGEX_STR}|a)(?<unit>${UNIT_STR})?$`  
- 示例：  
  - `l-283` → `left:283px`  
  - `top-0px` → `top:0`  
  - `right-m-672` → `right:-672px`  
  - `inset-x-10` → `left:10px;right:10px`  

#### 层级（z-index）  
- 语法：`(z-index|z)-(m-|-)?(数值)`  
- 正则：`/^(z-index|z)-(?<isMinus>(m-|-))?(?<value>0|[1-9]\d*)$/`  
- 示例：  
  - `z-9` → `z-index:9`  
  - `z-index-m-9` → `z-index:-9`  


### 4. Flex布局相关  
#### Flex容器与方向  
- 语法1（启用flex）：`(display|d)-flex` 或 `flex` → `display:flex`  
- 语法2（方向）：`(flex-direction|flex)-(row|row-reverse|column|col|column-reverse|col-reverse)`  
- 示例：  
  - `flex-row` → `flex-direction:row`  
  - `flex-col-reverse` → `flex-direction:column-reverse`  

#### Flex换行  
- 语法：`(flex-wrap|flex)-(inherit|initial|nowrap|wrap|wrap-reverse)`  
- 示例：  
  - `flex-wrap` → `flex-wrap:wrap`  
  - `flex-nowrap` → `flex-wrap:nowrap`  

#### 主轴对齐（justify-content）  
- 语法：`(justify-content|justify)-(参数)`，参数包括`start|end|center|space-between|space-around|space-evenly`等  
- 示例：  
  - `justify-center` → `justify-content:center`  
  - `justify-space-between` → `justify-content:space-between`  

#### 交叉轴对齐（align-items）  
- 语法：`(align-items|items|align-content|content)-(参数)`，参数包括`start|end|center|baseline|stretch`等  
- 示例：  
  - `items-center` → `align-items:center`  
  - `content-start` → `align-content:flex-start`  

#### Flex组合对齐  
- 语法：`flex-(主轴参数)-(交叉轴参数)`  
- 示例：  
  - `flex-space-between-center` → `display:flex;justify-content:space-between;align-items:center`  
  - `flex-center-center` → `display:flex;justify-content:center;align-items:center`  

#### Flex子项系数  
- 语法：`flex-(1|auto|none|数值)`  
- 示例：  
  - `flex-1` → `flex:1`  
  - `flex-none` → `flex:none`  

#### Flex伸缩（grow/shrink）  
- 语法：`(flex-|)(shrink|grow)-(数值|initial|inherit)`  
- 示例：  
  - `flex-grow-1` → `flex-grow:1`  
  - `shrink-0` → `flex-shrink:0`  


### 5. 字体与文本相关  
#### 字体大小  
- 语法：`(font-size|fs|text)-(数值)(单位)?`  
- 示例：  
  - `fs-22` → `font-size:22px`  
  - `text-44rem` → `font-size:44rem`  

#### 字体粗细  
- 语法：`(font-weight|fw|font)-(100-900|bold|bolder|lighter|normal)`  
- 示例：  
  - `fw-700` → `font-weight:700`  
  - `font-bold` → `font-weight:bold`  

#### 行高  
- 语法：`(lh|line-height|leading)-(((数值)(单位)?)|normal|unset|inherit|initial)`  
- 示例：  
  - `lh-14` → `line-height:14px`  
  - `leading-normal` → `line-height:normal`  

#### 文本对齐  
- 语法：`(text-align|text)-(left|center|right|justify)`  
- 示例：  
  - `text-center` → `text-align:center`  
  - `text-align-right` → `text-align:right`  

#### 文本溢出与换行  
- 语法1（单行省略）：`ellipsis` 或 `text-ellipsis` → `overflow:hidden;text-overflow:ellipsis;white-space:nowrap`  
- 语法2（多行省略）：`(text-)?ellipsis-[1-9]\d*`  
- 示例：  
  - `ellipsis-2` → `overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical`  

#### 字间距  
- 语法：`(letter-spacing|tracking)-(数值)(单位)?`  
- 示例：  
  - `tracking-4` → `letter-spacing:4px`  
  - `letter-spacing-2rem` → `letter-spacing:2rem`  


### 6. 颜色相关  
#### 字体颜色  
- 语法：`(color|c|text)-?(16进制色[8位,6位,3位]|自定义颜色)(-透明度)?`  
- 示例：  
  - `c-red` → `color:rgba(255,0,0,1)`  
  - `text-43ad7f-25` → `color:rgba(67,173,127,0.25)`  

#### 背景色  
- 语法：`(bg|background)-(伪类-)?(16进制色[6位或3位]|自定义颜色)(-透明度)?`  
- 示例：  
  - `bg-black-35` → `background:rgba(0,0,0,0.35)`  
  - `background-active-43ad7f-35` → `active时background:rgba(67,173,127,0.35)`  

#### 边框色  
- 语法：`(border-color|border-c|border)(方向-)?(-(伪类-))?(16进制色[6位或3位]|自定义颜色)(-透明度)?`  
- 示例：  
  - `border-c-black-35` → `border-color:rgba(0,0,0,0.35)`  
  - `border-t-black-35` → `border-top-color:rgba(0,0,0,0.35)`  


### 7. 边框相关  
#### 边框宽度  
- 语法：`(border|border-width|border-w)-(方向-)?(数值)(单位)?`（默认边框样式为`solid`，颜色为`black`）  
- 示例：  
  - `border-2` → `border-width:2px;border-style:solid;border-color:black`  
  - `border-w-x-2em` → `border-width:2em;border-style:solid;border-color:black`  

#### 边框样式  
- 语法：`(border-style|border)-(方向-)?(none|hidden|dotted|dashed|solid|double|groove|ridge|inset|outset|inherit)`  
- 示例：  
  - `border-dashed` → `border-style:dashed`  
  - `border-t-dotted` → `border-top-style:dotted`  

#### 边框圆角  
- 语法：`(border-radius|br|rounded)-(方向-)?(数值)(单位)?`，方向包括`trbl`（单角）、`tl|tr|bl|br`（对角）  
- 示例：  
  - `br-4` → `border-radius:4px`  
  - `rounded-t-10` → `border-top-radius:10px`  


### 8. 背景相关  
#### 背景裁剪  
- 语法：`bg-clip-(border-box|padding-box|content-box)`  
- 示例：  
  - `bg-clip-content` → `background-clip:content-box`  

#### 背景渐变  
- 语法：`bg-(gradient|lg)-(方向)-(开始颜色)(-透明度)-(结束颜色)(-透明度)`，方向为`trbl`组合（如`br`为右下）  
- 示例：  
  - `bg-lg-br-red-green` → `background-image:linear-gradient(to bottom right, rgba(245,63,63,1), rgba(0,188,121,1))`  


### 9. 动画与过渡相关  
#### 过渡属性  
- 语法：`transition(-(none|all|colors|orientation))?`  
- 示例：  
  - `transition` → 默认过渡（包含颜色、阴影、变换等常用属性）  
  - `transition-colors` → 仅过渡颜色相关属性  

#### 过渡时间/延迟/曲线  
- 语法1（时间）：`duration-(毫秒数)` → `transition-duration:xxxms`  
- 语法2（延迟）：`(transition-delay|delay)-(毫秒数)` → `transition-delay:xxxms`  
- 语法3（曲线）：`(ease|transition-ease)-(linear|in|out|in-out)` → `transition-timing-function:xxx`  
- 示例：  
  - `duration-300` → `transition-duration:300ms`  
  - `ease-in-out` → `transition-timing-function:ease-in-out`  

#### 变换（缩放/旋转/平移）  
- 缩放：`scale(-[xy])?(数值)` → `transform:scaleX(xxx)`/`scaleY(xxx)`/`scale(xxx)`  
  - 示例：`scale-x-50` → `transform:scaleX(0.5)`  
- 旋转：`rotate(-[xyz])?(m-|-)?(角度)` → `transform:rotateX(xxxdeg)`等  
  - 示例：`rotate-y-50` → `transform:rotateY(50deg)`  
- 平移：`translate(-[xy])?(m-|-)?(数值)(单位)?` → `transform:translateX(xxx)`等  
  - 示例：`translate-x-10` → `transform:translateX(10px)`  


### 10. 其他常用属性  
#### 透明度  
- 语法：`(opacity|op)-(0-100)` → `opacity:xxx/100`  
- 示例：`op-50` → `opacity:0.5`  

#### 鼠标样式  
- 语法：`cursor-(pointer|default|wait|help|progress)`等  
- 示例：`cursor-pointer` → `cursor:pointer`  

#### 用户选择  
- 语法：`(user-)?select-(none|auto|text|all)`  
- 示例：`select-none` → `user-select:none`  

#### 阴影  
- 语法：`shadow(-(sm|md|lg|xl|2xl|inner|none))?`  
- 示例：  
  - `shadow` → 默认阴影  
  - `shadow-lg` → 大阴影  
  - `shadow-inner` → 内阴影  


通过以上语法规则，原子化AutoCSS实现了用简洁的class属性替代繁琐的CSS代码，同时保持高度的灵活性和扩展性，适配各类UI开发场景。


## 六、进阶特性
1. **规则覆盖与扩展**：通过`overrideRules`配置覆盖或新增样式规则，支持自定义正则匹配与样式生成逻辑（如覆盖z-index规则）。  
2. **媒体查询扩展**：通过`mediaQueries`配置自定义断点，满足不同屏幕适配需求。  
3. **组合属性**：提供常用样式组合简写，如`square-80`（宽高均为80px）、`ellipsis-2`（文字超出2行省略）。  


## 七、核心优势
- **低代码友好**：用class简写替代CSS代码，减少开发量，适合快速搭建UI。  
- **性能优化**：基于vite HMR实时生成，只保留页面使用的样式，避免冗余。  
- **灵活性高**：无预设样式，支持全场景自定义，适配不同项目风格。  

通过原子化AutoCSS，开发者可聚焦业务逻辑，大幅提升样式开发效率，尤其适合uniapp x跨端项目与低代码平台。