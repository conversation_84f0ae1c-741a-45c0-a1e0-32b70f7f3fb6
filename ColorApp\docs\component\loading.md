<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/loading?title=Loading"></Mobile>

# Loading
> 组件类型：UxLoadingComponentPublicInstance

支持圆环、菊花形状，可自定义文案

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| [type](#type) | String | spinner | loading类型 |
| color | String | `$ux.Conf.placeholderColor` | loading颜色 |
| [darkColor](#darkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| size | Any | `$ux.Conf.fontSize` | loading大小 |
| textColor | String |  | 文案颜色 |
| textSize | Any | `$ux.Conf.fontSize` | 文案字体大小 |
| bold | Boolean | false | 文字加粗 |
| margin | Array |  | 边距[上右下左][上下左右][上下左右] |
| mt | Any |  | 距上单位px |
| mr | Any |  | 距右单位px |
| mb | Any |  | 距下单位px |
| ml | Any |  | 距左单位px |
| padding | Array |  | 填充[上右下左][上下左右][上下左右] |
| pt | Any |  | 上内边距单位px |
| pr | Any |  | 右内边距单位px |
| pb | Any |  | 下内边距单位px |
| pl | Any |  | 左内边距单位px |
| xstyle | Array |  | 自定义样式 |

### [type](#type)

| 值   | 说明 |
|:------:|:----:|
| circular圆环
 |  |
| spinner菊花
 |  |
| icon图标
 |  |

### [darkColor](#darkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

