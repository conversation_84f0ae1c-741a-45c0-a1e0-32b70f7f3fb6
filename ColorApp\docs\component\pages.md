<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/pages?title=Pages"></Mobile>

# Pages
> 组件类型：UxPagesComponentPublicInstance

需搭配Tabbar组件使用

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| 0 | Slot |  | 第1个页面插槽 |
| 1 | Slot |  | 第2个页面插槽 |
| 2 | Slot |  | 第3个页面插槽 |
| 3 | Slot |  | 第4个页面插槽 |
| 4 | Slot |  | 第5个页面插槽 |
| index | Number | 0 | 当前页面下标 |
| pages | Number | 0 | 页面数量 |

