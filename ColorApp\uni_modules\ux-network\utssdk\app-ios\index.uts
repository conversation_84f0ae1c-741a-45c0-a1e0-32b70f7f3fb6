
import { OnNetworkStatusChange, OffNetworkStatusChange, UxNetworkStatusChangeOptions } from '../interface.uts'

// @UTSJS.keepAlive
// export const onNetworkStatusChange: OnNetworkStatusChange = (options: UxNetworkStatusChangeOptions) => {
// 	let callback = (isConnected: boolean): void => {
// 		options.onListener(isConnected)
// 	}
// 	UxNetwork.on(listener = callback)
// }

@UTSJS.keepAlive
export function onNetworkStatusChange(options: UxNetworkStatusChangeOptions) {
	let callback = (isConnected: boolean): void => {
		options.onListener(isConnected)
	}
	UxNetwork.on(listener = callback)
}

export const offNetworkStatusChange: OffNetworkStatusChange = (): void => {
	UxNetwork.off()
}