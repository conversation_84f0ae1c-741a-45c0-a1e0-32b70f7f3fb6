/**
 * date to string
 */
export const toDate = (date: Date): string => {
	let padding = (n: number): string => {
		return `${n}`.padStart(2, '0')
	}
	
	return `${date.getFullYear()}-${padding(date.getMonth() + 1)}-${padding(date.getDate())} ${padding(date.getHours())}:${padding(date.getMinutes())}:${padding(date.getSeconds())}`
}

/**
 * date from string
 */
export const fromDate = (date: string): Date => {
	let d = date.split(/[^0-9]/)
	
	let year = d.length < 1 ? 0 : parseInt(d[0])
	let month = d.length < 2 ? 0 : parseInt(d[1]) - 1
	let day = d.length < 3 ? 0 : parseInt(d[2])
	let hour = d.length < 4 ? 0 : parseInt(d[3])
	let minute = d.length < 5 ? 0 : parseInt(d[4])
	let second = d.length < 6 ? 0 : parseInt(d[5])
	
	return new Date(year, month, day, hour, minute, second)
}