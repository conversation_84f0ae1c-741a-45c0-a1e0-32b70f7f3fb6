<template>
	<list-item :type="type">
		<slot></slot>
	</list-item>
</template>

<script setup>
	
	/**
	 * list-item
	 * @description ListView 列表容器子项
	 * @demo pages/component/list.uvue
	 * @tutorial https://www.uxframe.cn/component/list.html
	 * @property {Number}			type  				Number | 对应list-item的类型 list-view 将对同类型条目进行复用，所以合理的类型拆分，可以很好地提升 list-view 性能
	 * <AUTHOR>
	 * @date 2023-11-04 01:12:28
	 */
	
	defineOptions({
		name: 'ux-list-item'
	})
	
	defineProps({
		type: {
			type: Number,
			default: 0
		}
	})
</script>

<style lang="scss">
	.ux-list-item {
		
	}
</style>