
export type UxRequestPermissionTipsOptions = {
	permission: string,
	content: string
}

export type UxRequestPermissionNameOptions = 'network' | 'location' | 'push' | 'camera' | 'photo' | 'bluetooth' | 'microphone' | 'calendar' | 'contact' | 'sms' | 'phone' | 'phone_state'

export type UxRequestPermissionsOptions = {
	name ?: UxRequestPermissionNameOptions,
	permissions ?: string[],
	complete ?: ((allRight: boolean, permissions : string[], doNotAskAgain: boolean) => void) | null
}

export type SetRequestPermissionTips = (options : UxRequestPermissionTipsOptions[]) => void

export type RequestPermissions = (options : UxRequestPermissionsOptions) => void

export type UxOpenAppAuthorizeSettingOptions = 'settings' | 'notification'

export type OpenAppAuthorizeSetting = (options : UxOpenAppAuthorizeSettingOptions) => void

export interface UxPermissionCallback extends IUniError {
	
}