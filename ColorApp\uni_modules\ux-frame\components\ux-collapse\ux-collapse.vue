<template>
	<view class="ux-collapse">
		<slot></slot>
	</view>
</template>

<script setup lang="ts">
	
	/**
	* 折叠面板
	* @description 支持手风琴模式，可禁用单项，可显示下划线。需搭配 `ux-collapse-item` 使用
	* @demo pages/component/collapse.uvue
	* @tutorial https://www.uxframe.cn/component/collapse.html
	* @property {Boolean} 			accordion=[true|false]					Boolean | 是否开启手风琴效果（默认 true ）
	* @value true
	* @value false
	* @property {String}			color									String | 标题文字颜色
	* @property {String} 			darkColor=[none|auto|color]				String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	* @value none 不显示
	* @value auto 自动适配深色模式
	* @value color 其他颜色
	* @property {Any}				size									Any | 标题文字大小 (默认 $ux.Conf.fontSize)
	* @property {Boolean}			bold = [true|false]						Boolean | 标题文字加粗 (默认 false)
	* @property {String}			titleBackground							String | 标题背景颜色
	* @property {String} 			titleBackgroundDark=[none|auto|color]	String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	* @value none 不显示
	* @value auto 自动适配深色模式
	* @value color 其他颜色
	* @property {String}			background								String | 背景颜色
	* @property {String} 			backgroundDark=[none|auto|color]		String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	* @value none 不显示
	* @value auto 自动适配深色模式
	* @value color 其他颜色
	* @property {Boolean} 			border=[true|false]						Boolean | 显示下边框（默认 true ）
	* @value true
	* @value false
	* <AUTHOR>
	* @date 2023-11-22 10:15:35
	*/
	
	import { computed, ref, watch } from 'vue'
	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	
	defineOptions({
		name: 'ux-collapse',
		mixins: [xstyleMixin]
	})
	
	const props = defineProps({
		accordion: {
			type: Boolean,
			default: true
		},
		color: {
			type: String,
			default: ''
		},
		darkColor: {
			type: String,
			default: ''
		},
		size: {
			default: 0
		},
		bold: {
			type: Boolean,
			default: false
		},
		titleBackground: {
			type: String,
			default: '#ffffff'
		},
		titleBackgroundDark: {
			type: String,
			default: '#333333'
		},
		background: {
			type: String,
			default: ''
		},
		backgroundDark: {
			type: String,
			default: ''
		},
		border: {
			type: Boolean,
			default: true
		},
	})
	
	const nodes = ref<Array<UxCollapseItemComponentPublicInstance>>([] as Array<UxCollapseItemComponentPublicInstance>)
	
	const parentData = computed((): Map<string, any> => {
		let data = new Map<string, any>()
		
		data.set('color', props.color)
		data.set('darkColor', props.darkColor)
		data.set('size', $ux.Util.getPx(props.size))
		data.set('bold', props.bold)
		data.set('titleBackground', props.titleBackground)
		data.set('titleBackgroundDark', props.titleBackgroundDark)
		data.set('background', props.background)
		data.set('backgroundDark', props.backgroundDark)
		data.set('border', props.border)
		
		return data
	})
	
	function setData(child : UxCollapseItemComponentPublicInstance) {
		child._.exposed.setData(parentData.value)
	}
	
	function register(child: UxCollapseItemComponentPublicInstance) {
		nodes.value.push(child)
		setData(child)
	}
	
	function close() {
		if (props.accordion && nodes.value.length > 0) {
			nodes.value.forEach((item) => {
				item._.exposed.close()
			})
		}
	}
	
	watch(parentData, () => {
		nodes.value.forEach((child : UxCollapseItemComponentPublicInstance) => {
			setData(child)
		})
	})
	
	defineExpose({
		register,
		close
	})
</script>

<style lang="scss">
	
	.ux-collapse {
		
	}

</style>