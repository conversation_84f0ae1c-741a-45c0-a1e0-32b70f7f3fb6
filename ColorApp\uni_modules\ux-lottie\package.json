{"id": "ux-lottie", "displayName": "UxFrame Lottie动画组件", "version": "1.0.1", "description": "支持android、ios、web", "keywords": ["uxframe", "ux-lottie", "lottie"], "engines": {"HBuilderX": "^4.31"}, "dcloudext": {"type": "uts-vue-component", "sale": {"regular": {"price": "98.00"}, "sourcecode": {"price": "198.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "y"}, "client": {"Vue": {"vue2": "y", "vue3": "y"}, "App": {"app-android": "y", "app-ios": "y", "app-harmony": "u"}, "H5-mobile": {"Safari": "u", "Android Browser": "u", "微信浏览器(Android)": "u", "QQ浏览器(Android)": "u"}, "H5-pc": {"Chrome": "u", "IE": "u", "Edge": "u", "Firefox": "u", "Safari": "u"}, "小程序": {"微信": "u", "阿里": "u", "百度": "u", "字节跳动": "u", "QQ": "u", "钉钉": "u", "快手": "u", "飞书": "u", "京东": "u"}, "快应用": {"华为": "u", "联盟": "u"}}}}, "name": "ux-lottie", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"lottie-miniprogram": "^1.0.12", "lottie-web": "^5.12.2"}}