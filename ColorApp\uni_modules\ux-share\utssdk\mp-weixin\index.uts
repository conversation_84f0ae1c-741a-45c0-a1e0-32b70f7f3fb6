
import { ShareWithSystem, UxShareWithSystemOptions } from '../interface.uts';
import { UxShareSuccessCallbackImpl, UxShareErrorCallbackImpl } from '../unierror.uts'

/**
 * 系统分享
 * @param UxShareWithSystemOptions options 参数
 */
export const shareWithSystem: ShareWithSystem = function(options: UxShareWithSystemOptions) {
	const err = new UxShareErrorCallbackImpl('shareWithSystem:imageUrl is null')
	options.fail?.(err)
	options.complete?.(err)
}