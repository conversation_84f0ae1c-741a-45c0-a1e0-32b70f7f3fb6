<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/divider?title=Divider"></Mobile>

# Divider
> 组件类型：UxDividerComponentPublicInstance

支持横向、竖向，可配置非细线、虚线

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| [theme](#theme) | String |  | 按钮类型 |
| color | String | `$ux.Conf.borderColor` | 颜色 |
| [darkColor](#darkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| [direction](#direction) | String | row | 方向 |
| hairline | Boolean | true | 细线条 |
| dashed | Boolean | false | 虚线 |
| margin | Array |  | 边距[上右下左][上下左右][上下左右] |
| mt | Any |  | 距上单位px |
| mr | Any |  | 距右单位px |
| mb | Any |  | 距下单位px |
| ml | Any |  | 距左单位px |
| padding | Array |  | 填充[上右下左][上下左右][上下左右] |
| pt | Any |  | 上内边距单位px |
| pr | Any |  | 右内边距单位px |
| pb | Any |  | 下内边距单位px |
| pl | Any |  | 左内边距单位px |
| xstyle | Array |  | 自定义样式 |

### [theme](#theme)

| 值   | 说明 |
|:------:|:----:|
| text | 文字
 |
| info | 默认
 |
| primary | 主要
 |
| success | 成功
 |
| warning | 警告
 |
| error | 错误
 |

### [darkColor](#darkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [direction](#direction)

| 值   | 说明 |
|:------:|:----:|
| row横向
 |  |
| col竖向
 |  |

