<template>
	<!-- #ifdef APP -->
	<list-item class="ux-chatlist-item" :type="type">
		<slot></slot>
	</list-item>
	<!-- #endif -->
	<!-- #ifndef APP -->
	<view class="ux-chatlist-item">
		<slot></slot>
	</view>
	<!-- #endif -->
</template>

<script setup>
	
	/**
	 * UxChatList 子项
	 * @demo pages/component/chatlist.uvue
	 * @tutorial https://www.uxframe.cn/component/chatlist.html
	 * <AUTHOR>
	 * @date 2024-05-28 14:12:28
	 */
	
	defineOptions({
		name: 'ux-chatlist-item'
	})
	
	defineProps({
		type: {
			type: String,
			default: ''
		}
	})
</script>


<style lang="scss">
	.ux-chatlist-item {
		transform: rotate(-180deg);
	}
</style>