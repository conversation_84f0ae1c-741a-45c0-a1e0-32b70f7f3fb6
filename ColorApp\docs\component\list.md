<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/list?title=List"></Mobile>

# List
> 组件类型：UxListComponentPublicInstance

扩展支持自定义下拉刷新、上拉加载自定义样式，可显示返回顶部按钮

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| default | Slot |  | 默认slot |
| refresher | Slot |  | refresherslot |
| loadmore | Slot |  | loadmoreslot |
| backtop | Slot |  | backtopslot |
| list | UTSJSONObject |  | UTSJSONObject[]虚拟节点数据列表 |
| pageSize | Number | 10 | 单页item数量 |
| cachePages | Number | 1 | 缓存上下页面数 |
| [direction](#direction) | String | vertical | 滚动方向，可取值none、all、horizontal、vertical |
| [associativeContainer](#associativeContainer) | String |  | 关联的滚动容器 |
| bounces | Boolean | true | 控制是否回弹效果 |
| upperThreshold | Number | 50 | 距顶部/左边多远时(单位px)，触发scrolltoupper事件 |
| lowerThreshold | Number | 50 | 距底部/右边多远时(单位px)，触发scrolltolower事件 |
| scrollTop | Number | 0 | 设置竖向滚动条位置 |
| scrollLeft | Number | 0 | 设置横向滚动条位置 |
| scrollIntoView | String |  | 值应为某子元素id(id不能以数字开头)。设置哪个方向可滚动，则在哪个方向滚动到该元素 |
| scrollWithAnimation | Boolean | true | 是否在设置滚动条位置时使用滚动动画，设置false没有滚动动画 |
| refresherEnabled | Boolean | false | 开启下拉刷新 |
| refresherThreshold | Number | 45 | 设置下拉刷新阈值 |
| refresherMaxDragDistance | Number | 80 | 设置下拉最大拖拽距离(单位px) |
| refresherBackground | String | transparent | 设置下拉刷新区域背景颜色 |
| refresherTriggered | Boolean | false | 设置当前下拉刷新状态，true表示下拉刷新已经被触发，false表示下拉刷新未被触发 |
| refresherStates | Array |  | 下拉刷新状态文案 |
| loadmoreEnabled | Boolean | false | 开启上拉加载 |
| loadmoreStates | Array |  | 上拉加载状态文案 |
| showScrollbar | Boolean | false | 控制是否出现滚动条 |
| customNestedScroll | Boolean | false | 子元素是否开启嵌套滚动将滚动事件与父元素协商处理 |
| placeholder | Boolean |  | 底部导航栏高度占位 |
| disabled | Boolean | false | 禁止滚动 |
| background | String | transparent | 背景颜色 |
| xstyle | Array |  | 自定义样式 |

### [direction](#direction)

| 值   | 说明 |
|:------:|:----:|
| none无
 |  |
| all全部
 |  |
| horizontal水平
 |  |
| vertical垂直
 |  |

### [associativeContainer](#associativeContainer)

| 值   | 说明 |
|:------:|:----:|
| nested-scroll-view | 嵌套滚动
 |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| refresherpulling | 下拉刷新控件被下拉 |  |
| refresherrefresh | 下拉刷新被触发 |  |
| refresherrestore | 下拉刷新被复位 |  |
| refresherabort | 下拉刷新被中止 |  |
| loadmore | 上拉加载触发 |  |
| scrolltoupper | 滚动到顶部/左边，会触发scrolltoupper事件 |  |
| scrolltolower | 滚动到底部/右边，会触发scrolltolower事件 |  |
| scroll | 滚动时触发 |  |
| scrollend | 滚动结束时触发 |  |
  
  