# mp-html

HTML解析组件，支持将HTML字符串解析并通过uni-app x原生组件渲染。

## 特性

- 支持常用HTML标签解析
- 支持CSS样式解析
- 支持图片、链接等媒体元素
- 跨平台兼容（App、小程序、H5）
- 高性能原生渲染

## 使用方法

```vue
<template>
  <mp-html :content="htmlContent" />
</template>

<script setup lang="uts">
const htmlContent = '<p>Hello <strong>World</strong>!</p>'
</script>
```

## 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| content | string | '' | HTML内容 |
| selectable | boolean | false | 是否可选择文本 |
| show-img-menu | boolean | true | 是否显示图片菜单 |

## 事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| link-tap | 链接点击 | {href: string} |
| img-tap | 图片点击 | {src: string} |
| error | 解析错误 | {error: string} |

## 支持的HTML标签

- 文本标签：p, span, div, h1-h6, strong, b, em, i, u, del, s
- 列表标签：ul, ol, li
- 媒体标签：img, a
- 其他：br, hr

## 支持的CSS样式

- 文字样式：color, font-size, font-weight, text-align
- 布局样式：margin, padding, width, height
- 背景样式：background-color