<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/modal?title=Modal"></Mobile>

# Modal
> 组件类型：UxModalComponentPublicInstance

支持顶部对话框，底部对话框，内容输入对话框，弹出对话框，对话框嵌套

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| [theme](#theme) | String | primary | 主题 |
| [mode](#mode) | String | modal | 模式 |
| [position](#position) | String | center | 显示位置 |
| popoverId | String |  | 弹窗目标ID |
| title | String |  | 标题 |
| titleSize | Any | 16 | 标题大小 |
| titleColor | String | #000 | 标题颜色 |
| [titleDarkColor](#titleDarkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| titleBold | Boolean | true | 标题加粗 |
| titleStyle | String |  | 标题自定义样式 |
| content | String |  | 内容 |
| contentSize | Any | 15 | 内容大小 |
| contentColor | String | #333 | 内容颜色 |
| [contentDarkColor](#contentDarkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| [contentAlign](#contentAlign) | String | center | 对齐方式 |
| bodyStyle | String |  | 主题内容自定义样式 |
| contentStyle | String |  | 内容自定义样式 |
| placeholder | String | 请输入... | 占位内容 |
| placeholderSize | Any | 15 | 占位内容大小 |
| placeholderColor | String | #888 | 占位内容颜色 |
| confirmText | String | 确定 | 确定按钮文字 |
| confirmColor | String | `$ux.Conf.primaryColor` | 确定按钮文字颜色 |
| confirmSize | Any | 15 | 确定按钮文字大小 |
| confirmStyle | String |  | 确定按钮自定义样式 |
| cancelText | String | 取消 | 取消按钮文字 |
| cancelColor | String | `$ux.Conf.primaryColor` | 取消按钮文字颜色 |
| cancelSize | Any | 15 | 取消按钮文字大小 |
| cancelStyle | String |  | 取消按钮自定义样式 |
| background | String |  | 背景颜色 |
| [backgroundDark](#backgroundDark) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| showBlur | Boolean | false | 开启模糊背景 |
| blurRadius | Number | 10 | 模糊半径 |
| blurColor | String | rgba(10,10,10,0.3) | 模糊颜色 |
| showClose | Boolean | false | 显示关闭 |
| showCancel | Boolean | false | 点击关闭 |
| showFooter | Boolean | true | 显示底部 |
| editable | Boolean | false | 输入内容 |
| autoFocus | Boolean | false | 自动聚焦 |
| width | Any | 84% | 宽度 |
| height | Any | 240 | 最小高度 |
| maxHeight | Any | 500 | 最大高度 |
| radius | Any | 20 | 圆角 |
| maskOpacity | Number | 0.6 | 遮罩层透明度 |
| maskClose | Boolean | false | 遮罩层关闭 |

### [theme](#theme)

| 值   | 说明 |
|:------:|:----:|
| text | 文字
 |
| info | 默认
 |
| primary | 主要
 |
| success | 成功
 |
| warning | 警告
 |
| error | 错误
 |

### [mode](#mode)

| 值   | 说明 |
|:------:|:----:|
| modal对话框
 |  |
| popover弹窗暂只支持Web
 |  |

### [position](#position)

| 值   | 说明 |
|:------:|:----:|
| center居中显示
 |  |
| top居上显示
 |  |
| bottom居下显示
 |  |

### [titleDarkColor](#titleDarkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [contentDarkColor](#contentDarkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [contentAlign](#contentAlign)

| 值   | 说明 |
|:------:|:----:|
| left左对齐
 |  |
| center居中
 |  |
| right右对齐
 |  |

### [backgroundDark](#backgroundDark)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| confirm | 确定按钮点击时触发 |  |
| cancel | 取消按钮点击时触发 |  |
| close | 关闭按钮点击时触发发 |  |
| input | 输入时触发 |  |
  
  