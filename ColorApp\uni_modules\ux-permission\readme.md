
<p align="center">
    <img alt="logo" src="https://www.uxframe.cn/logo/logo.png" width="120" height="120" style="margin-bottom: 10px;">
</p>
<h3 align="center" style="margin: 30px 0 30px;font-weight: bold;font-size:40px;">UxFrame 权限申请提示SDK 1.0.1</h3>

## 特别说明

如果您已经购买了[UxFrame 低代码高性能UI框架](https://ext.dcloud.net.cn/plugin?id=16148), 则无需再次购买本插件，请点击上方`进入交流群`联系我免费获取离线版插件！

## 说明

插件默认不注册权限，请根据您的需求在项目根目录下分别新建（如果已有则合并）`AndroidManifest.xml`和`Info.plist`2个文件，并实现下面权限注册，即可正常使用

Android权限

``` ts
<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" xmlns:tools="http://schemas.android.com/tools"
	package="您的APP包名">
	
	<uses-permission android:name="android.permission.INTERNET" />
	<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
	<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
	<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
	<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
	<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
	<uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
	<uses-permission android:name="android.permission.CAMERA" />
	<uses-permission android:name="android.permission.BLUETOOTH" />
	<uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
	<uses-permission android:name="android.permission.RECORD_AUDIO" />
	<uses-permission android:name="android.permission.READ_CONTACTS" />
	<uses-permission android:name="android.permission.WRITE_CALENDAR" />
	<uses-permission android:name="android.permission.READ_SMS" />
	<uses-permission android:name="android.permission.SEND_SMS" />
	<uses-permission android:name="android.permission.RECEIVE_SMS" />
	<uses-permission android:name="android.permission.CALL_PHONE" />
	<uses-permission android:name="android.permission.READ_PHONE_STATE" />
	<uses-permission android:name="android.permission.VIBRATE" />
	<uses-permission android:name="android.permission.WRITE_SETTINGS" />
	<uses-permission android:name="android.permission.WAKE_LOCK"/>
	
	<application>
		
	</application>
</manifest>

```

iOS权限

```ts
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>我们需要您的位置信息来提供更好的服务</string>
		
		<key>NSLocationAlwaysUsageDescription</key>
		<string>我们需要您的位置信息来提供更好的服务</string>
		
		<key>NSPushNotificationAlertStyle</key>
		<string>alert</string>
		<key>NSPushNotificationTypes</key>
		<string>alert,sound,badge</string>
		<key>NSUserNotificationAlertStyle</key>
		<string>alert</string>
		<key>UIBackgroundModes</key>
		<array>
		    <string>remote-notification</string>
		</array>
		
		<key>NSUserNotificationsUsageDescription</key>
		<string>我们需要推送通知来发送重要更新和提醒</string>
		
		<key>NSCameraUsageDescription</key>
		<string>我们需要访问您的摄像机来拍摄照片/视频</string>
		
		<key>NSPhotoLibraryUsageDescription</key>
		<string>我们需要访问您的相册来保存或选择照片</string>
		
		<key>NSMicrophoneUsageDescription</key>
		<string>我们需要访问您的麦克风来录音</string>
		
		<key>NSCalendarsUsageDescription</key>
		<string>我们需要访问您的日历来提供更好的服务</string>
		
		<key>NSRemindersUsageDescription</key>
		<string>我们需要访问您的备忘录来提供更好的服务</string>
		
		<key>CFBundleURLSchemes</key>
		<array>
		    <string>telprompt</string>
		    <string>smsprompt</string>
		</array>
		
		<key>NSBluetoothAlwaysUsageDescription</key>
		<string>我们需要您的同意来使用蓝牙来提供更好的服务</string>
		
		<key>NSBluetoothPeripheralUsageDescription</key>
		<string>我们需要您的同意来使用蓝牙来提供更好的服务</string>
		
		<key>NSContactsUsageDescription</key>
		<string>我们需要您的同意来访问联系人来提供更好的服务</string>
	</dict>
</plist>

```

### 使用方式

``` ts

import * as uxPermission from "@/uni_modules/ux-permission"

function setRequestPermissionTips() {
	let permissons = [{
		permission: 'location', // 可能有多个权限包装在一起，统一这个说明
		// permission: 'android.permission.CAMERA', // 单个权限说明
		content: "<h4 style=\"font-size:40px;\">获取位置权限说明</h4><font color=#cccccc>我们需要您的位置信息来提供更好的服务</font>"
	},{
		permission: 'push',
		content: "<h4 style=\"font-size:40px;\">系统通知权限说明</h4><font color=#cccccc>我们需要推送通知来发送重要更新和提醒</font>"
	},{
		permission: 'camera',
		content: "<h4 style=\"font-size:40px;\">拍照/拍摄/存储空间权限说明</h4><font color=#cccccc>我们需要访问您的摄像机来拍摄照片/视频</font>"
	},{
		permission: 'photo',
		content: "<h4 style=\"font-size:40px;\">获取照片/视频/存储空间权限说明</h4><font color=#cccccc>我们需要访问您的相册来保存或选择照片</font>"
	},{
		permission: 'microphone',
		content: "<h4 style=\"font-size:40px;\">获取麦克风权限说明</h4><font color=#cccccc>我们需要访问您的麦克风来录音</font>"
	},{
		permission: 'bluetooth',
		content: "<h4 style=\"font-size:40px;\">获取蓝牙权限说明</h4><font color=#cccccc>我们需要您的同意来使用蓝牙来提供更好的服务</font>"
	},{
		permission: 'calendar',
		content: "<h4 style=\"font-size:40px;\">获取日历权限说明</h4><font color=#cccccc>我们需要访问您的日历来提供更好的服务</font>"
	},{
		permission: 'contact',
		content: "<h4 style=\"font-size:40px;\">获取通讯录权限说明</h4><font color=#cccccc>我们需要您的同意来访问联系人来提供更好的服务</font>"
	},{
		permission: 'sms',
		content: "<h4 style=\"font-size:40px;\">获取短信权限说明</h4><font color=#cccccc>我们需要访问短信来提供更好的服务</font>"
	},{
		permission: 'phone',
		content: "<h4 style=\"font-size:40px;\">拨打/管理电话权限说明</h4><font color=#cccccc>我们需要访问电话来提供更好的服务</font>"
	},{
		permission: 'phone_state',
		content: "<h4 style=\"font-size:40px;\">获取设备信息权限说明</h4><font color=#cccccc>我们需要获取设备信息来提供更好的服务</font>"
	}] as uxPermission.UxRequestPermissionTipsOptions[]
	
	uxPermission.setRequestPermissionTips(permissons)
}

function requestPermissions() {
	uxPermission.requestPermissions({
		name: 'camera', // 内部封装权限包，可能包含多个权限
		// permissions: permissons.map((e): string => e.permission), // 更细致的权限列表
		complete: (allRight: boolean, permissions : string[], doNotAskAgain: boolean) => {
			console.log(allRight, permissions);
			uni.showToast({
				title: allRight ? '已授权' : '已拒绝'
			})
		}
	} as uxPermission.UxRequestPermissionsOptions)
}

function openAppAuthorizeSetting() {
	uxPermission.openAppAuthorizeSetting('settings')
}

```

## 文档教程 ⬇️

[https://www.uxframe.cn](https://www.uxframe.cn)
