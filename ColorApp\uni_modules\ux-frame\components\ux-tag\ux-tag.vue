<template>
	<view class="ux-tag" :style="style" @click="click">
		<ux-icon v-if="icon != ''" :type="icon" :size="iconSize" :color="color" :custom-family="customFamily"></ux-icon>
		<text v-if="text" :style="[textStyle]">{{ text }}</text>
		<view v-if="closable" class="ux-tag__close">
			<ux-icon type="close" :size="11" color="white"></ux-icon>
		</view>
	</view>
</template>

<script setup lang="ts">

	/**
	 * Tag 标签
	 * @description 支持多种主题标签、镂空标签，可配置关闭按钮
	 * @demo pages/component/tag.uvue
	 * @tutorial https://www.uxframe.cn/component/tag.html
	 * @property {String}			theme=[text|info|primary|success|warning|error]		String | 主题
	 * @value text			文字
	 * @value info   		默认
	 * @value primary   	主要
	 * @value success  		成功
	 * @value warning   	警告
	 * @value error  		错误
	 * @property {String}			text								String | 标签文字
	 * @property {String}			color								String | 标签文字颜色
	 * @property {String} 			darkColor=[none|auto|color]			String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Any}				size								Any | 标签文字大小 (默认 $ux.Conf.fontSize)
	 * @property {Boolean}			bold = [true|false]					Boolean | 标签文字加粗 (默认 false)
	 * @property {String}			background							String | 标签背景颜色
	 * @property {String} 			backgroundDark=[none|auto|color]	String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Any}				corner								Any | 圆角 (默认 5)
	 * @property {Boolean}			plain = [true|false]				Boolean | 是否镂空 (默认 false)
	 * @property {Boolean}			closable							Boolean | 显示可关闭图标 父级需加入 ’overflow: visible‘ 才可正常显示（默认 false ）
	 * @property {String}			icon								String | 标签图标
	 * @property {String}			iconSize							String | 图标大小 (默认 $ux.Conf.fontSize)
	 * @property {String}			customFamily						String | 自定义字体family
	 * @property {String}			direction=[row|column]				String | 布局方向 (默认 row)
	 * @value row 水平
	 * @value column 垂直
	 * @property {Array}			margin						Array | 边距 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}				mt							Any | 距上 单位px
	 * @property {Any}				mr							Any | 距右 单位px
	 * @property {Any}				mb							Any | 距下 单位px
	 * @property {Any}				ml							Any | 距左 单位px
	 * @property {Array}			padding						Array | 填充 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}				pt							Any | 上内边距 单位px
	 * @property {Any}				pr							Any | 右内边距 单位px
	 * @property {Any}				pb							Any | 下内边距 单位px
	 * @property {Any}				pl							Any | 左内边距 单位px
	 * @property {Array}			xstyle						Array<any> | 自定义样式
	 * @property {Boolean}			disabled = [true|false]		Boolean | 是否禁用 (默认 false)
	 * @event {Function}			click						Function | 点击触发
	 * @event {Function}			close						Function | 点击关闭触发
	 * <AUTHOR>
	 * @date 2024-12-03 01:40:08
	 */
	
	import { computed } from 'vue'
	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	import { useThemeColor, useForegroundColor, useFontColor, useFontSize, UxThemeType } from '../../libs/use/style.uts'
	
	defineOptions({
		name: 'ux-tag',
		mixins: [xstyleMixin]
	})
	
	const emit = defineEmits(['click', 'close'])
	
	const props = defineProps({
		theme: {
			type: String,
			default: 'info'
		},
		text: {
			type: String,
			default: ''
		},
		color: {
			type: String,
			default: ''
		},
		darkColor: {
			type: String,
			default: ''
		},
		size: {
			default: 0
		},
		bold: {
			type: Boolean,
			default: false
		},
		background: {
			type: String,
			default: ''
		},
		backgroundDark: {
			type: String,
			default: ''
		},
		corner: {
			default: 5
		},
		plain: {
			type: Boolean,
			default: false
		},
		closable: {
			type: Boolean,
			default: false
		},
		icon: {
			type: String,
			default: ''
		},
		iconSize: {
			default: 14
		},
		customFamily: {
			type: String,
			default: ''
		},
		direction: {
			type: String,
			default: 'row'
		},
	})
	
	const theme = computed(():string => {
		return props.theme
	})
	
	const fontSize = computed(():number => {
		return useFontSize($ux.Util.getPx(props.size), 1)
	})
	
	const fontColor = computed(():string => {
		return useFontColor(props.color, props.darkColor)
	})
	
	const infoColor = computed(():string => {
		return $ux.Conf.darkMode.value ? $ux.Conf.infoColor.dark.value : $ux.Conf.infoColor.color.value
	})
	
	const borderColor = computed(():string => {
		return $ux.Conf.darkMode.value ? $ux.Conf.borderColor.dark.value : $ux.Conf.borderColor.color.value
	})
	
	const color = computed(() : string => {
		if(props.plain) {
			if (theme.value == 'text') {
				return fontColor.value
			} else  {
				return useThemeColor(theme.value as UxThemeType)
			}
		} else {
			if (theme.value == 'text') {
				return fontColor.value
			} else if (theme.value == 'info') {
				return infoColor.value
			} else {
				return '#ffffff'
			}
		}
	})
	
	const backgroundColor = computed(() : string => {
		
		if(props.background != '') {
			return useForegroundColor(props.background, props.backgroundDark)
		}
		
		if(props.plain == true) {
			return 'transparent'
		} else {
			if (theme.value == 'text') {
				return 'transparent'
			} else if (theme.value == 'info') {
				return 'transparent'
			} else {
				return useThemeColor(theme.value as UxThemeType)
			}
		}
	})
	
	const disabledColor = computed(():string => {
		if(props.plain) {
			return $ux.Color.getRgba(color.value, 0.5)
		} else {
			return $ux.Color.getRgba(backgroundColor.value, 0.5)
		}
	})
	
	const border = computed(() : string => {
		if (theme.value == 'text') {
			return 'transparent'
		} else if (theme.value == 'info') {
			return borderColor.value
		} else {
			return useThemeColor(theme.value as UxThemeType)
		}
	})
	
	const style = computed(() => {
		let css = {}
		
		css['flex-direction'] = props.direction
		
		if(props.disabled) {
			if (theme.value != 'text') {
				css['border'] = `1rpx solid ${$ux.Color.getRgba(border.value, 0.6)}`
				
				if(!props.plain && theme.value != 'info') {
					css['background-color'] = $ux.Color.getRgba(backgroundColor.value, 0.6)
				}
			}
		} else {
			if(theme.value != 'text') {
				css['border'] = `1rpx solid ${border.value}`
			}
			
			css['background-color'] = backgroundColor.value
		}
		
		// #ifdef WEB
		css['cursor'] = props.disabled ? 'not-allowed' : 'pointer'
		// #endif
		
		css['box-sizing'] = 'border-box'
		css['border-radius'] = $ux.Util.addUnit(props.corner)
		css['padding'] = '8px 10px'
		
		css = $ux.Util.xStyle(css, props.margin, props.mt, props.mr, props.mb, props.ml, props.padding, props.pt, props.pr, props.pb, props.pl)
		
		return css
	})
	
	const textStyle = computed(() => {
		let css = {}
		
		css['font-size'] = $ux.Util.addUnit(fontSize.value)
		css['color'] = props.disabled ? disabledColor.value : color.value
		css['font-weight'] = props.bold?'bold':'normal'
		
		if (props.icon != '') {
			if(props.direction == 'row') {
				css['margin-left'] = '4px'
			} else {
				css['margin-top'] = '4px'
			}
		}
		
		return css
	})
	
	function click(e : MouseEvent) {
		if (props.disabled) {
			return
		}
		
		emit('click', e)
	}
	
	function close(e : MouseEvent) {
		if (props.disabled) {
			return
		}
		
		emit('close', e)
	}
</script>

<style lang="scss" scoped>
	.ux-tag {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		overflow: visible;
		position: relative;
		
		&__close {
			position: absolute;
			top: -8px;
			right: -8px;
			width: 16px;
			height: 16px;
			border-radius: 15px;
			background-color: rgba(0, 0, 0, 0.4);
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
</style>