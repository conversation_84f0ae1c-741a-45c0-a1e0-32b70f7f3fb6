<template>
	<ux-sticky-section>
		<ux-sticky-header>
			<view class="ux-table__header" :style="headerStyle">
				<view class="ux-table__cell" :style="headerCellStyle(cell)" v-for="(cell, i) in columns" :key="i" @click="onSort(cell)">
					<view class="ux-table__wrap">
						<ux-radio v-if="selection && cell.key == selectKey" :readonly="true" :checked="isAll" shape="square" :color="activeColor" :value="cell.key"></ux-radio>
						<text v-else class="ux-table__text" :style="headerTextStyle">{{ cell.title }}</text>
					</view>
					<view v-if="cell.sorted == true" class="ux-table__sort">
						<ux-icon class="ux-table__sort--up" type="trigonup-filled" :color="cell.desc == 'desc' ? activeColor : '#a9a9a9'"></ux-icon>
						<ux-icon class="ux-table__sort--down" type="trigondown-filled" :color="cell.desc == 'asc' ? activeColor : '#a9a9a9'"></ux-icon>
					</view>
				</view>
			</view>
		</ux-sticky-header>
	</ux-sticky-section>
</template>

<script setup lang="ts">
	/**
	 * Table Header
	 * @demo pages/component/table.uvue
	 * @tutorial https://www.uxframe.cn/component/table.html
	 * <AUTHOR>
	 * @date 2024-12-07 23:23:24
	 */
	
	import { PropType, computed } from 'vue'
	import { $ux } from '../../index'
	import { useFontSize, useFontColor, useForegroundColor, useThemeColor } from '../../libs/use/style.uts'
	import { UxTableColumn } from '../../libs/types/types.uts'
	
	defineOptions({
		name: 'ux-table-header'
	})
	
	const emit = defineEmits(['sort'])

	const props = defineProps({
		fixed: {
			type: Boolean,
			default: false
		},
		columns: {
			type: Array as PropType<UxTableColumn[]>,
			default: () : UxTableColumn[] => [] as UxTableColumn[]
		},
		totalWidth: {
			type: Number,
			default: 0
		},
		tableWidth: {
			type: Number,
			default: 0
		},
		cellHeight: {
			default: 40
		},
		headerColor: {
			type: String,
			default: "#333333"
		},
		headerSize: {
			default: 0
		},
		headerBold: {
			type: Boolean,
			default: true
		},
		headerBackground: {
			type: String,
			default: "#f5f5f5"
		},
		borderColor: {
			type: String,
			default: "#e8e8e8"
		},
		border: {
			type: Boolean,
			default: false
		},
		selection: {
			type: Boolean,
			default: false
		},
		isAll: {
			type: Boolean,
			default: false
		}
	})
	
	const selectKey = '__SELECT__'
	
	const activeColor = computed((): string => {
		return useThemeColor('primary')
	})
	
	const headerSize = computed((): number => {
		return useFontSize($ux.Util.getPx(props.headerSize), 1)
	})
	
	const headerColor = computed((): string => {
		return useFontColor(props.headerColor, 'auto')
	})
	
	const headerBackground = computed((): string => {
		return useForegroundColor(props.headerBackground, 'auto')
	})
	
	const headerStyle = computed(() => {
		let css = {}
		
		if(!props.fixed && props.totalWidth > 0) {
			css['width'] = $ux.Util.addUnit(props.totalWidth)
			css['min-width'] = $ux.Util.addUnit(props.totalWidth)
		}
		
		css['height'] = $ux.Util.addUnit(props.cellHeight)
		
		return css
	})
	
	const headerTextStyle = computed(() => {
		let css = {}
		
		css['fontSize'] = $ux.Util.addUnit(headerSize.value)
		css['color'] = headerColor.value
		
		if(props.headerBold) {
			css['font-weight'] = 'bold'
		}
		
		return css
	})
	
	function getCellWidth(col: UxTableColumn) : string {
		let w = col.width ?? '20%'
		if (w == '') {
			return 'auto'
		}
		
		if (w.indexOf('%') != -1) {
			w = `${parseFloat(w.replace('%', '')) * props.tableWidth / 100}px`
		}
		
		return w
	}
	
	function headerCellStyle(col: UxTableColumn) {
		let cellWidth = getCellWidth(col)
		
		let css = {}
		
		if(cellWidth == 'auto') {
			css['flex'] = '1'
		} else {
			css['width'] = cellWidth
			css['min-width'] = cellWidth
		}
		
		css['height'] = $ux.Util.addUnit(props.cellHeight)
		css['background-color'] = headerBackground.value
		
		if(props.border) {
			css['border-top'] = `1px solid ${props.borderColor}`
			css['border-right'] = `1px solid ${props.borderColor}`
		}
		
		if(col.align == 'left') {
			css['justify-content'] = 'flex-start'
		} else if(col.align == 'right') {
			css['justify-content'] = 'flex-end'
		} else {
			css['justify-content'] = 'center'
		}
		
		return css
	}
	
	function onSort(col: UxTableColumn) {
		emit('sort', col)
	}
	
</script>

<style lang="scss" scoped>
	/* #ifdef H5 */
	uni-view {
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: stretch;
		align-content: stretch;
		justify-content: flex-start;
		box-sizing: border-box;
		min-height: 0px;
		min-width: 0px;
		overflow: hidden;
	}
	/* #endif */
	
	.ux-table {
		
		&__header {
			display: flex;
			flex-direction: row;
			flex-wrap: nowrap;
		}
		
		&__sort {
			width: 30px;
			height: 30px;
			display: flex;
			flex-direction: column;
			
			&--up {
				position: absolute;
				top: 2px;
			}
			
			&--down {
				position: absolute;
				top: 10px;
			}
		}
		
		&__cell {
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
		}
		
		&__wrap {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
		}
		
		&__text {
			text-align: center;
			padding: 4px 8px;
			/* #ifdef WEB */
			word-break: break-all;
			/* #endif */
		}
	}
</style>