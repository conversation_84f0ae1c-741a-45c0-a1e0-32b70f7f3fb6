<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/refresher?title=Refresher"></Mobile>

# Refresher
> 组件类型：UxRefresherComponentPublicInstance

支持自定义文案

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| state | Number | [0 | 状态下标 |
| states | Array | ['下拉刷新','释放刷新','刷新中...','刷新成功'] | 状态文案 |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| click | 点击时触发 |  |
  
  