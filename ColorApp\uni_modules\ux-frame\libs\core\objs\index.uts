/**
   @Name    :	对象
   <AUTHOR>   UxFrame
   @Date    :   2024-01-09 22:13:21
*/

export class Objs {

	// isObject = (target : any) : boolean => {
	// 	return target instanceof UTSJSONObject;
	// }

	// deepMerge(target : any, source : any) : any {
	// 	if (!(target instanceof UTSJSONObject) || !(source instanceof UTSJSONObject)) {
	// 		return source;
	// 	}

	// 	for (const key in source) {
	// 		if (isObject(source[key])) {
	// 			if (isObject(target[key]) && isObject(source[key])) {
	// 				deepMerge(target[key]!, source[key]!);
	// 			} else {
	// 				target[key] = source[key];
	// 			}
	// 		} else {
	// 			target[key] = source[key];
	// 		}
	// 	}

	// 	return target;
	// }

	/**
	 * 移除空对象
	 */
	removeNulls(obj : UTSJSONObject | null) : UTSJSONObject {
		if (obj == null) {
			return {}
		}

		let data = {} as UTSJSONObject

		// #ifdef UNI-APP-X
		let keys = UTSJSONObject.keys(obj)
		for (let i = 0; i < keys.length; i++) {
			let k = keys[i]
			
			if (obj.get(k) != null) {
				let _obj = obj.get(k)!
		
				if (_obj instanceof UTSJSONObject) {
					data.set(k, this.handleJson(_obj))
				} else if (_obj instanceof Array) {
					data.set(k, this.handleArray(_obj as Array<any>))
				} else {
					data.set(k, _obj)
				}
			}
		}
		// #endif

		// #ifndef UNI-APP-X
		for (let k in obj) {
			if (obj[k] != null) {
				let _obj = obj[k]!
		
				if (Array.isArray(_obj)) {
					data[k] = this.handleArray(_obj as Array<any>)
				} else if (typeof _obj == 'object') {
					data[k] = this.handleJson(_obj)
				} else {
					data[k] = _obj
				}
			}
		}
		// #endif

		return data
	}

	private handleJson(obj : UTSJSONObject) : UTSJSONObject {
		let data = {} as UTSJSONObject

		// #ifdef UNI-APP-X
		let keys = UTSJSONObject.keys(obj)
		for (let i = 0; i < keys.length; i++) {
			let k = keys[i]
			
			if (obj.get(k) != null) {
				let _obj = obj.get(k)!
		
				if (_obj instanceof UTSJSONObject) {
					data.set(k, this.handleJson(_obj))
				} else if (_obj instanceof Array) {
					data.set(k, this.handleArray(_obj as Array<any>))
				} else {
					data.set(k, _obj)
				}
			}
		}
		// #endif
		
		// #ifndef UNI-APP-X
		for (let k in obj) {
			if (obj[k] != null) {
				let _obj = obj[k]!
		
				if (Array.isArray(_obj)) {
					data[k] = this.handleArray(_obj as Array<any>)
				} else if (typeof _obj == 'object') {
					data[k] = this.handleJson(_obj)
				} else {
					data[k] = _obj
				}
			}
		}
		// #endif

		return data
	}

	private handleArray(objs : Array<any>) : Array<any> {
		let datas = new Array<any>()
		
		// #ifdef UNI-APP-X
		for (let i = 0; i < objs.length; i++) {
			let obj = objs[i]
		
			if (obj instanceof UTSJSONObject) {
				datas.push(this.handleJson(obj))
			} else if (obj instanceof Array) {
				datas.push(this.handleArray(obj as Array<any>))
			} else {
				datas.push(obj)
			}
		}
		// #endif

		// #ifndef UNI-APP-X
		for (let i = 0; i < objs.length; i++) {
			let obj = objs[i]
		
			if (Array.isArray(obj)) {
				datas.push(this.handleArray(obj as Array<any>))
			} else if (typeof obj == 'object') {
				datas.push(this.handleJson(obj))
			} else {
				datas.push(obj)
			}
		}
		// #endif

		return datas
	}
}