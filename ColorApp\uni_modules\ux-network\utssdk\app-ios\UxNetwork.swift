import Network
import SystemConfiguration

class UxNetworkMonitor {
    static let shared = UxNetworkMonitor()
    
    // MARK: - 监听工具
    private var nwMonitor: NWPathMonitor? // iOS 12+
    private var reachability: SCNetworkReachability? // iOS 5-11
    private var timer: Timer? // 主动轮询
    
	var onStatusChange: ((Bool) -> Void)?
	
    // MARK: - 状态
    var isConnected: Bool = false {
        didSet {
            onStatusChange?(isConnected)
        }
    }
    
    // MARK: - 生命周期
    func startMonitoring() {
        stopMonitoring() // 先停止旧的监听
        
        if #available(iOS 12, *) {
            setupNWPathMonitor()
        } else {
            setupSCNetworkReachability()
        }
        startPolling() // 启动轮询
    }
    
    func stopMonitoring() {
        nwMonitor?.cancel()
        nwMonitor = nil
        
        if let reachability = reachability {
            SCNetworkReachabilitySetCallback(reachability, nil, nil)
            SCNetworkReachabilitySetDispatchQueue(reachability, nil)
            self.reachability = nil
        }
        
        timer?.invalidate()
        timer = nil
    }
    
    // MARK: - iOS 12+ 实现（NWPathMonitor）
    @available(iOS 12, *)
    private func setupNWPathMonitor() {
        let monitor = NWPathMonitor()
        nwMonitor = monitor
        
        monitor.pathUpdateHandler = { [weak self] path in
            self?.isConnected = path.status == .satisfied
        }
        
        monitor.start(queue: DispatchQueue(label: "NetworkMonitorQueue"))
    }
    
    // MARK: - iOS 5-11 实现（SCNetworkReachability）
    private func setupSCNetworkReachability() {
        var zeroAddress = sockaddr()
        zeroAddress.sa_len = UInt8(MemoryLayout<sockaddr>.size)
        zeroAddress.sa_family = sa_family_t(AF_INET)
        
        guard let reachability = SCNetworkReachabilityCreateWithAddress(nil, &zeroAddress) else {
            isConnected = false
            return
        }
        self.reachability = reachability
        
        var context = SCNetworkReachabilityContext(
            version: 0,
            info: Unmanaged.passUnretained(self).toOpaque(),
            retain: nil,
            release: nil,
            copyDescription: nil
        )
        
        let callback: SCNetworkReachabilityCallBack = { _, flags, info in
            guard let info = info else { return }
            let monitor = Unmanaged<UxNetworkMonitor>.fromOpaque(info).takeUnretainedValue()
            monitor.isConnected = flags.contains(.reachable) && !flags.contains(.connectionRequired)
        }
        
        SCNetworkReachabilitySetCallback(reachability, callback, &context)
        SCNetworkReachabilitySetDispatchQueue(reachability, DispatchQueue.main)
        
        // 立即触发一次初始检查
        var flags = SCNetworkReachabilityFlags()
        SCNetworkReachabilityGetFlags(reachability, &flags)
        callback(reachability, flags, &context)
    }
    
    // MARK: - 主动轮询（双保险）
    private func startPolling() {
        timer = Timer.scheduledTimer(
            withTimeInterval: 2.0,
            repeats: true
        ) { [weak self] _ in
            self?.checkNetworkStatus()
        }
    }
    
    private func checkNetworkStatus() {
        if #available(iOS 12, *), let path = nwMonitor?.currentPath {
            isConnected = path.status == .satisfied
        } else if let reachability = reachability {
            var flags = SCNetworkReachabilityFlags()
            SCNetworkReachabilityGetFlags(reachability, &flags)
            isConnected = flags.contains(.reachable) && !flags.contains(.connectionRequired)
        }
    }
}

public class UxNetwork {
	
    static func on(listener: @escaping (_ isConnected: Bool) -> Void) -> Void {
		UxNetworkMonitor.shared.startMonitoring()
		UxNetworkMonitor.shared.onStatusChange = { isConnected in
		    listener(isConnected)
		}
	}
	
	static func off(){
		UxNetworkMonitor.shared.stopMonitoring()
	}
}