<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/chart-ring?title=Chart-ring"></Mobile>

# Chart-ring
> 组件类型：UxChartRingComponentPublicInstance

canvas绘制的环形进度条图表

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| [mode](#mode) | String | ring | 模式 |
| data | UxChartRingType |  | 数据 |
| delay | Number | 0 | 延迟初始化 |

### [mode](#mode)

| 值   | 说明 |
|:------:|:----:|
| ring圆环
 |  |
| dashboard仪表盘
 |  |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| changing | 进度发生改变时触发 |  |
  
  