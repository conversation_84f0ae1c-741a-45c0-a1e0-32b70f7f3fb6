<template>
	<view class="ux-empty">
		<image v-if="url != ''" class="ux-empty__image" :style="style" mode="widthFix" :src="url"></image>
		<text v-if="description != ''" class="ux-empty__description" :style="textStyle">{{ description }}</text>
		<view class="ux-empty__bottom">
			<slot></slot>
		</view>
	</view>
</template>

<script setup lang="ts">
	/**
	 * empty 空状态
	 * @description 内置多种情景下的空状态，支持自定义
	 * @demo pages/component/empty.uvue
	 * @tutorial https://www.uxframe.cn/component/empty.html
	 * @property {String}			image=[empty|404|error|search|network|message|address|comment|order|coupon|cart]	String | 占位图 (默认 empty)
	 * @value empty 默认
	 * @value 404 404
	 * @value error 错误
	 * @value search 搜索
	 * @value network 网络
	 * @value message 消息
	 * @value address 地址
	 * @value comment 评论
	 * @value order 订单
	 * @value coupon 优惠券
	 * @value cart 购物车
	 * @property {Any}				width								Any | 图片宽 高度自适应 (默认 200)
	 * @property {String}			description							String | 描述文字
	 * @property {String}			color								String | 文字颜色
	 * @property {String} 			darkColor=[none|auto|color]			String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Any}				size								Any | 文字大小 (默认 $ux.Conf.fontSize)
	 * @property {Boolean}			bold = [true|false]					Boolean | 文字加粗 (默认 false)
	 * <AUTHOR>
	 * @date 2024-12-03 11:01:08
	 */
	
	import { computed } from 'vue'
	import { $ux } from '../../index'
	import { useFontSize, usePlaceholderColor } from '../../libs/use/style.uts'
	
	defineOptions({
		name: 'ux-empty'
	})
	
	const props = defineProps({
		image: {
			type: String,
			default: 'empty',
		},
		width: {
			default: 200
		},
		description: {
			type: String,
			default: ''
		},
		color: {
			type: String,
			default: '#c8c8c8'
		},
		darkColor: {
			type: String,
			default: '#b8b8b8'
		},
		size: {
			default: 0
		},
		bold: {
			type: Boolean,
			default: false
		},
	})
	
	const fontSize = computed(():number => {
		return useFontSize($ux.Util.getPx(props.size), 1)
	})
	
	const fontColor = computed(():string => {
		return usePlaceholderColor(props.color, props.darkColor)
	})
	
	const style = computed(()=> {
		let css = {}
		
		css['width'] = $ux.Util.addUnit(props.width)
		
		return css
	})
	
	const textStyle = computed(() => {
		let css = {}
		
		css['font-size'] = $ux.Util.addUnit(fontSize.value)
		css['color'] = fontColor.value
		css['font-weight'] = props.bold?'bold':'normal'
		
		return css
	})
	
	const url = computed(() : string => {
		if(props.image == '') {
			return ''
		}
		
		let list = ['empty', '404', 'error', 'search', 'network','cart', 'message', 'coupon', 'comment', 'order', 'address']
		
		if (list.includes(props.image)) {
			return `/uni_modules/ux-frame/static/${props.image}.png`
		}
		
		return props.image
	})
</script>

<style lang="scss" scoped>
	.ux-empty {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		box-sizing: border-box;
		padding: 32px 0;;
		
		
		&__image {
			width: 200px;
		}
		
		&__description {
		    margin-top: 24px;
		    padding: 0 60px;
		    color: #a9a9a9;
		    font-size: 14px;
		}
		
		&__bottom {
			 margin-top: 24px;
		}
	}
</style>