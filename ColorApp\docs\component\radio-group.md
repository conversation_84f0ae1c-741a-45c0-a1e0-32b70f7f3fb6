<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/radio-group?title=Radio-group"></Mobile>

# Radio-group
> 组件类型：UxRadioGroupComponentPublicInstance



## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| [direction](#direction) | String | row | 方向 |
| [theme](#theme) | String | primary | 主题颜色 |
| [mode](#mode) | String | radio | 单选模式 |
| [shape](#shape) | String | circle | 形状，mode=radio时有效 |
| size | Any | `$ux.Conf.fontSize` | 字体大小 |
| color | String | `$ux.Conf.fontColor` | 文本颜色 |
| [darkColor](#darkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| checkedColor | String | #ffffff | 选择颜色 |
| [checkedColorDark](#checkedColorDark) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| backgroundColor | String | `$ux.Conf.backgroundColor` | radio背景色 |
| [backgroundDark](#backgroundDark) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| activeBackgroundColor | String | `$ux.Conf.primaryColor` | radio背景选择颜色 |
| [activeBackgroundDark](#activeBackgroundDark) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| borderColor | String | `$ux.Conf.borderColor` | radio边框色 |
| activeBorderColor | String | `$ux.Conf.primaryColor` | radio边框选择颜色 |
| inverse | Boolean | true | 反选 |
| readonly | Boolean | false | 只读 |
| disabled | Boolean | false | 是否禁用 |
| margin | Array |  | 边距[上右下左][上下左右][上下左右] |
| mt | Any |  | 距上单位px |
| mr | Any |  | 距右单位px |
| mb | Any |  | 距下单位px |
| ml | Any |  | 距左单位px |
| padding | Array |  | 填充[上右下左][上下左右][上下左右] |
| pt | Any |  | 上内边距单位px |
| pr | Any |  | 右内边距单位px |
| pb | Any |  | 下内边距单位px |
| pl | Any |  | 左内边距单位px |
| xstyle | Array |  | 自定义样式 |

### [direction](#direction)

| 值   | 说明 |
|:------:|:----:|
| row水平
 |  |
| col垂直
 |  |

### [theme](#theme)

| 值   | 说明 |
|:------:|:----:|
| primary | 主色
 |
| warning | 警告
 |
| success | 成功
 |
| error | 错误
 |
| info | 文本
 |

### [mode](#mode)

| 值   | 说明 |
|:------:|:----:|
| radio单选
 |  |
| button按钮
 |  |
| cell单元格
 |  |

### [shape](#shape)

| 值   | 说明 |
|:------:|:----:|
| circle圆形
 |  |
| square方形
 |  |

### [darkColor](#darkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [checkedColorDark](#checkedColorDark)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [backgroundDark](#backgroundDark)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [activeBackgroundDark](#activeBackgroundDark)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| change | 值改变时触发 |  |
  
  