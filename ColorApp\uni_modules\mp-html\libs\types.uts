/**
 * HTML节点类型定义
 */
export type HtmlNodeType = {
  /** 节点类型：text | element */
  type: string
  /** 标签名 */
  name?: string
  /** 文本内容 */
  text?: string
  /** 属性 */
  attrs?: UTSJSONObject
  /** 样式 */
  style?: UTSJSONObject
  /** 子节点 */
  children?: HtmlNodeType[]
}

/**
 * CSS样式解析结果
 */
export type CssStyleType = {
  color?: string
  fontSize?: string
  fontWeight?: string
  textAlign?: string
  backgroundColor?: string
  margin?: string
  padding?: string
  width?: string
  height?: string
  display?: string
}

/**
 * 解析器配置
 */
export type ParserConfigType = {
  /** 是否显示图片菜单 */
  showImgMenu: boolean
  /** 是否可选择文本 */
  selectable: boolean
  /** 基础字体大小 */
  baseFontSize: number
}

/**
 * 事件参数类型
 */
export type LinkTapEventType = {
  href: string
}

export type ImgTapEventType = {
  src: string
}

export type ErrorEventType = {
  error: string
}