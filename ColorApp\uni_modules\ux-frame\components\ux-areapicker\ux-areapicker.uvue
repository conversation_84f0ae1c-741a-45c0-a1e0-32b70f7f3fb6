<template>
	<ux-drawer ref="uxDrawerRef" direction="top" :height="`${pickerHeight}px`" :border="false" >
		<view class="ux_areapicker_popup__slot" @click="open">
			<slot></slot>
		</view>

		<template v-slot:drawer>
			<view class="ux_areapicker_popup__box" :style="[style]">
				<view class="ux_areapicker_popup__title">
					{{ props.title }}
					<view class="ux_areapicker_popup__title_c" @click="close">
						<ux-icon type="close"></ux-icon>
					</view>
				</view>
				<view class="ux_areapicker_popup__is_changed">
					<view class="ux_areapicker_popup__is_changed_item" v-for="(obj, index) in changes" :key="index"
						@click="replaceChoose(index)">
						<view class="ux_areapicker_popup__is_changed_item_slot_line">
							<view class="ux_areapicker_popup__is_changed_item_slot" :class="steps > index? 'done': ''"></view>
						</view>
						<view class="ux_areapicker_popup__is_changed_item_title" :class="steps === index? 'active': ''">
							{{ obj[props.labelKey] }}</view>
						<ux-icon type="arrowright"></ux-icon>
					</view>
				</view>
				<scroll-view :id="myId" class="ux_areapicker_popup__choose" :style="{ 'top': `${top}px` }">
					<view class="ux_areapicker_popup__choose_title">
						{{ listText }}
					</view>
					<view class="ux_areapicker_popup__choose_item" v-for="(obj, index) in list" :key="index"
						@click="choose(index)">
						{{ obj[props.labelKey] }}
					</view>
				</scroll-view>
			</view>
		</template>
	</ux-drawer>
</template>

<script lang="uts" setup>
	/**
	 * Area地区选择器
	 * @demo pages/component/areapicker.uvue
	 * @tutorial https://www.uxframe.cn/component/areapicker.html
	 * @property {String}			title						String | 选择器的标题
	 * @property {String}			labelKey					String | label的键值 默认为label
	 * @property {String}			valueKey					String | value的键值 默认为value
	 * @property {String}			activeColor					String | 激活选中的颜色 默认#3c9cff
	 * @property {Function}			load						Function | 查询下级数据的钩子
	 * @property {Function}			defaultLoad					Function | 默认回显数据的钩子
	 * @event {Function}			change						Function | 选择完毕时触发
	 * @event {Function}			close						Function | 点击关闭时触发
	 * <AUTHOR>
	 * @date 2025年3月6日 22点56分
	 */
	
	import { $ux } from '../../index';
	import { useThemeColor, useForegroundColor, useBorderColor, useFontColor, useFontSize, useSubTitleColor, useCancelColor, useRadius } from '../../libs/use/style.uts';
	type LoadCallback = (chooseData ?: UTSJSONObject) => Promise<UTSJSONObject> | null;
	type DefaultLoadCallback = () => Promise<UTSJSONObject[]> | null;

	defineOptions({ name: 'ux-areapikcer' });

	const emit = defineEmits(['change', 'close']);

	const props = defineProps({
		title: {
			type: String,
			default: '请选择'
		},
		labelKey: {
			type: String,
			default: 'label'
		},
		valueKey: {
			type: String,
			default: 'value'
		},
		activeColor: {
			type: String,
			default: '#3c9cff'
		},
		load: {
			type: Function as PropType<LoadCallback | null>,
			default: null
		},
		defaultLoad: {
			type: Function as PropType<DefaultLoadCallback | null>,
			default: null
		}
	});
	
	const uxDrawerRef = ref<UxPopoverComponentPublicInstance | null>(null);
	
	const myId = `ux-upload-${$ux.Random.uuid()}`
	const steps = ref<number>(0);
	const changes = ref<any[]>([] as any[])
	const listText = ref<string>('');
	const list = ref<any[]>([] as any[]);
	const replaceIndex = ref<number | null>(null);
	
	const top = computed((): number => {
		return changes.value.length * 44 + uni.rpx2px(101);
	});
	
	const pickerHeight = computed((): number => {
		return 600;
	});
	
	const backgroundColor = computed((): string => {
		return useForegroundColor('#ffffff', 'auto')
	})
	
	const style = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set("--color", props.activeColor);
		css.set('height', `${pickerHeight.value}px`)
		css.set('background-color', backgroundColor.value)
		
		return css
	})

	/**
	 * 加载要选择的数据
	 */
	const nextData = async (chooseData : any = undefined) => {
		if(null === props.load){
			return false;
		}
		const _load = props.load as LoadCallback;
		const data = await _load(chooseData);

		//	没有下级数据要选择了
		if (!data) {
			emit('change', changes.value);
			uxDrawerRef.value!.close()
			return false;
		}

		listText.value = '';
		list.value = [];

		listText.value = data.key || '请选择';
		data.list?.forEach(a => {
			list.value.push(a);
		});
		return true;
	}

	/**
	 * 预备下一个要选择的元素
	 */
	const nextReady = () => {
		const k = { placeholder: true };
		k[props.labelKey] = listText.value;
		changes.value.push(k)
	}

	/**
	 * 打开选择 调用load方法加载数据
	 */
	const open = async () => {
		if (0 === changes.value.length) {
			await nextData();
		}
		setTimeout(() => {
			uxDrawerRef.value!.open()
		}, 50);
	}

	/**
	 * 选择了某条数据 记录该数据 并调用load方法查询下次要选择的数据
	 */
	const choose = async (index : number) => {
		//	本次选择的数据
		const chooseData = list.value[index];

		//	替换指定数据
		if (null !== replaceIndex.value) {
			changes.value[steps.value] = chooseData;
			replaceIndex.value = null;
			//	重新步进并将步进索引后续数据全部删除重选
			changes.value.splice(steps.value + 1);
		}

		if (0 === steps.value && 0 === changes.value.length) {
			//	首次点击 加入已选择的元素
			changes.value.push(chooseData);
		} else {
			//	非首次点击 替换已预备的元素
			changes.value[steps.value] = chooseData;
		}

		//	查询下次要选择的数据
		if (await nextData(chooseData)) {
			//	步进 +1
			steps.value += 1;
			//	预备下一个要选择元素 【请选择xxxx】
			nextReady();
		}
	}

	/**
	 * 记录要替换的元素索引
	 */
	const replaceChoose = async (index : number) => {
		//	如果要替换的元素是预装元素  跳过记录
		if (changes.value[index].placeholder) {
			return;
		}
		replaceIndex.value = index;
		//	将步进值定位到要替换的位置 重新步进
		steps.value = index;
		if (0 === index) {
			//	如果索引位为0 则全部重选
			nextData();
			return;
		}
		//	加载要替换的元素的下一级数据
		nextData(changes.value[index - 1]);
	}

	const close = () => {
		uxDrawerRef.value!.close()
		emit('close');
	}

	defineExpose({
		open,
		close
	});

	nextTick(async () => {
		if(null !== props.defaultLoad){
			const _load = props.defaultLoad as DefaultLoadCallback;
			const data = await _load();
			if (data && data.length) {
				data.forEach(a => {
					changes.value.push(a);
				});
				steps.value = changes.value.length - 1;;
				nextData(changes.value[steps.value - 1]);
			}
		}
	})
</script>

<style lang="scss" scoped>
	.ux_areapicker_popup__slot {
		width: 100%;
	}

	.ux_areapicker_popup__box {
		width: 100%;
		position: relative;

		.ux_areapicker_popup__title {
			height: 80rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			position: relative;
			font-weight: bold;

			.ux_areapicker_popup__title_c {
				position: absolute;
				right: 20rpx;
				top: 20rpx;
			}
		}

		.ux_areapicker_popup__is_changed {
			border-bottom: 1px solid #ddd;
			margin-bottom: 20rpx;
			position: fixed;
			left: 0;
			top: 80rpx;
			right: 0;

			.ux_areapicker_popup__is_changed_item {
				height: 44px;
				line-height: 44px;
				display: flex;
				flex-direction: row;
				align-items: center;
				padding: 0 30rpx;

				.ux_areapicker_popup__is_changed_item_title {
					flex: 1;
					margin: 0 30rpx;
				}

				.done {
					background-color: var(--color);
				}

				.active {
					color: var(--color);
				}
				
				.ux_areapicker_popup__is_changed_item_slot_line{
					width: 10px;
					height: 100%;
					position: relative;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					
					.ux_areapicker_popup__is_changed_item_slot {
						width: 10px;
						height: 10px;
						border-radius: 10px;
						border: 1px solid var(--color);
					}
					
					&:before{
						content: '';
						border-left: 1px solid var(--color);
						position: absolute;
						left: 4px;
						top: 0;
						height: 17px;
					}
					
					&:after{
						content: '';
						border-left: 1px solid var(--color);
						position: absolute;
						left: 4px;
						top: 27px;
						height: 17px;
					}
				}
				
				&:first-child{
					
					.ux_areapicker_popup__is_changed_item_slot_line{
						&:before{
							border: none;
							height: 0;
						}
						
						&:after{
							top: 27px;
							height: 17px;
						}
					}
					
				}
				
				&:last-child{
					
					.ux_areapicker_popup__is_changed_item_slot_line{
						&:before{
							top: 0;
							height: 17px;
						}
						
						&:after{
							border: none;
							height: 0;
						}
					}
					
				}
			}
		}

		.ux_areapicker_popup__choose {
			padding: 0 20rpx;
			position: absolute;
			left: 0;
			top: 80rpx;
			right: 0;
			bottom: 0;

			.ux_areapicker_popup__choose_title {
				font-weight: bold;
			}

			.ux_areapicker_popup__choose_item {
				height: 80rpx;
				line-height: 80rpx;
				margin-left: 20rpx;
			}
		}
	}
</style>