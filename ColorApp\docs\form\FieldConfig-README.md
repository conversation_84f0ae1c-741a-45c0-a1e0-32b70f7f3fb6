### 表单配置类：BindField 详细说明

`BindField` 是一个用于表单字段配置的特性类，通过属性设置可以灵活定义表单中各种字段的行为、样式和验证规则。以下是对该配置类所有字段的详细说明。

### 一、基本信息
```csharp
/// <summary>
/// 字段Id（prop绑定值）
/// </summary>
public string Id { get; set; }

/// <summary>
/// 字段名称/描述
/// </summary>
public string Name { get; set; }

/// <summary>
/// 字段标题
/// </summary>
public string Title { get; set; }
```
- **Id**：用于数据绑定的标识符，通常对应模型属性名
- **Name**：字段的内部名称，用于逻辑处理
- **Title**：显示给用户的字段标题

### 二、分组与样式
```csharp
/// <summary>
/// 字段分组标题
/// </summary>
public string Group { get; set; }

/// <summary>
/// 分组样式名称
/// </summary>
public string GroupClass { get; set; }

/// <summary>
/// 字段样式
/// </summary>
public string Class { get; set; }
```
- **Group**：将字段归类到特定分组
- **GroupClass**：分组的CSS类名
- **Class**：字段本身的CSS类名

### 三、状态控制
```csharp
/// <summary>
/// 是否必填
/// </summary>
public bool Required { get; set; }

/// <summary>
/// 是否屏蔽
/// </summary>
public bool Disabled { get; set; }

/// <summary>
/// 是否只读
/// </summary>
public bool Readonly { get; set; }

/// <summary>
/// 是否隐藏
/// </summary>
public bool Hide { get; set; }
```
- **Required**：标记字段为必填项
- **Disabled**：禁用字段，用户无法交互
- **Readonly**：只读模式，用户可查看但不可修改
- **Hide**：完全隐藏该字段

### 四、输入控制
```csharp
/// <summary>
/// 输入框提示文字
/// </summary>
public string Placeholder { get; set; }

/// <summary>
/// 字段提示信息
/// </summary>
public string Tooltip { get; set; }

/// <summary>
/// 当字段组件类型为Select时，要显示的层级
/// </summary>
public int Level { get; set; }
```
- **Placeholder**：输入框内的占位提示文字
- **Tooltip**：鼠标悬停时显示的详细提示
- **Level**：下拉选择框的层级（适用于级联选择）

### 五、字段类型
```csharp
/// <summary>
/// 字段组件类型：Text（输入框）、NumberInput（数字输入框）、Tag（标签输入框）等，详见：FieldEditorType
/// </summary>
public string Type { get; set; }

/// <summary>
/// 数据类型：string、bool、datetime、int、long、decimal、json，详见：DataTypes
/// </summary>
public string DataType { get; set; }

/// <summary>
/// 数据类型是否为数组
/// </summary>
public bool IsArray { get; set; }
```
- **Type**：字段的UI组件类型（基于FieldEditorType枚举）
- **DataType**：数据的实际类型
- **IsArray**：标记数据是否为数组类型

### 六、数据源配置
```csharp
/// <summary>
/// 数据源类型：Enum（枚举）、Json（JSON数据源）、Menu（代码库数据源）、API（API数据源）、Question（问卷库）
/// </summary>
public string DataSourceType { get; set; } = "Json";

/// <summary>
/// 数据源（根据DataSourceType生成），当为Enum时内DataSource容为枚举名称，当为Json时可以是字符串通过小写逗号分配，也可以是是 id和text JSON对象，如：[{"id":"1", "text":"张三"}, {"id":"1", "text":"李四"}]，当为Menu时内容为菜单栏目Id，当为API时内容为API地址，当为Question时内容为问卷Id
/// </summary>
public dynamic DataSource { get; set; }

/// <summary>
/// 默认数据源内容，如：先生,女士
/// </summary>
public string DefaultDataSource { get; set; }

/// <summary>
/// 当DataSourceType="code"时的，默认代码库Id
/// </summary>
public string CodeId { get; set; }
```
- **DataSourceType**：定义数据源的获取方式
- **DataSource**：实际的数据源对象
- **DefaultDataSource**：默认的简单数据源（如文本列表）
- **CodeId**：代码库数据源的标识

### 七、验证规则
```csharp
/// <summary>
/// 字段验证类型：pattern（正则验证）、equal（比较验证）、lessThan（小于验证）、greaterThan（大于验证）、code（验证码）
/// </summary>
public string ValidationType { get; set; }

/// <summary>
/// 正则验证表达式
/// </summary>
public string Validation { get; set; }

/// <summary>
/// 验证字段
/// </summary>
public string ValidField { get; set; }

/// <summary>
/// 验证提示信息
/// </summary>
public string ValidMesasge { get; set; }

/// <summary>
/// 验证提示信息（英文）
/// </summary>
public string ValidMesasgeEN { get; set; }
```
- **ValidationType**：验证规则的类型
- **Validation**：具体的验证表达式（如正则表达式）
- **ValidField**：要验证的字段
- **ValidMesasge**：验证失败时的中文提示
- **ValidMesasgeEN**：验证失败时的英文提示

### 八、输入限制
```csharp
/// <summary>
/// 输入框类型：text、mobile、email、number、idcard、password，详见：InputTypes
/// </summary>
public string InputType { get; set; }

/// <summary>
/// 最多可输入字符长度，或文件大小限制（单位KB）
/// </summary>
public int MaxLength { get; set; }

/// <summary>
/// 最少输入字符数
/// </summary>
public int MinLength { get; set; }

/// <summary>
/// 英文字符数限制
/// </summary>
public int AlphaWordLimit { get; set; }
```
- **InputType**：输入框的具体类型
- **MaxLength**：最大输入长度或文件大小限制
- **MinLength**：最小输入长度
- **AlphaWordLimit**：英文字符数量限制

### 九、数值控制（针对数字类型）
```csharp
/// <summary>
/// 最少可上传文件数量
/// </summary>
public int Min { get; set; }

/// <summary>
/// 最多可上传文件数量
/// </summary>
public int Max { get; set; }

/// <summary>
/// 数字类型NumberInput范围：最小值
/// </summary>
public decimal MinDecimal { get; set; }

/// <summary>
/// 数字类型NumberInput范围：最大值
/// </summary>
public decimal MaxDecimal { get; set; }

/// <summary>
/// 数字输入框小数精度
/// </summary>
public int Precision { get; set; }
```
- **Min/Max**：文件上传数量限制
- **MinDecimal/MaxDecimal**：数字输入范围
- **Precision**：小数位数精度

### 十、其他配置
```csharp
/// <summary>
/// 宽度限制
/// </summary>
public int MaxWidth { get; set; }

/// <summary>
/// 高度限制
/// </summary>
public int MaxHeight { get; set; }

/// <summary>
/// 默认数据
/// </summary>
public string DefaultValue { get; set; }

/// <summary>
/// 显示字数统计
/// </summary>
public bool WordCount { get; set; }

/// <summary>
/// 字段排序
/// </summary>
public int Order { get; set; }
```
- **MaxWidth/MaxHeight**：尺寸限制
- **DefaultValue**：字段默认值
- **WordCount**：是否显示字数统计
- **Order**：字段显示顺序

### 十一、高级配置
```csharp
/// <summary>
/// 扩展数据配置
/// </summary>
public object Data { get; set; }

/// <summary>
/// 字段验证规则配置：以数组对象形式定义复杂规则
/// </summary>
public string RuleConfig { get; set; }

/// <summary>
/// 该字段是否在表格列表中展示
/// </summary>
public bool ShowList { get; set; }

/// <summary>
/// 该字段只在后台显示
/// </summary>
public bool AdminShow { get; set; }

/// <summary>
/// 只有后台可编辑
/// </summary>
public bool AdminEdit { get; set; }
```
- **Data**：扩展配置数据
- **RuleConfig**：复杂的字段联动规则
- **ShowList**：是否在表格中显示
- **AdminShow/AdminEdit**：后台可见/可编辑控制

### 十二、文件与格式
```csharp
/// <summary>
/// 可上传的文件格式：例如：.jpg,.png,.gif
/// </summary>
public string Format { get; set; }

/// <summary>
/// 该字段渲染表格时的列宽
/// </summary>
public int ColumnWidth { get; set; }

/// <summary>
/// 示例图
/// </summary>
public string Example { get; set; }

/// <summary>
/// 背景图
/// </summary>
public string BgImage { get; set; }
```
- **Format**：文件上传格式限制
- **ColumnWidth**：表格列宽
- **Example/BgImage**：示例图和背景图URL

### 十三、权限与标识
```csharp
/// <summary>
/// 可访问该字段的权限列表
/// </summary>
public List<string> Permissions { get; set; } = new();

/// <summary>
/// 字段标识
/// </summary>
public List<string> Flags { get; set; } = new();

/// <summary>
/// 其他配置项
/// </summary>
public Dictionary<string, string> Configs { get; set; } = new();
```
- **Permissions**：访问该字段所需的权限
- **Flags**：字段标识标签
- **Configs**：其他自定义配置项

### 字段编辑器类型（FieldEditorType）
这是一个枚举类型，定义了所有支持的字段组件类型：

```csharp
public enum FieldEditorType
{
    Text,           // 输入框
    InputEditor,    // 标题编辑器
    Image,          // 图片
    File,           // 文件
    Video,          // 视频
    SmallEditor,    // 简易编辑器
    Editor,         // 全功能编辑器
    MPEditor,       // 小程序编辑器
    JsonEditor,     // JSON编辑器
    Date,           // 日期
    DateTime,       // 日期和时间
    DateRange,      // 日期范围
    DateTimeRange,  // 日期时间范围
    Select,         // 选择框
    TreeSelect,     // 目录树选择
    Picker,         // 多级选择框
    SelectInput,    // 选择输入框
    Tag,            // 标签框
    TextArea,       // 文本框
    Switch,         // 是否选择
    ButtonGroup,    // 按钮组
    RadioGroup,     // 单选组
    CheckboxGroup,  // 多选组
    Checkbox,       // 复选
    Address,        // 地址
    ChinaAddress,   // 国内地址
    PhoneFormat,    // 电话
    NumberRange,    // 数字范围
    Custom,         // 自定义表单
    Face,           // 人脸
    Color,          // 颜色选择器
    Dic,            // 键值列表
    DataTable,      // 数据表
    Questions       // 问卷
}
```

这个配置类通过丰富的属性设置，提供了强大的表单字段自定义能力，可以满足各种复杂的表单需求。