<template>
  <div
    v-if="!field.Hide"
    class="form-group-item"
    :class="classes"
  >
    <!-- 表单项标题 -->
    <div
      v-if="field.Title"
      class="h-row"
      :class="[
        field.Required ? 'h-form-item-required' : '',
        titleClass ? titleClass : '',
      ]"
      validable="true"
    >
      <div class="h-col h-col-xl-24 h-col-lg-24 h-col-md-24 h-col-sm-24 h-col-xs-24">
        <label class="h-form-item-label" style="width: 100%"
          ><span>{{ field.Title }}</span></label
        >
      </div>
    </div>
    <!-- 表单项渲染 -->
    <FormItem
      :label="field.Name"
      :labelCell="labelCell"
      :prop="field.Id"
      :class="'form-item-' + field.Id.toLowerCase()"
      :required="field.Required"
    >
      <!-- Input类表单 -->
      <div v-if="field.Type == 'Text'" class="h-input-group">
        <el-input
          :type="getInputType(field.InputType)"
          :readonly="field.Readonly"
          v-model="dataModel[field.Id]"
          :maxlength="field.MaxLength > 0 ? field.MaxLength : -1"
          :disabled="field.Disabled"
          :placeholder="getPlacehoder(field)"
          :clearable="true"
          @change="handleChange(field)"
        />
        <!-- 发送验证码按钮 -->
        <Button
          v-if="field.ValidationType === 'code'"
          color="primary"
          @click="handleSendCode(field)"
          :disabled="!resend"
          tabindex="-1"
          >{{ getCodeText }}</Button
        >
      </div>
      <!-- 数字输入框 -->
      <NumberInput
        v-else-if="field.Type == 'NumberInput'"
        @change="handleChange(field)"
        v-model="dataModel[field.Id]"
        :min="field.MinDecimal"
        :max="field.MaxDecimal"
      />
      <!-- 开关 -->
      <HSwitch v-model="dataModel[field.Id]" v-else-if="field.Type == 'Switch'"
        ><template #open>开启</template><template #close>关闭</template></HSwitch
      >
      <!-- 勾选框 -->
      <Checkbox
        v-model="dataModel[field.Id]"
        :datas="getDataSource(field)"
        v-else-if="field.Type == 'Checkbox' || field.Type === 'CheckboxGroup'"
        @change="handleChange(field)"
        :limit="field.Max"
      />
      <!-- 单选框 -->
      <Radio
        v-model="dataModel[field.Id]"
        :datas="getDataSource(field)"
        v-else-if="
          field.Type == 'Radio' ||
          field.Type === 'RadioGroup' ||
          field.Type === 'ButtonGroup'
        "
        @change="handleChange(field)"
      />
      <!-- 标题编辑器 -->
      <InputEditor
        v-model="dataModel[field.Id]"
        v-else-if="field.Type == 'InputEditor'"
        :required="field.Required"
      />
      <!-- 自动完成输入框 -->
      <AutoComplete
        v-else-if="field.Type == 'SelectInput'"
        :datas="field.option ? null : getDataSource(field)"
        :option="field.option"
        :must-match="false"
        type="title"
        v-model="dataModel[field.Id]"
        :placeholder="getLang('请选择或输入...', 'Please select or enter...')"
        :fieldId="field.Id"
        @change="hanldAutoCompleteChange(field)"
        :required="field.Required"
      />
      <!-- 企业搜索 -->
      <CompanySearch
        v-else-if="field.Type == 'CompanySearch'"
        :option="field.option"
        v-model="dataModel[field.Id]"
        :placeholder="getPlacehoder(field)"
        :fieldId="field.Id"
        @change="handleChange(field)"
        :required="field.Required"
      />
      <!-- 下拉选择框 -->
      <Select
        v-else-if="field.Type == 'Select'"
        :datas="getDataSource(field)"
        v-model="dataModel[field.Id]"
        :equalWidth="true"
        :autosize="false"
        :limit="field.Max > 0 ? field.Max : null"
        :placeholder="getPlacehoder(field)"
        :disabled="field.Disabled"
        :multiple="field.Multiple"
        :required="field.Required"
        :deletable="!field.Required"
        :filterable="true"
        @change="handleSelectChange(field, $event)"
      />
      <!-- 地址选择 -->
      <Address
        v-else-if="field.Type == 'Address'"
        v-model="dataModel[field.Id]"
        :countryDataSource="field.DataSource"
        :required="field.Required"
        :showCountry="field.Data && field.Data.indexOf('国家') !== -1"
        :showProvince="field.Data && field.Data.indexOf('省') !== -1"
        :showCity="field.Data && field.Data.indexOf('市') !== -1"
        :showAddress="field.Data && field.Data.indexOf('详细地址') !== -1"
        :isCity="field.Data && field.Data.indexOf('城市') !== -1"
        @change="handleChange(field)"
      />
      <!-- 电话号码输入框 -->
      <Phone
        v-else-if="field.Type == 'PhoneFormat'"
        v-model="dataModel[field.Id]"
        :required="field.Required"
        @change="handleChange(field)"
      />
      <!-- 数字范围 -->
      <NumberRange
        v-else-if="field.Type == 'NumberRange'"
        v-model="dataModel[field.Id]"
        :min="field.MinDecimal"
        :max="field.MaxDecimal"
        :disabled="field.Disabled"
        :fieldId="field.Id"
        :field="field"
      />
      <!-- 树形选择框 -->
      <XTreeselect
        :options="getDataSource(field)"
        v-model="dataModel[field.Id]"
        v-else-if="field.Type == 'TreeSelect'"
        :multiple="field.Multiple"
        :disabled="field.Disabled"
        :required="field.Required"
      />
      <!-- 标签输入框 -->
      <TagInput
        v-model="dataModel[field.Id]"
        v-if="field.Type == 'Tag'"
        :required="field.Required"
      />
      <!-- 日期选择框 -->
      <div v-else-if="field.Type.startsWith('Date')" class="flex-h">
        <Checkbox
          v-if="field.Type != 'DateRange' && field.Flags && field.Flags.indexOf('时间待定') !== -1"
          v-model="dataModel[field.Id]"
          trueValue="0001/1/1"
          falseValue=""
          style="margin-right: 10px"
        >
          时间待定</Checkbox
        >
        <!-- 单日期选择框 -->
        <DatePicker
          v-if="field.Type != 'DateRange'"
          v-model="dataModel[field.Id]"
          class="flex1"
          :format="getDateFormat(field)"
          :type="field.Flags.indexOf('datetime') !== -1 ? 'datetime' : field.Type.toLowerCase()"
          :option="getDateOption(field)"
          hasSeconds
          :required="field.Required"
        />
        <!-- 日期范围选择框 -->
        <DateRangePicker
          v-else
          v-model="dataModel[field.Id]"
          style="max-width: 450px"
          :type="field.Flags.indexOf('datetime') !== -1 ? 'datetimerange' : 'daterange'"
          :format="getDateFormat(field)"
          :option="getDateOption(field)"
          :required="field.Required"
        />
      </div>
      <!-- 视频上传 -->
      <VideoUpload
        v-else-if="field.Type == 'Video'"
        v-model="dataModel[field.Id]"
        :required="field.Required"
        :min="field.Min || 1"
        :limit="field.Max || 1"
        :sizeLimit="field.MaxLength"
        @change="handleChange(field)"
      />
      <!-- 人脸上传&验证 -->
      <div v-else-if="field.Type == 'Face'">
        <PhotoUpload
          :cropSize="{
            width: field.MaxWidth || 295,
            height: field.MaxHeight || 413,
          }"
          :maxSize="getFaceMaxSize(field)"
          :limitMinSize="[200, 280]"
          :centerBox="true"
          checkFace
          v-model="dataModel[field.Id]"
          style="width: 135px; height: 110px; line-height: normal"
          @input="handleChange(field)"
        >
        </PhotoUpload>
      </div>
      <!-- 图片、文件、音频上传 -->
      <div
        v-else-if="
          field.Type == 'Image' || field.Type == 'File' || field.Type == 'Audio'
        "
      >
        <Oss
          :type="getUploadType(field)"
          dataType="file"
          v-model="dataModel[field.Id]"
          :min="field.Min || 1"
          :limit="field.Max || 1"
          :sizeLimit="field.MaxLength"
          :fileType="field.Format"
          :maxSize="{ width: field.MaxWidth, height: field.MaxHeight }"
          :thumbSize="field.ThumbSize"
          :required="field.Required"
          :bgImage="field.BgImage"
          :example="field.Example"
        >
          {{ field.Placeholder }}
        </Oss>
      </div>
      <!-- TextArea -->
      <el-input
        v-else-if="field.Type == 'TextArea'"
        type="textarea"
        @input="currentValue(field.MaxLength, field.Id)"
        v-model="dataModel[field.Id]"
        style="width: 100%"
        :class="field.Data"
        :autosize="field.Flags.indexOf('autosize') !== -1"
        :placeholder="field.Placeholder"
      />
      <!-- TextArea字数限制提醒信息 -->
      <template v-for="words in fieldWords" :key="words.id">
        <div
          v-if="field.Id === words.id"
          class="words font-size: 12px; line-height: 20px; padding-top: 10px"
        >
          <div v-if="words.words >= 0" style="text-align: right">
            {{ getLang("剩余可输入字符：", "The remaining input characters: ") }}
            <span :class="{ green: words.words > 0, red: words.words <= 0 }">{{
              words.words
            }}</span>
          </div>
          <div v-else class="red">
            {{ getLang("已超出", "It is more than") }} {{ Math.abs(words.words) }}
            {{ getLang("个限制字符！", "A limit character!") }}
          </div>
        </div>
      </template>

      <!-- FormItem的slot -->
      <slot name="formitem" :field="field"></slot>
      <!-- 表单项的Tooltip -->
      <div
        style="color: red; font-size: 12px; line-height: 20px; margin-top: 10px"
        v-if="field.Tooltip"
        v-html="field.Tooltip"
      ></div>
    </FormItem>
  </div>
</template>

<script setup>
import { ref, reactive, inject } from 'vue'
import manba from "manba"
import useUtils from '../../hooks/utils';

const {getLang, showError} = useUtils()

const props = defineProps({
  // 字段对象
  field: {
    type: Object,
    required: true
  },
  // 数据绑定对象
  dataModel: {
    type: Object,
    required: true
  },
  // 标题样式
  titleClass: {
    type: String,
    default: 'h-form-item'
  },
  // 字段样式
  classes: String,
  labelCell: {
    type: Number,
    default: 24
  }
})

const emit = defineEmits(['change'])

const fieldWords = ref([])
const resend = ref(true)
const getCodeText = ref('获取验证码')

// 注入Form组件提供的依赖
const validField = inject('validField')
const requireds = inject('requireds')
const removeProp = inject('removeProp')
const setConfig = inject('setConfig')
const updateErrorMessage = inject('updateErrorMessage')
const updateProp = inject('updateProp')
const params = inject('params')

const getInputType = (type) => {
  if (!type || type === "idcard") return "text"
  if (type === "mobile") return "tel"
  return type
}

const getPlacehoder = (field) => {
  if (field.Placeholder) return field.Placeholder
  if (field.Type === "Select") return getLang("请选择...", "Please select...")
  if (field.Required) return getLang("请输入...", "Please enter...")
  return field.Name
}

const getDataSource = (field) => {
  try {
    if (!field.DataSource) return []
    if (field.DataSource instanceof Array) {
      for (let i of field.DataSource) {
        if (!i.text) {
          i["text"] = i.id
        }
      }
      return field.DataSource
    }
    if (field.DataSource.startsWith("[")) return JSON.parse(field.DataSource)
    return field.DataSource.split(",").map((a) => {
      return { id: a, text: a }
    })
  } catch (error) {
    showError(error)
    console.log(error, "##getDataSource", field)
  }
}

const getDateOption = () => {
  const opts = {
    shortcuts: [
      {
        title: "当前时间",
        value() {
          return manba()
        },
      },
    ],
  }
  return opts
}

const getDateFormat = (field) => {
  if (field.Type.toLowerCase() == "datetime" || field.Flags.indexOf("datetime") !== -1) {
    return "YYYY-MM-DD HH:mm:ss"
  }
  return "YYYY-MM-DD"
}

const getUploadType = (field) => {
  if (field.Min <= 1 && field.Max <= 1)
    return field.Type == "Image" ? "image" : "file"
  else return field.Type == "Image" ? "images" : "files"
}

const getFaceMaxSize = (field) => {
  if (field.MaxWidth > 0 || field.MaxHeight > 0) {
    return field.MaxWidth > field.MaxHeight ? field.MaxWidth : field.MaxHeight
  }
  return 480
}

const handleChange = (field) => {
  console.log('###handle change：', field)
  emit('change', { field, data: props.dataModel[field.Id] })
}

const handleSelectChange = (field, e) => {
  emit('change', { field, data: e })
}

const handleSendCode = (field) => {
  emit('change', { field, type: 'sendCode' })
}

const hanldAutoCompleteChange = (field, e) => {
  emit('change', { field, data: e })
}

const currentValue = (maxlength, id) => {
  if (maxlength > 0) {
    let val = props.dataModel[id]
    for (var i = 0; i < fieldWords.value.length; i++) {
      if (fieldWords.value[i].id == id) {
        fieldWords.value[i].words = maxlength - val.length
        return
      }
    }
  }
}

</script>
<style lang="less" scoped>
.form-group-item{
  margin-bottom: 12px;
  >.h-form-item:last-child {
    padding-bottom: 0!important;
  }
  &:last-child {
    margin-bottom: 0;
  }
}
</style>