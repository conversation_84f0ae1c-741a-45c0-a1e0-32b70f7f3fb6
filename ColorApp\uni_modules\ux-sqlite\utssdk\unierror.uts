import { UxSqlErrorCode, UxSqlFail } from "./interface.uts"

export const UniErrorSubject = 'ux-sqlite'

export const UxSqlErrors : Map<UxSqlErrorCode, string> = new Map([
  [-1, 'open db fail'],
  [-2, 'close db fail'],
])

export class UxSqlFailImpl extends UniError implements UxSqlFail {
  constructor(errCode : UxSqlErrorCode) {
    super();
    this.errSubject = UniErrorSubject;
    this.errCode = errCode;
    this.errMsg = UxSqlErrors.get(errCode) ?? '';
  }
}
