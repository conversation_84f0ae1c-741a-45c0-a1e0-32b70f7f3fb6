<template>
	<scroll-view :id="`scroll-${myId}`" ref="uxScroll" class="ux-drag" :style="scrollStyle"
		:scroll-top="scrollTop"
		:scroll-y="true"
		:scroll-with-animation="false"
		:show-scrollbar="false"
		@scroll="scroll">
		<view :id="myId" class="ux-drag__body" :style="style"
			@touchstart="touchstart" 
			@touchmove="touchmove" 
			@touchend="touchend" 
			@touchcancel="touchend" 
			@mousedown="touchstartPc"
			@mousemove="touchmovePc"
			@mouseup="touchendPc"
			@mouseleave="touchendPc">
			<template v-for="(item, index) in datas" :key="index">
				<ux-drag-item :itemId="item.id" :index="item.index" :border="border" @click="click(item.data)">
					<slot :data="item.data"></slot>
				</ux-drag-item>
			</template>
			
			<view v-if="isDrag" ref="uxDragItemRef" class="ux-drag__item" :style="dragStyle">
				<slot :data="dragItem!.data"></slot>
			</view>
		</view>
	</scroll-view>
</template>

<script setup lang="ts">
	/**
	 * Drag 拖拽排序
	 * @description 支持列表式、宫格式拖动排序，支持设置列数，列表式支持边界滚动，支持动态新增删除项目
	 * @demo pages/component/drag.uvue
	 * @tutorial https://www.uxframe.cn/component/drag.html
	 * @property {String}			name					String | 唯一索引名称 (默认 id)
	 * @property {Array}			list					UTSJSONObject[] | 数据列表
	 * @property {Number}			col						Number | 列数  (默认 1)
	 * @property {Any}				height					Any | 单项高度 (默认 45)
	 * @property {Boolean}			border					Boolean | 显示下边框 (默认 false)
	 * @property {Boolean}			longtouch				Boolean | 是否开启长按拖动 (默认 true)
	 * @property {Number}			touchtime				Number | 长按拖动触发时间 开启长按拖动才有效 (默认 250)
	 * @property {Number}			speed					Number | 列表边界滚动速度 (默认 1)
	 * @property {Boolean}			disabled=[true|false]	Boolean | 是否禁用 (默认 false)
	 * @value true
	 * @value false
	 * @event {Function}			click					Function | 点击项目时触发
	 * @event {Function}			change					Function | 拖拽完成时触发
	 * @event {Function}			start					Function | 拖拽开始时触发
	 * @event {Function}			end						Function | 拖拽结束时触发
	 * <AUTHOR>
	 * @date 2024-12-08 17:27:15
	 */
	
	import { PropType, computed, getCurrentInstance, onMounted, provide, ref, watch } from "vue"
	import { $ux } from '../../index'
	import { UxDragInfo } from "../../libs/types/types.uts"
	
	type Item = {
		col : number,
		row : number,
		index : number
	}
	
	type Pos = {
		x : number,
		y : number
	}
	
	type Data = {
		id: string,
		index: number,
		data: UTSJSONObject,
		node: UxDragInfo | null
	}
	
	defineOptions({
		name: 'ux-drag'
	})
	
	defineSlots<{
		default(props : { data : UTSJSONObject }) : any
	}>()
	
	const emit = defineEmits(['click', 'change', 'start', 'end'])
	
	const props = defineProps({
		name: {
			type: String,
			default: 'id',
		},
		list: {
			type: Array as PropType<UTSJSONObject[]>,
			default: () => [] as UTSJSONObject[],
			required: true
		},
		col: {
			type: Number,
			default: 1
		},
		height: {
			default: 45
		},
		border: {
			type: Boolean,
			default: false
		},
		longtouch: {
			type: Boolean,
			default: true
		},
		touchtime: {
			type: Number,
			default: 250
		},
		speed: {
			type: Number,
			default: 1
		},
		disabled: {
			type: Boolean,
			default: false
		},
	})
	
	const instance = getCurrentInstance()?.proxy
	
	const myId = `ux-drag-${$ux.Random.uuid()}`
	const uxDragItemRef = ref<Element | null>(null)
	const datas = ref<Data[]>([] as Data[])
	let tempDatas = [] as Data[]
	const cellHeight = ref(0)
	const cellWidth = ref(0)
	let activeIndex = -1
	let targetIndex = -1
	const x = ref(0)
	const y = ref(0)
	const isDrag = ref(false)
	let dragItem: Data | null = null
	const dragPos = ref<Pos>({x: 0, y: 0} as Pos)
	const dragDuration = ref(0)
	const dragAlpha = ref(1)
	const dragId = ref('')
	let startTop = 0
	let toBottom = false
	let touchTimer = 0
	
	const scrollTop = ref(0)
	let scrollState = ''
	let scrollTimer = 0
	
	provide('name', props.name)
	provide('height', props.height)
	provide('col', props.col)
	provide('dragId', dragId)
	
	const list = computed(() => {
		return props.list
	})
	
	const col = computed((): number => {
		return props.col
	})
	
	const rows = computed((): number => {
		return Math.ceil(datas.value.length / col.value)
	})
	
	const totalHeight = computed((): number => {
		return rows.value * cellHeight.value
	})
	
	const style = computed((): string => {
		return `height: ${totalHeight.value}px`
	})
	
	const scrollStyle = computed((): string => {
		return props.col == 1 ? `height: 100%` : ''
	})
	
	const dragStyle = computed(() => {
		let css = {}
		
		css['opacity'] = dragAlpha.value
		css['transition-duration'] = `${dragDuration.value}ms`
		css['width'] = `${cellWidth.value}px`
		css['height'] = `${cellHeight.value}px`
		css['top'] = `${dragPos.value.y + 6}px`
		css['left'] = `${dragPos.value.x + 6}px`
		
		return css
	})
	
	function end() {
		let results = copyDatas(datas.value)
		results.sort((a, b) : number => a.index - b.index)
		
		emit('end')
		emit('change', results.map((e: Data): UTSJSONObject => e.data))
	}
	
	async function calcPos(x : number, y : number) {
		let rect = await $ux.Util.getBoundingClientRect(`#${myId}`, instance)
		
		let _row = Math.floor((y - rect.top) / cellHeight.value)
		let _col = Math.floor((x - rect.left) / cellWidth.value)
		
		let lastRows = datas.value.length % col.value
		let maxCol = lastRows == 0 ? col.value : lastRows
		
		let maxRow = Math.floor((datas.value.length - 1) / col.value)
		_row = Math.min(Math.max(0, _row), maxRow)
		_col = Math.min(Math.max(0, _col), col.value - 1)
		
		let index = (_row + 1) * col.value - (col.value - _col)
		_col = maxRow == _col ? Math.min(maxCol - 1, _col) : _col
		
		index = Math.min(Math.max(0, index), datas.value.length - 1)
	
		return {
			row: _row,
			col: _col,
			index: index
		} as Item
	}
	
	async function updateDragPos(left: number, top: number, duration: number) {
		const rect = await $ux.Util.getBoundingClientRect(`#scroll-${myId}`, instance)
		const _scrollTop = rect.top
		const _scrollLeft = rect.left
		const _scrollHeight = rect.height
		const _scrollWidth = rect.width
		
		const offsetRect = await $ux.Util.getScrollOffset(`#scroll-${myId}`, instance)
		const offsetTop = offsetRect.scrollTop
		const offsetHeight = offsetRect.scrollHeight
		
		dragDuration.value = duration
		
		// 起始位置从下往上发生滚动时 修正top值
		if(!toBottom) {
			if(offsetTop < offsetHeight - _scrollHeight) {
				top -= startTop
			}
		}
		
		// y轴位置 = scroll-view 距顶位置 + 拖拽位移 - scroll-view 滚动距离
		dragPos.value.y = _scrollTop + top - offsetTop
		
		// 起始位置从上往下发生滚动时，无需减 offsetTop
		if(toBottom) {
			if(_scrollHeight + offsetTop == offsetHeight) {
				dragPos.value.y = _scrollTop + top
			}
		}
		
		// 顶部边界限定
		dragPos.value.y = Math.max(dragPos.value.y, _scrollTop)
		
		// 底部边界限定
		dragPos.value.y = Math.min(dragPos.value.y, _scrollTop + _scrollHeight - cellHeight.value)
		
		// 发生滚动时边界限定
		if(scrollState == 'up') {
			dragPos.value.y = _scrollTop
		} else if(scrollState == 'down') {
			dragPos.value.y = _scrollTop + _scrollHeight - cellHeight.value
		}
		
		// x轴位置
		if(props.col > 1) {
			dragPos.value.x = _scrollLeft + left
			
			dragPos.value.x = Math.max(dragPos.value.x, _scrollLeft)
			dragPos.value.x = Math.min(dragPos.value.x, _scrollWidth - cellWidth.value / 2)
		}
	}
	
	function copyDatas(arr: Data[]) {
		return JSON.parse(JSON.stringify(arr)) as Data[]
	}
	
	function resetIndex() {
		for (let i = 0; i < datas.value.length; i++) {
			let index = tempDatas.findIndex((e): boolean => datas.value[i].id == e.id)
			if(index != -1) {
				datas.value[i].index = tempDatas[index].index
			}
		}
	}
	
	function scroll(e : ScrollEvent) {
		scrollTop.value = e.detail.scrollTop
	}
	
	async function scrollTo(y: number) {
		if(props.col != 1) {
			return
		}
		
		const rect = await $ux.Util.getBoundingClientRect(`#scroll-${myId}`, instance)
		const _scrollHeight = rect.height
		const _scrollTop = y - rect.top
		
		scrollState = 'stop'
		if(_scrollTop < cellHeight.value) {
			// 靠顶部边缘
			scrollState = 'up'
			if(scrollTop.value < 0) {
				scrollTop.value = 0
				scrollState = 'stop'
				clearInterval(scrollTimer)
			}
		} else if(_scrollTop > _scrollHeight - cellHeight.value) {
			// 靠底部边缘
			scrollState = 'down'
			if(scrollTop.value > totalHeight.value) {
				scrollTop.value = totalHeight.value
				scrollState = 'stop'
				clearInterval(scrollTimer)
			}
		}
		
		if(scrollState != 'stop') {
			scrollTimer = setInterval(() => {
				if(scrollState == 'up') {
					// 向上滚动
					scrollTop.value -= props.speed
					if(scrollTop.value <= 0) {
						scrollTop.value = 0
						clearInterval(scrollTimer)
					}
				}
				if(scrollState == 'down') {
					// 向下滚动
					scrollTop.value += props.speed
					if(scrollTop.value >= totalHeight.value) {
						scrollTop.value = totalHeight.value
						clearInterval(scrollTimer)
					}
				}
			}, 16.6)
		}
	}
	
	async function _touchstart(e : Pos) {
		if(props.disabled) {
			return
		}
		
		const rect = await $ux.Util.getBoundingClientRect(`#scroll-${myId}`, instance)
		const _scrollTop = rect.top
		const _scrollLeft = rect.left
		const _scrollHeight = rect.height
		
		const offsetRect = await $ux.Util.getScrollOffset(`#scroll-${myId}`, instance)
		const offsetTop = offsetRect.scrollTop
		const offsetHeihgt = offsetRect.scrollHeight
		
		// 拖拽项
		const pos = await calcPos(e.x, e.y)
		
		activeIndex = pos.index
		targetIndex = activeIndex
		
		dragItem = datas.value.filter((e: Data): boolean => e.index == activeIndex)[0]
		
		let f = () => {
			// 起始位置
			let top = parseFloat(dragItem?.node?.node.$el!.style.getPropertyValue('top') as string);
			let left = parseFloat(dragItem?.node?.node.$el!.style.getPropertyValue('left') as string);
			
			x.value = e.x - left
			y.value = e.y - top
			
			dragPos.value.x = _scrollLeft + left
			dragPos.value.y = _scrollTop + top - offsetTop
			
			// 起始位置往下滚动
			toBottom = offsetTop < offsetHeihgt - _scrollHeight
			startTop = offsetTop
			
			// 拖拽id
			dragId.value = dragItem!.id
			
			emit('start')
		}
		
		isDrag.value = false
		touchTimer = setTimeout(() => {
			f()
			
			dragAlpha.value = 1
			isDrag.value = true
		}, props.longtouch ? props.touchtime : 150)
	}
	
	async function _touchmove(e : Pos) {
		if(props.disabled) {
			return
		}
		
		if (dragItem == null) return
		
		let top = e.y - y.value
		let left = e.x - x.value
		
		// 拖拽项位置
		await updateDragPos(left, top, 0)
		
		// 目标下标
		const pos = await calcPos(e.x, e.y)
		if (pos.index == activeIndex) return
		
		targetIndex = pos.index
		
		// 拷贝数组 不影响原数据
		let temps = copyDatas(tempDatas)
		
		// 交换位置
		let target = temps[targetIndex]
		let active = temps[activeIndex]
		temps.splice(activeIndex, 1, target)
		temps.splice(targetIndex, 1, active)
		
		activeIndex = targetIndex
		
		// 修改子组件下标
		for (let i = 0; i < temps.length; i++) {
			temps[i].index = i
		}
		
		// 重新排序
		temps.sort((a, b): number => a.index - b.index)
		
		// 保存上一次临时数据
		tempDatas = copyDatas(temps)
		
		// 改变原数组的下标 为了下次重新排序
		resetIndex()
		
		// 滚动位置
		await scrollTo(e.y)
	}
	
	async function _touchend(e : Pos) {
		if(props.disabled) {
			return
		}
		
		if (dragItem == null) return
		
		clearInterval(scrollTimer)
		
		dragAlpha.value = 0
		setTimeout(() => {
			isDrag.value = false
		}, 200)
		
		// 最后位置
		const pos = await calcPos(e.x, e.y)
		
		let top = cellHeight.value * pos.row
		let left = cellWidth.value * pos.col
		
		// 拖拽项位置
		await updateDragPos(left, top, 400)
		
		// 拷贝数组 不影响原数据
		let temps = copyDatas(tempDatas)
		
		// 重新排序
		temps.sort((a, b): number => a.index - b.index)
		
		// 修改子组件下标
		for (let i = 0; i < temps.length; i++) {
			temps[i].index = i
		}
		
		// 改变原数组的下标 为了下次重新排序
		resetIndex()
		
		// 停止拖拽
		dragId.value = ''
		
		// 结果排序
		end()
		
		activeIndex = -1
		targetIndex = -1
	}
	
	async function touchstart(e : TouchEvent) {
		await _touchstart({ x: e.changedTouches[0].clientX, y: e.changedTouches[0].clientY } as Pos)
	}
	
	async function touchstartPc(e : MouseEvent) {
		await _touchstart({ x: e.clientX, y: e.clientY } as Pos)
	}
	
	async function touchmove(e : TouchEvent) {
		e.stopPropagation()
		e.preventDefault()
		
		if (!isDrag.value || activeIndex == -1) return
		
		await _touchmove({ x: e.changedTouches[0].clientX, y: e.changedTouches[0].clientY } as Pos)
	}
	
	async function touchmovePc(e : MouseEvent) {
		e.stopPropagation()
		e.preventDefault()
		
		if (!isDrag.value || activeIndex == -1) return;
		
		await _touchmove({ x: e.clientX, y: e.clientY } as Pos)
	}
	
	async function touchend(e : TouchEvent) {
		clearTimeout(touchTimer)
		
		await _touchend({ x: e.changedTouches[0].clientX, y: e.changedTouches[0].clientY } as Pos)
	}
	
	async function touchendPc(e :MouseEvent) {
		clearTimeout(touchTimer)
		
		await _touchend({ x: e.clientX, y: e.clientY } as Pos)
	}
	
	function click(item: UTSJSONObject) {
		emit('click', item)
	}
	
	function register(node : UxDragInfo) {
		let index = datas.value.findIndex((e : Data) : boolean => e.id == node.id)
		if (index != -1) {
			datas.value[index].node = node
			datas.value[index].index = index
		}
		
		cellHeight.value = node.height
		cellWidth.value = node.width
	}
	
	function unregister(id : string) {
		let index = datas.value.findIndex((e : Data) : boolean => e.id == id)
		if (index > -1) {
			datas.value.splice(index, 1)
		}
	}
	
	function update() {
		let isUpt = false
		
		// 新增，不支持插入
		for (let i = 0; i < props.list.length; i++) {
			let newItem = props.list[i]
			let id = newItem[props.name] as string
			
			let b = datas.value.findIndex(e => e.id == id) != -1
			if(!b) {
				isUpt = true
				datas.value.push({
					id: id,
					index: datas.value.length,
					data: newItem
				} as Data)
			}
			
			b = tempDatas.findIndex(e => e.id == id) != -1
			if(!b) {
				tempDatas.push({
					id: id,
					index: tempDatas.length,
					data: newItem
				} as Data)
			}
		}
		
		// 删除
		for (let i = datas.value.length - 1; i >= 0; i--) {
			let id = datas.value[i].id
			let b = props.list.findIndex(e => id == e[props.name]) != -1
			if(!b) {
				isUpt = true
				datas.value.splice(i, 1)
			}
		}
		
		for (let i = tempDatas.length - 1; i >= 0; i--) {
			let id = tempDatas[i].id
			let b = props.list.findIndex(e => id == e[props.name]) != -1
			if(!b) {
				tempDatas.splice(i, 1)
			}
		}
		
		// 排序
		for (let i = 0; i < props.list.length; i++) {
			let id = props.list[i][props.name] as string
			
			let index = tempDatas.findIndex((e): boolean => id == e.id)
			if(index != -1) {
				tempDatas[index].index = i
			}
		}
		
		// 重排
		tempDatas.sort((a, b): number => a.index - b.index)
		for (let i = 0; i < tempDatas.length; i++) {
			tempDatas[i].index = i
		}
		resetIndex()
		
		if(isUpt) {
			end()
		}
	}
	
	watch(list, () => {
		update()
	}, {deep: true})
	
	onMounted(() => {
		datas.value = []
		for (let i = 0; i < props.list.length; i++) {
			datas.value.push({
				id: props.list[i][props.name] as string,
				index: i,
				data: props.list[i]
			} as Data)
		}
		
		tempDatas = copyDatas(datas.value)
	})
	
	defineExpose({
		register,
		unregister
	})
</script>

<style lang="scss">
	.ux-drag {
		width: 100%;
		overflow: hidden;
		
		&__body {
			width: 100%;
			position: relative;
		}
		
		&__item {
			opacity: 0;
			position: fixed;
			transition-timing-function: ease;
			transition-property: top, left, opacity;
			box-shadow: 0 0 10px #e9e9e9;
			/* #ifdef WEB */
			cursor: grab;
			/* #endif */
		}
	}
</style>