<template>
	<!-- #ifdef APP -->
	<flow-item @click="click">
		<slot></slot>
	</flow-item>
	<!-- #endif -->
	<!-- #ifndef APP -->
	<view :id="myId" class="ux-waterfall-item" :style="style" @click="click">
		<slot></slot>
	</view>
	<!-- #endif -->
</template>

<script setup lang="ts">
	
	/**
	 * WaterFall 子项
	 * @demo pages/component/waterfall.uvue
	 * @tutorial https://www.uxframe.cn/component/waterfall.html
	 * @property {Number}			type  			Number | 对应flow-item的类型 waterflow 将对同类型条目进行复用，所以合理的类型拆分，可以很好地提升 waterflow 性能
	 * @property {Number}			index			Number | 下标
	 * @property {Any}				height			Any | 高度
	 * @event {Function}			click			Function | 点击时触发
	 * <AUTHOR>
	 * @date 2024-05-26 19:21:24
	 */
	
	import { Ref, computed, getCurrentInstance, inject, onBeforeUnmount, onMounted, ref, watch } from 'vue'
	import { $ux } from '../../index'
	import { UxWaterfallItemInfo } from '../../libs/types/types.uts'
	
	defineOptions({
		name: 'ux-waterfall-item'
	})
	
	const emit = defineEmits(['click'])
	
	const props = defineProps({
		type: {
			type: Number,
			default: 0
		},
		index: {
			type: Number,
			default: 0
		},
		height: {
			default: 0
		}
	})
	
	function click(e: MouseEvent){
		emit('click', e)
	}
	
	// #ifndef APP
	const instance = getCurrentInstance()?.proxy!
	const myId = `ux-waterfall-item-${$ux.Random.uuid()}`
	const left = ref(0)
	const top = ref(0)
	const width = ref(0)
	const height = ref(0)
	const opacity = ref(0)
	
	const parentWidth = inject('width', ref(0)) as Ref<number>
	const crossAxisCount = inject('crossAxisCount', ref(2)) as Ref<number>
	const mainAxisGap = inject('mainAxisGap', ref(0)) as Ref<number>
	const crossAxisGap = inject('crossAxisGap', ref(0)) as Ref<number>
	
	const style = computed(() => {
		let css = {}
		
		css['opacity'] = opacity.value
		css['top'] = `${top.value}px`
		css['left'] = `${left.value}px`
		
		if(width.value > 0) {
			css['width'] = `${width.value}px`
		}
		
		if(height.value > 0) {
			// css['height'] = `${$ux.Util.addUnit(height.value)}`
		}
		
		return css
	})
	
	function updateData(_left: number, _top: number) {
		left.value = _left
		top.value = _top
		
		setTimeout(function() {
			opacity.value = 1
		}, 400);
	}
	
	async function register() {
		width.value = (parentWidth.value - crossAxisGap.value * (crossAxisCount.value + 1)) / crossAxisCount.value
		
		if($ux.Util.getPx(props.height) > 0) {
			height.value = $ux.Util.getPx(props.height)
		} else {
			let rect = await $ux.Util.getBoundingClientRect(`#${myId}`, instance)
			height.value = rect?.height ?? 0
		}
		
		const item: UxWaterfallItemInfo = {
			id: myId,
			top: top.value,
			left: left.value,
			width: width.value,
			height: height.value,
			node: instance as UxWaterfallItemComponentPublicInstance
		}
		
		$ux.Util.$dispatch(instance, 'ux-waterfall', 'register', item)
	}
	
	function unregister() {
		$ux.Util.$dispatch(instance, 'ux-waterfall', 'unregister', myId)
	}
	
	async function calc() {
		await register()
		
		setTimeout(() => {
			register()
		}, 200)
	}
	
	watch(crossAxisCount, () => {
		calc()
	})
	
	watch(mainAxisGap, () => {
		calc()
	})
	
	watch(crossAxisGap, () => {
		calc()
	})
	
	watch(parentWidth, () => {
		calc()
	})
	
	onMounted(() => {
		setTimeout(function() {
			calc()
		}, 200);
	})
	
	onBeforeUnmount(() => {
		unregister();
	})
	
	defineExpose({
		updateData
	})
	// #endif
</script>


<style lang="scss">
	.ux-waterfall-item {
		position: absolute;
		opacity: 0;
		transition-property: top, left, width, height;
		transition-duration: 200ms;
		
		&__img {
			position: absolute;
			width: 100%;
			height: 100%;
			opacity: 0;
		}
	}
</style>