import { OpenDatabase, OpenDatabaseOptions } from '../interface.uts'
import { CloseDatabase } from '../interface.uts'
import { DeleteDatabase, DeleteDatabaseOptions } from '../interface.uts'
import { ExecSQL, ExecSQLOptions } from '../interface.uts'
import { ExecQuery, ExecQueryOptions } from '../interface.uts'
import { ExecRawQuery, ExecRawQueryOptions } from '../interface.uts'
import { ExecInsert, ExecInsertOptions } from '../interface.uts'
import { ExecUpdate, ExecUpdateOptions } from '../interface.uts'
import { ExecDelete, ExecDeleteOptions } from '../interface.uts'
import { ExecTransaction, ExecTransactionOptions } from '../interface.uts'
import { ExecCount, ExecCountOptions } from '../interface.uts'
import { GetColumns, GetColumnsOptions } from '../interface.uts'
import { GetPath } from '../interface.uts'
import { IsOpen } from '../interface.uts'
import { UxSqlSuccess } from '../interface.uts'
import { UxSqlFailImpl } from '../unierror.uts'

import Context from 'android.content.Context';
import Locale from 'java.util.Locale';
import SQLiteOpenHelper from "android.database.sqlite.SQLiteOpenHelper";
import SQLiteDatabase from "android.database.sqlite.SQLiteDatabase";
import ContentValues from 'android.content.ContentValues';
import Cursor from 'android.database.Cursor';

let _orm: DBHelper | null = null 
let _database: string = ''
let _log: boolean = false

export class DBHelper extends SQLiteOpenHelper {

	db ?: SQLiteDatabase = null

	constructor(context : Context) {
		super(context, _database, null, 1);
	}

	override onCreate(db : SQLiteDatabase) {
		this.db = db
		this.db?.setLocale(Locale.CHINA)
	}

	override onUpgrade(db : SQLiteDatabase, oldVersion : Int, newVersion : Int) {
		if (newVersion > oldVersion) {
			
		}
	}
}

/**
 * 打开数据库
 * @param { OpenDatabaseOptions } options 参数
 */
export const openDatabase : OpenDatabase = function (options : OpenDatabaseOptions) {
	try {
		closeDatabase()
		
		_database = `${options.database ?? 'app'}.db`
		_log = options.log ?? true
		
		_orm = new DBHelper(UTSAndroid.getAppContext()!)
		_orm?.getWritableDatabase()?.rawQuery('SELECT name FROM sqlite_master', null)
		
		options.success?.({
			code: 0,
			count: 1,
			datas: []
		} as UxSqlSuccess)
	} catch (error) {
		options.fail?.(new UxSqlFailImpl(-1))
	}
	
	options.complete?.()
}

/**
 * 关闭数据库
 */
export const closeDatabase : CloseDatabase = function (): boolean {
	if(_orm != null) {
		_orm?.close()
		_orm = null
		return true
	}
	
	return false
}

/**
 * 删除数据库
 */
export const deleteDatabase : DeleteDatabase = function (options: DeleteDatabaseOptions): boolean {
	closeDatabase()
	
	return UTSAndroid.getAppContext()?.deleteDatabase(options.database ?? 'app.db') ?? false
}

/**
 * 执行SQL
 */
export const execSQL : ExecSQL = function (options: ExecSQLOptions): boolean {
	_orm?.getWritableDatabase().execSQL(options.sql)
	return true
}

/**
 * 查询
 */
export const query : ExecQuery = function (options: ExecQueryOptions): Array<Map<string, any | null>> {
	let cursor = _orm?.getReadableDatabase().query(options.distinct ?? false, options.table ?? '', options.selects?.toTypedArray(), options.whereSql, null, options.groupBy, options.having, options.orderBy, options.limit)
	return wrapDatas(cursor, options.columns)
}

/**
 * SQL查询
 */
export const rawQuery : ExecRawQuery = function (options: ExecRawQueryOptions): Array<Map<string, any | null>> {
	let cursor = _orm?.getReadableDatabase().rawQuery(options.sql, null)
	return wrapDatas(cursor, options.columns)
}

/**
 * 插入SQL
 */
export const insert : ExecInsert = function (options: ExecInsertOptions): number {
	return _orm?.getWritableDatabase().insertOrThrow(options.table, null, buildContentValues(options.content)) as number
}

/**
 * 更新SQL
 */
export const update : ExecUpdate = function (options: ExecUpdateOptions): number {
	return _orm?.getWritableDatabase().update(options.table, buildContentValues(options.content), options.whereSql, null) as number
}

/**
 * 删除SQL
 */
export const del : ExecDelete = function (options: ExecDeleteOptions): number {
	return _orm?.getWritableDatabase().delete(options.table, options.whereSql, null) as number
}

/**
 * 执行事务
 */
export const transaction : ExecTransaction = function (options: ExecTransactionOptions): boolean {
	let db = _orm?.getWritableDatabase()
	
	try {
		db?.beginTransaction()
	
		let success = options.operations()
		if(success) {
			db?.setTransactionSuccessful()
		} else {
			db?.endTransaction()
			return false
		}
	} catch (err) {
		db?.endTransaction()
		console.error('[ux-orm] transaction', err);
		return false
	}
	
	db?.endTransaction()
	return true
}

/**
 * 行数
 */
export const count : ExecCount = function (options: ExecCountOptions): number {
	let count = 0
	
	try {
		let cursor = _orm?.getReadableDatabase().query(options.table, ['COUNT(*)'].toTypedArray(), options.whereSql, null, null, null, null);
		if(cursor != null) {
			if (cursor.moveToFirst()) {
			    count = cursor.getInt(0)
			}
			cursor.close()
		}
	} catch (err ) {
		throw new(err)
	}
	
	return count
}

/**
 * 获取表字段
 */
export const getColumns : GetColumns = function (options: GetColumnsOptions): string[] {
	let columns: string[] = []
	
	let cursor = _orm?.getReadableDatabase()?.rawQuery("PRAGMA table_info(" + options.table + ")", null)
	if(cursor != null) {
		if (cursor.moveToFirst()) {
			do {
		        let columnName = cursor.getString(cursor.getColumnIndex("name"));
				columns.add(columnName)
			} while (cursor.moveToNext());
		}
	}
	
	return columns
}

/**
 * 获取数据库路径
 */
export const getPath : GetPath = function (): string {
	return _orm?.getReadableDatabase().getPath() ?? ''
}

/**
 * 是否打开数据库
 */
export const isOpen : IsOpen = function (): boolean {
	return _orm?.getReadableDatabase().isOpen() ?? false
}

/**
 * 包装数据集
 */
function wrapDatas(cursor ?: Cursor, columns ?: UTSJSONObject) : Array<Map<string, any | null>> {
	let datas = new Array<Map<string, any | null>>()
	
	if (cursor != null) {
		if(columns == null) {
			if (cursor.moveToFirst()) {
				do {
					try {
						let row = new Map<string, any | null>()
						
						let keys = cursor.getColumnNames()
						for (let i = 0; i < keys.size; i++) {
							let key = keys[i as Int]
							row.set(key, wrapTypeData(cursor, key))
						}
						
						datas.add(row)
					} catch (err) {
						throw new(err)
					}
				} while (cursor.moveToNext())
			}
		} else {
			if (cursor.moveToFirst()) {
				let _keys = UTSJSONObject.keys(columns)
				do {
					try {
						let row = new Map<string, any | null>()
						
						let keys = cursor.getColumnNames()
						for (let i = 0; i < keys.size; i++) {
							let key = keys[i as Int]
						
							for (let y = 0; y < _keys.length; y++) {
								let k = _keys[y]
								let col = columns.getJSON(k)!
								if (key.equals(col.getString('name'))) {
									row.set(k, wrapColumnData(cursor, col))
									break
								}
							}
						}
						
						datas.add(row)
					} catch (err) {
						throw new(err)
					}
				} while (cursor.moveToNext())
			}
		}
		
		cursor.close()
	}
	return datas
}

/**
 * 构建内容
 */
function buildContentValues(content ?: UTSJSONObject) {
	let values : ContentValues = new ContentValues()
	
	if(content == null) {
		return values
	}
	
	let keys = UTSJSONObject.keys(content)
	for (let i = 0; i < keys.length; i++) {
		let key = keys[i]
		let value = content[key]!
		
		if (value instanceof Date) {
			values.put(key, toDate(value))
		} else if (value instanceof Int) {
			values.put(key, value as Int)
		} else if (value instanceof Long) {
			values.put(key, value as Long)
		} else if (value instanceof Short) {
			values.put(key, value as Short)
		} else if (value instanceof Float) {
			values.put(key, value as Float)
		} else if (value instanceof Double) {
			values.put(key, value as Double)
		} else if (value instanceof ByteArray) {
			values.put(key, value as ByteArray)
		} else if (value instanceof UTSJSONObject) {
			values.put(key, JSON.stringify(value) as String)
		} else if (typeof value == 'boolean') {
			values.put(key, value as Boolean)
		} else if (typeof value == 'string') {
			values.put(key, value as String)
		} else if (typeof value == 'object') {
			values.put(key, JSON.stringify(value) as String)
		} else if (typeof value == 'number') {
			values.put(key, value as Int)
		}
	}
	
	return values
}

/**
 * 包装column数据
 */
function wrapColumnData(cursor : Cursor, column : UTSJSONObject) : any | null {
	let type = column.getString('type') ?? ''
	let value = cursor.getColumnIndex(column.getString('name') ?? '')
	
	if (type == 'string') {
		return cursor.getString(value)
	} else if (type == 'boolean') {
		return cursor.getInt(value) == 1
	} else if (type == 'int') {
		return cursor.getInt(value)
	} else if (type == 'double') {
		return cursor.getDouble(value)
	} else if (type == 'float') {
		return cursor.getFloat(value)
	} else if (type == 'long') {
		return cursor.getLong(value)
	} else if (type == 'short') {
		return cursor.getShort(value)
	} else if (type == 'blob') {
		return cursor.getBlob(value)
	} else if (type == 'timestamp') {
		return cursor.getLong(value)
	} else if (type == 'datetime') {
		let val = cursor.getString(value)
		return val == null ? null : fromDate(val)
	} else if (type == 'json') {
		let val = cursor.getString(value)
		return val == null ? null : JSON.parseObject(val)
	}
	return null
}

/**
 * 包装column数据
 */
function wrapTypeData(cursor : Cursor, key : string) : any | null {
	let index = cursor.getColumnIndex(key)
	let type = cursor.getType(index)

	if (type == Cursor.FIELD_TYPE_BLOB) {
		return cursor.getBlob(index)
	} else if (type == Cursor.FIELD_TYPE_FLOAT) {
		return cursor.getFloat(index)
	} else if (type == Cursor.FIELD_TYPE_INTEGER) {
		return cursor.getLong(index)
	} else if (type == Cursor.FIELD_TYPE_STRING) {
		return cursor.getString(index)
	}
	
	return null
}

/**
 * date from string
 */
function fromDate (date: string): Date {
	let d = date.split(/[^0-9]/)
	
	let year = d.length < 1 ? 0 : parseInt(d[0])
	let month = d.length < 2 ? 0 : parseInt(d[1]) - 1
	let day = d.length < 3 ? 0 : parseInt(d[2])
	let hour = d.length < 4 ? 0 : parseInt(d[3])
	let minute = d.length < 5 ? 0 : parseInt(d[4])
	let second = d.length < 6 ? 0 : parseInt(d[5])
	
	return new Date(year, month, day, hour, minute, second)
}

/**
 * date to string
 */
function toDate (date: Date): string {
	let padding = (n: number): string => {
		return `${n}`.padStart(2, '0')
	}
	
	return `${date.getFullYear()}-${padding(date.getMonth() + 1)}-${padding(date.getDate())} ${padding(date.getHours())}:${padding(date.getMinutes())}:${padding(date.getSeconds())}`
}