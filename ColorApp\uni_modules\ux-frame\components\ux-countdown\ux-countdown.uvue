<template>
	<slot :times="formatTime">
		<text class="ux-countdown" :style="[style, xstyle]">
			{{ value }}
		</text>
	</slot>
</template>

<script setup>
	
	/**
	 * CountDown 倒计时
	 * @description 支持时分秒倒计时，可配置时间格式、自定义样式、毫秒倒计时，支持反向正计时
	 * @demo pages/component/countdown.uvue
	 * @tutorial https://www.uxframe.cn/component/countdown.html
	 * @property {String}			theme=[text|info|primary|success|warning|error]		String | 主题 (默认 info)
	 * @value text			文字
	 * @value info   		默认
	 * @value primary   	主要
	 * @value success  		成功
	 * @value warning   	警告
	 * @value error  		错误
	 * @property {Boolean}			reverse							Boolean | 反向 正计时 (默认 false)
	 * @property {Number}			time							Number | 倒计时时长，单位ms (默认 0)
	 * @property {String}			format							String | 时间格式，DD-日，HH-时，mm-分，ss-秒，SSS-毫秒 (默认 HH:mm:ss )
	 * @property {Boolean}			autoplay						Boolean | 设置数值后是否自动开始滚动  (默认 true)
	 * @property {Boolean}			millisecond						Boolean | 是否展示毫秒倒计时  (默认 false)
	 * @property {String}			color							String | 字体颜色 ( 默认 $ux.Conf.fontColor )
	 * @property {String} 			darkColor=[none|auto|color]		String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Any}				size							Any | 字体大小 ( 默认 $ux.Conf.fontSize)
	 * @property {Boolean}			bold							Boolean | 字体是否加粗 (默认 false)
	 * @property {Array}			margin							Array | 边距 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}				mt								Any | 距上 单位px
	 * @property {Any}				mr								Any | 距右 单位px
	 * @property {Any}				mb								Any | 距下 单位px
	 * @property {Any}				ml								Any | 距左 单位px
	 * @property {Array}			padding							Array | 填充 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}				pt								Any | 上内边距 单位px
	 * @property {Any}				pr								Any | 右内边距 单位px
	 * @property {Any}				pb								Any | 下内边距 单位px
	 * @property {Any}				pl								Any | 左内边距 单位px
	 * @property {Array}			xstyle							Array<any> | 自定义样式
	 * @event {Function} 			finish 							Function | 倒计时结束时触发
	 * @event {Function} 			change 							Function | 倒计时变化时触发
	 * <AUTHOR>
	 * @date 2024-01-12 01:02:28
	 */
	
	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	import {UxCountDownTime} from '../../libs/types/types.uts'
	import { useThemeColor, useFontColor, useFontSize, UxThemeType } from '../../libs/use/style.uts'
	
	defineOptions({
		name: 'ux-countdown',
		mixins: [xstyleMixin]
	})
	
	defineSlots<{
		default(props : { times : UxCountDownTime }) : any
	}>()
	
	const emit = defineEmits(['change', 'finish'])
	
	const props = defineProps({
		theme: {
			type: String,
			default: 'info'
		},
		reverse: {
			type: Boolean,
			default: false
		},
		time: {
			type: Number,
			default: 0
		},
		format: {
			type: String,
			default: 'HH:mm:ss'
		},
		millisecond: {
			type: Boolean,
			default: false
		},
		autoplay: {
			type: Boolean,
			default: true
		},
		size: {
			default: 14
		},
		color: {
			type: String,
			default: ''
		},
		darkColor: {
			type: String,
			default: ''
		},
		bold: {
			type: Boolean,
			default: false
		},
	})
	
	const formatTime = ref<UxCountDownTime>({
		days: 0,
		hours: 0,
		minutes: 0,
		seconds: 0,
		milliseconds: 0
	} as UxCountDownTime)
	
	const value = ref('0')
	const timer = ref(0)
	const runing = ref(false)
	const endTime = ref(0)
	const remainTime = ref(0)
	
	const fontSize = computed(():number => {
		return useFontSize($ux.Util.getPx(props.size), 0)
	})
	
	const fontColor = computed(():string => {
		if(props.color != '') {
			return useFontColor(props.color, props.darkColor)
		} else {
			return useThemeColor(props.theme as UxThemeType)
		}
	})
	
	const style = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('color', fontColor.value)
		css.set('font-size', $ux.Util.addUnit(fontSize.value))
		css.set('font-weight', props.bold ? 'bold' : 'normal')
		
		css = $ux.Util.xStyle(css, props.margin, props.mt, props.mr, props.mb, props.ml, props.padding, props.pt, props.pr, props.pb, props.pl)
		
		return css
	})
	
	function cleartimeout() {
		clearTimeout(timer.value)
		timer.value = 0
	}
	
	function isSameSecond(time1 : number, time2 : number) : boolean {
		return Math.floor(time1 / 1000) == Math.floor(time2 / 1000)
	}
	
	function parseTimeData(time : number) : UxCountDownTime {
		const SECOND = 1000
		const MINUTE = 60 * SECOND
		const HOUR = 60 * MINUTE
		const DAY = 24 * HOUR
		
		if(props.reverse) {
			time = props.time - time
		}
		
		const days = Math.floor(time / DAY)
		const hours = Math.floor((time % DAY) / HOUR)
		const minutes = Math.floor((time % HOUR) / MINUTE)
		const seconds = Math.floor((time % MINUTE) / SECOND)
		const milliseconds = Math.floor(time % SECOND)
		
		return {
			days,
			hours,
			minutes,
			seconds,
			milliseconds
		} as UxCountDownTime
	}
	
	function parseFormat(format : string, timeData : UxCountDownTime) : string {
		let {
			days,
			hours,
			minutes,
			seconds,
			milliseconds
		} = timeData
		
		// 如果格式化字符串中不存在DD(天)，则将天的时间转为小时中去
		if (format.indexOf('DD') == -1) {
			hours += days * 24
		} else {
			// 对天补0
			format = format.replace('DD', `${days}`.padStart(2, '0'))
		}
		
		// 其他同理于DD的格式化处理方式
		if (format.indexOf('HH') == -1) {
			minutes += hours * 60
		} else {
			format = format.replace('HH', `${hours}`.padStart(2, '0'))
		}
		
		if (format.indexOf('mm') == -1) {
			seconds += minutes * 60
		} else {
			format = format.replace('mm', `${minutes}`.padStart(2, '0'))
		}
		
		if (format.indexOf('ss') == -1) {
			milliseconds += seconds * 1000
		} else {
			format = format.replace('ss', `${seconds}`.padStart(2, '0'))
		}
		
		return format.replace('SSS', `${milliseconds}`.padStart(3, '0'))
	}
	
	function pause() {
		runing.value = false;
		cleartimeout()
	}
	
	function getRemainTime() : number {
		return Math.max(endTime.value - Date.now(), 0)
	}
	
	function setRemainTime(remain : number) {
		remainTime.value = remain
		formatTime.value = parseTimeData(remain)
		value.value = parseFormat(props.format, formatTime.value)
		
		emit('change', formatTime.value)
		
		if (remain <= 0) {
			pause()
			emit('finish')
		}
	}
	
	function macroTick() {
		cleartimeout()
		
		timer.value = setTimeout(() => {
			const remain = getRemainTime()
		
			if (!isSameSecond(remain, remainTime.value) || remain == 0) {
				setRemainTime(remain)
			}
		
			if (remainTime.value != 0) {
				macroTick()
			}
		}, 30)
	}
	
	function microTick() {
		cleartimeout()
		
		timer.value = setTimeout(() => {
			setRemainTime(getRemainTime())
		
			if (remainTime.value != 0) {
				microTick()
			}
		}, 50)
	}
	
	function toTick() {
		if (props.millisecond) {
			microTick()
		} else {
			macroTick()
		}
	}
	
	function start() {
		if (runing.value) return
		runing.value = true
		
		endTime.value = Date.now() + remainTime.value
		
		toTick()
	}
	
	function reset() {
		pause()
		
		remainTime.value = props.time
		setRemainTime(remainTime.value)
		
		if (props.autoplay) {
			start()
		}
	}
	
	function init() {
		reset()
	}
	
	const time = computed(():number => {
		return props.time
	})
	
	watch(time, () => {
		init()
	})
	
	onMounted(() => {
		init()
	})
	
	onUnmounted(() => {
		cleartimeout()
	})
	
	defineExpose({
		start,
		pause,
		reset,
		getRemainTime
	})
</script>

<style lang="scss">
	.ux-countdown {}
</style>