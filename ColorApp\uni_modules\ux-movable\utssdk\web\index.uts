
export type UxMovableLayout = 'list' | 'grid'

export type UxMovableOptions = {
	layout : UxMovableLayout,
	column : number,
	spacing: number,
	aspectRatio : number,
	multiple: boolean,
	editable: boolean,
	swipe: boolean,
	disabled : boolean,
	change : (data : UxMovableItem[]) => void
}

export type UxMovableStyle = {
	/**
	 * 字体颜色
	 */
	color ?: string,
	/**
	 * 字体大小
	 */
	fontSize ?: number,
	/**
	 * 粗体
	 */
	fontWeight ?: boolean,
	/**
	 * 宽
	 */
	width ?: number,
	/**
	 * 高
	 */
	height ?: number,
	/**
	 * 圆角
	 */
	radius ?: number,
	/**
	 * 背景色
	 */
	backgroundColor ?: string
}

export type UxMovableBorderType = 'solid' | 'dashed'

export type UxMovableBorder = {
	/**
	 * 边框类型
	 */
	type : UxMovableBorderType
	/**
	 * 边框宽度
	 */
	width : number,
	/**
	 * 颜色
	 */
	color : string
}

export type UxMovableCheckbox = {
	/**
	 * 选中状态
	 */
	checked: boolean,
	/**
	 * 选中图标
	 */
	selectedUrl: string,
	/**
	 * 未选中图标
	 */
	unselectedUrl: string,
	/**
	 * 宽
	 */
	width : number,
	/**
	 * 高
	 */
	height : number,
	/**
	 * 间距
	 */
	margin ?: number,
}

export type UxMovableUpload = {
	/**
	 * 占位图
	 */
	url : string,
	/**
	 * 背景色
	 */
	backgroundColor ?: string
	/**
	 * 隐藏
	 */
	hide ?: boolean,
	/**
	 * 禁用
	 */
	disabled ?: boolean,
}

export type UxMovableInput = {
	/**
	 * 内容
	 */
	content: string,
	/**
	 * 占位内容
	 */
	placeholder ?: string,
	/**
	 * 最大长度
	 */
	maxLength ?: number,
	/**
	 * 输入事件
	 */
	input ?: (content: string) => void
}

export type UxMovableLoading = {
	/**
	 * 显示loading
	 */
	show : boolean,
	/**
	 * 进度百分比
	 */
	progress : number,
	/**
	 * 等待上传文案
	 */
	waitingLabel ?: string,
	/**
	 * loading 样式
	 */
	style ?: UxMovableStyle,
}

export type UxMovablePlaceholder = {
	/**
	 * 占位图
	 */
	url : string,
	/**
	 * 宽
	 */
	width : number,
	/**
	 * 高
	 */
	height : number,
	/**
	 * 圆角
	 */
	radius ?: number,
	/**
	 * 间距
	 */
	margin ?: number,
	/**
	 * 点击事件
	 */
	click ?: (data : UxMovableItem) => void
}

export type UxMovableItem = {
	/**
	 * 下标
	 */
	index: number,
	/**
	 * 唯一id
	 */
	id : number,
	/**
	 * 文本
	 */
	label ?: string,
	/**
	 * 文本样式
	 */
	labelStyle ?: UxMovableStyle,
	/**
	 * 副文本
	 * 仅 list 有效
	 */
	summary ?: string,
	/**
	 * 副文本样式
	 * 仅 list 有效
	 */
	summaryStyle ?: UxMovableStyle,
	/**
	 * 图片地址
	 * gird 背景图
	 * list 左侧图
	 */
	url ?: string,
	/**
	 * 缩略图尺寸
	 */
	thumbSize ?: number,
	/**
	 * 左上占位
	 * 仅 grid 有效
	 */
	leftTop ?: UxMovablePlaceholder,
	/**
	 * 右上占位
	 * 仅 grid 有效
	 */
	rightTop ?: UxMovablePlaceholder,
	/**
	 * 左下占位
	 * 仅 grid 有效
	 */
	leftBottom ?: UxMovablePlaceholder,
	/**
	 * 右下占位
	 * 仅 grid 有效
	 */
	rightBottom ?: UxMovablePlaceholder,
	/**
	 * 上传按钮
	 * 仅 grid 有效
	 */
	upload ?: UxMovableUpload,
	/**
	 * loading
	 * 仅 grid 有效
	 */
	loading ?: UxMovableLoading,
	/**
	 * item 高度
	 * 仅 list 有效
	 */
	height ?: number,
	/**
	 * 圆角
	 */
	radius ?: number,
	/**
	 * 边框
	 */
	border ?: UxMovableBorder
	/**
	 * 选中状态
	 * 仅 grid 多选有效
	 */
	selected ?: UxMovableCheckbox
	/**
	 * 显示左侧拖拽图标
	 * 仅 list 有效
	 */
	showDrag ?: boolean,
	/**
	 * 输入事件
	 */
	input ?: UxMovableInput
	/**
	 * 点击事件
	 */
	click ?: (data : UxMovableItem) => void
}

export class UxMovable {
	
	items: UxMovableItem[]
	options: UxMovableOptions
	
	constructor(options: UxMovableOptions) {
		this.items = []
		this.options = options
	}
	
	setData(data : UxMovableItem[]) {
		this.items = data
	}
	
	addData(data : UxMovableItem) {
		this.items.push(data)
	}
	
	delData(data : UxMovableItem) {
		const position = this.items.findIndex(e => e.id == data.id)
		if (position < 0) {
			return
		}
		
		this.items.splice(position, 1)
	}
	
	selectData(data: UxMovableItem) {
		const position = this.items.findIndex(e => e.id == data.id)
		if (position < 0) {
			return
		}
		
		this.items.splice(position, 1, data)
	}
	
	upload(data : UxMovableItem) {
		const position = this.items.findIndex(e => e.id == data.id)
		if (position < 0) {
			return
		}
		
		this.items.splice(position, 1, data)
	}
	
	setLayout(layout : UxMovableLayout) {
		this.options.layout = layout
	}

	setColumn(column : number) {
		this.options.column = column
	}
	
	setSpacing(spacing: number) {
		this.options.spacing = spacing
	}
	
	setAspectRatio(aspectRatio: number) {
		this.options.aspectRatio = aspectRatio
	}
	
	setEditable(editable : boolean) {
		this.options.editable = editable
	}
	
	setMultiple(multiple : boolean) {
		this.options.multiple = multiple
	}
	
	setSwipe(swipe : boolean) {
		this.options.swipe = swipe
	}

	setDisabled(disabled : boolean) {
		this.options.disabled = disabled
	}
}