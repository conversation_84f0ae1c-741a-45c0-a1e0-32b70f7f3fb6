<template>
	<view ref="uxSwipeactionRef" class="ux-swipeaction" :style="style"
		@touchstart="touchstart"
		@touchmove="touchmove"
		@touchend="touchend"
		@touchcancel="touchend" 
		@mousedown="touchstartPc"
		@mousemove="touchmovePc"
		@mouseup="touchendPc"
		@mouseleave="touchendPc">
		<view v-if="$slots['left'] != null" ref="uxSwipeactionLeftMenuRef" class="ux-swipeaction__menus--left">
			<slot name="left" :opened="opened"></slot>
		</view>
		<view ref="uxSwipeactionContentRef" class="ux-swipeaction__content">
			<slot></slot>
		</view>
		<view v-if="$slots['right'] != null" ref="uxSwipeactionRightMenuRef" class="ux-swipeaction__menus--right">
			<slot name="right" :opened="opened"></slot>
		</view>
	</view>
</template>

<script setup>
	
	/**
	 * Swipeaction 滑动操作栏
	 * @description 支持左侧/右侧滑动操作栏，支持独立和同时配置
	 * @demo pages/component/swipeaction.uvue
	 * @tutorial https://www.uxframe.cn/component/swipeaction.html
	 * @property {Slot}				left								Slot | 左侧按钮插槽
	 * @property {Slot}				right								Slot | 右侧按钮插槽
	 * @property {String} 			backgroundDark=[none|auto|color]	String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色 (默认 auto)
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Number}			threshold							Number | 临界值 (默认 30 )
	 * @property {Number}			duration							Number | 过度时间 (默认 200 )
	 * @property {Boolean}			disabled							Boolean | 禁用 (默认 false )
	 * <AUTHOR>
	 * @date 2024-06-09 07:39:25
	 */
	
	import { $ux } from '../../index'
	import { useForegroundColor } from '../../libs/use/style.uts'

	defineOptions({
		name: 'ux-swipeaction'
	})
	
	const emit = defineEmits(['open', 'close', 'click'])
	
	const props = defineProps({
		background: {
			type: String,
			default: '#ffffff'
		},
		backgroundDark: {
			type: String,
			default: 'auto'
		},
		threshold: {
			type: Number,
			default: 30
		},
		duration: {
			type: Number,
			default: 200
		},
		disabled: {
			type: Boolean,
			default: false
		},
	})
	
	const myId = `ux-swipeaction-${$ux.Random.uuid()}`
	const uxSwipeactionRef = ref<UniElement | null>(null)
	const uxSwipeactionContentRef = ref<UniElement | null>(null)
	const uxSwipeactionLeftMenuRef = ref<UniElement | null>(null)
	const uxSwipeactionRightMenuRef = ref<UniElement | null>(null)
	const opened = ref(false)
	const startX = ref(0)
	const status = ref('')
	const isOpen = ref(false)
	const isMove = ref(false)
	
	const backgroundColor = computed((): string => {
		return useForegroundColor(props.background, props.backgroundDark)
	})
	
	const style = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('background-color', backgroundColor.value)
		
		return css
	})

	async function _open() {
		if(status.value == 'right') {
			let arect = await uxSwipeactionRef.value?.getBoundingClientRectAsync()!
			let bodyWidth = arect.width
			
			let rect = await uxSwipeactionRightMenuRef.value?.getBoundingClientRectAsync()!
			let width = rect.width
			
			if(width == 0) {
				return
			}
			
			uxSwipeactionContentRef.value?.style.setProperty('transition-duration', `${props.duration}ms`)
			uxSwipeactionRightMenuRef.value?.style.setProperty('transition-duration', `${props.duration}ms`)
			
			nextTick(() => {
				uxSwipeactionContentRef.value?.style.setProperty('left', `${-width}px`)
				uxSwipeactionRightMenuRef.value?.style.setProperty('left', `${bodyWidth - width}px`)
			})
			
			emit('open')
		} else if(status.value == 'left') {
			let rect = await uxSwipeactionLeftMenuRef.value?.getBoundingClientRectAsync()!
			let width = rect.width
			
			if(width == 0) {
				return
			}
			
			uxSwipeactionContentRef.value?.style.setProperty('transition-duration', `${props.duration}ms`)
			uxSwipeactionLeftMenuRef.value?.style.setProperty('transition-duration', `${props.duration}ms`)
			
			nextTick(() => {
				uxSwipeactionContentRef.value?.style.setProperty('left', `${width}px`)
				uxSwipeactionLeftMenuRef.value?.style.setProperty('left', `${0}px`)
			})
			
			emit('open')
		}
		
		opened.value = true
		isOpen.value = true
	}
	
	async function _close() {
		uxSwipeactionContentRef.value?.style.setProperty('transition-duration', `${props.duration}ms`)
		
		if(status.value == 'right') {
			let arect = await uxSwipeactionRef.value?.getBoundingClientRectAsync()!
			let bodyWidth = arect.width
			
			let rect = await uxSwipeactionRightMenuRef.value?.getBoundingClientRectAsync()!
			let width = rect.width
			
			if(width != 0) {
				uxSwipeactionRightMenuRef.value?.style.setProperty('transition-duration', `${props.duration}ms`)
				
				nextTick(() => {
					uxSwipeactionContentRef.value?.style.setProperty('left', `${0}px`)
					uxSwipeactionRightMenuRef.value?.style.setProperty('left', `${bodyWidth + width}px`)
				})
				
				emit('close')
			}
		} else if(status.value == 'left') {
			let rect = await uxSwipeactionLeftMenuRef.value?.getBoundingClientRectAsync()!
			let width = rect.width
			
			if(width != 0) {
				uxSwipeactionLeftMenuRef.value?.style.setProperty('transition-duration', `${props.duration}ms`)
				
				nextTick(() => {
					uxSwipeactionContentRef.value?.style.setProperty('left', `${0}px`)
					uxSwipeactionLeftMenuRef.value?.style.setProperty('left', `${-width}px`)
				})
				
				emit('close')
			}
		}
		
		startX.value = 0
		opened.value = false
		isOpen.value = false
		status.value = ''
	}
	
	async function open(right: boolean) {
		if(right) {
			if(status.value == 'right') {
				return
			} else if(status.value == 'left') {
				await _close()
			}
			
			status.value = 'right'
		} else {
			if(status.value == 'right') {
				await _close()
			} else if(status.value == 'left') {
				return
			}
			
			status.value = 'left'
		}
		
		await _open()
	}
	
	async function close() {
		await _close()
	}
	
	async function _touchstart(x : number) {
		startX.value = x
		
		if (props.disabled) {
			return
		}
		
		if(status.value == '') {
			let rect = await uxSwipeactionContentRef.value?.getBoundingClientRectAsync()!
			if(x >= rect.width / 2) {
				status.value = 'right'
			} else {
				status.value = 'left'
			}
		}
		
		isMove.value = true
		
		uxSwipeactionContentRef.value?.style?.setProperty('transition-duration', '0ms')
		uxSwipeactionLeftMenuRef.value?.style?.setProperty('transition-duration', '0ms')
		uxSwipeactionRightMenuRef.value?.style?.setProperty('transition-duration', '0ms')
	}
	
	async function _touchmove(x : number) {
		if (props.disabled) {
			return
		}
		
		if(!isMove.value) {
			return
		}
		
		if(status.value == 'right') {
			if(uxSwipeactionRightMenuRef.value == null) {
				status.value = 'left'
				return
			}
			
			let arect = await uxSwipeactionRef.value?.getBoundingClientRectAsync()!
			let bodyWidth = arect.width
			
			let rect = await uxSwipeactionRightMenuRef.value?.getBoundingClientRectAsync()!
			let width = rect.width
			
			if(width == 0) {
				return
			}
			
			let left = x - startX.value
			
			if(opened.value) {
				left = -(width - left)
			}
			
			if (left > 0) {
				left = 0
			} else if (left < -width) {
				left = -width
			}
				
			left = Math.min(0, left)
			left = Math.max(-width, left)
			
			if(opened.value) {
				if(Math.abs(left) > width - props.threshold) {
					isOpen.value = true
				} else {
					isOpen.value = false
				}
				
			} else {
				if(Math.abs(left) < props.threshold) {
					isOpen.value = false
				} else {
					isOpen.value = true
				}
			}
			
			uxSwipeactionContentRef.value?.style?.setProperty('left', `${left}px`)
			uxSwipeactionRightMenuRef.value?.style?.setProperty('left', `${bodyWidth + left}px`)
		} else if(status.value == 'left') {
			if(uxSwipeactionLeftMenuRef.value == null) {
				status.value = 'right'
				return
			}
			
			let rect = await uxSwipeactionLeftMenuRef.value?.getBoundingClientRectAsync()!
			let width = rect.width
			
			if(width == 0) {
				return
			}
			
			let left: number
			if(opened.value) {
				left = x - startX.value
			} else {
				left = -width + (x - startX.value)
			}
			
			if (left < -width) {
				left = -width
			} else if (left > 0) {
				left = 0
			}
			
			left = Math.min(0, left)
			left = Math.max(-width, left)
			
			if(opened.value) {
				if(Math.abs(left) < props.threshold) {
					isOpen.value = true
				} else {
					isOpen.value = false
				}
			} else {
				if(width + left < props.threshold) {
					isOpen.value = false
				} else {
					isOpen.value = true
				}
			}
			
			uxSwipeactionContentRef.value?.style?.setProperty('left', `${ width + left}px`)
			uxSwipeactionLeftMenuRef.value?.style?.setProperty('left', `${left}px`)
		}
	}
	
	async function _touchend(x: number) {
		isMove.value = false
		
		if(Math.abs(x - startX.value) < 10) {
			emit('click')
			return
		}
		
		if (props.disabled) {
			return
		}
		
		if (isOpen.value) {
			await _open()
		} else {
			await _close()
		}
	}
	
	async function touchstart(e : TouchEvent) {
		await _touchstart(e.changedTouches[0].clientX)
	}
	
	async function touchmove(e : TouchEvent) {
		await _touchmove(e.changedTouches[0].clientX)
	}
	
	async function touchend(e : TouchEvent) {
		await _touchend(e.changedTouches[0].clientX)
	}
	
	async function touchstartPc(e : MouseEvent) {
		await _touchstart(e.clientX)
	}
	
	async function touchmovePc(e : MouseEvent) {
		await _touchmove(e.clientX)
	}
	
	async function touchendPc(e : MouseEvent) {
		await _touchend(e.clientX)
	}
	
	onMounted(() => {
		
	})
	
	defineExpose({
		open,
		close
	})
</script>


<style lang="scss">
	.ux-swipeaction {
		position: relative;
		flex: 1;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: flex-start;
		/* #ifdef WEB */
		cursor: grab;
		/* #endif */
		
		&__content {
			width: 100%;
			transition-property: left;
			transition-duration: 0ms;
			transition-timing-function: linear;
		}
		
		&__menus--left {
			position: absolute;
			left: -100%;
			height: 100%;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: flex-start;
			transition-property: left;
			transition-duration: 0ms;
			transition-timing-function: linear;
			/* #ifdef WEB */
			cursor: pointer;
			/* #endif */
		}
		
		&__menus--right {
			position: absolute;
			left: 100%;
			height: 100%;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: flex-start;
			transition-property: left;
			transition-duration: 0ms;
			transition-timing-function: linear;
			/* #ifdef WEB */
			cursor: pointer;
			/* #endif */
		}
	}
</style>