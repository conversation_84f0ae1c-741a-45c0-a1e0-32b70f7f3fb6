<template>
	<view class="container-box" :style="`width:${width}px;height:${height}px`">
		<view class="container" ref="container">
			<view class="container-cut" ref="containerCut">
				<image class="container-image" mode="aspectFit" :src="src" @load="load"></image>
				<view class="cut-box" ref="cutBox" @touchstart="(e:TouchEvent)=>onTouchStart(e,'cutBox')"
					@touchcancel="onTouchCancel" @touchmove="(e:TouchEvent)=>onTouchMove(e,'cutBox')" @touchend="onTouchEnd">
					<!-- 可操作点 -->
					<view class="cut-point cut-top cut-top-bottom" @touchstart="(e:TouchEvent)=>onTouchStart(e,'top')"
						@touchcancel="onTouchCancel" @touchmove="(e:TouchEvent)=>onTouchMove(e,'top')" @touchend="onTouchEnd">
					</view>
					<view class="cut-point cut-left cut-left-right" @touchstart="(e:TouchEvent)=>onTouchStart(e,'left')"
						@touchcancel="onTouchCancel" @touchmove="(e:TouchEvent)=>onTouchMove(e,'left')" @touchend="onTouchEnd">
					</view>
					<view class="cut-point cut-right cut-left-right" @touchstart="(e:TouchEvent)=>onTouchStart(e,'right')"
						@touchcancel="onTouchCancel" @touchmove="(e:TouchEvent)=>onTouchMove(e,'right')" @touchend="onTouchEnd">
					</view>
					<view class="cut-point cut-bottom cut-top-bottom" @touchstart="(e:TouchEvent)=>onTouchStart(e,'bottom')"
						@touchcancel="onTouchCancel" @touchmove="(e:TouchEvent)=>onTouchMove(e,'bottom')" @touchend="onTouchEnd">
					</view>
					<view class="cut-point cut-top-left" @touchstart="(e:TouchEvent)=>onTouchStart(e,'topleft')"
						@touchcancel="onTouchCancel" @touchmove="(e:TouchEvent)=>onTouchMove(e,'topleft')" @touchend="onTouchEnd">
					</view>
					<view class="cut-point cut-top-right" @touchstart="(e:TouchEvent)=>onTouchStart(e,'topright')"
						@touchcancel="onTouchCancel" @touchmove="(e:TouchEvent)=>onTouchMove(e,'topright')" @touchend="onTouchEnd">
					</view>
					<view class="cut-point cut-bottom-left" @touchstart="(e:TouchEvent)=>onTouchStart(e,'bottomleft')"
						@touchcancel="onTouchCancel" @touchmove="(e:TouchEvent)=>onTouchMove(e,'bottomleft')"
						@touchend="onTouchEnd"></view>
					<view class="cut-point cut-bottom-right" @touchstart="(e:TouchEvent)=>onTouchStart(e,'bottomright')"
						@touchcancel="onTouchCancel" @touchmove="(e:TouchEvent)=>onTouchMove(e,'bottomright')"
						@touchend="onTouchEnd"></view>
				</view>
			</view>
		</view>
	</view>
	<ux-row :padding="[10,10]" align="center" justify="center">
		<ux-button :background="mode=='free'?'#ffc004':'#ddd'" :color="mode=='free'?'#333':'#888888'" text="自由" :mr="30"
			@click="onActive('free')"></ux-button>
		<ux-button :background="mode=='equal'?'#ffc004':'#ddd'" :color="mode=='equal'?'#333':'#888888'" text="1:1"
			@click="onActive('equal')"></ux-button>
	</ux-row>
	<image :src="tempFilePath"></image>
	<view class="preview" ref="preview">
		<image class="preview-image" ref="previewImage" :src="src"></image>
	</view>
</template>

<script setup>
	type cutStyleType = {
		width : number,
		height : number
	}

	defineProps({
		src: {
			type: String,
			required: true // 图片地址。
		},
		width: {
			type: Number, // 容器宽度。
			default: 750 // 默认宽度。
		},
		height: {
			type: Number, // 容器高度。
			default: 750 // 默认高度。
		},
	})
	const tempFilePath = ref('') // 临时文件路径。
	const mode = ref('free') // 图片裁剪模式。自由：free、等宽：equal。
	const container = ref<UniElement | null>(null);
	const containerCut = ref<UniElement | null>(null);
	const cutBox = ref<UniElement | null>(null);
	const preview = ref<UniElement | null>(null);
	const previewImage = ref<UniElement | null>(null);
	let containerCutRect = null as DOMRect | null
	let cutBoxRect = null as DOMRect | null //ref<DOMRect | null>(null)
	let minSide = 'width'
	// 创建剪裁区域容器的尺寸信息。
	let cutStyle : cutStyleType = {
		width: 0, // 容器的宽度。
		height: 0, // 容器的高度。
	}
	// 保存图片原始宽高
	let imageStyle : cutStyleType = {
		width: 0, // 图片的宽度。
		height: 0, // 图片的高度。
	}
	let move = false; // 是否移动。
	let posX = {
		top: 0,
		left: 0,
		right: 0,
		bottom: 0
	} as UTSJSONObject; // 触摸点X坐标。
	let posY = {
		top: 0,
		left: 0,
		right: 0,
		bottom: 0
	} as UTSJSONObject; // 触摸点Y坐标。

	/**
	 * 获取cutBoxRect信息
	 */
	const getCutBoxRect = async () => {
		// 获取剪裁区域容器的尺寸信息。
		cutBoxRect = await cutBox.value!.getBoundingClientRectAsync()!; // 类型断言为DOMRect类型，确保获取到正确的尺寸信息。
	}

	const confirm = () => {
		getCutBoxRect()
		// 通过图片原始信息和containerCutRect获取比例值
		const scale = imageStyle.width / containerCutRect!.width // 图片宽度除以剪裁区域容器的宽度。
		// 获取cutBoxRect和containerCutRect的相对位置
		const x = cutBoxRect!.left - containerCutRect!.left // 当前剪裁容器减去可移动区域的left值。
		const y = cutBoxRect!.top - containerCutRect!.top // 当前剪裁容器减去可移动区域的top值。

		const width = cutBoxRect!.width // 当前剪裁容器的宽度。
		const height = cutBoxRect!.height // 当前剪裁容器的高度。
		// 获取图片实际剪裁区域
		const cutImage = {
			x: Math.floor(x * scale),
			y: Math.floor(y * scale),
			width: Math.floor(width * scale),
			height: Math.floor(height * scale)
		} as UTSJSONObject

		preview.value!.style.setProperty("height", `${cutImage['height']}px`)
		preview.value!.style.setProperty("width", `${cutImage['width']}px`)

		previewImage.value!.style.setProperty("width", `${imageStyle.width}px`)
		previewImage.value!.style.setProperty("height", `${imageStyle.height}px`)
		previewImage.value!.style.setProperty("top", `-${cutImage['y']}px`)
		previewImage.value!.style.setProperty("left", `-${cutImage['x']}px`)
		//根据最长边计算比例
		const max = Math.max(cutImage['width'] as number, cutImage['height'] as number) // 获取图片最短边。
		const s = 750 / max
		console.log(cutImage['width']);
		console.log(s);

		preview.value!.style.setProperty('transform', `scale(${s})`)

		setTimeout(function() {
			preview.value!.takeSnapshot({
				success: (res) => { // 截图成功回调。
					console.log(res.tempFilePath); // 截图文件路径。
					tempFilePath.value = res.tempFilePath; // 设置截图文件路径。
				},
				fail: (err) => { // 截图失败回调。
					console.log(err); // 错误信息。
				},
			})
		}, 10);
	}

	const touchModeEqual = (size : number) => {
		const minVal = minSide == 'width' ? containerCutRect!.width : containerCutRect!.height // 最小值。
		// 当minSide等于width时 取left差；当minSide等于height时取top差
		const diff = minSide == 'width' ? (cutBoxRect!.left - containerCutRect!.left) : (cutBoxRect!.top - containerCutRect!.top)
		if (size < 0 || size > minVal - diff) return // 高度不能小于0。
		cutBox.value!.style.setProperty("height", `${size}px`); // 设置剪裁区域容器的高度。
		cutBox.value!.style.setProperty("width", `${size}px`); // 设置剪裁区域容器的高度。
	}

	const touchTop = (y : number) => {
		// 当前剪裁容器减去可移动区域的top值，再加上移动的y值。
		const top = cutBoxRect!.top - containerCutRect!.top + y
		if (top < 0 || top + 40 > containerCutRect!.height - (containerCutRect!.bottom - cutBoxRect!.bottom)) return
		cutBox.value!.style.setProperty("top", `${top}px`); // 设置剪裁区域容器的top属性。
		// 当前剪裁容器高度减去Y值
		const height = cutBoxRect!.height - y

		switch (mode.value) {
			case 'equal':
				touchModeEqual(height)
				break;
			case 'free':
				cutBox.value!.style.setProperty("height", `${height}px`); // 设置剪裁区域容器的高度。
				break;
		}
	}
	const touchBottom = (y : number) => {
		// 当前剪裁容器高度减去Y值
		const height = cutBoxRect!.height + y
		if (height < 40 || height > containerCutRect!.height - (cutBoxRect!.top - containerCutRect!.top)) return // 高度不能小于0。
		// cutBox.value!.style.setProperty("height", `${height}px`); // 设置剪裁区域容器的高度。
		switch (mode.value) {
			case 'equal':
				touchModeEqual(height)
				break;
			case 'free':
				cutBox.value!.style.setProperty("height", `${height}px`); // 设置剪裁区域容器的高度。
				break;
		}
	}
	const touchLeft = (x : number) => {
		// 当前剪裁容器减去可移动区域的top值，再加上移动的y值。
		const left = cutBoxRect!.left - containerCutRect!.left + x
		if (left < 0 || left + 40 > containerCutRect!.width - (containerCutRect!.right - cutBoxRect!.right)) return
		cutBox.value!.style.setProperty("left", `${left}px`); // 设置剪裁区域容器的top属性。
		// 当前剪裁容器高度减去Y值
		const width = cutBoxRect!.width - x
		switch (mode.value) {
			case 'equal':
				touchModeEqual(width)
				break;
			case 'free':
				cutBox.value!.style.setProperty("width", `${width}px`); // 设置剪裁区域容器的高度。
				break;
		}
	}
	const touchRight = (x : number) => {
		// 当前剪裁容器高度减去Y值
		const width = cutBoxRect!.width + x
		if (width < 40 || width > containerCutRect!.width - (cutBoxRect!.left - containerCutRect!.left)) return // 高度不能小于0。
		switch (mode.value) {
			case 'equal':
				touchModeEqual(width)
				break;
			case 'free':
				cutBox.value!.style.setProperty("width", `${width}px`); // 设置剪裁区域容器的高度。
				break;
		}
	}
	const touchBox = (x : number, y : number) => {
		// 当前剪裁容器减去可移动区域的top值，再加上移动的y值。
		const left = cutBoxRect!.left - containerCutRect!.left + x
		const top = cutBoxRect!.top - containerCutRect!.top + y
		const right = containerCutRect!.right - cutBoxRect!.right - x
		const bottom = containerCutRect!.bottom - cutBoxRect!.bottom - y
		if (left > 0 && right > 0) {
			cutBox.value!.style.setProperty("left", `${left}px`);
		}
		if (top > 0 && bottom > 0) {
			cutBox.value!.style.setProperty("top", `${top}px`);
		}
	}

	const onTouchStart = (e : TouchEvent, type : string) => {
		// 阻止继续冒泡.
		e.stopPropagation();
		if (!move) {
			move = true
			posX[type] = e.touches[0].screenX
			posY[type] = e.touches[0].screenY
			getCutBoxRect()
		}
	}
	const onTouchMove = (e : TouchEvent, type : string) => {
		// 阻止继续冒泡.
		e.stopPropagation();
		// 阻止默认行为.
		e.preventDefault()
		let p = e.touches[0]
		let x = p.screenX - posX[type]! as number
		let y = p.screenY - posY[type]! as number

		switch (type) {
			case 'top':
				touchTop(y)
				break;
			case 'bottom':
				touchBottom(y)
				break;
			case 'left':
				touchLeft(x)
				break;
			case 'right':
				touchRight(x)
				break;
			case 'topleft':
				touchTop(y)
				touchLeft(x)
				break;
			case 'topright':
				touchTop(y)
				touchRight(x)
				break;
			case 'bottomleft':
				touchBottom(y)
				touchLeft(x)
				break;
			case 'bottomright':
				touchBottom(y)
				touchRight(x)
				break;
			case 'cutBox':
				touchBox(x, y)
				break;
		}
	}
	const onTouchEnd = (_ : TouchEvent) => {
		move = false
		confirm()
	}
	const onTouchCancel = (_ : TouchEvent) => {
		move = false
	}

	/**
	 * 设置cutBox宽高
	 */
	const setCutBoxStyle = () => {
		switch (mode.value) {
			case 'free':
				cutBox.value!.style.setProperty("width", `${cutStyle.width}px`); // 设置裁剪容器的宽度。
				cutBox.value!.style.setProperty("height", `${cutStyle.height}px`); //  设置裁剪容器的高度。
				break;
			case 'equal':
				cutBox.value!.style.setProperty("width", `${cutStyle[minSide]}px`); // 设置裁剪容器的宽度。
				cutBox.value!.style.setProperty("height", `${cutStyle[minSide]}px`); //  设置裁剪容器的高度。
				break;
		}
		setTimeout(() => { getCutBoxRect() }, 0);
	}

	/**
	 * 获取容器dom信息
	 */
	const getContainerRect = async () : Promise<DOMRect> => {
		const rect = await container.value?.getBoundingClientRectAsync()!;
		return rect as DOMRect; // 类型断言为DOMRect类型，确保获取到正确的尺寸信息。
	}

	/**
	 * 计算剪裁区域容器的尺寸信息。
	 */
	const calculationStyle = (img : cutStyleType, rect : cutStyleType) => {
		// 获取图片宽高中较长的
		const imgMax = Math.max(img.width, img.height) === img.width ? 'width' : 'height'; // 获取图片宽高中较长的值。
		// 剪裁区域最长边与容器边等长
		cutStyle[imgMax] = rect[imgMax];
		// 获取图片长端与容器比例
		const imgMaxRatio = (img[imgMax] as number) / (rect[imgMax] as number); // 获取图片长端与容器长端的比例。
		// 通过比例值计算短端值。
		minSide = imgMax === 'width' ? 'height' : 'width'; // 获取图片宽高中较短的值。
		cutStyle[minSide] = (img[minSide] as number) / (imgMaxRatio as number); // 通过比例值计算短端值。
		// 设置图片样式。
		// 图片的宽高比与容器的宽高比进行比较，确定图片的宽高。
		containerCut.value!.style.setProperty("width", `${cutStyle.width}px`); // 设置容器的宽度。
		containerCut.value!.style.setProperty("height", `${cutStyle.height}px`); //  设置容器的高度。

		setCutBoxStyle()
	}

	/**
	 * 获取裁剪容器可移动区域
	 */
	const getContainerCutRect = async () => {
		containerCutRect = await containerCut.value?.getBoundingClientRectAsync()!; // 类型断言为DOMRect类型，确保获取到正确的尺寸信息。
	}
	/**
	 * 图片加载完毕后的回调函数
	 */
	const load = async (e : UniImageLoadEvent) => {
		const box = await getContainerRect()
		imageStyle = {
			width: e.detail.width, // 图片宽度。
			height: e.detail.height, // 图片高度。
		} as cutStyleType
		const rect : cutStyleType = {
			width: box.width, // 容器宽度。
			height: box.height, // 容器高度。
		}
		calculationStyle(imageStyle, rect) // 计算图片样式，并设置给图片元素。

		setTimeout(() => {
			getContainerCutRect()
		}, 0)
	}


	/**
	 * 修改剪裁模式
	 */
	const onActive = (m : string) => {
		mode.value = m
		cutBox.value!.style.setProperty("top", 0);
		cutBox.value!.style.setProperty("left", 0);
		setCutBoxStyle()
	}
</script>

<style lang="scss">
	.preview {
		background-color: red;
		position: absolute;
		top: 2000px;
		transform-origin: top left; // 设置旋转中心点为左上角。

		.preview-image {
			position: absolute;
		}
	}

	.container-box {
		padding: 20rpx;
		background-color: #666666;

		.container {
			width: 100%;
			height: 100%;

			.container-cut {
				margin: auto;
				position: relative; // 设置容器的定位方式为相对定位。

				.container-image {
					width: 100%;
					height: 100%;
				}

				.cut-box {
					border: 1px solid #ffc004;
					position: absolute; // 设置剪裁区域的定位方式为绝对定位。
					top: 0;
					left: 0;

					.cut-point {
						width: 20px;
						height: 20px;
						position: absolute;
					}

					.cut-top-left {
						top: 0;
						border-top: 2px solid #ffc004; // 设置剪裁区域的边框样式。
						border-left: 2px solid #ffc004; // 设置剪裁区域的边框样式。
					}

					.cut-top-right {
						right: 0;
						border-top: 2px solid #ffc004; // 设置剪裁区域的边框样式。
						border-right: 2px solid #ffc004; // 设置剪裁区域的边框样式。
					}

					.cut-bottom-left {
						bottom: 0;
						border-bottom: 2px solid #ffc004; // 设置剪裁区域的边框样式。
						border-left: 2px solid #ffc004; // 设置剪裁区域的边框样式。
					}

					.cut-bottom-right {
						bottom: 0;
						right: 0;
						border-bottom: 2px solid #ffc004; // 设置剪裁区域的边框样式。
						border-right: 2px solid #ffc004; // 设置剪裁区域的边框样式。
					}

					.cut-top-bottom {
						left: 50%;
						transform: translateX(-50%); // 水平居中。
					}

					.cut-top {
						top: 0;
						border-top: 2px solid #ffc004; // 设置剪裁区域的边框样式。
					}

					.cut-bottom {
						bottom: 0; // 设置剪裁区域的底部位置。
						border-bottom: 2px solid #ffc004; // 设置剪裁区域的边框样式。;
					}

					.cut-left-right {
						top: 50%;
						transform: translateY(-50%); // 水平居中。
					}

					.cut-left {
						left: 0;
						border-left: 2px solid #ffc004; // 设置剪裁区域的边框样式。
					}

					.cut-right {
						right: 0; // 设置剪裁区域的右侧位置。
						border-right: 2px solid #ffc004; // 设置剪裁区域的边框样式。;;
					}
				}
			}
		}
	}
</style>