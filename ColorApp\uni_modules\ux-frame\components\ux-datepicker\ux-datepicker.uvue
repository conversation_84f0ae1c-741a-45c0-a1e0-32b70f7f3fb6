<template>
	<ux-drawer ref="uxDrawerRef" direction="top" :height="`${pickerHeight}px`" :touchable="touchClose" :opacity="opacity" :maskClose="maskClose" :border="false" @close="onMaskClose">
		<view class="ux-picker__slot" @click="click()">
			<slot></slot>
		</view>

		<template v-slot:drawer>
			<view v-if="isShow" class="ux-picker" :style="[style]">
				<view class="ux-picker__title" :style="titleBorder">
					<text v-if="title != ''" class="ux-picker__title--text" :style="titleStyle">{{ title }}</text>
					<view v-if="btnPositon == 'top'" :class="btnType == 'normal' || title != '' ? 'ux-picker__btn--cancel' : 'ux-picker__btn--cancel--bigger'"
						:style="titleBackground"
						hover-class="ux-picker__hover" :hover-start-time="20" :hover-stay-time="200" @click="onCancel">
						<text class="ux-picker__btn--text" :style="[cancelStyle]">取消</text>
					</view>
					<view v-if="btnPositon == 'top'" :class="btnType == 'normal' || title != '' ? 'ux-picker__btn--confirm' : 'ux-picker__btn--confirm--bigger'"
						:style="[titleBackground, confirmBorder]"
						 hover-class="ux-picker__hover" :hover-start-time="20" :hover-stay-time="200" @click="onConfirm">
						<text class="ux-picker__btn--text" :style="[confirmStyle]">{{ confirm }}</text>
					</view>
					<view v-if="btnPositon == 'bottom'" class="ux-picker__close" @click="onCancel">
						<ux-icon type="close"></ux-icon>
					</view>
				</view>
				<!-- #ifdef APP -->
				<picker-view v-if="mode != 'calendar'" style="flex: 1;" 
					:indicator-style="indicatorStyle" 
					:mask-top-style="maskTopStyle" 
					:mask-bottom-style="maskBottomStyle"
					:value="values"
					@change="change">
				<!-- #endif -->
				<!-- #ifndef APP -->
				<picker-view v-if="mode != 'calendar'" style="flex: 1;" 
					:indicator-style="indicatorStyle" 
					indicator-class="ux-picker__indicator"
					:mask-style="maskStyle"
					:value="values"
					@change="change">
				<!-- #endif -->
					<picker-view-column v-for="(items, i) in datas" :key="i">
						<!-- #ifdef APP-ANDROID -->
						<list-view :show-scrollbar="false" :bounces="false">
							<list-item v-for="(item, index) in items" :key="index">
								<ux-picker-item :text="item.name" :size="size" :color="color" :selectColor="selectColor" :selectStyle="selectStyle" :index="index" :selectedIndex="values[i]"></ux-picker-item>
							</list-item>
						</list-view>
						<!-- #endif -->
						<!-- #ifdef APP-HARMONY -->
						<text class="ux-picker__text" :style="[textStyle, {'font-weight': index == values[i] ? 'bold' : 'normal', 'color': index == values[i] ? selectColor : color}]" v-for="(item, index) in items" :key="index">{{ item.name }}</text>
						<!-- #endif -->
						<!-- #ifdef APP-IOS || WEB || MP -->
						<template v-for="(item, index) in items" :key="index">
							<ux-picker-item :text="item.name" :size="size" :color="color" :selectColor="selectColor" :selectStyle="selectStyle" :index="index" :selectedIndex="values[i]"></ux-picker-item>
						</template>
						<!-- #endif -->
					</picker-view-column>
				</picker-view>
				<view v-else class="ux-picker__calendar">
					<ux-calendar 
						:value="value" 
						:select-mode="selectMode" 
						:lunar="lunar" 
						:range="range"
						:disabled="disabled" 
						:format="format" 
						:theme="theme" 
						@change="calendarChange"></ux-calendar>
				</view>
				<view v-if="btnPositon == 'bottom'" class="ux-picker__btns">
					<view class="ux-picker__btns--cancel" :style="cancelBtnStyle" hover-class="ux-picker__hover" :hover-start-time="20" :hover-stay-time="200" @click="onCancel">
						<text class="ux-picker__btns--text" :style="cancelTextStyle">取消</text>
					</view>
					<view class="ux-picker__btns--confirm" :style="confirmBtnStyle" hover-class="ux-picker__hover" :hover-start-time="20" :hover-stay-time="200" @click="onConfirm">
						<text class="ux-picker__btns--text" :style="confirmTextStyle">{{ confirm }}</text>
					</view>
				</view>
			</view>
		</template>
	</ux-drawer>
</template>

<script setup>

	/**
	 * DatePicker 日期选择器
	 * @description 支持日历、年月日、年月日时分秒、年月日时分、时分秒、年月、年、周等日期选择，可限制选择范围
	 * @demo pages/component/datepicker.uvue
	 * @tutorial https://www.uxframe.cn/component/datepicker.html
	 * @property {String} 			name												String | 标识符，在回调事件中返回
	 * @property {String} 			theme=[primary|warning|success|error|info]			String | 主题颜色
	 * @value primary 	主色
	 * @value warning 	警告
	 * @value success 	成功
	 * @value error 	错误
	 * @value info 		文本
	 * @property {String}		mode=[calendar|date|datetime|dateminute|datehour|hourminute|time|month|year|week]		String | 模式 (默认 date)
	 * @value calendar 日历
	 * @value date 日期
	 * @value datetime 日期时间
	 * @value datehour 日期小时
	 * @value dateminute 日期分钟
	 * @value hourminute 小时分钟
	 * @value time 时间
	 * @value month 月
	 * @value year 年
	 * @value week 周
	 * @property {String}		format=[yyyy-MM-dd|yyyy/MM/dd|yyyy年MM月dd日|yyyy年MM月dd日 周|yyyy年MM月dd日 星期|yyyy年MM月dd日 礼拜]		String | 格式化规则
	 * @property {Array}		value																string[] | 值 (默认 now)
	 * @property {Array}		range																String[] | 范围 (例如 ['2000-01-01', '2023-12-10'])
	 * @property {Number}		max																	Number | 默认年份最大值 (默认 +1)
	 * @property {Number}		min																	Number | 默认年份最小值 (默认 -10)
	 * @property {String}		selectMode=[single|muti|range]										String | 选择模式 仅calendar有效 (默认 single)
	 * @value single 单选
	 * @value muti 多选
	 * @value range 范围
	 * @property {Boolean}		lunar=[true|false]													Boolean | 显示农历 仅calendar有效 (默认 true)
	 * @property {Boolean}		touchable=[true|false]												Boolean | 滑动翻页 仅calendar有效 (默认 true)
	 * @value true
	 * @value false
	 * @property {String}		title																String | 标题
	 * @property {String}		titleColor															String | 标题颜色 (默认 $ux.Conf.titleColor)
	 * @property {Any}			size																Any | 内容大小 (默认 $ux.Conf.fontSize)
	 * @property {String}		color																String | 内容颜色 (默认 $ux.Conf.fontColor)
	 * @property {String} 		darkColor=[none|auto|color]											String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {String}		selectColor															String | 内容选中颜色 (默认 $ux.Conf.fontColor)
	 * @property {String} 		selectDarkColor=[none|auto|color]									String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Boolean}		selectStyle=[border|box]											Boolean | 选择区域遮罩样式 (默认 border)
	 * @value border 边框 
	 * @value box 盒子
	 * @property {String}		confirm																String | 确定文字 (默认 确定)
	 * @property {String}		confirmColor														Strng | 确定文字颜色 (默认 $ux.Conf.primaryColor)
	 * @property {Any}			btnSize																Any | 按钮大小 (默认 $ux.Conf.fontSize)
	 * @property {String}		btnType=[normal|bigger]												String | 按钮类型 (默认 normal)
	 * @value normal 正常
	 * @value bigger 大按钮
	 * @property {String}		btnPositon=[top|bottom]												String | 按钮位置 (默认 bottom)
	 * @value top 顶部按钮
	 * @value bottom 底部按钮
	 * @property {Any}			radius																Any | 圆角 (默认 $ux.Conf.radius)
	 * @property {Number}		opacity																Number | 遮罩透明度 0-1 (默认 $ux.Conf.maskAlpha)
	 * @property {Boolean}		touchClose=[true|false]												Boolean | 允许滑动关闭 (默认 false)
	 * @value true
	 * @value false
	 * @property {Boolean}		maskClose=[true|false]												Boolean | 遮罩层关闭 (默认 true)
	 * @value true
	 * @value false
	 * @property {Boolean}		disabled=[true|false]												Boolean | 是否禁用 (默认 false)
	 * @value true
	 * @value false
	 * @event {Function}		change																Function(UxDatePickerEvent) | 确定选择时触发
	 * @event {Function}		close																Function | 关闭时触发
	 * <AUTHOR>
	 * @date 2023-11-08 18:00:46
	 */
	
	import { $ux } from '../../index'
	import { UxPickerData, UxDatePickerEvent, UxCalendarDate, UxCalendarEvent } from '../../libs/types/types.uts'
	import { useThemeColor, useForegroundColor, useBorderColor, useFontColor, useFontSize, useSubTitleColor, useCancelColor, useRadius, UxThemeType } from '../../libs/use/style.uts'
	
	defineOptions({
		name: 'ux-datepicker'
	})
	
	const emit = defineEmits(['change', 'update:modelValue', 'close'])
	
	const props = defineProps({
		modelValue: {
			type: String,
			default: ''
		},
		name: {
			type: String,
			default: ''
		},
		theme: {
			type: String,
			default: 'primary',
		},
		mode: {
			type: String,
			default: 'date'
		},
		format: {
			type: String,
			default: ''
		},
		value: {
			type: Array as PropType<Array<string>>,
			default: () : string[] => {
				return [] as string[]
			}
		},
		range: {
			type: Array as PropType<Array<string>>,
			default: () : string[] => {
				return [] as string[]
			}
		},
		max: {
			type: Number,
			default: 1
		},
		min: {
			type: Number,
			default: 10
		},
		selectMode: {
			type: String,
			default: 'single'
		},
		lunar: {
			type: Boolean,
			default: true
		},
		title: {
			type: String,
			default: ''
		},
		titleColor: {
			type: String,
			default: ''
		},
		size: {
			default: 0
		},
		color: {
			type: String,
			default: '#999999'
		},
		darkColor: {
			type: String,
			default: '#c5c5c5'
		},
		selectColor: {
			type: String,
			default: ''
		},
		selectDarkColor: {
			type: String,
			default: ''
		},
		selectStyle: {
			type: String,
			default: 'border'
		},
		btnSize: {
			default: 0
		},
		btnType: {
			type: String,
			default: 'normal'
		},
		btnPositon: {
			type: String,
			default: 'bottom'
		},
		confirm: {
			type: String,
			default: '确定'
		},
		confirmColor: {
			type: String,
			default: ''
		},
		radius: {
			default: -1
		},
		opacity: {
			type: Number,
			default: -1
		},
		touchable: {
			type: Boolean,
			default: true
		},
		touchClose: {
			type: Boolean,
			default: false
		},
		maskClose: {
			type: Boolean,
			default: true
		},
		disabled: {
			type: Boolean,
			default: false
		}
	})
	
	const uxDrawerRef = ref<UxDrawerComponentPublicInstance | null>(null)
	const values = ref<number[]>([] as number[])
	const datas = ref<UxPickerData[][]>([] as UxPickerData[][])
	const calendarValues = ref<UxCalendarDate[]>([] as UxCalendarDate[])
	const timer = ref(0)
	const isShow = ref(false)
	
	const pickerHeight = computed((): number => {
		const cH = 340
		const itemH = 50
		const itemLen = 5
		const topH = 50
		const bottomH = 70
		
		if(props.mode == 'calendar') {
			if(props.btnPositon == 'bottom') {
				return cH + topH + bottomH
			} else {
				return cH + topH + 20
			}
		} else {
			if(props.btnPositon == 'bottom') {
				return itemH * itemLen + topH + bottomH
			} else {
				return itemH * itemLen + topH + 20
			}
		}
	})
	
	const fontSize = computed(():number => {
		return useFontSize($ux.Util.getPx(props.size), 0)
	})
	
	const btnSize = computed(():number => {
		return useFontSize($ux.Util.getPx(props.btnSize), 0)
	})
	
	const titleColor = computed(():string => {
		return useSubTitleColor(props.titleColor, 'auto')
	})
	
	const color = computed(():string => {
		return useFontColor(props.color, props.darkColor)
	})
	
	const selectColor = computed(():string => {
		return useFontColor(props.selectColor, props.selectDarkColor)
	})
	
	const borderColor = computed(():string => {
		return useBorderColor('', '')
	})
	
	const radius = computed(():number => {
		return useRadius($ux.Util.getPx(props.radius), 2)
	})
	
	const confirmColor = computed(():string => {
		if(props.confirmColor == '') {
			return useThemeColor(props.theme as UxThemeType)
		} else {
			return props.confirmColor
		}
	})
	
	const cancelColor = computed(():string => {
		return useCancelColor('', 'auto')
	})
	
	const backgroundColor = computed((): string => {
		return useForegroundColor('#ffffff', 'auto')
	})
	
	const maskTopStyle = computed(() : string => {
		// #ifdef APP-HARMONY
		return 'background-image: linear-gradient(to bottom, transparent, transparent);'
		// #endif
		
		// #ifndef APP-HARMONY
		return `
			background-color: rgba(0,0,0,0);
			background-image: none;
			`
		// #endif
	})
	
	const maskBottomStyle = computed(() : string => {
		// #ifdef APP-HARMONY
		return 'background-image: linear-gradient(to bottom, transparent, transparent);'
		// #endif
		
		// #ifndef APP-HARMONY
		return `
			background-color: rgba(0,0,0,0);
			background-image: none;
			`
		// #endif
	})
	
	const maskStyle = computed(() : string => {
		return `
			background-image: linear-gradient(
				to top,
				rgba(0, 0, 0, 0),
				rgba(0, 0, 0, 0)
			);
			`
	})
	
	const indicatorStyle = computed(() : string => {
		
		if(props.selectStyle == 'box') {
			// #ifdef WEB || MP
			return `height: 50px;background-color: rgba(190,190,190,0.2);margin:0 5px;border-radius: 10px;width: ${uni.getWindowInfo().windowWidth/datas.value.length-10}px;`
			// #endif
			// #ifdef APP-HARMONY
			return `height:50px;background-color:rgba(190,190,190,0.2);left:20px;right:20px;flex: 1;border-radius: 10px;position:absolute;`
			// #endif
			// #ifdef APP-ANDROID || APP-IOS
			return `height: 50px;background-color: rgba(190,190,190,0.2);margin:0 5px;border-radius: 10px;`
			// #endif
		} else {
			return `height: 50px;border-top: 1rpx solid ${borderColor.value};border-bottom: 1rpx solid ${borderColor.value};`
		}
	})
	
	const style = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('border-top-left-radius', $ux.Util.addUnit(radius.value))
		css.set('border-top-right-radius', $ux.Util.addUnit(radius.value))
		css.set('height', `${pickerHeight.value}px`)
		css.set('background-color', backgroundColor.value)
		
		// #ifdef WEB
		css.set('cursor', props.disabled? 'not-allowed' : 'pointer')
		// #endif
		
		return css
	})
	
	const textStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('font-size', $ux.Util.addUnit(fontSize.value))
		
		return css
	})
	
	const titleStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('color', titleColor.value)
		
		return css
	})
	
	const titleBackground = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('background-color', backgroundColor.value)
		
		return css
	})
	
	const titleBorder = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		if(props.btnPositon == 'bottom' || props.btnType == 'normal' || props.title != '') {
			
		} else {
			css.set('border-bottom', `1rpx solid ${borderColor.value}`)
		}
		
		return css
	})
	
	const cancelStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('font-size', $ux.Util.addUnit(btnSize.value))
		css.set('color', cancelColor.value)
		
		return css
	})
	
	const confirmStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('font-size', $ux.Util.addUnit(btnSize.value))
		css.set('color', confirmColor.value)
		
		return css
	})
	
	const confirmBorder = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		if(props.btnPositon == 'bottom' || props.btnType == 'normal' || props.title != '') {
			
		} else {
			css.set('border-left', `1rpx solid ${borderColor.value}`)
		}
		
		return css
	})
	
	const confirmBtnStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('background-color', confirmColor.value)
		
		return css
	})
	
	const confirmTextStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('color', '#ffffff')
		
		return css
	})
	
	const cancelBtnStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('background-color', $ux.Color.getRgba(confirmColor.value, 0.05))
		css.set('border', `1px solid ${$ux.Color.getRgba(confirmColor.value, 0.8)}`)
		
		return css
	})
	
	const cancelTextStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('color', confirmColor.value)
		
		return css
	})
	
	const pickerStyle = computed(() : string => {
		return `background: ${backgroundColor.value}`
	})
	
	function dateFormat() : string {
		if (props.mode == 'date') {
			return props.format == '' ? 'yyyy-MM-dd' : props.format
		} else if (props.mode == 'datetime') {
			return props.format == '' ? 'yyyy-MM-dd HH:mm:ss' : props.format
		} else if (props.mode == 'datehour') {
			return props.format == '' ? 'yyyy-MM-dd HH' : props.format
		} else if (props.mode == 'dateminute') {
			return props.format == '' ? 'yyyy-MM-dd HH:mm' : props.format
		} else if (props.mode == 'hourminute') {
			return props.format == '' ? 'HH:mm' : props.format
		} else if (props.mode == 'time') {
			return props.format == '' ? 'HH:mm:ss' : props.format
		} else if (props.mode == 'month') {
			return props.format == '' ? 'yyyy-MM' : props.format
		} else if (props.mode == 'year') {
			return props.format == '' ? 'yyyy' : props.format
		} else if (props.mode == 'week') {
			return props.format == '' ? 'yyyy-MM-dd 周' : props.format
		}
		
		return 'yyyy-MM-dd'
	}
	
	function setYear(min : Date, max : Date) {
		// 过滤日期范围
		let _min = min.getFullYear()
		let _max = max.getFullYear()
		
		let years = [] as UxPickerData[]
		for (let i = _max, len = _min; i >= len; i--) {
			years.push({
				name: `${i}年`,
				value: i,
			} as UxPickerData);
		}
		
		if (datas.value.length == 0) {
			datas.value.push(years)
		} else {
			datas.value.splice(0, 1, years)
		}
	}
	
	function setMonth(min : Date, max : Date) {
		
		if (datas.value[0].length < values.value[0]) {
			return
		}
		
		let year = datas.value[0][values.value[0]].value as number
		
		let months = [] as UxPickerData[]
		
		let _min = new Date(min.getFullYear(), min.getMonth(), 0, 0, 0, 0).getTime()
		let _max = new Date(max.getFullYear(), max.getMonth() + 1, 0, 0, 0, 0).getTime()
		
		for (var i = 1; i <= 12; i++) {
			// 过滤日期范围
			const str = `${year}-${i.toString().padStart(2, '0')}`
			let date = $ux.Date.toDate(str)
			if (date.getTime() < _min || date.getTime() > _max) {
				continue
			}
		
			months.push({
				name: `${i.toString().padStart(2, '0')}月`,
				value: i,
			} as UxPickerData)
		}
		
		if(values.value[1] > months.length - 1) {
			values.value[1] = months.length - 1
		}
		
		if (datas.value.length == 1) {
			datas.value.push(months)
		} else {
			datas.value.splice(1, 1, months)
		}
	}
	
	function setDay(min : Date, max : Date) {
		if (datas.value[0].length < values.value[0]) {
			return
		}
		let year = datas.value[0][values.value[0]].value as number
		
		if (datas.value[1].length < values.value[1]) {
			return
		}
		let month = datas.value[1][values.value[1]].value as number
		
		let days = $ux.Date.newDate(`${year}-${month}`).getMonthDays().length
		
		let dates = [] as UxPickerData[]
		
		let _min = new Date(min.getFullYear(), min.getMonth(), min.getDate(), 0, 0, 0).getTime()
		let _max = new Date(max.getFullYear(), max.getMonth(), max.getDate(), 0, 0, 0).getTime()
		
		for (var i = 1; i <= days; i++) {
			// 过滤日期范围
			const str = `${year}-${month.toString().padStart(2, '0')}-${i.toString().padStart(2, '0')}`
			
			let date = $ux.Date.toDate(str)
			if (date.getTime() < _min || date.getTime() > _max) {
				continue
			}
		
			dates.push({
				name: `${i.toString().padStart(2, '0')}日`,
				value: i,
			} as UxPickerData)
		}
		
		if(values.value[2] > dates.length - 1) {
			values.value[2] = dates.length - 1
		}
		
		if (datas.value.length == 2) {
			datas.value.push(dates)
		} else {
			datas.value.splice(2, 1, dates)
		}
	}
	
	function setWeek(min : Date, max : Date) {
		let _weeks = $ux.Date.weeks()
		
		let dates = [] as UxPickerData[]
		
		let _min = new Date(min.getFullYear(), min.getMonth(), min.getDate(), 0, 0, 0).getTime()
		let _max = new Date(max.getFullYear(), max.getMonth(), max.getDate(), 0, 0, 0).getTime()
		
		for (var i = 0; i < _weeks.length; i++) {
			// 过滤日期范围
			let date = $ux.Date.toDate(_weeks[i])
			if (date.getTime() < _min || date.getTime() > _max) {
				continue
			}
		
			dates.push({
				name: `${_weeks[i]} ${$ux.Date.weekName(date.getDay(), dateFormat())}`,
				value: `${_weeks[i]} ${$ux.Date.weekName(date.getDay(), dateFormat())}`,
			} as UxPickerData)
		}
		
		if (datas.value.length == 0) {
			datas.value.push(dates)
		} else {
			datas.value.splice(0, 1, dates)
		}
	}
	
	function setHour(min : Date, max : Date) {
		if (props.mode == 'time' || props.mode == 'hourminute') {
			let hours = [] as UxPickerData[]
		
			for (var i = 0; i <= 23; i++) {
				// 过滤日期范围
				if (i < min.getHours() || i > max.getHours()) {
					continue
				}
		
				hours.push({
					name: `${i.toString().padStart(2, '0')}时`,
					value: i,
				} as UxPickerData)
			}
		
			if (datas.value.length == 0) {
				datas.value.push(hours)
			} else {
				datas.value.splice(0, 1, hours)
			}
		} else {
			// 日期
		
			if (datas.value[0].length < values.value[0]) {
				return
			}
			let year = datas.value[0][values.value[0]].value as number
		
			if (datas.value[1].length < values.value[1]) {
				return
			}
			let month = datas.value[1][values.value[1]].value as number
		
			if (datas.value[2].length < values.value[2]) {
				return
			}
			
			let index = values.value[2] > (datas.value[2].length - 1) ? (datas.value[2].length - 1) : values.value[2]
			let day = datas.value[2][index].value as number
		
			let _date = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`
		
			let hours = [] as UxPickerData[]
		
			let _min = new Date(min.getFullYear(), min.getMonth(), min.getDate(), min.getHours(), 0, 0).getTime()
			let _max = new Date(max.getFullYear(), max.getMonth(), max.getDate(), max.getHours(), 0, 0).getTime()
		
			for (var i = 0; i <= 23; i++) {
				// 过滤日期范围
				const str = `${_date} ${i.toString().padStart(2, '0')}:00:00`
				let date = $ux.Date.toDate(str)
				if (date.getTime() < _min || date.getTime() > _max) {
					continue
				}
		
				hours.push({
					name: `${i.toString().padStart(2, '0')}时`,
					value: i,
				} as UxPickerData)
			}
			
			if(values.value[3] > hours.length - 1) {
				values.value[3] = hours.length - 1
			}
		
			if (datas.value.length == 3) {
				datas.value.push(hours)
			} else {
				datas.value.splice(3, 1, hours)
			}
		}
	}
	
	function setMinute(min : Date, max : Date) {
		if (props.mode == 'time' || props.mode == 'hourminute') {
			// 时
			if (datas.value[0].length < values.value[0]) {
				return
			}
			let hour = datas.value[0][values.value[0]].value as number
		
			let minutes = [] as UxPickerData[]
		
			let now = new Date()
			let _min = new Date(now.getFullYear(), now.getMonth(), now.getDate(), min.getHours(), min.getMinutes(), 0).getTime()
			let _max = new Date(now.getFullYear(), now.getMonth(), now.getDate(), max.getHours(), max.getMinutes(), 0).getTime()
		
			for (var i = 0; i <= 59; i++) {
				// 过滤日期范围
				const str = `${hour.toString().padStart(2, '0')}:${i.toString().padStart(2, '0')}:00`
				let date = $ux.Date.toDate(str)
				if (date.getTime() < _min || date.getTime() > _max) {
					continue
				}
		
				minutes.push({
					name: `${i.toString().padStart(2, '0')}分`,
					value: i,
				} as UxPickerData)
			}
		
			if (datas.value.length == 1) {
				datas.value.push(minutes)
			} else {
				datas.value.splice(1, 1, minutes)
			}
		} else {
			// 日期
			if (datas.value[0].length < values.value[0]) {
				return
			}
			let year = datas.value[0][values.value[0]].value as number
		
			if (datas.value[1].length < values.value[1]) {
				return
			}
			let month = datas.value[1][values.value[1]].value as number
		
			if (datas.value[2].length < values.value[2]) {
				return
			}
			
			let index = values.value[2] > (datas.value[2].length - 1) ? (datas.value[2].length - 1) : values.value[2]
			let day = datas.value[2][index].value as number
		
			let _date = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`
		
			// 时
			if (datas.value[3].length < values.value[3]) {
				return
			}
			let hour = datas.value[3][values.value[3]].value as number
		
			let minutes = [] as UxPickerData[]
		
			let _min = new Date(min.getFullYear(), min.getMonth(), min.getDate(), min.getHours(), min.getMinutes(), 0).getTime()
			let _max = new Date(max.getFullYear(), max.getMonth(), max.getDate(), max.getHours(), max.getMinutes(), 0).getTime()
		
			for (var i = 0; i <= 59; i++) {
				// 过滤日期范围
				const str = `${_date} ${hour.toString().padStart(2, '0')}:${i.toString().padStart(2, '0')}:00`
				let date = $ux.Date.toDate(str)
				if (date.getTime() < _min || date.getTime() > _max) {
					continue
				}
		
				minutes.push({
					name: `${i.toString().padStart(2, '0')}分`,
					value: i,
				} as UxPickerData)
			}
			
			if(values.value[4] > minutes.length - 1) {
				values.value[4] = minutes.length - 1
			}
		
			if (datas.value.length == 4) {
				datas.value.push(minutes)
			} else {
				datas.value.splice(4, 1, minutes)
			}
		}
	}
	
	function setSecond(min : Date, max : Date) {
		if (props.mode == 'time') {
			// 时
			if (datas.value[0].length < values.value[0]) {
				return
			}
			let hour = datas.value[0][values.value[0]].value as number
		
			// 分
			if (datas.value[1].length < values.value[1]) {
				return
			}
			let minute = datas.value[1][values.value[1]].value as number
		
			let seconds = [] as UxPickerData[]
		
			let now = new Date()
			let _min = new Date(now.getFullYear(), now.getMonth(), now.getDate(), min.getHours(), min.getMinutes(), min.getSeconds()).getTime()
			let _max = new Date(now.getFullYear(), now.getMonth(), now.getDate(), max.getHours(), max.getMinutes(), max.getSeconds()).getTime()
		
			for (var i = 0; i <= 59; i++) {
				// 过滤日期范围
				const str = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:${i.toString().padStart(2, '0')}`
				let date = $ux.Date.toDate(str)
				if (date.getTime() < _min || date.getTime() > _max) {
					continue
				}
		
				seconds.push({
					name: `${i.toString().padStart(2, '0')}秒`,
					value: i,
				} as UxPickerData)
			}
		
			if (datas.value.length == 2) {
				datas.value.push(seconds)
			} else {
				datas.value.splice(2, 1, seconds)
			}
		} else {
			// 日期
		
			if (datas.value[0].length < values.value[0]) {
				return
			}
			let year = datas.value[0][values.value[0]].value as number
		
			if (datas.value[1].length < values.value[1]) {
				return
			}
			let month = datas.value[1][values.value[1]].value as number
		
			if (datas.value[2].length < values.value[2]) {
				return
			}
			
			let index = values.value[2] > (datas.value[2].length - 1) ? (datas.value[2].length - 1) : values.value[2]
			let day = datas.value[2][index].value as number
		
			let _date = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`
		
			// 时
			if (datas.value[3].length < values.value[3]) {
				return
			}
			let hour = datas.value[3][values.value[3]].value as number
		
			// 分
			if (datas.value[4].length < values.value[4]) {
				return
			}
			let minute = datas.value[4][values.value[4]].value as number
		
			let seconds = [] as UxPickerData[]
		
			let _min = min.getTime()
			let _max = max.getTime()
		
			for (var i = 0; i <= 59; i++) {
				// 过滤日期范围
				const str = `${_date} ${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:${i.toString().padStart(2, '0')}`
				let date = $ux.Date.toDate(str)
				if (date.getTime() < _min || date.getTime() > _max) {
					continue
				}
		
				seconds.push({
					name: `${i.toString().padStart(2, '0')}秒`,
					value: i,
				} as UxPickerData)
			}
		
			if(values.value[5] > seconds.length - 1) {
				values.value[5] = seconds.length - 1
			}
		
			if (datas.value.length == 5) {
				datas.value.push(seconds)
			} else {
				datas.value.splice(5, 1, seconds)
			}
		}
	}
	
	function setDefault() {
		let defaults = [] as number[]
		
		let now = new Date()
		
		if (props.value.length > 0 && props.value[0] != '') {
			now = $ux.Date.toDate(props.value[0])
		}
		
		if (props.mode == 'date') {
			defaults = [now.getFullYear(), now.getMonth() + 1, now.getDate()]
		} else if (props.mode == 'datetime') {
			defaults = [now.getFullYear(), now.getMonth() + 1, now.getDate(), now.getHours(), now.getMinutes(), now.getSeconds()]
		} else if (props.mode == 'datehour') {
			defaults = [now.getFullYear(), now.getMonth() + 1, now.getDate(), now.getHours()]
		} else if (props.mode == 'dateminute') {
			defaults = [now.getFullYear(), now.getMonth() + 1, now.getDate(), now.getHours(), now.getMinutes()]
		} else if (props.mode == 'hourminute') {
			defaults = [now.getHours(), now.getMinutes()]
		} else if (props.mode == 'time') {
			defaults = [now.getHours(), now.getMinutes(), now.getSeconds()]
		} else if (props.mode == 'month') {
			defaults = [now.getFullYear(), now.getMonth() + 1]
		} else if (props.mode == 'year') {
			defaults = [now.getFullYear()]
		} else if (props.mode == 'week') {
			defaults = [now.getFullYear(), now.getMonth() + 1, now.getDate()]
		}
		
		for (let i = 0; i < values.value.length; i++) {
			let index = datas.value[i].findIndex((e : UxPickerData) : boolean => e.value.toString() == defaults[i].toString()) as number
			values.value[i] = index == -1 ? 0 : index
		}
	}
	
	function getDateRange() : Date[] {
		let min = ''
		let max = ''
		
		const newDate = $ux.Date.newDate()
		if (props.mode == 'date') {
			min = `${newDate.getYear() - props.min}-01-01`
			max = `${newDate.getYear() + props.max}-12-31`
		} else if (props.mode == 'datetime') {
			min = `${newDate.getYear() - props.min}-01-01 00:00:00`
			max = `${newDate.getYear() + props.max}-12-31 23:59:59`
		} else if (props.mode == 'datehour') {
			min = `${newDate.getYear() - props.min}-01-01 00`
			max = `${newDate.getYear() + props.max}-12-31 23`
		} else if (props.mode == 'dateminute') {
			min = `${newDate.getYear() - props.min}-01-01 00:00`
			max = `${newDate.getYear() + props.max}-12-31 23:59`
		} else if (props.mode == 'hourminute') {
			min = `00:00:00`
			max = `23:59:59`
		} else if (props.mode == 'time') {
			min = `00:00:00`
			max = `23:59:59`
		} else if (props.mode == 'month') {
			min = `${newDate.getYear() - props.min}-01`
			max = `${newDate.getYear() + props.max}-12`
		} else if (props.mode == 'year') {
			min = `${newDate.getYear() - props.min}`
			max = `${newDate.getYear() + props.max}`
		} else if (props.mode == 'week') {
			min = `${newDate.getYear() - props.min}-01-01`
			max = `${newDate.getYear() + props.max}-12-31`
		}
		
		if (props.range.length >= 2) {
			min = props.range[0] as string
			max = props.range[props.range.length - 1] as string
		}
		
		return [$ux.Date.toDate(min), $ux.Date.toDate(max)]
	}
	
	function setData() {
		let range = getDateRange()
		
		let min = range[0]
		let max = range[1]
		
		if (props.mode == 'date') {
			if (values.value.length < 3) {
				values.value = [0, 0, 0]
			}
		
			setYear(min, max)
			setMonth(min, max)
			setDay(min, max)
		
		} else if (props.mode == 'datetime') {
			if (values.value.length < 6) {
				values.value = [0, 0, 0, 0, 0, 0]
			}
		
			setYear(min, max)
			setMonth(min, max)
			setDay(min, max)
			setHour(min, max)
			setMinute(min, max)
			setSecond(min, max)
		
		} else if (props.mode == 'datehour') {
			if (values.value.length < 4) {
				values.value = [0, 0, 0, 0]
			}
		
			setYear(min, max)
			setMonth(min, max)
			setDay(min, max)
			setHour(min, max)
		
		} else if (props.mode == 'dateminute') {
			if (values.value.length < 5) {
				values.value = [0, 0, 0, 0, 0]
			}
		
			setYear(min, max)
			setMonth(min, max)
			setDay(min, max)
			setHour(min, max)
			setMinute(min, max)
		
		} else if (props.mode == 'hourminute') {
			if (values.value.length < 2) {
				values.value = [0, 0]
			}
			
			setHour(min, max)
			setMinute(min, max)
		
		} else if (props.mode == 'time') {
			if (values.value.length < 3) {
				values.value = [0, 0, 0]
			}
		
			setHour(min, max)
			setMinute(min, max)
			setSecond(min, max)
		
		} else if (props.mode == 'month') {
			if (values.value.length < 2) {
				values.value = [0, 0]
			}
		
			setYear(min, max)
			setMonth(min, max)
		
		} else if (props.mode == 'year') {
			if (values.value.length < 1) {
				values.value = [0]
			}
		
			setYear(min, max)
		
		} else if (props.mode == 'week') {
			if (values.value.length < 1) {
				values.value = [0]
			}
		
			setWeek(min, max)
		}
	}
	
	function change(ev : PickerViewChangeEvent) {
		values.value = ev.detail.value
		
		setData()
	}
	
	function calendarChange(e : UxCalendarEvent) {
		calendarValues.value = e.value
	}
	
	function getResult(indexs: number[], values : string[]) : UxDatePickerEvent {
		
		let format = ''
		let date = ''
		let time = ''
		let year = 0
		let month = 0
		let day = 0
		let hour = 0
		let minute = 0
		let second = 0
		let week = 0
		
		if (props.mode == 'date') {
			date = values.join('-')
			format = $ux.Date.fmtDate(date, dateFormat())
			
			let _date = $ux.Date.toDate(date)
			year = _date.getFullYear()
			month = _date.getMonth() + 1
			day = _date.getDate()
			week = _date.getDay()
		} else if (props.mode == 'datetime') {
			date = values.slice(0, 3).join('-')
			time = values.splice(3, 6).join(':')
			format = $ux.Date.fmtDate(`${date} ${time}`, dateFormat())
			
			let _date = $ux.Date.toDate(`${date} ${time}`)
			year = _date.getFullYear()
			month = _date.getMonth() + 1
			day = _date.getDate()
			hour = _date.getHours()
			minute = _date.getMinutes()
			second = _date.getSeconds()
			week = _date.getDay()
		} else if (props.mode == 'datehour') {
			date = values.slice(0, 3).join('-')
			time = values.splice(3, 4).join(':')
			format = $ux.Date.fmtDate(`${date} ${time}`, dateFormat())
			
			let _date = $ux.Date.toDate(`${date} ${time}`)
			year = _date.getFullYear()
			month = _date.getMonth() + 1
			day = _date.getDate()
			hour = _date.getHours()
			week = _date.getDay()
		} else if (props.mode == 'dateminute') {
			date = values.slice(0, 3).join('-')
			time = values.splice(3, 5).join(':')
			format = $ux.Date.fmtDate(`${date} ${time}`, dateFormat())
			
			let _date = $ux.Date.toDate(`${date} ${time}`)
			year = _date.getFullYear()
			month = _date.getMonth() + 1
			day = _date.getDate()
			hour = _date.getHours()
			minute = _date.getMinutes()
			week = _date.getDay()
		} else if (props.mode == 'hourminute') {
			time = values.join(':')
			format = $ux.Date.fmtDate(time, dateFormat())
			
			let _date = $ux.Date.toDate(time)
			hour = _date.getHours()
			minute = _date.getMinutes()
		} else if (props.mode == 'time') {
			time = values.join(':')
			format = $ux.Date.fmtDate(time, dateFormat())
			
			let _date = $ux.Date.toDate(time)
			hour = _date.getHours()
			minute = _date.getMinutes()
			second = _date.getSeconds()
		} else if (props.mode == 'month') {
			date = values.join('-')
			format = $ux.Date.fmtDate(date, dateFormat())
			
			let _date = $ux.Date.toDate(date)
			year = _date.getFullYear()
			month = _date.getMonth() + 1
		} else if (props.mode == 'year') {
			date = values.join('-')
			format = $ux.Date.fmtDate(date, dateFormat())
			
			let _date = $ux.Date.toDate(date)
			year = _date.getFullYear()
		} else if (props.mode == 'week') {
			let dates = values.join('-')
			date = dates.split(' ')[0]
			format = $ux.Date.fmtDate(date, dateFormat())
			
			let _date = $ux.Date.toDate(date)
			year = _date.getFullYear()
			month = _date.getMonth() + 1
			day = _date.getDate()
			week = _date.getDay()
		}
		
		return {
			name: props.name,
			index: indexs,
			value: values,
			format: format,
			date: date,
			time: time,
			year: year,
			month: month,
			day: day,
			hour: hour,
			minute: minute,
			second: second,
			week: week
		} as UxDatePickerEvent
	}
	
	function getCalendarResult(): UxDatePickerEvent {
		let format = ''
		let date = ''
		let time = ''
		let year = 0
		let month = 0
		let day = 0
		let hour = 0
		let minute = 0
		let second = 0
		let week = 0
		let _values = [] as string[]
		
		if(props.selectMode == 'single') {
			if(calendarValues.value.length > 0) {
				date = calendarValues.value[0].fullDate
				format = $ux.Date.fmtDate(date, dateFormat())
				
				let _date = $ux.Date.toDate(date)
				year = _date.getFullYear()
				month = _date.getMonth() + 1
				day = _date.getDate()
				week = _date.getDay()
			}
		} else {
			_values = calendarValues.value.map((e : UxCalendarDate) : string => e.fullDate)
		}
		
		return {
			name: props.name,
			index: [] as number[],
			value: _values,
			format: format,
			date: date,
			time: time,
			year: year,
			month: month,
			day: day,
			hour: hour,
			minute: minute,
			second: second,
			week: week
		} as UxDatePickerEvent
	}
	
	function click() {
		if (props.disabled) {
			return
		}
		
		isShow.value = true
		
		if (datas.value.length == 0) {
			setData()
		}
		
		setDefault()
		
		setTimeout(() => {
			uxDrawerRef.value!.open()
		}, 50);
	}
	
	function open() {
		click()
	}
	
	function close() {
		uxDrawerRef.value!.close()
		emit('close')
	}
	
	function onMaskClose() {
		emit('close')
	}
	
	function onCancel(e: MouseEvent) {
		close()
		e.stopPropagation()
	}
	
	function onConfirm(e: MouseEvent) {
		if (props.mode == 'calendar') {
			let result = getCalendarResult()
			
			emit('update:modelValue', result.date)
			emit('change', getCalendarResult())
		} else {
			
			const _values = values.value.map((i : number, index : number) : string => {
				return datas.value[index].length == 0 ? '' : datas.value[index][i].value.toString().padStart(2, '0')
			}).filter((val : string) : boolean => val != '')
			
			let result = getResult(values.value, _values)
			
			emit('update:modelValue', result.date)
			emit('change', result)
		}
		
		close()
		
		e.stopPropagation()
	}
	
	defineExpose({
		open,
		close
	})
</script>

<style lang="scss">
	.ux-picker__slot {
		/* #ifdef MP */
		width: 100%;
		/* #endif */
	}
	
	.ux-picker {
		width: 100%;
		background-color: white;
		position: relative;

		&__title {
			width: 100%;
			height: 50px;
			padding: 0 15px;
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;

			&--text {
				font-size: 13px;
				color: #333;
			}
		}
		
		&__close {
			position: absolute;
			width: 50px;
			height: 40px;
			right: 0;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		&__btn--cancel {
			position: absolute;
			left: 8px;
			padding: 0 13px;
			background-color: white;
		}

		&__btn--cancel--bigger {
			position: absolute;
			left: 0;
			width: 50%;
			height: 100%;
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
			background-color: white;
		}

		&__btn--confirm {
			position: absolute;
			right: 8px;
			padding: 0 13px;
			background-color: white;
		}

		&__btn--confirm--bigger {
			position: absolute;
			right: 0;
			width: 50%;
			height: 100%;
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
			background-color: white;
		}

		&__btn--text {
			font-size: 14px;
		}
		
		&__btns {
			width: 100%;
			height: 70px;
			padding: 0 15px;
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
			
			&--cancel {
				margin: 0 8px;
				flex: 1;
				height: 42px;
				display: flex;
				flex-direction: row;
				justify-content: center;
				align-items: center;
				border-radius: 42px;
			}
			
			&--confirm {
				margin: 0 8px;
				flex: 1;
				height: 42px;
				display: flex;
				flex-direction: row;
				justify-content: center;
				align-items: center;
				border-radius: 42px;
			}
			
			&--text {
				font-size: 14px;
			}
		}

		&__hover {
			opacity: 0.6;
		}
	}

	.ux-picker__calendar {
		width: 100%;
		height: 340px;
	}

	.ux-picker__item {
		height: 50px;
	}

	.ux-picker__text {
		line-height: 50px;
		text-align: center;
		color: black;
	}
	
	/* #ifndef APP */
	.ux-picker__indicator::before,
	.ux-picker__indicator::after {
		border-top: 0px solid #fff;
		border-bottom: 0px solid #fff;
	}
	
	.uni-picker-view-indicator::before,
	.uni-picker-view-indicator::after, {
		visibility: hidden;
	}
	/* #endif */
</style>