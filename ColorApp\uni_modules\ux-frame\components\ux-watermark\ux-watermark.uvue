<template>
	<view :id="myId" class="ux-watermark" :style="{'position': fullscreen ? 'fixed' : ''}">
		<text v-if="mode == 'text'" :id="itemId" class="ux-watermark__item" :style="itemStyle">{{ content }}</text>
		<image v-if="mode == 'img'" :id="itemId" class="ux-watermark__item" :style="itemStyle" :src="content" mode="aspectFit"></image>
		
		<view ref="uxWatermarkRef" class="ux-watermark__body" :style="style">
			<!-- #ifdef WEB -->
			<canvas class="ux-watermark__canvas" :id="canvasId" :canvas-id="canvasId"></canvas>
			<!-- #endif -->
			
			<!-- #ifdef MP -->
			<text class="ux-watermark__text" :style="textStyle(item)" v-for="(item,index) in texts" :key="index">{{ content }}</text>
			<!-- #endif -->
		</view>
	</view>
</template>

<script setup>
	
	/**
	 * Watermark 水印
	 * @description 支持局部水印，支持全屏水印
	 * @demo pages/component/watermark.uvue
	 * @tutorial https://www.uxframe.cn/component/watermark.html
	 * @property {String}			mode=[text|img]			String | 模式 (默认 text)
	 * @value text 文本
	 * @value img 图片 暂不支持
	 * @property {String}			content					String | 水印文字
	 * @property {String}			color					String | 水印颜色 (默认 rgba(0, 0, 0, 0.2))
	 * @property {String}			darkColor				String | 深色 (默认 rrgba(255, 255, 255, 0.8))
	 * @property {Any}				size					Any | 文字大小 (默认 16)
	 * @property {Any}				imgWidth				Any | 图片宽 (默认 60)
	 * @property {Any}				imgHeight				Any | 图片高 (默认 30)
	 * @property {Any}				gutter					Any | 间隙 (默认 25)
	 * @property {Boolean}			fullscreen				Boolean | 全屏
	 * <AUTHOR>
	 * @date 2024-06-12 19:16:24
	 */
	
	import { $ux } from '../../index'
	import { useFontColor } from '../../libs/use/style.uts'
	import { useResize } from '../../libs/use/resize'
	
	type Position = {
		x : number,
		y : number
	}
	
	defineOptions({
		name: 'ux-watermark'
	})
	
	const props = defineProps({
		mode: {
			type: String,
			default: 'text'
		},
		content: {
			type: String,
			default: ''
		},
		color: {
			type: String,
			default: 'rgba(0, 0, 0, 0.2)'
		},
		darkColor: {
			type: String,
			default: 'rgba(255, 255, 255, 0.8)'
		},
		size: {
			default: 16
		},
		imgWidth: {
			default: 60
		},
		imgHeight: {
			default: 30
		},
		gutter: {
			default: 25
		},
		fullscreen: {
			type: Boolean,
			default: false
		}
	})
	
	const instance = getCurrentInstance()?.proxy
	
	const myId = `ux-watermark-${$ux.Random.uuid()}`
	const itemId = `ux-watermark-item-${$ux.Random.uuid()}`
	const uxWatermarkRef = ref<Element | null>(null)
	const canvasId = `ux-watermark-canvas-${$ux.Random.uuid()}`
	const dpr = uni.getWindowInfo().pixelRatio
	let ctx: any | null = null
	const width = ref(0)
	const height = ref(0)
	const textWidth = ref(0)
	const textHeight = ref(0)
	const texts = ref<Position[]>([])
	const isOk = ref(false)
	
	const isDark = computed((): boolean => {
		return $ux.Conf.darkMode.value
	})
	
	const content = computed((): string => {
		return props.content
	})
	
	const fontColor = computed(() : string => {
		return useFontColor(props.color, props.darkColor)
	})
	
	const style = computed(():Map<string, any> => {
		let css = new Map<string, any>()
		
		if(props.fullscreen) {
			css.set('top', `${35}%`)
		} else {
			css.set('left', `${15}%`)
		}
		
		css.set('width', `${width.value}px`)
		css.set('height', `${height.value}px`)
		
		css.set('opacity', isOk.value ? 1 : 0)
		
		if(isOk.value) {
			css.set('transform','rotate(-45deg)')
		}
		
		return css
	})
	
	const itemStyle = computed(():Map<string, any> => {
		let css = new Map<string, any>()
		
		if(props.mode == 'text') {
			css.set('font-size', `${$ux.Util.getPx(props.size)}px`)
		} else {
			css.set('width', `${$ux.Util.getPx(props.imgWidth)}px`)
			css.set('height', `${$ux.Util.getPx(props.imgHeight)}px`)
		}
		
		return css
	})
	
	function textStyle(item: Position) {
		let css = new Map<string, any>()
		
		css.set('top', `${item.y}px`)
		css.set('left', `${item.x}px`)
		
		if(props.mode == 'text') {
			css.set('font-size', `${$ux.Util.getPx(props.size)}px`)
			css.set('color', fontColor.value)
		} else {
			css.set('width', `${$ux.Util.getPx(props.imgWidth)}px`)
			css.set('height', `${$ux.Util.getPx(props.imgHeight)}px`)
		}
		
		return css
	}
	
	function calcTexts(): Position[] {
		
		let w = textWidth.value + $ux.Util.getPx(props.gutter)
		let h = textHeight.value + $ux.Util.getPx(props.gutter)
		
		let maxCol = Math.ceil(width.value / w)
		let maxRow = Math.ceil(height.value / h)
		
		let _texts = [] as Position[]
		for (let i = 0; i < maxRow; i++) {
			for (let j = 0; j < maxCol; j++) {
				_texts.push({
					x: j * w,
					y: i * h
				} as Position)
			}
		}
		
		return _texts
	}
	
	function draw() {
		// #ifdef APP
		const _ctx = ctx! as DrawableContext
		// #endif
		
		// #ifdef WEB
		const _ctx = ctx! as CanvasRenderingContext2D
		// #endif
		
		// #ifdef APP
		_ctx.reset()
		// #endif
		
		_ctx.fillStyle = 'rgba(0,0,0,0)'
		_ctx.fillRect(0, 0, width.value, height.value)
		
		_ctx.font = `${$ux.Util.getPx(props.size)}px`
		_ctx.fillStyle = fontColor.value
		_ctx.textAlign = 'left'
		
		texts.value.forEach((pos : Position) => {
			_ctx.fillText(content.value, pos.x, pos.y)
		})
		
		// #ifdef APP
		_ctx.update()
		// #endif
	}
	
	function initText() {
		uni.createSelectorQuery().in(instance).select(`#${itemId}`).boundingClientRect((rect) => {
			let node = rect as NodeInfo
			textWidth.value = node.width ?? 0
			textHeight.value = node.height ?? 0
			texts.value = calcTexts()
			
			// #ifndef MP
			draw()
			
			setTimeout(() => {
				draw()
			}, 100)
			// #endif
			
			isOk.value = true
		}).exec()
	}
	
	function init() {
		isOk.value = false
		
		uni.createSelectorQuery().in(instance).select(`#${myId}`).boundingClientRect((rect) => {
			let node = rect as NodeInfo
			
			width.value = node.width ?? uni.getWindowInfo().windowWidth
			height.value = node.height ?? uni.getWindowInfo().windowHeight
			
			width.value = Math.max(width.value, height.value)
			height.value = Math.max(width.value, height.value)
			
			let f = async () => {
				// #ifdef APP
				ctx = uxWatermarkRef.value!.getDrawableContext()
				// #endif
				// #ifdef WEB
				let context = await $ux.Util.createCanvasContext(canvasId, instance)
				ctx = context.getContext('2d')
				ctx!.canvas.width = ctx!.canvas.offsetWidth * dpr
				ctx!.canvas.height = ctx!.canvas.offsetHeight * dpr
				ctx!.scale(dpr, dpr)
				// #endif
				
				initText()
			}
			
			f()
		}).exec()
	}
	
	watch(content, () => {
		init()
	})
	
	watch(isDark, () => {
		init()
	})
	
	onMounted(() => {
		setTimeout(() => {
			init()
		}, 200)
		
		useResize(uni.getElementById(myId), () => {
			init()
		})
	})
</script>


<style lang="scss">
	.ux-watermark {
		/* #ifndef APP-ANDROID */
		pointer-events: none;
		/* #endif */
		width: 100%;
		height: 100%;
		background-color: transparent;
		position: relative;
		
		&__item {
			position: fixed;
			opacity: 0;
			display: flex;
		}
		
		&__body {
			overflow: hidden;
			position: absolute;
			background-color: transparent;
			transform-origin: left center;
		}
		
		&__canvas {
			width: 100%;
			height: 100%;
			background-color: transparent;
		}
		
		&__text {
			position: absolute;
		}
	}
</style>