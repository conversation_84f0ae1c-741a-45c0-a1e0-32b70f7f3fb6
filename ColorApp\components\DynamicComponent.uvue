<template>
	<view v-if="config" class="dynamic-component">
		<!-- 基础组件渲染 -->
		<view 
			v-if="config.component === 'view'"
			:class="config.props != null ? config.props['class'] as string : ''"
			:style="getComponentStyle(config.props)"
			@click="handleEvent('click', $event)"
		>
			<DynamicComponent 
				v-for="(child, index) in config.children" 
				:key="child.id || index"
				:config="child"
				:data-source="dataSource"
				@event="$emit('event', $event)"
			/>
			<text v-if="getDisplayValue()" class="component-text">{{ getDisplayValue() }}</text>
		</view>
		
		<!-- 输入框组件 -->
		<view v-else-if="config.component === 'input'" class="input-container">
			<text v-if="config.props != null && config.props['label'] != null" class="input-label">{{ config.props['label'] }}</text>
			<input 
				:type="config.props != null && config.props['type'] != null ? config.props['type'] as string : 'text'"
				:placeholder="config.props != null && config.props['placeholder'] != null ? config.props['placeholder'] as string : ''"
				:value="getBindValue()"
				:disabled="config.props != null && config.props['disabled'] != null ? config.props['disabled'] as boolean : false"
				:class="config.props != null ? config.props['class'] as string : ''"
				:style="getComponentStyle(config.props)"
				@input="handleInput"
				@focus="handleEvent('focus', $event)"
				@blur="handleEvent('blur', $event)"
				class="input-field"
			/>
		</view>
		
		<!-- 按钮组件 -->
		<button 
			v-else-if="config.component === 'button'"
			:disabled="config.props != null && config.props['disabled'] != null ? config.props['disabled'] as boolean : false"
			:class="config.props != null ? config.props['class'] as string : ''"
			:style="getComponentStyle(config.props)"
			@click="handleButtonClick"
			class="button-component"
		>
			<text class="button-text">{{ getDisplayValue() }}</text>
		</button>
		
		<!-- 文本组件 -->
		<text 
			v-else-if="config.component === 'text'"
			:class="config.props != null ? config.props['class'] as string : ''"
			:style="getComponentStyle(config.props)"
			@click="handleEvent('click', $event)"
		>
			{{ getDisplayValue() }}
		</text>
		
		<!-- 图片组件 -->
		<image 
			v-else-if="config.component === 'image'"
			:src="config.props != null && config.props['src'] != null ? config.props['src'] as string : ''"
			:mode="config.props != null && config.props['mode'] != null ? config.props['mode'] as string : 'aspectFit'"
			:class="config.props != null ? config.props['class'] as string : ''"
			:style="getComponentStyle(config.props)"
			@load="handleEvent('load', $event)"
			@error="handleEvent('error', $event)"
			@click="handleEvent('click', $event)"
		/>
		
		<!-- 滚动视图组件 -->
		<scroll-view 
			v-else-if="config.component === 'scroll-view'"
			:scroll-x="config.props != null && config.props['scrollX'] != null ? config.props['scrollX'] as boolean : false"
			:scroll-y="config.props != null && config.props['scrollY'] != null ? config.props['scrollY'] as boolean : true"
			:class="config.props != null ? config.props['class'] as string : ''"
			:style="getComponentStyle(config.props)"
			@scroll="handleEvent('scroll', $event)"
		>
			<DynamicComponent 
				v-for="(child, index) in config.children" 
				:key="child.id || index"
				:config="child"
				:data-source="dataSource"
				@event="$emit('event', $event)"
			/>
		</scroll-view>
		
		<!-- 列表组件 -->
		<list-view 
			v-else-if="config.component === 'list-view'"
			:class="config.props != null ? config.props['class'] as string : ''"
			:style="getComponentStyle(config.props)"
			@scroll="handleEvent('scroll', $event)"
		>
			<list-item 
				v-for="(item, index) in getListData()" 
				:key="index"
				:class="config.props != null ? config.props['itemClass'] as string : ''"
			>
				<DynamicComponent 
					v-for="(child, childIndex) in config.children" 
					:key="child.id || childIndex"
					:config="child"
					:data-source="item"
					@event="$emit('event', $event)"
				/>
			</list-item>
		</list-view>
		
		<!-- 未知组件类型 -->
		<view v-else class="unknown-component">
			<text class="unknown-text">未知组件类型: {{ config.component }}</text>
		</view>
	</view>
</template>

<script setup lang="uts">
	import { ref, computed, watch } from 'vue'
	
	// 组件属性定义
	type ComponentConfig = {
		id: string
		component: string
		props?: UTSJSONObject
		children?: ComponentConfig[]
		value?: UTSJSONObject | string
		dataSource?: string
		events?: UTSJSONObject
	}
	
	type EventData = {
		type: string
		target: string
		data: any
		config: ComponentConfig
	}
	
	// Props 定义
	const props = defineProps<{
		config: ComponentConfig
		dataSource?: UTSJSONObject
	}>()
	
	// Emits 定义
	const emit = defineEmits<{
		event: [data: EventData]
	}>()
	
	// 响应式数据
	const inputValue = ref('')
	
	// 计算属性：获取显示值
	const getDisplayValue = (): string => {
		if (props.config.value != null) {
			if (typeof props.config.value === 'string') {
				return props.config.value
			} else if (props.config.value instanceof UTSJSONObject) {
				// 多语言支持
				const locale = uni.getLocale()
				const valueObj = props.config.value as UTSJSONObject
				if (valueObj[locale] != null) {
					return valueObj[locale] as string
				} else if (valueObj['zh-CN'] != null) {
					return valueObj['zh-CN'] as string
				} else if (valueObj['en-US'] != null) {
					return valueObj['en-US'] as string
				}
			}
		}
		return ''
	}
	
	// 获取数据绑定值
	const getBindValue = (): string => {
		const dataBind = props.config.props != null ? props.config.props['dataBind'] as string : null;
		if (dataBind != null && props.dataSource != null) {
			const value = props.dataSource[dataBind]
			return value != null ? value as string : ''
		}
		return inputValue.value
	}
	
	// 获取列表数据
	const getListData = (): UTSJSONObject[] => {
		if (props.dataSource != null && props.dataSource['list'] != null) {
			const listData = props.dataSource['list']
			if (listData instanceof Array) {
				return listData as UTSJSONObject[]
			}
		}
		return []
	}
	
	// 获取组件样式
	const getComponentStyle = (styleProps: UTSJSONObject | null): UTSJSONObject => {
		const style: UTSJSONObject = {}
		
		if (styleProps != null) {
			// 处理 flex 相关样式
			if (styleProps['flexDirection'] != null) {
				style['flex-direction'] = styleProps['flexDirection']
			}
			if (styleProps['justifyContent'] != null) {
				style['justify-content'] = styleProps['justifyContent']
			}
			if (styleProps['alignItems'] != null) {
				style['align-items'] = styleProps['alignItems']
			}
			if (styleProps['flex'] != null) {
				style['flex'] = styleProps['flex']
			}
			
			// 处理尺寸样式
			if (styleProps['width'] != null) {
				style['width'] = styleProps['width']
			}
			if (styleProps['height'] != null) {
				style['height'] = styleProps['height']
			}
			
			// 处理边距样式
			if (styleProps['margin'] != null) {
				style['margin'] = styleProps['margin']
			}
			if (styleProps['padding'] != null) {
				style['padding'] = styleProps['padding']
			}
			
			// 处理背景和边框
			if (styleProps['backgroundColor'] != null) {
				style['background-color'] = styleProps['backgroundColor']
			}
			if (styleProps['borderRadius'] != null) {
				style['border-radius'] = styleProps['borderRadius']
			}
		}
		
		return style
	}
	
	// 处理输入事件
	const handleInput = (event: InputEvent) => {
		const value = event.detail.value
		inputValue.value = value
		
		// 数据绑定更新
		const dataBind = props.config.props != null ? props.config.props['dataBind'] as string : null
		if (dataBind != null && props.dataSource != null) {
			props.dataSource[dataBind] = value
		}
		
		// 触发事件
		handleEvent('input', event)
	}
	
	// 处理按钮点击事件
	const handleButtonClick = (event: Event) => {
		// 处理配置中的事件
		if (props.config.events != null && props.config.events['click'] != null) {
			const clickEvents = props.config.events['click']
			if (clickEvents instanceof Array) {
				const eventArray = clickEvents as UTSJSONObject[]
				for (let i = 0; i < eventArray.length; i++) {
					const eventConfig = eventArray[i]
					executeEvent(eventConfig)
				}
			}
		}
		
		// 触发通用事件
		handleEvent('click', event)
	}
	
	// 执行事件配置
	const executeEvent = (eventConfig: UTSJSONObject) => {
		const eventType = eventConfig['type'] as string
		
		switch (eventType) {
			case 'validateForm':
				// 表单验证逻辑
				const target = eventConfig['target'] as string
				console.log('验证表单:', target)
				break
				
			case 'apiRequest':
				// API 请求逻辑
				const options = eventConfig['options'] as UTSJSONObject
				console.log('API 请求:', options)
				// 这里可以集成实际的 API 请求逻辑
				break
				
			case 'redirect':
				// 页面跳转逻辑
				const path = eventConfig['path'] as string
				uni.navigateTo({
					url: path
				})
				break
				
			default:
				console.log('未知事件类型:', eventType)
		}
	}
	
	// 处理通用事件
	const handleEvent = (type: string, event: any) => {
		const eventData: EventData = {
			type: type,
			target: props.config.id,
			data: event.detail || event,
			config: props.config
		}
		
		emit('event', eventData)
	}
</script>

<style scoped>
	.dynamic-component {
		flex-direction: column;
	}
	
	.input-container {
		flex-direction: column;
		margin-bottom: 15px;
	}
	
	.input-label {
		font-size: 14px;
		color: #333333;
		margin-bottom: 5px;
	}
	
	.input-field {
		height: 40px;
		padding: 0 12px;
		background-color: #ffffff;
		border: 1px solid #e0e0e0;
		border-radius: 4px;
		font-size: 14px;
	}
	
	.button-component {
		height: 44px;
		background-color: #007aff;
		border-radius: 4px;
		border: none;
		align-items: center;
		justify-content: center;
		margin-top: 15px;
	}
	
	.button-text {
		color: #ffffff;
		font-size: 16px;
		font-weight: bold;
	}
	
	.component-text {
		font-size: 14px;
		color: #333333;
	}
	
	.unknown-component {
		padding: 20px;
		background-color: #fff3cd;
		border: 1px solid #ffeaa7;
		border-radius: 4px;
		align-items: center;
		justify-content: center;
	}
	
	.unknown-text {
		color: #856404;
		font-size: 14px;
	}
	
	/* 主题样式类 */
	.bg-primary {
		background-color: #007aff;
	}
	
	.text-white {
		color: #ffffff;
	}
	
	.text-center {
		text-align: center;
	}
	
	.flex-column {
		flex-direction: column;
	}
	
	.flex-row {
		flex-direction: row;
	}
	
	.justify-center {
		justify-content: center;
	}
	
	.align-center {
		align-items: center;
	}
</style>