import androidx.appcompat.app.AppCompatActivity
import android.app.Activity
import android.graphics.Color
import android.os.Bundle
import android.view.Gravity
import android.view.ViewGroup
import android.view.View
import android.widget.TextView
import android.widget.Button
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.webkit.WebView
import android.webkit.WebViewClient
import android.webkit.WebResourceRequest
import android.animation.ObjectAnimator
import android.animation.Animator
import android.os.Handler
import android.os.Looper
import uts.sdk.modules.uxPlus.R
import io.dcloud.uts.UTSAndroid
import io.dcloud.uts.console

object UxOpenWeb {
	
	fun show(title: String, url: String, darkMode: Boolean, close: () -> Unit): Boolean {
		try {
			val activity = UTSAndroid.getUniActivity()!!
			val frameContent = activity.window.decorView.findViewById<FrameLayout>(android.R.id.content)
			
			var popupView = activity.layoutInflater.inflate(R.layout.popup_webview, null) as LinearLayout
			var webView = popupView.findViewById<WebView>(R.id.webview)
			val titleView = popupView.findViewById<TextView>(R.id.title)
			val closeBtn = popupView.findViewById<Button>(R.id.btn_close)
			
			titleView.setText(title)
			
			if(darkMode) {
				popupView.setBackgroundColor(Color.BLACK)
				titleView.setTextColor(Color.WHITE)
				closeBtn.setBackgroundResource(R.drawable.ic_close_dark)
			} else {
				popupView.setBackgroundColor(Color.WHITE)
				titleView.setTextColor(Color.BLACK)
				closeBtn.setBackgroundResource(R.drawable.ic_close)
			}
			
			webView.settings.javaScriptEnabled = true
			webView.webViewClient = object : WebViewClient() {
			    override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
			        return super.shouldOverrideUrlLoading(view, request)
			    }
			}
			
			val backgroundView = View(activity)
			backgroundView.layoutParams = FrameLayout.LayoutParams(
			    FrameLayout.LayoutParams.MATCH_PARENT,
			    FrameLayout.LayoutParams.MATCH_PARENT
			)
			backgroundView.setBackgroundColor(Color.argb(128, 0, 0, 0))
			
			val height = frameContent.height.toInt() - 100
			
			val layoutParams = FrameLayout.LayoutParams(
			    ViewGroup.LayoutParams.MATCH_PARENT,
			    height
			)
			
			layoutParams.gravity = Gravity.BOTTOM
			frameContent.addView(backgroundView)
			frameContent.addView(popupView, layoutParams)
			
			val animator = ObjectAnimator.ofFloat(popupView, "translationY", frameContent.height.toFloat(), 0f)
			animator.duration = 300
			animator.start()
			
			val handler = Handler(Looper.getMainLooper())
			handler.postDelayed({
			    webView.loadUrl(url)
			}, 600)
			
			closeBtn.setOnClickListener { _ ->
			    val _animator = ObjectAnimator.ofFloat(popupView, "translationY", 0f, frameContent.height.toFloat())
			    _animator.duration = 300
			    _animator.addListener(object: Animator.AnimatorListener {
			        override fun onAnimationStart(animation: Animator) {
			        }
			    
			        override fun onAnimationEnd(animation: Animator) {
			            frameContent.removeView(popupView)
						frameContent.removeView(backgroundView)
			        }
			    
			        override fun onAnimationCancel(animation: Animator) {
			        }
			    
			        override fun onAnimationRepeat(animation: Animator) {
			        }
			    })
				close()
			    _animator.start()
			}
			
			return true
		} catch(err: Error) {
			console.error(err)
			return false
		}
	}
}