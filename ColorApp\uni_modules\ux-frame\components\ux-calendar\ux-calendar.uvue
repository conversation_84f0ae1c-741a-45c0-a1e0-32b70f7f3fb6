<template>
	<view :id="myId" class="ux-calendar">
		<view class="ux-calendar__header">
			<view class="ux-calendar__header__left">
				<ux-button icon="double-left" theme="text" :icon-size="16" @click="preYear()"></ux-button>
				<ux-button icon="arrowleft" theme="text" :icon-size="16" @click="preMonth()"></ux-button>
			</view>
			<text class="ux-calendar__header__date" :style="textStyle">{{ dateText }}</text>
			<view class="ux-calendar__header__right">
				<ux-button icon="arrowright" theme="text" :icon-size="16" @click="nextMonth()"></ux-button>
				<ux-button icon="double-right" theme="text" :icon-size="16" @click="nextYear()"></ux-button>
			</view>
		</view>
		<view ref="weekEle" class="ux-calendar__week" :style="weekStyle">
			<!-- #ifndef APP -->
			<text class="ux-calendar__week--text" :style="{'color': color}" v-for="(week, index) in weekDays" :key="index">{{ week }}</text>
			<!-- #endif -->
		</view>
		<swiper class="ux-calendar__swiper" 
			:current="eleIndex" 
			:disable-touch="!touchable || selectMode == 'range'" 
			:circular="true"
			:indicator-dots="false" 
			:autoplay="false" 
			:duration="200"
			@change="eleChange">
			<swiper-item class="ux-calendar__swiper--item">
				<view android-layer-type="hardware" ref="month0Ele" class="ux-calendar__month"
					@touchstart="touchstart" @touchmove="touchmove" @touchend="touchend" @touchcancel="touchend"
					@mousedown="touchstartPc" @mousemove="touchmovePc" @mouseup="touchendPc" @mouseleave="mouseleave">
						<!-- #ifndef APP -->
						<canvas class="ux-calendar__month__canvas" :id="month0ID" :canvas-id="month0ID"></canvas>
						<!-- #endif -->
				</view>
			</swiper-item>
			<swiper-item class="ux-calendar__swiper--item">
				<view android-layer-type="hardware" ref="month1Ele" class="ux-calendar__month"
					@touchstart="touchstart" @touchmove="touchmove" @touchend="touchend" @touchcancel="touchend"
					@mousedown="touchstartPc" @mousemove="touchmovePc" @mouseup="touchendPc" @mouseleave="mouseleave">
						<!-- #ifndef APP -->
						<canvas class="ux-calendar__month__canvas" :id="month1ID" :canvas-id="month1ID"></canvas>
						<!-- #endif -->
				</view>
			</swiper-item>
			<swiper-item class="ux-calendar__swiper--item">
				<view android-layer-type="hardware" ref="month2Ele" class="ux-calendar__month"
					@touchstart="touchstart" @touchmove="touchmove" @touchend="touchend" @touchcancel="touchend"
					@mousedown="touchstartPc" @mousemove="touchmovePc" @mouseup="touchendPc" @mouseleave="mouseleave">
						<!-- #ifndef APP -->
						<canvas class="ux-calendar__month__canvas" :id="month2ID" :canvas-id="month2ID"></canvas>
						<!-- #endif -->
				</view>
			</swiper-item>
		</swiper>

		<view v-if="showToday" class="ux-calendar__today" @click="today()">
			<text class="ux-calendar__today__text">今日</text>
		</view>
	</view>
</template>

<script setup>

	/**
	 * Calendar 日历
	 * @description 支持农历显示、节假日显示、单选、多选、拖拽范围选择模式
	 * @demo pages/component/calendar.uvue
	 * @tutorial https://www.uxframe.cn/component/calendar.html
	 * @property {String}		format=[yyyy-MM-dd|yyyy/MM/dd|yyyy年MM月dd日|yyyy年MM月dd日 周|yyyy年MM月dd日 星期|yyyy年MM月dd日 礼拜]		String | 格式化规则
	 * @property {Array}		value									String[] | 值 (默认 [now])
	 * @property {Array}		range									String[] | 范围 (例如 ['2000-01-01', '2023-12-10'])
	 * @property {String}		selectMode=[single|muti|range]			String | 选择模式 (默认 single)
	 * @value single 单选
	 * @value muti 多选
	 * @value range 范围
	 * @property {Boolean}		lunar=[true|false]						Boolean | 显示农历 (默认 true)
	 * @value true
	 * @value false
	 * @property {Boolean}		holiday=[true|false]					Boolean | 显示节日 (默认 true)
	 * @value true
	 * @value false
	 * @property {Boolean}		touchable=[true|false]					Boolean | 滑动翻页 (默认 false)
	 * @value true
	 * @value false
	 * @property {String}		theme									String | 主题 (默认 $ux.Conf.primaryColor)
	 * @property {String}		color									String | 颜色 优先级高于主题
	 * @property {String}		background								String | 背景色 (默认 $ux.Conf.backgroundColor)
	 * @property {String} 		backgroundDark=[none|auto|color]		String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Boolean}		showToday=[true|false]					Boolean | 显示今日按钮 (默认 false)
	 * @value true
	 * @value false
	 * @property {Boolean}		disabled=[true|false]					Boolean | 是否禁用 (默认 false)
	 * @value true
	 * @value false
	 * @event {Function}		click									Function | 点击时触发
	 * @event {Function}		change									Function(UxPickerEvent) | 确定选择时触发
	 * <AUTHOR>
	 * @date 2023-12-15 01:20:08
	 */
	
	import { $ux } from '../../index'
	import { UxCalendarEvent, UxCalendarDate } from '../../libs/types/types.uts'
	import { useThemeColor, useFontColor, useBorderColor, UxThemeType } from '../../libs/use/style.uts'
	import { useResize } from '../../libs/use/resize'
	import { Calendar } from './calendar.uts'
	
	type CoordsType = {
		x : number;
		y : number;
		width : number;
		height : number;
		data : UxCalendarDate
	}
	
	defineOptions({
		name: 'ux-calendar'
	})
	
	const emit = defineEmits(['change', 'click', 'touchstart', 'touchmove', 'touchend'])
	
	const props = defineProps({
		name: {
			type: String,
			default: ''
		},
		theme: {
			type: String,
			default: 'primary'
		},
		format: {
			type: String,
			default: ''
		},
		value: {
			type: Array as PropType<Array<string>>,
			default: () : string[] => {
				return [] as string[]
			}
		},
		range: {
			type: Array as PropType<Array<string>>,
			default: () : string[] => {
				return [] as string[]
			}
		},
		selectMode: {
			type: String,
			default: 'single'
		},
		lunar: {
			type: Boolean,
			default: true
		},
		holiday: {
			type: Boolean,
			default: true
		},
		touchable: {
			type: Boolean,
			default: true
		},
		color: {
			type: String,
			default: ''
		},
		background: {
			type: String,
			default: ''
		},
		backgroundDark: {
			type: String,
			default: ''
		},
		showToday: {
			type: Boolean,
			default: false
		},
		disabled: {
			type: Boolean,
			default: false
		}
	})
	
	let instance = getCurrentInstance()?.proxy
	
	const myId = `ux-calendar-${$ux.Random.uuid()}`	
	const myCalendar = new Calendar() as Calendar
	
	const eleIndex = ref(0)
	const weekEle = ref<Element | null>(null)
	const month0Ele = ref<Element | null>(null)
	const month1Ele = ref<Element | null>(null)
	const month2Ele = ref<Element | null>(null)
	
	let month0Canvas: any | null = null
	let month1Canvas: any | null = null
	let month2Canvas: any | null = null
	
	let month0Ctx: any | null = null
	let month1Ctx: any | null = null
	let month2Ctx: any | null = null
	
	let monthWidth = 0
	let monthHeight = 0
	let monthLeft = 0
	let monthTop = 0
	
	const dpr = uni.getWindowInfo().pixelRatio
	
	const month0ID = `month0-${$ux.Random.uuid()}`
	const month1ID = `month1-${$ux.Random.uuid()}`
	const month2ID = `month2-${$ux.Random.uuid()}`
	
	const weeks = ref<Array<Array<UxCalendarDate>>>([] as Array<Array<UxCalendarDate>>)
	const nextWeeks = ref<Array<Array<UxCalendarDate>>>([] as Array<Array<UxCalendarDate>>)
	const lastWeeks = ref<Array<Array<UxCalendarDate>>>([] as Array<Array<UxCalendarDate>>)
	
	const weekDays = ['一', '二', '三', '四', '五', '六', '日']
		
	const coords = ref<Array<CoordsType>>([] as Array<CoordsType>)
		
	const timeData = ref<UxCalendarDate>({
		_date: new Date(),
		fullDate: '',
		timestamp: 0,
		year: 0,
		month: 0,
		date: 0,
		day: 0,
		lunar: '',
		holiday: '',
		isToday: false,
		disabled: false,
	} as UxCalendarDate)
		
	const timeDatas = ref<UxCalendarDate[]>([] as UxCalendarDate[])
	
	const isPressed = ref(false)
	const touches = ref<Touch[]>([] as Touch[])
	const selectFirst = ref(false)
	
	const darkMode = computed(():boolean => {
		return $ux.Conf.darkMode.value
	})
	
	const color = computed(():string => {
		return useFontColor('', '')
	})
	
	const selectColor = computed(():string => {
		if(props.color != '') {
			return useFontColor(props.color, 'auto')
		} else {
			return useThemeColor(props.theme as UxThemeType)
		}
	})
	
	const borderColor = computed(():string => {
		return useBorderColor('',  '')
	})
	
	const monthColor = computed(():string => {
		return darkMode.value ? '#444444' : '#f2f2f2'
	})
	
	const textStyle = computed(():string => {
		return `color: ${color.value}`
	})
	
	const weekStyle = computed(():string => {
		return `border-bottom: 1rpx solid ${borderColor.value}`
	})
	
	const min = computed(():number => {
		if (props.range.length >= 2) {
			return $ux.Date.toDate(props.range[0] as string).getTime()
		}
		
		return 0
	})
	
	const max = computed(():number => {
		if (props.range.length >= 2) {
			return $ux.Date.toDate(props.range[props.range.length - 1] as string).getTime()
		}
		
		return 0
	})
	
	const dateText = computed(():string => {
		if (props.selectMode == 'muti') {
			if (timeDatas.value.length == 0) {
		
			} else if (timeDatas.value.length == 1) {
				return $ux.Date.fmtDate(timeDatas.value[0].fullDate, 'yyyy年MM月dd日')
			} else {
				return `${$ux.Date.fmtDate(timeDatas.value[0].fullDate, 'yyyy年MM月dd日')} 等${timeDatas.value.length}个日期`
			}
		} else if (props.selectMode == 'range') {
			if (timeDatas.value.length == 0) {
		
			} else if (timeDatas.value.length == 1) {
				return $ux.Date.fmtDate(timeDatas.value[0].fullDate, 'yyyy年MM月dd日')
			} else {
				return `${$ux.Date.fmtDate(timeDatas.value[0].fullDate, 'yyyy年MM月dd日')} ~ ${$ux.Date.fmtDate(timeDatas.value[timeDatas.value.length - 1].fullDate, 'yyyy年MM月dd日')}`
			}
		}
		
		return $ux.Date.fmtDate(timeData.value.fullDate, props.format)
	})
	
	const currentEle = computed(():Element => {
		return eleIndex.value == 0 ? month0Ele.value! : (eleIndex.value == 1 ? month1Ele.value! : month2Ele.value!)
	})
	
	const currentCtx = computed(():any | null => {
		return eleIndex.value == 0 ? month0Ctx : (eleIndex.value == 1 ? month1Ctx : month2Ctx)
	})
	
	const currentCanvas = computed(():any | null => {
		return eleIndex.value == 0 ? month0Canvas : (eleIndex.value == 1 ? month1Canvas : month2Canvas)
	})
	
	function change() {
		emit('change', {
			name: props.name,
			value: timeDatas.value
		} as UxCalendarEvent)
	}
	
	async function initSize() {
		const rect = await currentEle.value.getBoundingClientRectAsync()!
		monthWidth = rect.width
		monthHeight = rect.height
		monthLeft = rect.left
		monthTop = rect.top
	}
	
	function selectDate(touches : UniTouch[]): UxCalendarDate | null {
		const clientX = touches[0].clientX
		const clientY = touches[0].clientY
		
		const x = clientX - monthLeft
		const y = clientY - monthTop
		
		for (let i = 0; i < coords.value.length; i++) {
			const grid = coords.value[i]
		
			const maxX = grid.x + grid.width
			const maxY = grid.y + grid.height
		
			const isSelect = (grid.x < x && x < maxX) && (grid.y < y && y < maxY)
		
			if (isSelect) {
				return grid.data
			}
		}
		
		return null
	}
	
	function yearText(time: string): string {
		return `${time == '' ? '' : time.split('-')[0]} /`
	}
	
	function monthText(month: number): string {
		return `${month == 0 ? 12 : (month == 13 ? 1 : month)}`.padStart(2, '0')
	}
	
	function luarText(day: UxCalendarDate): string {
		let lunar = day.lunar
		
		if (props.holiday && day.holiday != '') {
			lunar = day.holiday
		}
		
		return lunar
	}
	
	function daySelected(day: UxCalendarDate, time: string): boolean {
		let selected: boolean
		
		if (props.selectMode == 'single') {
			selected = time == day.fullDate
		} else {
			let index = timeDatas.value.findIndex((e : UxCalendarDate) : boolean => e.fullDate == day.fullDate)
			selected = index != -1
		}
		
		return selected
	}
	
	function dayDisabled(day: UxCalendarDate): boolean {
		let disabled = false
		if (min.value > 0 && max.value > 0) {
			disabled = day.timestamp < min.value || day.timestamp > max.value
		}
		if (day.disabled || disabled) {
			return true
		}
		
		return false
	}
	
	function dayColor(day: UxCalendarDate) : string {
		let _color: string
		
		if(dayDisabled(day)) {
			_color = $ux.Color.getRgba(color.value, 0.5)
		} else if(daySelected(day, timeData.value.fullDate)) {
			_color = '#ffffff'
		} else {
			_color = color.value
		}
		
		return _color
	}
	
	function drawWeek() {
		// #ifdef APP
		if (weekEle.value == null) return
			
		let ctx = weekEle.value?.getDrawableContext()!
		
		let rect = weekEle.value?.getBoundingClientRect()
		const width = (rect?.width ?? 0) / weekDays.length
		
		for (let i = 0; i < weekDays.length; i++) {
			let left = i * width + 2
			let w = width - 4
			let h = 26
			
			const text = weekDays[i]
			let textLeft = w / 2 + left
			let textTop = h / 2 + 6
			
			ctx.font = '12px'
			ctx.textAlign = 'center'
			ctx.fillStyle = color.value
			ctx.fillText(text, textLeft, textTop)
			ctx.update()
		}
		// #endif
	}
	
	function drawMonth(weekType : number, ele : Element | null, ctx: any | null, weeks : Array<Array<UxCalendarDate>>, time : string, month : number) {
		if (ele == null) {
			return
		}
		
		const width = monthWidth / weeks[0].length
		const height = monthHeight / weeks.length
		
		if (ctx == null) {
			return
		}
		
		// #ifdef APP
		ctx = ctx as DrawableContext
		// #endif
			
		if (time != '') {
			coords.value = []
		}
		
		// #ifdef APP
		ctx.font = '180px'
		// #endif
		// #ifndef APP
		ctx.font = '180px Arial'
		// #endif
		ctx.fillStyle = monthColor.value
		ctx.textAlign = 'center'
		ctx.fillText(monthText(month), monthWidth / 2, monthHeight / 2 + 60)
		
		// #ifdef APP
		ctx.font = '28px'
		// #endif
		// #ifndef APP
		ctx.font = '28px Arial'
		// #endif
		ctx.fillText(yearText(time), monthWidth / 2 - 140, monthHeight / 2 - 35)
		
		for (let weekIndex = 0; weekIndex < weeks.length; weekIndex++) {
			for (let dayIndex = 0; dayIndex < weeks[weekIndex].length; dayIndex++) {
				const day = weeks[weekIndex][dayIndex]
			
				let left = dayIndex * width
				let top = weekIndex * height
			
				let padding = 3
			
				let dayWidth = width
				let dayHeight = height
			
				let text = day.date.toString()
				let textLeft = dayIndex * width + (width / 2)
				let textTop = weekIndex * height + (props.lunar ? 22 : 29)
			
				let selected = daySelected(day, time)
				
				// #ifdef APP
				ctx.font = '16px'
				// #endif
				// #ifndef APP
				ctx.font = '16px Arial'
				// #endif
				ctx.textAlign = 'center'
				
				let drawText = () => {
					ctx.fillStyle = dayColor(day)
					ctx.fillText(text, textLeft, textTop)
				}
			
				let drawCircle = () => {
					ctx.beginPath()
					ctx.arc(left + (width / 2), top + (height / 2), 20, 0, Math.PI * 2)
					ctx.closePath()
			
					ctx.fillStyle = selectColor.value
					ctx.fill()
				}
			
				let drawStart = () => {
					ctx.fillStyle = $ux.Color.getRgba(selectColor.value, 0.4)
			
					if (timeDatas.value.length > 1) {
						let t = top + padding
						let h = height - padding * 2
						ctx.fillRect(left + width / 2, t, width / 2, h)
					}
			
					ctx.beginPath()
					ctx.arc(left + (width / 2), top + (height / 2), 20, 0, Math.PI * 2)
					ctx.closePath()
			
					ctx.fillStyle = selectColor.value
					ctx.fill()
				}
			
				let drawEnd = () => {
					ctx.fillStyle = $ux.Color.getRgba(selectColor.value, 0.4)
			
					if (timeDatas.value.length > 1) {
						let t = top + padding
						let h = height - padding * 2
						ctx.fillRect(left, t, width / 2, h)
					}
			
					ctx.beginPath()
					ctx.arc(left + (width / 2), top + (height / 2), 20, 0, Math.PI * 2)
					ctx.closePath()
			
					ctx.fillStyle = selectColor.value
					ctx.fill()
				}
			
				let drawRange = () => {
					let t = top + padding
					let w = width
					let h = height - padding * 2
					let r = h / 2
					
					// TODO PC等大屏圆角过尖
					
					ctx.fillStyle = $ux.Color.getRgba(selectColor.value, 0.4)
					
					if (day.day == 1) {
						ctx.beginPath()
						ctx.moveTo(left, t)
						ctx.lineTo(left + w / 2, t)
						ctx.bezierCurveTo(left + w, t + h / 2 - r, left + w, t + h / 2 + r, left + w / 2, t + h)
						ctx.lineTo(left, t + h)
						ctx.closePath()
						ctx.fill()
					} else if (day.day == 2) {
						ctx.beginPath()
						ctx.moveTo(left + w, t)
						ctx.lineTo(left + w / 2, t)
						ctx.bezierCurveTo(left, t + h / 2 - r, left, t + h / 2 + r, left + w / 2, t + h)
						ctx.lineTo(left + w, t + h)
						ctx.closePath()
						ctx.fill()
					} else {
						ctx.fillRect(left, t, w, h)
					}
				}
			
				// 日期是否禁用
				if (dayDisabled(day)) {
					drawText()
				} else {
					if (selected) {
						if (props.selectMode == 'range') {
							if (day.fullDate == timeDatas.value[0].fullDate) {
								drawStart()
							} else if (day.fullDate == timeDatas.value[timeDatas.value.length - 1].fullDate) {
								drawEnd()
							} else {
								drawRange()
							}
						} else {
							drawCircle()
						}
			
						drawText()
					} else {
						drawText()
					}
			
					if (weekType == 0) {
						coords.value.push({
							x: left,
							y: top,
							width: dayWidth,
							height: dayHeight,
							data: day
						} as CoordsType)
					}
				}
			
				if (props.lunar) {
					let lunarLeft = dayIndex * width + (width / 2)
					let lunarTop = weekIndex * height + (height / 2) + 13
			
					// #ifdef APP
					ctx.font = '9px'
					// #endif
					// #ifndef APP
					ctx.font = '9px Arial'
					// #endif
					ctx.textAlign = 'center'
					ctx.fillStyle = dayColor(day)
					ctx.fillText(luarText(day), lunarLeft, lunarTop)
				}
			}
		}
		
		// #ifdef APP
		ctx.update()
		// #endif
	}
	
	function redraw(date : string) {
		timeDatas.value = myCalendar.getDateRange(timeDatas.value)
		
		// #ifdef APP
		currentEle.value.getDrawableContext()!.reset()
		// #endif
		
		// #ifndef APP
		currentCtx.value?.clearRect(0, 0, currentCanvas.value!.width, currentCanvas.value!.height);
		// #endif
		
		drawMonth(0, currentEle.value, currentCtx.value, weeks.value, date, timeData.value.month)
		
		change()
	}
	
	function touchRange(date : UxCalendarDate) {
		timeData.value = myCalendar.getDateInfo(date.fullDate)
		
		let index = timeDatas.value.findIndex((e : UxCalendarDate) : boolean => e.fullDate == timeData.value.fullDate)
		
		if (selectFirst.value) {
			if (index == -1) {
				if (date._date.getTime() > timeDatas.value[timeDatas.value.length - 1]._date.getTime()) {
					timeDatas.value = [date, timeDatas.value[timeDatas.value.length - 1]]
					selectFirst.value = false
				} else {
					timeDatas.value.push(timeData.value)
				}
		
				redraw(date.fullDate)
			} else {
				if (date.fullDate == timeDatas.value[0].fullDate) {
					return
				}
		
				if (index < timeDatas.value.length) {
					timeDatas.value = timeDatas.value.slice(index, timeDatas.value.length)
		
					redraw(date.fullDate)
		
					if (timeDatas.value.length <= 1) {
						selectFirst.value = false
					}
				}
			}
		} else {
			if (index == -1) {
				if (date._date.getTime() < timeDatas.value[0]._date.getTime()) {
					timeDatas.value = [date, timeDatas.value[0]]
				} else {
					timeDatas.value.push(timeData.value)
				}
		
				redraw(date.fullDate)
			} else {
				if (date.fullDate == timeDatas.value[0].fullDate) {
					return
				}
				if (date.fullDate == timeDatas.value[timeDatas.value.length - 1].fullDate) {
					return
				}
		
				if (index < timeDatas.value.length) {
					timeDatas.value = timeDatas.value.slice(0, index + 1)
		
					redraw(date.fullDate)
				}
			}
		}
	}
	
	function _touchstart(e : TouchEvent, _touches: UniTouch[]) {
		emit('touchstart', e)
		
		if (props.disabled) {
			return
		}
		
		if(props.selectMode == 'range') {
			e.preventDefault()
		}
		
		isPressed.value = true
		touches.value = _touches
		
		let _date = selectDate(touches.value)
		
		if (_date != null && timeDatas.value.length > 1) {
			// 选择第一个则后退选择
			if (_date.fullDate == timeDatas.value[0].fullDate) {
				selectFirst.value = true
			}
		}
	}
	
	function touchstart(e : TouchEvent) {
		_touchstart(e, e.changedTouches)
	}
	
	function touchstartPc(e : MouseEvent) {
		// #ifndef APP
		// @ts-expect-error
		_touchstart(e, [{
			clientX: e.clientX,
			clientY: e.clientY,
			screenX: e.screenX,
			screenY: e.screenY
		}] as UniTouch[])
		// #endif
	}
	
	function _touchmove(e : TouchEvent, _touches: UniTouch[]) {
		emit('touchmove', e)
		
		if(!isPressed.value) {
			return
		}
		
		if(props.selectMode == 'range') {
			e.preventDefault()
		}
		
		if (props.disabled) {
			return
		}
		
		if (props.selectMode != 'range') {
			return
		}
		
		let _date = selectDate(_touches)
		
		if (_date != null) {
			touchRange(_date)
		}
	}
	
	function touchmove(e : TouchEvent) {
		_touchmove(e, e.changedTouches)
	}
	
	function touchmovePc(e : MouseEvent) {
		// #ifndef APP
		// @ts-expect-error
		_touchmove(e, [{
			clientX: e.clientX,
			clientY: e.clientY,
			screenX: e.screenX,
			screenY: e.screenY
		}] as UniTouch[])
		// #endif
	}
	
	async function _touchend(e : TouchEvent, _touches: UniTouch[]) {
		emit('touchend', e)
		
		if(!isPressed.value) {
			return
		}
		
		await initSize()
		
		isPressed.value = false
		selectFirst.value = false
		
		if(props.selectMode == 'range') {
			e.preventDefault()
		}
		
		if (props.disabled) {
			return
		}
		
		if (touches.value.length != 1) {
			return
		}
		
		const dx = _touches[0].clientX - touches.value[0].clientX
		const dy = _touches[0].clientY - touches.value[0].clientY
		const dis = Math.sqrt(dx * dx + dy * dy)
		
		if (dis > 0) {
			return
		}
		
		let _date = selectDate(_touches)
		
		if (_date != null) {
			timeData.value = myCalendar.getDateInfo(_date.fullDate)
		
			if (props.selectMode == 'muti') {
				let index = timeDatas.value.findIndex((e : UxCalendarDate) : boolean => e.fullDate == timeData.value.fullDate)
				if (index == -1) {
					timeDatas.value.push(timeData.value)
				} else {
					timeDatas.value.splice(index, 1)
				}
		
				// #ifdef APP
				currentEle.value.getDrawableContext()!.reset()
				// #endif
				
				// #ifndef APP
				currentCtx.value?.clearRect(0, 0, currentCanvas.value!.width, currentCanvas.value!.height);
				// #endif
				
				drawMonth(0, currentEle.value, currentCtx.value, weeks.value, _date.fullDate, timeData.value.month)
			} else if (props.selectMode == 'range') {
				touchRange(_date)
			} else {
				timeDatas.value = [timeData.value]
		
				// #ifdef APP
				currentEle.value.getDrawableContext()!.reset()
				// #endif
				
				// #ifndef APP
				currentCtx.value?.clearRect(0, 0, currentCanvas.value!.width, currentCanvas.value!.height);
				// #endif
				
				drawMonth(0, currentEle.value, currentCtx.value, weeks.value, _date.fullDate, timeData.value.month)
			}
		
			change()
			emit('click', _date.fullDate)
		}
	}
	
	function touchend(e : TouchEvent) {
		_touchend(e, e.changedTouches)
	}
	
	function touchendPc(e : MouseEvent) {
		// #ifndef APP
		// @ts-expect-error
		_touchend(e, [{
			clientX: e.clientX,
			clientY: e.clientY,
			screenX: e.screenX,
			screenY: e.screenY
		}] as UniTouch[])
		// #endif
	}
	
	function mouseleave() {
		isPressed.value = false
	}
	
	function currentWeeks(index: number): Array<Array<UxCalendarDate>> {
		if (eleIndex.value == 0) {
			switch (index){
				case 0:
					return weeks.value
				case 1:
					return nextWeeks.value
				case 2:
					return lastWeeks.value
			}
		} else if (eleIndex.value == 1) {
			switch (index){
				case 0:
					return lastWeeks.value
				case 1:
					return weeks.value
				case 2:
					return nextWeeks.value
			}
		} else if (eleIndex.value == 2) {
			switch (index){
				case 0:
					return nextWeeks.value
				case 1:
					return lastWeeks.value
				case 2:
					return weeks.value
			}
		}
		
		return weeks.value
	}
	
	function draw() {
		drawWeek()
		
		let getCtx = (_el: Element | null, _ctx: any | null): any | null => {
			// #ifdef APP
			return _ctx == null? _el?.getDrawableContext() : _ctx
			// #endif
			// #ifndef APP
			return _ctx
			// #endif
		}
		
		if (eleIndex.value == 0) {
			drawMonth(0, month0Ele.value, getCtx(month0Ele.value, month0Ctx) , currentWeeks(0), timeData.value.fullDate, timeData.value.month)
			
			drawMonth(1, month1Ele.value, getCtx(month1Ele.value, month1Ctx), currentWeeks(1), '', timeData.value.month + 1)
			
			drawMonth(2, month2Ele.value, getCtx(month2Ele.value, month2Ctx), currentWeeks(2), '', timeData.value.month - 1)
		} else if (eleIndex.value == 1) {
			drawMonth(2, month0Ele.value, getCtx(month0Ele.value, month0Ctx), currentWeeks(0), '', timeData.value.month - 1)
			
			drawMonth(0, month1Ele.value, getCtx(month1Ele.value, month1Ctx), currentWeeks(1), timeData.value.fullDate, timeData.value.month)
			
			drawMonth(1, month2Ele.value, getCtx(month2Ele.value, month2Ctx), currentWeeks(2), '', timeData.value.month + 1)
		} else if (eleIndex.value == 2) {
			drawMonth(1, month0Ele.value, getCtx(month0Ele.value, month0Ctx), currentWeeks(0), '', timeData.value.month + 1)
			
			drawMonth(2, month1Ele.value, getCtx(month1Ele.value, month1Ctx), currentWeeks(1), '', timeData.value.month - 1)
			
			drawMonth(0, month2Ele.value, getCtx(month2Ele.value, month2Ctx), currentWeeks(2), timeData.value.fullDate, timeData.value.month)
		}
	}
	
	async function reset() {
		await initSize()
		
		// #ifdef APP
		month0Ctx = month0Ele.value?.getDrawableContext()
		month1Ctx = month1Ele.value?.getDrawableContext()
		month2Ctx = month2Ele.value?.getDrawableContext()
		// #endif
		
		// #ifndef APP
		const context0 = await $ux.Util.createCanvasContext(month0ID, instance)
		month0Ctx = context0!.getContext('2d')
		month0Canvas = month0Ctx!.canvas
		month0Canvas!.width = month0Canvas!.offsetWidth * dpr
		month0Canvas!.height = month0Canvas!.offsetHeight * dpr
		month0Ctx!.scale(dpr, dpr)
		
		const context1 = await $ux.Util.createCanvasContext(month1ID, instance)
		month1Ctx = context1!.getContext('2d')
		month1Canvas = month1Ctx!.canvas
		month1Canvas!.width = month1Canvas!.offsetWidth * dpr
		month1Canvas!.height = month1Canvas!.offsetHeight * dpr
		month1Ctx!.scale(dpr, dpr)
		
		const context2 = await $ux.Util.createCanvasContext(month2ID, instance)
		month2Ctx = context2!.getContext('2d')
		month2Canvas = month2Ctx!.canvas
		month2Canvas!.width = month2Canvas!.offsetWidth * dpr
		month2Canvas!.height = month2Canvas!.offsetHeight * dpr
		month2Ctx!.scale(dpr, dpr)
		// #endif
		
		// #ifdef APP
		month0Ele.value?.getDrawableContext()?.reset()
		month1Ele.value?.getDrawableContext()?.reset()
		month2Ele.value?.getDrawableContext()?.reset()
		// #endif
		
		// #ifndef APP
		month0Ctx?.clearRect(0, 0, month0Canvas!.width, month0Canvas!.height);
		month1Ctx?.clearRect(0, 0, month1Canvas!.width, month1Canvas!.height);
		month2Ctx?.clearRect(0, 0, month2Canvas!.width, month2Canvas!.height);
		// #endif
		
		coords.value = []
		
		const time = myCalendar.getDate(timeData.value.fullDate, 0, 'month')
		weeks.value = myCalendar.getWeeks(time.fullDate)
		
		const nextTime = myCalendar.getDate(timeData.value.fullDate, 1, 'month')
		nextWeeks.value = myCalendar.getWeeks(nextTime.fullDate)
		
		const lastTime = myCalendar.getDate(timeData.value.fullDate, -1, 'month')
		lastWeeks.value = myCalendar.getWeeks(lastTime.fullDate)
		
		draw()
		
		// 解决鸿蒙第一次渲染不出来的问题
		// #ifdef APP-HARMONY
		setTimeout(() => {
			draw()
		}, 100)
		// #endif
	}
	
	function setDate(n : number, dt : string) {
		const time = myCalendar.getDate(n == 0 ? '' : timeData.value.fullDate, n, dt)
		timeData.value = myCalendar.getDateInfo(time.fullDate)
		
		if (props.selectMode == 'muti') {
		
		} else if (props.selectMode == 'range') {
		
		} else {
			timeDatas.value = [timeData.value]
		}
		
		setTimeout(() => {
			reset()
			change()
		}, 300)
	}
	
	function preYear() {
		setDate(-1, 'year')
	}
	
	function nextYear() {
		setDate(1, 'year')
	}
	
	function preMonth() {
		setDate(-1, 'month')
	}
	
	function nextMonth() {
		setDate(1, 'month')
	}
	
	function today() {
		setDate(0, 'day')
	}
	
	function eleChange(e : SwiperChangeEvent) {
		let index = e.detail.current
		
		if ((eleIndex.value + 1) % 3 == index) {
			nextMonth()
		} else {
			preMonth()
		}
		
		eleIndex.value = index
	}
	
	function setDefault() {
		if (props.value.length == 0) {
			timeData.value = myCalendar.getDateInfo()
			timeDatas.value = [timeData.value]
		} else {
			if (props.selectMode == 'muti') {
				props.value.forEach((e : any | null) => {
					timeData.value = myCalendar.getDateInfo(e! as string)
					timeDatas.value.push(timeData.value)
				})
			} else if (props.selectMode == 'range') {
				props.value.forEach((e : any | null) => {
					timeData.value = myCalendar.getDateInfo(e! as string)
					timeDatas.value.push(timeData.value)
				})
				timeDatas.value = myCalendar.getDateRange(timeDatas.value)
			} else {
				timeData.value = myCalendar.getDateInfo(props.value[0] as string)
				timeDatas.value = [timeData.value]
			}
		}
	}
	
	const selectMode = computed(():string => {
		return props.selectMode
	})
	
	const range = computed(():string[] => {
		return props.range
	})
	
	const holiday = computed(():boolean => {
		return props.holiday
	})
	
	const lunar = computed(():boolean => {
		return props.lunar
	})
	
	watch(selectMode, () => {
		setDefault()
		reset()
	})
	
	watch(range, () => {
		setDefault()
		reset()
	})
	
	watch(holiday, () => {
		setDefault()
		reset()
	})
	
	watch(lunar, () => {
		setDefault()
		reset()
	})
	
	watch(darkMode, () => {
		reset()
	})
	
	onMounted(() => {
		let f = () => {
			setDefault()
			reset()
			change()
		}
		
		uni.createSelectorQuery()
			.in(instance)
			.select('.ux-calendar')
			.boundingClientRect().exec((_) => {
				setTimeout(() => {
					f()
				}, 100);
			})
		
		useResize(uni.getElementById(myId), () => {
			reset()
		})
	})
</script>

<style lang="scss">
	.ux-calendar {
		width: 100%;
		position: relative;

		&__header {
			width: 100%;
			height: 35px;
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
			position: relative;

			&__left {
				position: absolute;
				left: 5px;
				display: flex;
				flex-direction: row;
				justify-content: flex-start;
				align-items: center;
			}

			&__right {
				position: absolute;
				right: 5px;
				display: flex;
				flex-direction: row;
				justify-content: flex-end;
				align-items: center;
			}

			&__date {
				font-weight: bold;
				font-size: 13px;
				color: black;
				margin-bottom: 2px;
			}
		}

		&__week {
			width: 100%;
			height: 35px;
			margin-bottom: 5px;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: space-around;

			&--text {
				font-size: 12px;
				color: black;
			}
		}
		
		&__swiper {
			width: 100%;
			height: 250px;
			
			&--item {
				width: 100%;
				height: 100%;
			}
		}

		&__month {
			width: 100%;
			height: 100%;
			display: flex;
			flex-direction: column;
			justify-content: flex-start;
			align-items: flex-start;
			position: relative;
			
			&__canvas {
				width: 100%;
				height: 100%;
			}
			
			&__bg {
				position: absolute;
				width: 100%;
				height: 100%;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				
				&--text {
					font-size: 180px;
					color: #f2f2f2;
				}
			}
			
			&__week {
				width: 100%;
				display: flex;
				flex-direction: row;
				justify-content: space-around;
				align-items: center;
			}
			
			&__day {
				margin-top: 4px;
				flex: 1;
				height: 37px;
				border-radius: 37px;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				
				&--text {
					font-size: 15px;
					line-height: 16px;
					color: #333333;
				}
				
				&--lunar {
					font-size: 10px;
					line-height: 12px;
					color: #666;
				}
			}
		}

		&__today {
			position: absolute;
			right: 0;
			top: 50%;
			width: 30px;
			height: 20px;
			border-top-left-radius: 50px;
			border-bottom-left-radius: 50px;
			background-color: rgba(160, 160, 160, 0.6);
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;

			&__text {
				font-size: 10px;
				line-height: 10px;
				color: white;
			}
		}
	}
</style>