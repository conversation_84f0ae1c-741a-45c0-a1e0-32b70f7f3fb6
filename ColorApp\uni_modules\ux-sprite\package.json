{"id": "ux-sprite", "displayName": "UxFrame Sprite 精灵图动画组件", "version": "1.0.1", "description": "支持android、ios、web、mp", "keywords": ["uxframe", "sprite", "精灵图", "动画"], "repository": "", "engines": {"HBuilderX": "^4.23"}, "dcloudext": {"type": "component-uts", "sale": {"regular": {"price": "98.00"}, "sourcecode": {"price": "198.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "y"}, "client": {"Vue": {"vue2": "y", "vue3": "y"}, "App": {"app-android": "y", "app-ios": "y", "app-harmony": "u"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "u", "百度": "u", "字节跳动": "u", "QQ": "u", "钉钉": "u", "快手": "u", "飞书": "u", "京东": "u"}, "快应用": {"华为": "u", "联盟": "u"}}}}}