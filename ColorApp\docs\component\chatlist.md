<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/chatlist?title=Chatlist"></Mobile>

# Chatlist
> 组件类型：UxChatlistComponentPublicInstance

支持上拉加载聊天记录

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| default | Slot |  | 默认slot |
| loadmore | Slot |  | loadmoreslot |
| upperThreshold | Number | 50 | 距顶部/左边多远时(单位px)，触发scrolltoupper事件 |
| lowerThreshold | Number | 50 | 距底部/右边多远时(单位px)，触发scrolltolower事件 |
| scrollTop | Number | 0 | 设置竖向滚动条位置 |
| scrollIntoView | String |  | 值应为某子元素id(id不能以数字开头)。设置哪个方向可滚动，则在哪个方向滚动到该元素 |
| loadmoreStates | Array |  | 上拉加载状态文案 |
| background | String |  | 背景色 |
| xstyle | Array |  | 自定义样式 |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| loadmore | 上拉加载触发 |  |
| scrolltoupper | 滚动到顶部/左边，会触发scrolltoupper事件 |  |
| scrolltolower | 滚动到底部/右边，会触发scrolltolower事件 |  |
| scroll | 滚动时触发 |  |
  
  