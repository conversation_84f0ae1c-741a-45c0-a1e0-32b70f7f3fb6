import ByteArrayOutputStream from "java.io.ByteArrayOutputStream";
import CompressFormat from "android.graphics.Bitmap.CompressFormat"
import Bitmap from "android.graphics.Bitmap";
import BitmapFactory from "android.graphics.BitmapFactory";
import URL from "java.net.URL";
import BufferedInputStream from "java.io.BufferedInputStream";
import HttpURLConnection from "java.net.HttpURLConnection";
import FileProvider from "androidx.core.content.FileProvider";
import Intent from "android.content.Intent";
import File from "java.io.File";
import Build from "android.os.Build";

export default class Util {
	
	static getBitmap(path ?: string, width : number, heitht : number, callback : (bitmap : Bitmap | null, bytes : ByteArray | null) => void) {
		if (path == null || path == '') {
			callback(null, null)
		} else {
			let isPng = path.toLowerCase().indexOf('.png') != -1
			
			if (this.isNetworkImage(path)) {
				this.getNetworkBitmap(path, (bmp : Bitmap | null) => {
					if (bmp != null) {
						let thumbBmp = Bitmap.createScaledBitmap(bmp, width.toInt(), heitht.toInt(), true);
						callback(bmp, this.bmpToByteArray(thumbBmp, true, isPng))
					} else {
						callback(null, null)
					}
				})
			} else {
				try {
					let bmp = BitmapFactory.decodeFile(UTSAndroid.getResourcePath(path))
					if(bmp == null) {
						bmp = BitmapFactory.decodeFile(UTSAndroid.convert2AbsFullPath(path))
					}
					
					if(bmp == null) {
						let _path = UTSAndroid.getResourcePath(UTSAndroid.convert2AbsFullPath(path))
						bmp = BitmapFactory.decodeFile(_path)
					}
					
					if(bmp == null) {
						bmp = BitmapFactory.decodeFile(path)
					}
					
					if (bmp != null) {
						let thumbBmp = Bitmap.createScaledBitmap(bmp, width.toInt(), heitht.toInt(), true);
						callback(bmp, this.bmpToByteArray(thumbBmp, true, isPng))
					} else {
						callback(null, null)
					}
				} catch (e) {
					console.error(e);
					callback(null, null)
				}
			}
		}
	}

	static getNetworkBitmap(path ?: string, callback : (bitmap : Bitmap | null) => void) {
		if (path == null) {
			callback(null)
		}

		UTSAndroid.getDispatcher("io").async(function (_) {
			if (!Thread.currentThread().name.contains("DefaultDispatcher")) {
				callback(null)
				return
			}
		
			try {
				let connection = new URL(path!).openConnection() as HttpURLConnection
				connection.connect()
		
				let inputStream = new BufferedInputStream(connection.inputStream)
				let bitmap = BitmapFactory.decodeStream(inputStream)
		
				inputStream.close()
		
				callback(bitmap)
			} catch (e) {
				console.error(e);
				callback(null)
			}
		}, null)
	}

	static bmpToByteArray(bmp : Bitmap, needRecycle : boolean, isPng : boolean) : ByteArray {
		let output = new ByteArrayOutputStream();

		bmp.compress(isPng ? CompressFormat.PNG : CompressFormat.JPEG, 90, output);

		let result = output.toByteArray();

		try {
			output.close();
			
			if (needRecycle) {
				bmp.recycle();
			}
		} catch (e) {
			e.printStackTrace();
		}

		return result;
	}

	static isNetworkImage(imagePath : String) : boolean {
		return imagePath.startsWith("http://") || imagePath.startsWith("https://")
	}
	
	static getPath(path: String): String {
		if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
			let filePath = path + ''
			let file = new File(filePath)
			
			if (!file.exists()) {
				return path
			}
			
			let contentUri = FileProvider.getUriForFile(UTSAndroid.getAppContext()!, UTSAndroid.getAppContext()!.getPackageName() + ".uxWeixin.fileprovider", file)
			
			// 授权给微信访问路径
			UTSAndroid.getAppContext()!.grantUriPermission("com.tencent.mm", contentUri, Intent.FLAG_GRANT_READ_URI_PERMISSION)
			
			return contentUri.toString();
		} else {
			return path
		}
	}

	static requestPermission(callback : (ok : boolean) => void) {
		let permissionNeed = ["android.permission.INTERNET"]
		// if (UTSAndroid.getSystemPermissionDenied(UTSAndroid.getUniActivity()!, permissionNeed).isEmpty()) {
		// 	callback(false)
		// 	return;
		// }

		UTSAndroid.requestSystemPermission(UTSAndroid.getUniActivity()!, permissionNeed, function (allRight : boolean, _ : string[]) {
			if (allRight) {
				if (!UTSAndroid.getSystemPermissionDenied(UTSAndroid.getUniActivity()!, permissionNeed).isEmpty()) {
					callback(false)
					return;
				}

				if (!UTSAndroid.checkSystemPermissionGranted(UTSAndroid.getUniActivity()!, permissionNeed)) {
					callback(false)
					return;
				}

				callback(true)
			} else {
				callback(false)
			}
		}, function (_ : boolean, _ : string[]) {
			callback(false)
		})
	}
}