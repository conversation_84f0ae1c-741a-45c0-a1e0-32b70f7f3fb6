<template>
	<view :id="myId" @click="click">
		<slot></slot>
	</view>
</template>

<script setup lang="ts">
	
	/**
	 * IndexBar 子项
	 * @demo pages/component/indexbar.uvue
	 * @tutorial https://www.uxframe.cn/component/indexbar.html
	 * @property {String}			headerId		String | 吸顶头部Id，请保持唯一
	 * @property {String}			name			String | 索引名称，请保持唯一
	 * @event {Function}			click			Function | 点击时触发
	 * <AUTHOR>
	 * @date 2024-12-05 11:25:54
	 */
	
	import { getCurrentInstance, onBeforeUnmount, onMounted } from 'vue'
	import { $ux } from '../../index'
	import { UxIndexbarItem } from '../../libs/types/types.uts'
	
	defineOptions({
		name: 'ux-indexbar-item'
	})
	
	const emit = defineEmits(['click'])
	
	const props = defineProps({
		headerId: {
			type: String,
			default: ''
		},
		name: {
			type: String,
			default: ''
		},
	})
	
	const myId = `ux-indexbar-item-${$ux.Random.uuid()}`
	
	const instance = getCurrentInstance()?.proxy
	
	function click(){
		emit('click', props.name)
	}
	
	function register() {
		let item = {
			id: props.headerId,
			name: props.name,
			height: 0,
			startY: 0,
			endY: 0
		} as UxIndexbarItem
		
		$ux.Util.$dispatch(instance, 'ux-indexbar', 'register', item)
		
		let f = async () => {
			const rect = await $ux.Util.getBoundingClientRect(`#${props.headerId}`)
			const rect2 = await uni.getElementById(`${myId}`)?.getBoundingClientRectAsync()!
			item.height = (rect?.height ?? 0) + (rect2?.height ?? 0)
			
			$ux.Util.$dispatch(instance, 'ux-indexbar', 'register', item)
		}
		
		setTimeout(() => {
			f()
		}, 200);
	}
	
	function unregister() {
		$ux.Util.$dispatch(getCurrentInstance()?.proxy!, 'ux-indexbar', 'unregister', props.headerId)
	}
	
	onMounted(() => {
		register()
	})
	
	onBeforeUnmount(() => {
		unregister();
	})
</script>

<style lang="scss" scoped>
	
</style>