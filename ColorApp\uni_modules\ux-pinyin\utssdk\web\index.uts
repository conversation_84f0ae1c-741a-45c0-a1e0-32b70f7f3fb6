import { ToPinyin, <PERSON><PERSON>hine<PERSON> } from '../interface.uts'

import { pinyin } from 'pinyin-pro'

/**
 * 汉字转拼音，支持多音字
 * @param hanzi 汉字
 */
export const toPinyin: ToPinyin = (hanzi: string): string => {
	return pinyin(hanzi, { toneType: 'none' })
}

/**
 * 是否是汉字
 * @param hanzi 汉字
 */
export const isChinese: IsChinese = (hanzi: string): boolean => {
	if(hanzi == '') {
		return false
	}
	
	let b = true
	// TODO
	
	return b
}