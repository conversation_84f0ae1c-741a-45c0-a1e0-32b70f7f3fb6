<template>
	<view v-if="list.length > 0" ref="uxToastRef" class="ux-toast">
		<template v-for="(item, index) in list">
			<slot :itemId="item.id" :data="item" :index="index">
				<ux-toast-item :itemId="item.id" :index="index" :logo="item.logo" :content="item.content"></ux-toast-item>
			</slot>
		</template>
	</view>
</template>

<script setup lang="ts">
	
	/**
	 * Toast 消息提示框
	 * @description 支持上下左右位置显示，支持拖拽关闭、自动关闭，内置多种关闭动画
	 * @demo pages/component/toast.uvue
	 * @tutorial https://www.uxframe.cn/component/toast.html
	 * @property {String}			position=[center|top|right|bottom|left]		String | 显示位置 (默认 top)
	 * @value center 居中显示
	 * @value top 居上显示
	 * @value right 居右上显示
	 * @value bottom 居下显示
	 * @value left 居左上显示
	 * @property {String}			anim=[fade|none]							String | 动画效果 (默认 fade)
	 * @value fade 渐变过渡
	 * @value none 无动画
	 * @property {Number}			max											Number | 最大存在个数 (默认 0 不限制)
	 * @property {Any}				offset										Any | 偏移 (默认 15)
	 * @property {Any}				hreshold									Any | 移动阈值 (默认 100)
	 * @property {Number}			duration									Number | 关闭过渡时间 (默认 700)
	 * @property {Boolean}			autoclose = [true|false]					Boolean | 自动关闭 (默认 true )
	 * @property {Boolean}			clickclose = [true|false]					Boolean | 点击关闭 (默认 true )
	 * @property {Boolean}			touchclose = [true|false]					Boolean | 长按拖拽关闭 暂只支持Web (默认 false )
	 * <AUTHOR>
	 * @date 2024-12-04 16:36:15
	 */
	
	import { nextTick, onMounted, provide, ref } from 'vue'
	import { $ux } from '../../index'
	import { UxToastData } from '../../libs/types/types.uts'
	
	defineOptions({
		name: 'ux-toast'
	})
	
	defineSlots<{
		default(props : { itemId: string, data : UxToastData, index: number }) : any
	}>()
	
	const emit = defineEmits(['click'])
	
	const props = defineProps({
		position: {
			type: String,
			default: 'top'
		},
		anim: {
			type: String,
			default: 'fade'
		},
		max: {
			type: Number,
			default: 0
		},
		offset: {
			default: 15
		},
		threshold: {
			default: 100
		},
		duration: {
			type: Number,
			default: 1200
		},
		autoclose: {
			type: Boolean,
			default: true
		},
		clickclose: {
			type: Boolean,
			default: true
		},
		touchclose: {
			type: Boolean,
			default: false
		},
	})
	
	provide('position', props.position)
	provide('anim', props.anim)
	provide('duration', props.duration)
	provide('threshold', $ux.Util.getPx(props.threshold))
	provide('autoclose', props.autoclose)
	provide('touchclose', props.touchclose)
	
	const timer = ref(0)
	const list = ref<UxToastData[]>([] as UxToastData[])
	
	function isClosed(e: UxToastData): boolean {
		if(e.closed ?? false) {
			return true
		}
		
		if(props.autoclose && new Date().getTime() - (e.time ?? 0) >= props.duration) {
			return true
		}
		
		return false
	}
	
	function reset() {
		const statusbarHeight = props.position == 'top' || props.position == 'center' ? 0 : uni.getWindowInfo().statusBarHeight
		
		let _list = list.value.filter((e: UxToastData):boolean => !isClosed(e))
		
		for (var i = _list.length - 1; i >= 0; i--) {
			let el = _list[i]
			
			let top = 0
			let bottom = 0
			let left = 0
			let right = 0
			
			let offset = (_list.length - 1 - i) * (el.height ?? 0) + (_list.length == 1 ? 0 : (statusbarHeight + 15))
			
			if(props.position == 'top') {
				top = offset
			} else if(props.position == 'right') {
				top = offset
				right = $ux.Util.getPx(props.offset)
			} else if(props.position == 'bottom') {
				bottom = offset
			} else if(props.position == 'left') {
				top = offset
				left = $ux.Util.getPx(props.offset)
			} else if(props.position == 'center') {
				el.node?.$el?.style?.setProperty('transform', `translateY(${offset}px)`)
			}
			
			if(top > 0) {
				el.node?.$el?.style?.setProperty('top', `${top}px`)
			}
			
			if(bottom > 0) {
				el.node?.$el?.style?.setProperty('bottom', `${bottom}px`)
			}
			
			if(left > 0) {
				el.node?.$el?.style?.setProperty('left', `${left}px`)
			}
			
			if(right > 0) {
				el.node?.$el?.style?.setProperty('right', `${right}px`)
			}
		}
	}
	
	function register(el: UxToastData) {
		let index = list.value.findIndex((e: UxToastData):boolean => e.id == el.id)
		if(index != -1) {
			list.value[index].time = new Date().getTime()
			list.value[index].height = el.height
			list.value[index].node = el.node
		}
		
		reset()
	}
	
	function unregister(id: string) {
		let index = list.value.findIndex((e: UxToastData):boolean => e.id == id)
		if(index != -1) {
			list.value[index].closed = true
		}
		
		reset()
	}
	
	function close(el: UxToastData) {
		el.node?._.exposed.close()
		
		setTimeout(() => {
			unregister(el.id)
		}, 550);
	}
	
	function clear() {
		if(!props.autoclose) {
			return
		}
		
		clearTimeout(timer.value)
		
		timer.value = setTimeout(() => {
			for (var i = list.value.length - 1; i >= 0; i--) {
				if(isClosed(list.value[i])) {
					list.value.splice(i, 1)
				}
			}
		}, 5000)
	}
	
	function push(el: UxToastData) {
		clear()
		list.value.push(el)
		
		setTimeout(() => {
			let _list = list.value.filter((e: UxToastData):boolean => !isClosed(e))
			if(props.max > 0 && _list.length > 1) {
				if(_list.length > props.max) {
					close(_list[0])
				}
			}
		}, 1000);
	}
	
	function show(el: UxToastData) {
		list.value = []
		
		nextTick(() => {
			list.value = [el] as UxToastData[]
		})
	}
	
	function click(id: string) {
		let index = list.value.findIndex((e: UxToastData):boolean => e.id == id)
		
		if(index != -1) {
			let el = list.value[index]
			
			emit('click', el)
			
			if(!props.autoclose && props.clickclose) {
				close(el)
			}
		}
	}
	
	onMounted(() => {
		clear()
	})
	
	defineExpose({
		push,
		show,
		register,
		unregister,
		click
	})
</script>

<style lang="scss" scoped>
	.ux-toast {
		pointer-events: none;
		position: fixed;
		top: 0;
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		z-index: 100000;
	}
</style>