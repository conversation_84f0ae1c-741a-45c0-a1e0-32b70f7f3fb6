<template>
	<view :id="myId" class="ux-drag-item" 
		:style="{
			width: width,
			height: height,
			top: top,
			left: left,
			opacity: isDrag ? 0 : 1,
			borderBottom: border? '1px solid #F0f0f0' : ''
		}"
		@touchstart="touchstart"
		@touchend="touchend"
		@mousedown="touchstart" 
		@mouseup="touchend" 
		@mouseleave="touchend">
		<slot></slot>
	</view>
</template>

<script setup lang="ts">
	/**
	 * DragItem 拖拽子项，需搭配 ux-drag 使用
	 * @demo pages/component/drag.uvue
	 * @tutorial https://www.uxframe.cn/component/drag.html
	 * @property {String}			itemId				String | 唯一索引
	 * @property {Number}			index				Number | 排序下标
	 * @property {Boolean}			border				Boolean | 显示下边框 (默认 false)
	 * <AUTHOR>
	 * @date 2024-12-08 17:27:15
	 */
	
	import { computed, getCurrentInstance, inject, onMounted, onUnmounted, ref, watch } from 'vue'
	import { $ux } from '../../index'
	import { UxDragInfo } from "../../libs/types/types.uts"
	import { useResize } from '../../libs/use/resize'

	defineOptions({
		name: 'ux-drag-item'
	})

	const emit = defineEmits(['click'])

	const props = defineProps({
		itemId: {
			type: String,
			required: true
		},
		index: {
			type: Number,
			required: true
		},
		border: {
			type: Boolean,
			default: false
		},
	})

	const instance = getCurrentInstance()?.proxy
	const myId = `ux-drag-item-${$ux.Random.uuid()}`
	const cellHeight = ref(0)
	const cellWidth = ref(0)
	const timer = ref(0)
	
	const dragId = inject('dragId', ref('')) as Ref<string>

	const col = computed(() : number => {
		return inject('col', 0) as number
	})

	const height = computed(() : string => {
		return `${$ux.Util.getPx(inject('height', 0))}px`
	})

	const width = computed(() : string => {
		return (100 / col.value).toString() + '%'
	})
	
	const top = computed(() : string => {
		return `${cellHeight.value * Math.floor(props.index / col.value)}px`
	})

	const left = computed(() : string => {
		return `${cellWidth.value * (props.index % col.value)}px`
	})
	
	const isDrag = computed(() => {
		return dragId.value == props.itemId
	})

	const style = computed(() => {
		let css = new Map<string, any>()
		
		css.set('width', width.value)
		css.set('height', height.value)
		css.set('top', top.value)
		css.set('left', left.value)
		css.set('opacity', isDrag.value ? 0 : 1)
		
		if(props.border) {
			css.set('border-bottom', '1px solid #F0f0f0')
		}
		
		return css
	})
	
	function getNode() {
		return {
			top: top.value,
			left: left.value,
		} as UTSJSONObject
	}

	function touchstart() {
		timer.value = new Date().getTime()
	}
	
	function touchend() {
		if(timer.value > 0) {
			if(new Date().getTime() - timer.value < 120) {
				emit("click")
			}
		}
		
		timer.value = 0
	}
	
	function register() {
		uni.createSelectorQuery()
			.in(instance)
			.select(`#${myId}`)
			.boundingClientRect().exec((ret) => {
				let node = ret[0] as NodeInfo;
				cellWidth.value = node.width ?? 0
				cellHeight.value = node.height ?? 0
				
				$ux.Util.$dispatch(instance!, 'ux-drag', 'register', {
					id: props.itemId,
					width: cellWidth.value,
					height: cellHeight.value,
					node: instance as UxDragItemComponentPublicInstance,
				} as UxDragInfo)
			})
	}

	onMounted(() => {
		setTimeout(() => {
			register()
		}, 30);
		
		useResize(uni.getElementById(myId), () => {
			register()
		})
	})

	onUnmounted(() => {
		$ux.Util.$dispatch(instance!, 'ux-drag', 'unregister', props.itemId)
	})
	
	defineExpose({
		getNode
	})

</script>

<style lang="scss">
	.ux-drag-item {
		position: absolute;
		left: 0px;
		top: 0px;
		transition-timing-function: ease;
		transition-property: top, left, opacity;
		transition-duration: 400ms;
		/* #ifdef WEB */
		cursor: grab;
		/* #endif */
	}

	/* #ifdef WEB */
	.ux-drag-item:active {
		cursor: grabbing;
	}
	/* #endif */
</style>