<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/empty?title=Empty"></Mobile>

# Empty
> 组件类型：UxEmptyComponentPublicInstance

内置多种情景下的空状态，支持自定义

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| [image](#image) | String | empty | 占位图 |
| width | Any | 200 | 图片宽高度自适应 |
| description | String |  | 描述文字 |
| color | String |  | 文字颜色 |
| [darkColor](#darkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| size | Any | `$ux.Conf.fontSize` | 文字大小 |
| bold | Boolean | false | 文字加粗 |

### [image](#image)

| 值   | 说明 |
|:------:|:----:|
| empty默认
 |  |
| 404404
 |  |
| error错误
 |  |
| search搜索
 |  |
| network网络
 |  |
| message消息
 |  |
| address地址
 |  |
| comment评论
 |  |
| order订单
 |  |
| coupon优惠券
 |  |
| cart购物车
 |  |

### [darkColor](#darkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

