<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/badge?title=Badge"></Mobile>

# Badge
> 组件类型：UxBadgeComponentPublicInstance

支持多种形状、支持圆点、反转显示，可配置超出显示样式

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| [theme](#theme) | String | primary | 主题 |
| value | Number |  | 角标数量 |
| max | Number | 99 | 最大值 |
| [overflow](#overflow) | String | normal | 超出显示样式 |
| [shape](#shape) | String | normal | 形状 |
| [dot](#dot) | Boolean | false | 显示圆点 |
| [inverted](#inverted) | Boolean | false | 反转色 |
| [fixed](#fixed) | Boolean | true | 固定定位 |
| size | Any | `$ux.Conf.fontSize` | 字体大小 |
| color | String | `$ux.Conf.fontColor` | 字体色 |
| [darkColor](#darkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| backgroundColor | String | `$ux.Conf.backgroundColor` | 背景色 |
| [backgroundDark](#backgroundDark) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| top | Any | 0 | 距上 |
| right | Any | 0 | 距右 |
| left | Any | 0 | 距左 |
| bottom | Any | 0 | 距下 |
| padding | Array |  | 自定义角标padding |
| xstyle | Array |  | 自定义样式 |

### [theme](#theme)

| 值   | 说明 |
|:------:|:----:|
| primary | 主要
 |
| success | 成功
 |
| warning | 警告
 |
| error | 错误
 |

### [overflow](#overflow)

| 值   | 说明 |
|:------:|:----:|
| normal正常
 |  |
| format格式化
 |  |

### [shape](#shape)

| 值   | 说明 |
|:------:|:----:|
| normal圆角
 |  |
| lt左上角
 |  |
| rt右上角
 |  |
| rb右下角
 |  |
| lb左下角
 |  |
| ltrb左上右下角
 |  |
| rtlb右上左下角
 |  |

### [dot](#dot)

| 值   | 说明 |
|:------:|:----:|
| true
 |  |
| false
 |  |

### [inverted](#inverted)

| 值   | 说明 |
|:------:|:----:|
| true
 |  |
| false
 |  |

### [fixed](#fixed)

| 值   | 说明 |
|:------:|:----:|
| true
 |  |
| false
 |  |

### [darkColor](#darkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [backgroundDark](#backgroundDark)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

