<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/watermark?title=Watermark"></Mobile>

# Watermark
> 组件类型：UxWatermarkComponentPublicInstance

支持局部水印，支持全屏水印

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| [mode](#mode) | String | text | 模式 |
| content | String |  | 水印文字 |
| color | String | rgba(0,0,0,0.2) | 水印颜色 |
| darkColor | String | rrgba(255,255,255,0.8) | 深色 |
| size | Any | 16 | 文字大小 |
| imgWidth | Any | 60 | 图片宽 |
| imgHeight | Any | 30 | 图片高 |
| gutter | Any | 25 | 间隙 |
| fullscreen | Boolean |  | 全屏 |

### [mode](#mode)

| 值   | 说明 |
|:------:|:----:|
| text文本
 |  |
| img图片暂不支持
 |  |

