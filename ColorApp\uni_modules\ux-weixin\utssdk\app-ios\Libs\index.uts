import { WXApiAuthReq, WXApiSignInReq, WXApiSignInCallback, WXApiSignInResult } from "../interface";


@UTSiOS.keyword('private') var signInCallback : WXApiSignInCallback | null = null

export class SnSignInWxPluginClass implements UTSiOSHookProxy, WXApiDelegate {
	// uts 插件创建时的回调。
	onCreate() {
	}
	// 通过 url scheme 方式唤起 app 时的回调函数。(iOS9 之前的系统回调此方法，iOS9 之后的系统请使用 applicationOpenURLOptions)
	applicationHandleOpenURL(application : UIApplication | null, url : URL | null) : boolean {
		return WXApi.handleOpen(url, delegate = this)
	}

	// 通过 url scheme 方式唤起 app 时的回调函数。
	applicationOpenURLOptions(app : UIApplication | null, url : URL, options : Map<UIApplication.OpenURLOptionsKey, any> | null = null) : boolean {
		return WXApi.handleOpen(url, delegate = this)
	}

	// 当应用程序接收到与用户活动相关的数据时调用此方法，例如，当用户使用 Universal Link 唤起应用时。
	applicationContinueUserActivityRestorationHandler(application : UIApplication | null, userActivity : NSUserActivity | null, restorationHandler : ((res : [any] | null) => void) | null = null) : boolean {
		return WXApi.handleOpenUniversalLink(userActivity!, delegate = this)
	}

	onReq(req : BaseReq) {

	}

	onResp(resp : BaseResp) {
		if (resp.isKind(of = SendAuthResp.self)) {
			let res = resp as SendAuthResp | null
			if (res != null && res!.state == "wx_oauth_authorization_state") {
				signInCallback!(
					{
						success: true,
						msg: 'success',
						code: res!.code
					} as WXApiSignInResult
				)
			} else {
				signInCallback!(
					{
						success: false,
						msg: 'fail',
					} as WXApiSignInResult
				)
			}
		} else {
			// 其他Resp
		}
	}

}


export function registerApp(regInfo : WXApiRegInfo) : void {
	WXApi.registerApp(regInfo.appId, universalLink = regInfo.universalLink)
}

export function wxLogin(signInReq : WXApiSignInReq, callback : WXApiSignInCallback) : void {
	if (WXApi.isWXAppInstalled()) {
		signInCallback = callback
		let req = new SendAuthReq()
		req.state = signInReq.state
		req.scope = signInReq.scope
		DispatchQueue.main.async(execute = () : void => {
			WXApi.send(req)
		})
	} else {
		callback({
			success: false,
			msg: 'fail,wxapp not installed',
		} as WXApiSignInResult)
	}
}