import { UxDouyinRegisterOptions, UxDouyinLoginOptions, UxDouyinShareOptions, UxDouyinMiniProgram, UxDouyinSuccess } from '../interface.uts'
import { UxDouyinFailImpl } from '../unierror';

import Activity from 'android.app.Activity';
import Intent from 'android.content.Intent';
import Bundle from 'android.os.Bundle';
import DouYin<PERSON>penApi from 'com.bytedance.sdk.open.douyin.api.DouYinOpenApi';
import DouYinOpenApiFactory from 'com.bytedance.sdk.open.douyin.DouYinOpenApiFactory';
import Authorization from 'com.bytedance.sdk.open.aweme.authorize.model.Authorization';
import IApiEventHandler from 'com.bytedance.sdk.open.aweme.common.handler.IApiEventHandler';
import BaseReq from 'com.bytedance.sdk.open.aweme.common.model.BaseReq';
import BaseResp from 'com.bytedance.sdk.open.aweme.common.model.BaseResp';
import CommonConstants from 'com.bytedance.sdk.open.aweme.CommonConstants';

import util from './util.uts'

var isAuth = false
var authResp: UxDouyinLoginOptions | null = null
var shareResp: UxDouyinShareOptions | null = null

/**
* 注册
* @param {UxDouyinRegisterOptions} options
* @tutorial https://www.uxframe.cn/api/douyin.html#register
*/
export const register = function (options : UxDouyinRegisterOptions) {
	let ok = UxDouyin.register(options.appid)
	if(ok) {
		options.success?.({
			code: '0',
			msg: 'register success'
		} as UxDouyinSuccess)
	} else {
		options.fail?.(new UxDouyinFailImpl(9010001))
	}
}

/**
* 登录
* @param {UxDouyinLoginOptions} options
* @tutorial https://www.uxframe.cn/api/douyin.html#login
*/
export const login = function (options : UxDouyinLoginOptions) {
	isAuth = true
	authResp = options
	UxDouyin.login(options.state)
}

/**
* 分享
* @param {UxDouyinShareOptions} options
* @tutorial https://www.uxframe.cn/api/douyin.html#share
*/
export const share = function (options : UxDouyinShareOptions) {
	isAuth = false
	shareResp = options
	
	if(options.type == 0) {
		if(options.imageUrl == null) {
			options.fail?.(new UxDouyinFailImpl(9010002))
			return
		}
		
		let url = UTSAndroid.convert2AbsFullPath(util.getPath(options.imageUrl!))
		UxDouyin.shareImage(url)
	} else if(options.type == 1) {
		UxDouyin.shareHtml(options.title!, options.href!, options.summary!, options.imageUrl)
	} else if(options.type == 2) {
		UxDouyin.shareProgram(options.miniProgram?.id!, options.miniProgram?.title!, options.miniProgram?.path!, options.miniProgram?.query, options.miniProgram?.imageId!, options.miniProgram?.state)
	}
}

export class DouYinEntryActivity extends Activity implements IApiEventHandler {

    api: DouYinOpenApi | null = null
	
	constructor() {
		super();
	}
	
    override onCreate(savedInstanceState ?: Bundle) {
        super.onCreate(savedInstanceState)
		
        this.api = DouYinOpenApiFactory.create(this)
        this.api?.handleIntent(this.getIntent(), this)
    }

	override onNewIntent(intent : Intent) : void {
		super.onNewIntent(intent);
	
		this.setIntent(intent);
	
		this.api?.handleIntent(intent, this);
	}

    override onReq(req: BaseReq) {
        this.finish()
    }

    override onResp(resp: BaseResp) {
        if (resp.type == CommonConstants.ModeType.SEND_AUTH_RESPONSE) {
			if(isAuth) {
				var response = resp as Authorization.Response
				if (resp.isSuccess) {
					authResp?.success?.({
						code: response.authCode,
						msg: 'auth success'
					})
				} else {
				    authResp?.fail?.(new UxDouyinFailImpl(9010005))
				}
			} else {
				if (resp.isSuccess) {
					shareResp?.success?.({
						code: '0',
						msg: 'share success'
					})
				} else {
				    shareResp?.fail?.(new UxDouyinFailImpl(9010005))
				}
			}
			
            this.finish()
        }
    }

    override onErrorIntent(intent?: Intent) {
		authResp?.fail?.(new UxDouyinFailImpl(9010006))
        this.finish()
    }
}