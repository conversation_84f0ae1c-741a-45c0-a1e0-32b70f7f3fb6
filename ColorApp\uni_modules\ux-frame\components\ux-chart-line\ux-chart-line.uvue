<template>
	<view class="ux-chart-line">
		<canvas id="line" class="canvas" @touchstart="touchstart" @touchmove="touchmove" @touchend="touchend" @touchcancel="touchend" />
	</view>
</template>

<script setup>
	
	/**
	 * 图表 - 折线图
	 * @description canvas绘制的折线图图表
	 * @demo pages/component/chart-line.uvue
	 * @tutorial https://www.uxframe.cn/component/chart-line.html
	 * @property {Object}		data							UxChartLineType | 数据
	 * @property {Number}		delay							Number | 延迟初始化 (默认 0)
	 * <AUTHOR>
	 * @date 2025-02-27 17:35:45
	 */

	import { $ux } from '../../index'
	import { useFontSize, useFontColor, useThemeColor, useColor } from '../../libs/use/style.uts'
	import { UxChartLineType } from '../../libs/types/chart.uts'

	defineOptions({
		name: 'ux-chart-line'
	})

	type Pos = {
		x : number,
		y : number
	}

	const props = defineProps({
		data: {
			type: Object as PropType<UxChartLineType>,
			required: true
		},
		delay: {
			type: Number,
			default: 0
		}
	})
	
	let ctx : CanvasRenderingContext2D | null = null
	
	const data = computed((): UxChartLineType => {
		return props.data as UxChartLineType
	})
	
	// x轴
	let xAxis : string[] = []

	// 数据
	let datas : number[][] = []

	// 上次数据
	let lastData = new Map<number, number>()

	// canvas宽高
	let width = 0
	let height = 0

	// 绘制区域宽高
	let w = 0
	let h = 0

	// x轴位置
	let x0 : Pos = { x: 0, y: 0 }
	let x1 : Pos = { x: 0, y: 0 }

	// y轴位置
	let y0 : Pos = { x: 0, y: 0 }
	let y1 : Pos = { x: 0, y: 0 }
	
	// 原点
	let zero: Pos = {x: 0, y: 0}

	// x轴左偏移
	const offsetX = 45

	// y轴底偏移
	const offsetY = 45

	const fontSize = computed((): number => {
		return useFontSize($ux.Util.getPx(data.value.fontSize ?? 12), 0)
	})
	
	const fontColor = computed((): string => {
		return useFontColor(data.value.color ?? '', data.value.darkColor ?? '')
	})

	const areaColor = computed((): string[][] => {
		let theme = useThemeColor('primary')
		
		let colors = [] as string[][]
		let _colors = data.value.areaColor ?? [] as string[]
		for (let i = 0; i < data.value.data.length; i++) {
			let c = theme
			if(i <= _colors.length - 1) {
				c = _colors[i]
			}
			
			colors.push([c, $ux.Color.getRgba(c, 0.6)] as string[])
		}
		
		return colors
	})
	
	const dashesColor = computed((): string => {
		return useColor(data.value.dashesColor ?? '#c5cacf', data.value.dashesColorDark ?? '')
	})
	
	const lineColor = computed((): string => {
		return useColor(data.value.lineColor ?? '#c5cacf', data.value.lineColorDark ?? '')
	})
	
	const yStep = computed(() => {
		return data.value.yStep ?? 5
	})

	function maxValue() {
		let a = 0
		datas.forEach(e => {
			let b = Math.max(...e)
			a = Math.ceil(Math.max(a, b))
		})
		return a
	}

	function minValue() {
		let a = 0
		datas.forEach(e => {
			let b = Math.min(...e)
			a = Math.ceil(Math.min(a, b))
		})
		return a
	}

	function stepValue(value : number) {
		const roughStep = value / (yStep.value - 1)
		const stepPower = Math.pow(10, Math.floor(Math.log10(roughStep)))
		const step = Math.floor(roughStep / stepPower) * stepPower

		// 确保步长至少为1
		return Math.max(step, 1)
	}

	function drawAxis() {
		if (ctx == null) return

		// x轴最大值 最小值
		let minX = 0
		let maxX = w

		// 获取数据范围
		const max = maxValue()
		const min = minValue()
		const range = max - min // 数值总跨度

		// 自动计算合适的步长
		let step = stepValue(range)

		// 绘制y轴
		for (let value = min; value <= max; value += step) {
			const y = h - (value - min) / range * h + offsetY

			// x轴虚线网格
			if (value / step != 0) {
				ctx!.strokeStyle = dashesColor.value
				ctx!.setLineDash([5, 5])
				ctx!.beginPath()
				ctx!.moveTo(x0.x, y)
				ctx!.lineTo(x1.x, y)
				ctx!.stroke()
				ctx!.setLineDash([])
			} else {
				// y轴零点位置
				zero = {
					x: x0.x,
					y: y
				}
			}

			// y轴刻度值
			ctx!.font = `${fontSize.value}px Arial`
			ctx!.fillStyle = fontColor.value
			ctx!.textAlign = 'right'
			ctx!.fillText(value.toString(), y0.x - 8, y + 4)
		}

		// 绘制x轴
		ctx!.beginPath()
		ctx!.moveTo(x0.x, zero.y)
		ctx!.lineTo(x1.x, zero.y)
		ctx!.closePath()
		ctx!.strokeStyle = lineColor.value
		ctx!.stroke()

		// x轴每一段宽度
		const xWidth = w / xAxis.length
		
		// 文本旋转角度
		const angle = data.value.labelAngle ?? 0
		
		// 绘制x轴数据
		xAxis.forEach((label, index) => {
			// 刻度线位置
			const x = x0.x + xWidth * index

			ctx!.beginPath()
			ctx!.moveTo(x, zero.y)
			ctx!.lineTo(x, zero.y + 5)
			ctx!.stroke()

			// 文本
			ctx!.font = `${fontSize.value}px Arial`
			ctx!.fillStyle = fontColor.value
			ctx!.textAlign = angle == 0 ? 'center' : 'right'
			ctx!.save()
			ctx!.translate(x + (angle == 0 ? (xWidth / 2) : 6), x0.y + 15)
			ctx!.rotate(-Math.PI / (180 / angle)) // 倾斜防止重叠
			ctx!.fillText(label, 0, 0)
			ctx!.restore()
		})

		// 补充最后一个刻度
		ctx!.beginPath()
		ctx!.moveTo(offsetX + x1.x, zero.y)
		ctx!.lineTo(offsetX + x1.x, zero.y + 5)
		ctx!.stroke()
	}

	function drawLine(vals : number[], i: number) {
		if (ctx == null) {
			return
		}

		// 获取数据范围
		const max = maxValue()
		const min = minValue()
		const range = max - min // 数值总跨度

		// x轴每一段宽度
		const xWidth = w / xAxis.length

		// 节点相对位置
		let points : Pos[] = vals.map((val : number, index : number) : Pos => {
			let x = x0.x + xWidth * index
			let y = zero.y - h * (val / range)
			
			return {
				x: x,
				y: y
			} as Pos
		})

		// 折线
		ctx!.beginPath()
		ctx!.moveTo(points[0].x, points[0].y)
		points.forEach(point => ctx!.lineTo(point.x, point.y))
		ctx!.strokeStyle = lineColor.value
		ctx!.lineWidth = 2
		ctx!.stroke()

		// 节点
		points.forEach(point => {
			ctx!.beginPath()
			ctx!.arc(point.x, point.y, 3, 0, 2 * Math.PI)
			ctx!.fillStyle = '#ffffff'
			ctx!.fill()
			ctx!.strokeStyle = lineColor.value
			ctx!.lineWidth = 3
			ctx!.stroke()
		})

		// 区域
		const gradient = ctx!.createLinearGradient(0, 0, 0, h)
		areaColor.value[i].forEach((color, idx) => {
			gradient.addColorStop(idx, color)
		})
		
		ctx!.beginPath()
		ctx!.moveTo(points[0].x, points[0].y)
		points.forEach(point => ctx!.lineTo(point.x, point.y))
		ctx!.lineTo(points[points.length - 1].x, zero.y)
		ctx!.lineTo(points[0].x, zero.y)
		ctx!.closePath()
		ctx!.fillStyle = gradient
		ctx!.fill()
	}

	function draw() {
		if (xAxis.length == 0) {
			return
		}
		
		if (ctx != null) {
			ctx!.clearRect(0, 0, width, height)
		}
		
		drawAxis()
		datas.forEach((e, i) => drawLine(e, i))
	}

	function touchstart(e : TouchEvent) {

	}

	function touchmove(e : TouchEvent) {

	}

	function touchend(e : TouchEvent) {

	}

	function init() {
		xAxis = data.value.xAxis
		datas = data.value.data

		uni.createCanvasContextAsync({
			id: 'line',
			component: getCurrentInstance()?.proxy!,
			success: (context : CanvasContext) => {
				ctx = context.getContext('2d')!;
				const canvas = ctx!.canvas;

				// 处理高清屏逻辑
				const dpr = uni.getDeviceInfo().devicePixelRatio ?? 1;
				canvas.width = canvas.offsetWidth * dpr;
				canvas.height = canvas.offsetHeight * dpr;
				ctx!.scale(dpr, dpr)

				width = canvas.offsetWidth
				height = canvas.offsetHeight

				w = width
				h = height - offsetY * 2
				x0 = { x: offsetX, y: offsetY + h } as Pos
				x1 = { x: w, y: offsetY + h } as Pos
				y0 = { x: offsetX, y: offsetY + h } as Pos
				y1 = { x: offsetX, y: 0 } as Pos

				setTimeout(function () {
					draw()
				}, props.delay);
			}
		})
	}

	watch(data, () => {
		datas = data.value.data
		xAxis = data.value.xAxis
		draw()
	}, {deep: true})

	onMounted(() => {
		init()
	})
</script>

<style lang="scss" scoped>
	.ux-chart-line {
		flex: 1;

		.canvas {
			width: 100%;
			height: 100%;
		}
	}
</style>