<template>
	<view class="ux-loading" :style="style">
		<image v-if="type == 'icon'" class="ux-loading__icon" :style="loadingStyle" src="/uni_modules/ux-frame/static/loading.gif" mode=""></image>
		<view v-else android-layer-type="hardware" class="ux-loading__icon" :style="loadingStyle">
			<canvas class="ux-loading__canvas" :id="myId" :canvas-id="myId"></canvas>
		</view>
		<text class="ux-loading__text" v-if="$slots['default'] != null" :style="textStyle">
			<slot></slot>
		</text>
	</view>
</template>

<script setup lang="ts">
	
	/**
	 * loading 加载中
	 * @description 支持圆环、菊花形状，可自定义文案
	 * @demo pages/component/loading.uvue
	 * @tutorial https://www.uxframe.cn/component/loading.html
	 * @property {String} 			type = [circular|spinner|icon] 			String | loading类型 (默认 spinner)
	 * @value circular 圆环
	 * @value spinner 菊花
	 * @value icon 图标
	 * @property {String}			color								String | loading颜色 (默认 $ux.Conf.placeholderColor)
	 * @property {String} 			darkColor=[none|auto|color]			String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Any}				size								Any | loading大小 (默认 $ux.Conf.fontSize)
	 * @property {String} 			textColor 							String | 文案颜色
	 * @property {Any} 				textSize 							Any | 文案字体大小  (默认 $ux.Conf.fontSize)
	 * @property {Boolean}			bold = [true|false]					Boolean | 文字加粗 (默认 false)
	 * @property {Array}			margin								Array | 边距 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}				mt									Any | 距上 单位px
	 * @property {Any}				mr									Any | 距右 单位px
	 * @property {Any}				mb									Any | 距下 单位px
	 * @property {Any}				ml									Any | 距左 单位px
	 * @property {Array}			padding								Array | 填充 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}				pt									Any | 上内边距 单位px
	 * @property {Any}				pr									Any | 右内边距 单位px
	 * @property {Any}				pb									Any | 下内边距 单位px
	 * @property {Any}				pl									Any | 左内边距 单位px
	 * @property {Array}			xstyle								Array<any> | 自定义样式
	 * <AUTHOR>
	 * @date 2024-11-28 01:38:10
	 */

	import { ref, computed, onMounted, onUnmounted, getCurrentInstance } from 'vue'
	import { onShow, onHide } from '@dcloudio/uni-app'
	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	import { useFontSize, usePlaceholderColor } from '../../libs/use/style.uts'

	defineOptions({
		name: 'ux-loading',
		mixins: [xstyleMixin]
	})

	const props = defineProps({
		type: {
			type: String,
			default: 'spinner'
		},
		color: {
			type: String,
			default: ''
		},
		darkColor: {
			type: String,
			default: ''
		},
		size: {
			default: 0
		},
		textColor: {
			type: String,
			default: ''
		},
		textSize: {
			default: 0
		},
		bold: {
			type: Boolean,
			default: false
		}
	})


	let width = 16
	let stop = false
	let timer = 0
	
	const myId = `ux-loading-${$ux.Random.uuid()}`
	const dpr = uni.getWindowInfo().pixelRatio
	
	const size = computed(() : number => {
		if ($ux.Util.getPx(props.size) > 0) {
			return $ux.Util.getPx(props.size)
		} else {
			return 15
		}
	})

	const fontSize = computed(() : number => {
		return useFontSize($ux.Util.getPx(props.textSize), 1)
	})

	const color = computed(() : string => {
		return usePlaceholderColor(props.color, props.darkColor)
	})

	const loadingStyle = computed(() => {
		let css = {}

		css['width'] = $ux.Util.addUnit(size.value)
		css['height'] = $ux.Util.addUnit(size.value)

		return css
	})

	const textStyle = computed(() => {
		let css = {}

		css['color'] = color.value
		css['font-size'] = $ux.Util.addUnit(fontSize.value)

		if (props.bold) {
			css['font-weight'] = 'bold'
		}

		return css
	})
	
	const style = computed(()=> {
		let css = {}
		
		css = $ux.Util.xStyle(css, props.margin, props.mt, props.mr, props.mb, props.ml, props.padding, props.pt, props.pr, props.pb, props.pl)
		
		return css
	})

	/**
	 * 圆环loading
	 */
	function drawCircular(ctx : any, canvas: any | null) {
		let startAngle = 0;
		let endAngle = 0
		let startSpeed = 0
		let endSpeed = 0
		let rotate = 0

		const ARC_LENGTH = 359
		const PI = Math.PI / 180
		const SPEED = 0.018
		const ROTATE_INTERVAL = 0.09
		const center = width / 2
		const lineWidth = width / 10

		let easeInOutCubic = (t : number) : number => {
			return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1
		}

		let draw = () : void => { }
		draw = () : void => {
			clearTimeout(timer)

			if (stop) {
				timer = setTimeout(() => {
					draw()
				}, 1000)
				return
			}
			
			// #ifdef WEB
			// @ts-ignore
			ctx.clearRect(0, 0, canvas!.width, canvas!.height)
			// #endif
			
			ctx.beginPath()
			ctx.arc(
				center,
				center,
				center - lineWidth,
				startAngle * PI + rotate,
				endAngle * PI + rotate)

			ctx.lineWidth = lineWidth
			ctx.strokeStyle = color.value
			ctx.stroke()

			if (endAngle < ARC_LENGTH && startAngle == 0) {
				endSpeed += SPEED
				endAngle = Math.min(ARC_LENGTH, easeInOutCubic(endSpeed) * ARC_LENGTH)
			} else if (endAngle == ARC_LENGTH && startAngle < ARC_LENGTH) {
				startSpeed += SPEED
				startAngle = Math.min(ARC_LENGTH, easeInOutCubic(startSpeed) * ARC_LENGTH)
			} else if (endAngle >= ARC_LENGTH && startAngle >= ARC_LENGTH) {
				endSpeed = 0
				startSpeed = 0
				startAngle = 0
				endAngle = 0
			}

			rotate += ROTATE_INTERVAL
			
			ctx.draw()

			timer = setTimeout(() => {
				draw()
			}, 20)
		}

		draw()
	}

	/**
	 * 菊花loading
	 */
	function drawSpinner(ctx : any, canvas : any | null) {
		let steps = 12
		let step = 0
		let lineWidth = width / 10

		// 线长度和距离圆心距离
		let length = width / 4 - lineWidth
		let offset = width / 4

		let draw = () : void => { }
		draw = () : void => {
			clearTimeout(timer)

			if (stop) {
				timer = setTimeout(() => {
					draw()
				}, 1000)
				return
			}
			
			// #ifdef WEB
			// @ts-ignore
			ctx.clearRect(0, 0, canvas!.width, canvas!.height)
			// #endif
			
			for (let i = 0; i < steps; i++) {
				const stepAngle = 360 / steps
				const angle = stepAngle * i
				const opacity = ((steps - (step % steps)) * stepAngle + angle) % 360 + 30

				// 正余弦
				const sin = Math.sin(angle / 180 * Math.PI)
				const cos = Math.cos(angle / 180 * Math.PI)

				// 开始绘制
				ctx.lineWidth = lineWidth
				ctx.lineCap = 'round'
				ctx.beginPath()
				ctx.moveTo(width / 2 + offset * cos, width / 2 + offset * sin)
				ctx.lineTo(width / 2 + (offset + length) * cos, width / 2 + (offset + length) * sin)
				ctx.strokeStyle = $ux.Color.getRgba(color.value, opacity / 360).toString()

				ctx.stroke();
			}
			
			ctx.draw()

			step += 1
			if (step >= steps) {
				step = 0
			}

			timer = setTimeout(() => {
				draw()
			}, 48)
		}

		draw()
	}
	
	async function anim() {
		stop = false

		if (props.type == 'icon') {
			return
		}
		
		let rect = await $ux.Util.getBoundingClientRect(`#${myId}`, getCurrentInstance()?.proxy)
		width = rect.width
		
		// #ifdef WEB
		// @ts-ignore
		let ctx = uni.createCanvasContext(myId, getCurrentInstance()?.proxy)
		ctx.width = rect.width * dpr
		ctx.height = rect.height * dpr
		// ctx?.scale(dpr, dpr)
		// #endif
		
		if (ctx == null) {
			console.error('[ux-loading] 加载失败');
			return
		}
		
		if (props.type == 'circular') {
			drawCircular(ctx!, ctx)
		} else {
			drawSpinner(ctx!, ctx)
		}
	}

	onMounted(() => {
		setTimeout(() => {
			anim()
		}, 200)
	})

	onUnmounted(() => {
		stop = true
		clearTimeout(timer)
	})

	onShow(() => {
		stop = false
	})

	onHide(() => {
		stop = true
	})
</script>

<style lang="scss" scoped>
	.ux-loading {
		display: flex;
		flex-direction: row;
		align-items: center;
		
		&__icon {
			width: 20px;
			height: 20px;
		}

		&__img {
			width: 20px;
			height: 20px;
		}

		&__text {
			margin-left: 8px;
			font-size: 14px;
		}
		
		&__canvas {
			width: 20px;
			height: 20px;
		}
	}
</style>