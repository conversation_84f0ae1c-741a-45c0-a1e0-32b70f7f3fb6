import UIKit
import WebKit
import Foundation
import UniformTypeIdentifiers
import DCloudUTSFoundation
import DCloudUniappRuntime

public class UxOpenWeb {
	
    static func show(title: String, url: String, darkMode: Bool, close: @escaping () -> Void) -> Bool {
		var popupView = PopupViewController(title: title, url: url, darkMode: darkMode)
		
		UTSiOS.getCurrentViewController().modalPresentationStyle = .overFullScreen
        UTSiOS.getCurrentViewController().present(popupView, animated: true, completion: nil)
        return false
    }
}

class PopupViewController: UIViewController {
    
    private var webView: WKWebView!
    private var closeButton: UIButton!
    private var backgroundView: UIView!
    private var titleBackgroundView: UIView!
    private var titleTextView: UILabel!
    
    var titleText: String?
    var url: String?
    var darkMode: Bool = false
	
	init(title: String, url: String, darkMode: Bool) {
	    self.titleText = title
	    self.url = url
	    self.darkMode = darkMode
	    super.init(nibName: nil, bundle: nil)
	}
	
	required init?(coder: NSCoder) {
	    fatalError("init(coder:) has not been implemented")
	}
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupViews()
    }
    
    private func setupViews() {
		
        backgroundView = UIView(frame: self.view.bounds)
        backgroundView.backgroundColor = darkMode ? UIColor.black : UIColor.white
        backgroundView.alpha = 0.0
        view.addSubview(backgroundView)
        
		titleBackgroundView = UIView()
		titleBackgroundView.backgroundColor = darkMode ? UIColor.black : UIColor.white
		titleBackgroundView.translatesAutoresizingMaskIntoConstraints = false
		titleBackgroundView.heightAnchor.constraint(equalToConstant: 50).isActive = true
		view.addSubview(titleBackgroundView)
			
        titleTextView = UILabel()
        titleTextView.text = titleText
        titleTextView.textAlignment = .center
        titleTextView.textColor = darkMode ? .white : .black
        titleTextView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(titleTextView)
		
		let fontSize: CGFloat = 18
		titleTextView.font = UIFont.boldSystemFont(ofSize: fontSize)
        
        closeButton = UIButton(type: .system)
		closeButton.tintColor = darkMode ? UIColor.white : UIColor.black
		if let imagePath = Bundle.main.path(forResource: darkMode ? "ic_close_dark" : "ic_close", ofType: "png") {
			let image = UIImage(contentsOfFile: imagePath)
			closeButton.setImage(UIImage(named: imagePath), for: .normal)
		}
        closeButton.addTarget(self, action: #selector(closeButtonTapped), for: .touchUpInside)
        closeButton.translatesAutoresizingMaskIntoConstraints = false
		closeButton.widthAnchor.constraint(equalToConstant: 22).isActive = true
		closeButton.heightAnchor.constraint(equalToConstant: 22).isActive = true
        view.addSubview(closeButton)
        
        webView = WKWebView()
        webView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(webView)
        
        NSLayoutConstraint.activate([
			titleBackgroundView.topAnchor.constraint(equalTo: view.topAnchor),
			titleBackgroundView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
			titleBackgroundView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
			
			titleTextView.centerYAnchor.constraint(equalTo: titleBackgroundView.centerYAnchor),
			titleTextView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
			titleTextView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
			
			closeButton.centerYAnchor.constraint(equalTo: titleBackgroundView.centerYAnchor),
			closeButton.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -15),
			
			webView.topAnchor.constraint(equalTo: titleBackgroundView.bottomAnchor),
			webView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
			webView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
			webView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
			
        ])
        
        if let url = url {
            webView.load(URLRequest(url: URL(string: url)!))
        }
        
        UIView.animate(withDuration: 0.3) {
            self.backgroundView.alpha = 1.0
        }
    }
    
    @objc private func closeButtonTapped() {
        UIView.animate(withDuration: 0.3, animations: {
            self.backgroundView.alpha = 0.0
        }) { _ in
            self.dismiss(animated: true, completion: nil)
        }
    }
}