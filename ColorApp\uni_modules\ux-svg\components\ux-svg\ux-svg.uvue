<template>
	<!-- #ifdef APP-ANDROID || APP-IOS -->
	<native-view @init="onInit"></native-view>
	<!-- #endif -->
	
	<!-- #ifdef APP-HARMONY -->
	<image class="ux-svg" :src="src"></image>
	<!-- #endif -->
	
	<!-- #ifndef APP -->
	<img class="ux-svg" :src="src"></img>
	<!-- #endif -->
</template>

<script setup lang="uts">
	/**
	* svg 组件
	* @demo pages/component/svg.uvue
	* @tutorial https://www.uxframe.cn/component/svg.html
	* @property {String} 			src						String | 路径
	* @property {String} 			color					String | 颜色
	* @event {Function} 			loaded 					Function | 加载完成时触发
	* <AUTHOR>
	* @date 2025-04-07 21:10:21
	*/
   
	import { UxSvg } from '@/uni_modules/ux-svg'
	
	const props = defineProps({
		src: {
			type: String,
			default: ''
		},
		color: {
			type: String,
			default: ''
		}
	})
	
	const emit = defineEmits<{
		(e : "loaded") : void
	}>()
		
	let svg : UxSvg | null = null
	
	// #ifdef APP-ANDROID || APP-IOS
	function onInit(e: UniNativeViewInitEvent) {
		svg = new UxSvg(e.detail.element);
		svg?.load(props.src, props.color)
	}
	
	watch((): string => props.src, () => {
		svg?.load(props.src, props.color)
		emit('loaded')
	})
	// #endif
</script>

<style lang="scss">
	.ux-svg {
		
	}
</style>
