
<p align="center">
    <img alt="logo" src="https://www.uxframe.cn/logo/logo.png" width="120" height="120" style="margin-bottom: 10px;">
</p>
<h3 align="center" style="margin: 30px 0 30px;font-weight: bold;font-size:40px;">UxFrame 3.1.8</h3>
<h3 align="center">基于UNI-APP-X开发的低代码高性能UI框架</h3>

## 重点说明 ⬇️

#### UxFrame UI框架(`荣获2023年插件大赛一等奖`)基于`uniappx setup`模式构建，是一个功能强大、性能卓越、美观精致的跨平台解决方案。
#### 目前框架已支持 `uniappx android`、`uniappx ios`、`uniappx 鸿蒙`、`uniappx 微信小程序`、`uniappx web`、`uniapp H5` 6端！
#### 已适配长屏、宽屏、PC、折叠屏、横屏，同时框架已适配`深色模式`，可自由切换主题，内置字体大小、单位、强调色、背景色、文字颜色、内边距、外边距、圆角等20+项全局配置，轻松实现高效的页面布局。

## 福利赠送

### 凡是购买UI库，免费赠送以下全部原生插件

[UxFrame 微信SDK](https://ext.dcloud.net.cn/plugin?id=16719)

[UxFrame 微信SDK（无支付，不与官方支付冲突](https://ext.dcloud.net.cn/plugin?id=22241)

[UxFrame 企业微信SDK](https://ext.dcloud.net.cn/plugin?id=16996)

[UxFrame 抖音SDK](https://ext.dcloud.net.cn/plugin?name=ux-douyin)

[UxFrame 实时音视频通话直播SDK](https://ext.dcloud.net.cn/plugin?id=17036)

[UxFrame Plus原生SDK](https://ext.dcloud.net.cn/plugin?id=19229)

[UxFrame 本地数据库sqlite SDK](https://ext.dcloud.net.cn/plugin?id=21732)

[UxFrame 零SQL本地数据库ORM SDK](https://ext.dcloud.net.cn/plugin?id=21609)

[UxFrame 生物指纹认证SDK](https://ext.dcloud.net.cn/plugin?id=22898)

[UxFrame 本地文件选择器SDK](https://ext.dcloud.net.cn/plugin?id=21725)

[UxFrame 自定义相机预览拍照组件](https://ext.dcloud.net.cn/plugin?name=ux-camera-view)

[UxFrame iOS StoreKit 应用评分SDK](https://ext.dcloud.net.cn/plugin?name=ux-storekit)

[UxFrame Blur 高斯模糊组件](https://ext.dcloud.net.cn/plugin?id=22664)

[UxFrame Sprite 精灵图动画组件](https://ext.dcloud.net.cn/plugin?id=22665)

[UxFrame 权限申请提示SDK](https://ext.dcloud.net.cn/plugin?id=22668)

[UxFrame 通知栏下载进度SDK](https://ext.dcloud.net.cn/plugin?id=22669)

[UxFrame 系统分享SDK](https://ext.dcloud.net.cn/plugin?id=22673)

[UxFrame MQTT SDK](https://ext.dcloud.net.cn/plugin?id=22685)

[UxFrame 网络状态监听SDK](https://ext.dcloud.net.cn/plugin?id=22896)

[UxFrame 中文转拼音SDK](https://ext.dcloud.net.cn/plugin?id=22897)

[UxFrame Lottie动画组件](https://ext.dcloud.net.cn/plugin?id=22904)

[UxFrame Shimmer 辉光效果组件](https://ext.dcloud.net.cn/plugin?id=23065)

## 推荐离线打包工具 嘎嘎快～

[uniapp应用开发工具，可以生成android和iOS离线工程，一键本地打包，支持uniapp和uniapp-x项目](https://u.tools/plugins/detail/uniapp%E5%BA%94%E7%94%A8%E5%BC%80%E5%8F%91%E5%B7%A5%E5%85%B7)


## 未来规划 ⬇️

在功能强大、性能卓越、美观精致的基础下同时支持UniApp X和Uniapp Vue3两种编译模式，一套代码能同时编译到2种模式下的多种平台: 鸿蒙OS、Android、iOS，Web，PCWeb，主流小程序(微信、支付宝、抖音、快手、QQ、钉钉、飞书、小红书)，并且实现低代码拖拽和AI生成代码功能！

## 快速预览

![Image](https://www.uxframe.cn/source/intro/preview.png)

## 扫码体验
### WEB/PC预览 [https://web.uxframe.cn](https://web.uxframe.cn)

![Image](https://www.uxframe.cn/source/intro/code.jpg)

## 文档教程 ⬇️

[https://www.uxframe.cn](https://www.uxframe.cn)
