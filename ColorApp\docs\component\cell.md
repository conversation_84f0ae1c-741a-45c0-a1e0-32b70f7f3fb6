<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/cell?title=Cell"></Mobile>

# Cell
> 组件类型：UxCellComponentPublicInstance

支持配置标题、描述文本、内容及样式，左右图标、插槽，最小高度、固定高度，对齐方式

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| left | Slot |  | 左边插槽 |
| right | Slot |  | 右边插槽 |
| title | String |  | 标题 |
| size | Any | 14 | 标题大小 |
| color | String | `$ux.Conf.fontColor` | 标题颜色 |
| [darkColor](#darkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| bold | Boolean | false | 标题加粗 |
| titleStyle | String |  | 标题样式 |
| summary | String |  | 描述文本 |
| summaryColor | String | `$ux.Conf.subtitleColor` | 描述文本颜色 |
| summarySize | Any | `$ux.Conf.fontSize.small` | 描述文本大小 |
| summaryStyle | String |  | 描述文本样式 |
| label | String |  | 右侧内容 |
| labelSize | Any | `$ux.Conf.fontSize` | 内容大小 |
| labelColor | String | `$ux.Conf.subtitleColor` | 内容颜色 |
| labelBold | Boolean | false | 内容加粗 |
| labelStyle | String |  | 内容样式 |
| placeholder | String |  | 右侧占位内容 |
| placeholderSize | Any | `$ux.Conf.fontSize.small` | 右侧占位内容大小 |
| placeholderColor | String | `$ux.Conf.placeholderColor` | 右侧占位内容颜色 |
| [placeholderDark](#placeholderDark) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| placeholderStyle | String |  | 内容样式 |
| icon | String |  | 标题图标 |
| iconColor | String | `$ux.Conf.fontColor` | 标题图标颜色 |
| iconSize | Any | `$ux.Conf.fontSize` | 标题图标大小 |
| iconStyle | String |  | 标题图标样式 |
| rightIcon | String |  | 右侧图标 |
| rightArrow | Boolean | true | 右侧箭头图标 |
| rightIconStyle | String |  | 右侧图标样式 |
| customFamily | String |  | 字体family |
| background | String |  | 背景色 |
| [backgroundDark](#backgroundDark) | String | auto | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| border | Boolean | true | 显示下边框 |
| borderColor | String | `$ux.Conf.borderColor` | 下边框颜色 |
| [borderDarkColor](#borderDarkColor) | String | auto | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| minHeight | Any | 0 | 最小高度 |
| height | Any | 40 | 固定高度优先级高于最小高度 |
| padding | Any | `$ux.Conf.rowPadding` | 内部水平边距 |
| center | Boolean | false | 内容是否垂直居中 |
| required | Boolean | false | 是否显示必填星号 |
| path | String |  | 页面跳转路径 |
| show | String | true | 是否显示cell |
| disabled | Boolean | false | 是否禁用cell |
| hover | Boolean | true | 显示点击态 |
| margin | Array |  | 边距[上右下左][上下左右][上下左右] |
| mt | Any |  | 距上单位px |
| mr | Any |  | 距右单位px |
| mb | Any |  | 距下单位px |
| ml | Any |  | 距左单位px |
| xstyle | Array |  | 自定义样式 |

### [darkColor](#darkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [placeholderDark](#placeholderDark)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [backgroundDark](#backgroundDark)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [borderDarkColor](#borderDarkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| click | 单元格点击时触发 |  |
  
  