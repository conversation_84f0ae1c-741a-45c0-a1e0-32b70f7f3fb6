<template>
	<switch class="ux-switch" :style="[style, xstyle]" :name="name" :checked="innerChecked" :color="color" :disabled="disabled" @change="change" />
</template>

<script setup>
	
	/**
	 * Switch 开关选择器
	 * @description 扩展官方组件
	 * @demo pages/component/switch.uvue
	 * @tutorial https://www.uxframe.cn/component/switch.html
	 * @property {String}			name												String | 表单的控件名称，作为键值对的一部分与表单(form组件)一同提交
	 * @property {String} 			theme=[primary|warning|success|error|info]			String | 主题颜色
	 * @value primary 	主色
	 * @value warning 	警告
	 * @value success 	成功
	 * @value error 	错误
	 * @value info 		文本
	 * @property {Boolean}			checked												Boolean | 是否选中 (默认 false)
	 * @property {String}			size = [normal|small|large]							String | 尺寸 (默认 normal)
	 * @value normal 正常
	 * @value small 小尺寸
	 * @value large 大尺寸
	 * @property {String}			color												String | 颜色 (默认 $ux.Conf.primaryColor)
	 * @property {String} 			darkColor=[none|auto|color]							String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Array}			margin												Array | 边距 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}				mt													Any | 距上 单位px
	 * @property {Any}				mr													Any | 距右 单位px
	 * @property {Any}				mb													Any | 距下 单位px
	 * @property {Any}				ml													Any | 距左 单位px
	 * @property {Array}			padding												Array | 填充 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}				pt													Any | 上内边距 单位px
	 * @property {Any}				pr													Any | 右内边距 单位px
	 * @property {Any}				pb													Any | 下内边距 单位px
	 * @property {Any}				pl													Any | 左内边距 单位px
	 * @property {Array}			xstyle												Array<any> | 自定义样式
	 * @property {Boolean}			disabled = [true|false]								Boolean | 是否禁用 (默认 false)
	 * @event {Function}			change												Function | 值改变时触发
	 * <AUTHOR>
	 * @date 2024-04-12 13:46:25
	 */
	
	import { UxSwitchEvent } from '../../libs/types/types.uts'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	import { useForegroundColor, useThemeColor, UxThemeType } from '../../libs/use/style.uts'
	
	defineOptions({
		name: 'ux-switch',
		mixins: [xstyleMixin]
	})
	
	const emit = defineEmits(['change', 'update:modelValue'])
	
	const props = defineProps({
		modelValue: {
			type: Boolean,
			default: false
		},
		name: {
			type: String,
			default: ''
		},
		checked: {
			type: Boolean,
			default: false
		},
		size: {
			type: String,
			default: 'normal'
		},
		theme: {
			type: String,
			default: 'primary'
		},
		color: {
			type: String,
			default: ''
		},
		darkColor: {
			type: String,
			default: ''
		},
		disabled: {
			type: Boolean,
			default: false
		}
	})
	
	const innerChecked = ref(false)
	
	const color = computed(() :string => {
		if(props.color != '') {
			return useForegroundColor(props.color, props.darkColor)
		} else {
			return useThemeColor(props.theme as UxThemeType)
		}
	})
	
	const style = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		// #ifdef APP-IOS
		if(props.size == 'small') {
			css.set('transform', 'scale(0.8)')
		} else if(props.size == 'large') {
			css.set('transform', 'scale(1)')
		} else {
			css.set('transform', 'scale(0.9)')
		}
		// #endif
		
		// #ifndef APP-IOS
		if(props.size == 'small') {
			css.set('transform', 'scale(0.6)')
		} else if(props.size == 'large') {
			css.set('transform', 'scale(0.8)')
		} else {
			css.set('transform', 'scale(0.7)')
		}
		// #endif
		
		return css
	})
	
	function change(e: UniSwitchChangeEvent) {
		
		emit('update:modelValue', e.detail.value)
		
		emit('change', {
			name: props.name,
			value: e.detail.value
		} as UxSwitchEvent)
	}
	
	const modelValue = computed((): boolean => {
		return props.modelValue
	})
	
	const checked = computed((): boolean => {
		return props.checked
	})
	
	watch(modelValue, () => {
		innerChecked.value = modelValue.value
	}, {immediate: true})
	
	watch(checked, () => {
		innerChecked.value = checked.value
	}, {immediate: true})
	
	onMounted(() => {
		
	})
	
</script>

<style lang="scss">

	.ux-switch {
		
	}

</style>