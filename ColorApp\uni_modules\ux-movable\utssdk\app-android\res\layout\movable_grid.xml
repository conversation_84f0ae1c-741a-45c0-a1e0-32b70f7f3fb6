<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">
	<FrameLayout
		android:id="@+id/wrap"
	    android:layout_width="match_parent"
	    android:layout_height="wrap_content">
	
	    <ImageView
	        android:id="@+id/img"
	        android:layout_width="match_parent"
	        android:layout_height="0dp"
	        android:adjustViewBounds="true"
	        android:scaleType="centerCrop"
	        android:visibility="gone" />
	
	    <TextView
	        android:id="@+id/label"
	        android:layout_width="match_parent"
	        android:layout_height="wrap_content"
	        android:layout_gravity="bottom"
	        android:background="#80000000"
	        android:padding="2dp"
	        android:textAlignment="center"
	        android:gravity="center"
	        android:textColor="#FFFFFF"
            android:textSize="12sp"
	        android:visibility="gone" />
	
	    <ImageView
	        android:id="@+id/leftTop"
	        android:layout_width="24dp"
	        android:layout_height="24dp"
	        android:layout_gravity="top|start"
	        android:visibility="gone" />
	
	    <ImageView
	        android:id="@+id/rightTop"
	        android:layout_width="24dp"
	        android:layout_height="24dp"
	        android:layout_gravity="top|end"
	        android:visibility="gone" />
		
		<ImageView
		    android:id="@+id/leftBottom"
		    android:layout_width="24dp"
		    android:layout_height="24dp"
		    android:layout_gravity="bottom|start"
		    android:scaleType="centerCrop"
		    android:visibility="gone" />
		
		<ImageView
		    android:id="@+id/rightBottom"
		    android:layout_width="24dp"
		    android:layout_height="24dp"
		    android:layout_gravity="bottom|end"
		    android:visibility="gone" />
			
		<View
		    android:id="@+id/overlay"
		    android:layout_width="match_parent"
		    android:layout_height="match_parent"
		    android:background="#80000000"
		    android:visibility="gone" />
			
		<TextView
		    android:id="@+id/progress"
		    android:layout_width="wrap_content"
		    android:layout_height="wrap_content"
		    android:layout_gravity="center"
		    android:text="0%"
		    android:textColor="#FFFFFF"
		    android:textSize="16sp"
		    android:textStyle="bold"
		    android:visibility="gone" />
	</FrameLayout>
	
	<EditText
	       android:id="@+id/input"
	       android:layout_width="match_parent"
	       android:layout_height="wrap_content"
           android:layout_marginTop="4dp"
	       android:hint="请输入内容"
           android:textColorHint="#a9a9a9"
           android:textSize="11sp"
           android:maxLines="2"
	       android:padding="2dp"
           android:background="@drawable/edittext_background"
           android:visibility="gone" />
</LinearLayout>
