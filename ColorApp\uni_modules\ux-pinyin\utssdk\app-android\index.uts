import { ToPinyin, <PERSON><PERSON>hine<PERSON> } from '../interface.uts'

import Pinyin from 'com.github.promeg.pinyinhelper.Pinyin'
import CnCityDict from 'com.github.promeg.tinypinyin.lexicons.android.cncity.CnCityDict'

let isInit = false

/**
 * 汉字转拼音，支持多音字
 * @param hanzi 汉字
 */
export const toPinyin: ToPinyin = (hanzi: string): string => {
	
	if(!isInit) {
		Pinyin.init(Pinyin.newConfig().with(CnCityDict.getInstance(UTSAndroid.getAppContext()!)))
		isInit = true
	}
	
	return Pinyin.toPinyin(hanzi, " ").toLowerCase()
}

/**
 * 是否是汉字
 * @param hanzi 汉字
 */
export const isChinese: IsChinese = (hanzi: string): boolean => {
	if(hanzi == '') {
		return false
	}
	
	let b = true
	for (let i = 0; i < hanzi.length; i++) {
	    let c = hanzi.charAt(i);
	    if(!Pinyin.isChinese(c as Char)) {
			b = false
			break
		}
	}
	
	return b
}