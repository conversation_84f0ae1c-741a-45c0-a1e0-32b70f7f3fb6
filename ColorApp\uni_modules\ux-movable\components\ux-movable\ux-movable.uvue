<template>
	<!-- #ifdef APP -->
	<native-view style="flex: 1;" @init="onInit"></native-view>
	<!-- #endif -->

	<!-- #ifndef APP -->
	<view id="movableRef" class="movable">
		<template v-if="movable != null">
			<MovableItem :item="item" :options="movable!.options" v-for="(item, index) in movable!.items" :key="index"></MovableItem>
		</template>
	</view>
	<!-- #endif -->
</template>

<script setup lang="uts">
	
	/**
	* 原生拖拽组件
	* @description 支持宫格和列表布局，内置照片墙上传场景，支持多选、编辑、滑动删除、禁用
	* @demo pages/component/movable.uvue
	* @tutorial https://www.uxframe.cn/component/movable.html
	* @property {String} 			layout=[grid|list]		String | 布局 (默认 grid)
	* @value grid 宫格
	* @value list 列表
	* @property {Number} 			column					Number | 列数 (默认 4)
	* @property {Number} 			spacing					Number | 间隔 (默认 8)
	* @property {Number} 			aspectRatio				Number | item 宽高比 仅 grid 有效 (默认 1)
	* @property {Boolean} 			multiple				Boolean | 多选 (默认 false)
	* @property {Boolean} 			editable				Boolean | 可编辑 (默认 false)
	* @property {Boolean} 			swipe					Boolean | 滑动删除 (默认 false)
	* @property {Boolean} 			disabled				Boolean | 禁用 (默认 false)
	* @event {Function} 			init 					Function | 初始化时触发
	* @event {Function} 			change 					Function | 值改变时触发
	* <AUTHOR>
	* @date 2025-04-07 21:10:21
	*/
	
	import { UxMovable, UxMovableOptions, UxMovableLayout, UxMovableItem, UxMovableStyle } from '@/uni_modules/ux-movable'

	// #ifndef APP
	import MovableItem from './movable-item.uvue'
	// #endif

	const emit = defineEmits(['init', 'change'])

	const props = defineProps({
		layout: {
			type: String as PropType<UxMovableLayout>,
			default: 'grid'
		},
		column: {
			type: Number,
			default: 4
		},
		spacing: {
			type: Number,
			default: 8
		},
		aspectRatio: {
			type: Number,
			default: 1
		},
		multiple: {
			type: Boolean,
			default: false
		},
		editable: {
			type: Boolean,
			default: false
		},
		swipe: {
			type: Boolean,
			default: false
		},
		disabled: {
			type: Boolean,
			default: false
		}
	})

	function change(data : UxMovableItem[]) {
		emit('change', data)
	}

	const movable = ref<UxMovable | null>(null)
	
	// #ifndef APP
	type Pos = {
		x: number,
		y: number
	}
	
	function getIndexPos(index: number, w: number, h: number): Pos {
		let x = props.spacing
		let y = props.spacing
		
		if(props.layout == 'grid') {
			let row = Math.floor(index / props.column)
			let colIndex = index % props.column
			x += (w + props.spacing) * colIndex
			y += (h + props.spacing) * row
		} else {
			y += h * index
		}
		
		return {
			x: x,
			y: y
		} as Pos
	}
	
	let targetIndex = -1
	let activeIndex = -1
	provide('UpdatePos', (id: number, x: number, y: number, w: number, h: number, end: boolean) => {
		let items: UxMovableItem[] = JSON.parseArray<UxMovableItem>(JSON.stringify(movable.value!.items))!
		
		if(end) {
			// 拖拽结束 重置位置
			activeIndex = movable.value!.items.findIndex(e => e.id == id)
			movable.value!.items[activeIndex].index = targetIndex
			return
		}
		
		for (let i = 0; i < items.length; i++) {
			let item = items[i]
			if(item.upload != null) continue
			
			// item的坐标范围
			let pos = getIndexPos(item.index, w, h)
			let itemX = pos.x
			let itemY = pos.y
			
			// 范围内交换位置
			let a = itemX <= x && x <= (itemX + w)
			let b = itemY <= y && y <= (itemY + h)
			if(a && b) {
				targetIndex = item.index
				activeIndex = items.findIndex(e => e.id == id)
				if(activeIndex != -1) {
					if(targetIndex == activeIndex) {
						break
					}
					
					const [movedItem] = items.splice(activeIndex, 1);
					items.splice(targetIndex, 0, movedItem);
					
					movable.value!.items.forEach((e, i) => {
						if(i != activeIndex) {
							e.index = items.findIndex(ee => ee.id == e.id)
						}
					})
				}
				break
			}
		}
	})
	// #endif
	
	var items = [] as UxMovableItem[]
	function setData(data : UxMovableItem[]) {
		items = data
		movable.value?.setData(data)
	}

	function addData(data : UxMovableItem) {
		items.push(data)
		movable.value?.addData(data)
	}

	function delData(data : UxMovableItem) {
		const position = items.findIndex(e => e.id == data.id)
		if (position < 0) {
			return
		}
		items.splice(position, 1)
		movable.value?.delData(data)
	}

	function selectData(data : UxMovableItem) {
		const position = items.findIndex(e => e.id == data.id)
		if (position < 0) {
			return
		}
		items.splice(position, 1, data)
		movable.value?.selectData(data)
	}

	function upload(data : UxMovableItem) {
		const position = items.findIndex(e => e.id == data.id)
		if (position < 0) {
			return
		}
		items.splice(position, 1, data)
		movable.value?.upload(data)
	}

	// #ifdef APP
	function onInit(e : UniNativeViewInitEvent) {
		const options = {
			layout: props.layout,
			column: props.column,
			spacing: props.spacing,
			aspectRatio: props.aspectRatio,
			multiple: props.multiple,
			editable: props.editable,
			swipe: props.swipe,
			disabled: props.disabled,
			change: change,
		} as UxMovableOptions
		
		// #ifdef APP-ANDROID
		movable.value = new UxMovable(e.detail.element, options)
		// #endif
		
		// #ifdef APP-IOS
		movable.value = new UxMovable(e.detail.element, options)
		movable.value?.initEvents(handleEvents)
		// #endif
		
		emit('init')
	}
	
	function handleEvents(type: Int, data: UxMovableItem) {
		let index = -1
		for (let i = 0; i < items.length; i++) {
			if(items[i].id == data.id) {
				index = i
				break
			}
		}
		
		if(index != -1) {
			let item = items[index]
			if(type == 0) {
				item.click?.(data)
			} else if(type == 1) {
				item.leftTop?.click?.(data)
			} else if(type == 2) {
				item.rightTop?.click?.(data)
			} else if(type == 3) {
				item.rightBottom?.click?.(data)
			} else if(type == 4) {
				item.leftBottom?.click?.(data)
			} else if(type == 5) {
				item.input?.input?.(data.input?.content ?? '')
			}
		}
	}
	// #endif
	
	// #ifndef APP
	onMounted(() => {
		const options = {
			layout: props.layout,
			column: props.column,
			spacing: props.spacing,
			aspectRatio: props.aspectRatio,
			multiple: props.multiple,
			editable: props.editable,
			swipe: props.swipe,
			disabled: props.disabled,
			change: change,
		} as UxMovableOptions
		
		movable.value = new UxMovable(options)
		emit('init')
	})
	// #endif

	watch(() : UxMovableLayout => props.layout, () => {
		movable.value?.setLayout(props.layout)
	}, { deep: true })

	watch(() : number => props.column, () => {
		movable.value?.setColumn(props.column)
	}, { deep: true })

	watch(() : number => props.spacing, () => {
		movable.value?.setSpacing(props.spacing)
	}, { deep: true })
	
	watch(() : number => props.aspectRatio, () => {
		movable.value?.setAspectRatio(props.aspectRatio)
	}, { deep: true })

	watch(() : boolean => props.swipe, () => {
		movable.value?.setSwipe(props.swipe)
	}, { deep: true })

	watch(() : boolean => props.editable, () => {
		movable.value?.setEditable(props.editable)
	}, { deep: true })

	watch(() : boolean => props.multiple, () => {
		movable.value?.setMultiple(props.multiple)
	}, { deep: true })

	watch(() : boolean => props.disabled, () => {
		movable.value?.setDisabled(props.disabled)
	}, { deep: true })

	defineExpose({
		setData,
		addData,
		delData,
		selectData,
		upload
	})
</script>

<style lang="scss">
	
	.movable {
		width: 100%;
		height: 100%;
	}
	
</style>