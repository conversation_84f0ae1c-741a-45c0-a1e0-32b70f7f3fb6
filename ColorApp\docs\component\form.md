<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/form?title=Form"></Mobile>

# Form
> 组件类型：UxFormComponentPublicInstance

支持任何组件，只作为数据校验

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| [direction](#direction) | String | horizontal | 布局方向 |
| width | Any | 自适应 | 宽度 |
| size | Any | `$ux.Conf.fontSize` | 字体大小 |
| errorSize | Any | 11 | 错误提示字体大小 |
| color | String | `$ux.Conf.fontColor` | 字体颜色 |
| darkColor | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |

### [direction](#direction)

| 值   | 说明 |
|:------:|:----:|
| horizontal水平
 |  |
| vertical垂直
 |  |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| submit | 提交数据时触发 |  |
  
  