<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/drawer?title=Drawer"></Mobile>

# Drawer
> 组件类型：UxDrawerComponentPublicInstance

支持点击、触摸滑动打开关闭

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| drawer | Slot |  | 侧边栏内容插槽 |
| touchable | Boolean | true | 触摸拖动 |
| [direction](#direction) | String | right | 方向 |
| fixed | Boolean | false | 固定定位 |
| width | String | 85% | 宽度 |
| height | String | 40% | 高度 |
| background | String | transparent | 背景色 |
| border | Boolean | false | 显示边框 |
| opacity | Number | `$ux.Conf.maskAlpha` | 遮罩透明度0-1 |
| duration | Number | 300ms | 过渡时间 |
| showBlur | Boolean | false | 开启模糊背景 |
| blurRadius | Number | 10 | 模糊半径 |
| blurColor | String | rgba(10,10,10,0.3) | 模糊颜色 |
| zIndex | Number | 20000 | 层级z-index |
| maskClose | Boolean | true | 遮罩层关闭 |
| interceptBack | Boolean | false | 拦截页面返回 |
| disabled | Boolean | false | 是否禁用 |

### [direction](#direction)

| 值   | 说明 |
|:------:|:----:|
| top向上
 |  |
| right向右
 |  |
| bottom向下
 |  |
| left向左
 |  |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| change | 状态改变时触发 |  |
| close | 关闭时触发 |  |
  
  