<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/signature?title=Signature"></Mobile>

# Signature
> 组件类型：UxSignatureComponentPublicInstance

支持横竖屏切换、支持背景色、笔触大小、笔触颜色、背景颜色配置

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| text | String | 签名区 | 背景文字 |
| size | Number | 2 | 笔触大小 |
| [theme](#theme) | String | info | 主题 |
| color | String | `$ux.Conf.infoColor` | 笔触颜色 |
| background | String | `$ux.Conf.backgroundColor` | 背景颜色 |
| confirm | String | 确定 | 确定文字 |
| confirmColor | String | `$ux.Conf.primaryColor` | 确定文字颜色 |
| minWidth | Number | 2 | 最小宽度 |
| maxWidth | Number | 6 | 最大宽度 |
| minSpeed | Number | 1.5 | 最小速度 |
| maxHistory | Number | 20 | 最大可撤销记录 |
| [fullScreen](#fullScreen) | Boolean | false | 全屏 |
| [format](#format) | String | png | 图片格式 |
| [showBtns](#showBtns) | Boolean | true | 显示操作按钮 |
| [disableScroll](#disableScroll) | Boolean | true | 禁止屏幕滚动 |
| [disabled](#disabled) | Boolean | false | 是否禁用 |
| margin | Array |  | 边距[上右下左][上下左右][上下左右] |
| mt | Number |  | 距上单位px |
| mr | Number |  | 距右单位px |
| mb | Number |  | 距下单位px |
| ml | Number |  | 距左单位px |
| padding | Array |  | 填充[上右下左][上下左右][上下左右] |
| pt | Number |  | 上内边距单位px |
| pr | Number |  | 右内边距单位px |
| pb | Number |  | 下内边距单位px |
| pl | Number |  | 左内边距单位px |
| xstyle | Array |  | 自定义样式 |

### [theme](#theme)

| 值   | 说明 |
|:------:|:----:|
| text | 文字
 |
| info | 默认
 |
| primary | 主要
 |
| success | 成功
 |
| warning | 警告
 |
| error | 错误
 |

### [fullScreen](#fullScreen)

| 值   | 说明 |
|:------:|:----:|
| true
 |  |
| false
 |  |

### [format](#format)

| 值   | 说明 |
|:------:|:----:|
| png
 |  |
| jpg
 |  |

### [showBtns](#showBtns)

| 值   | 说明 |
|:------:|:----:|
| true
 |  |
| false
 |  |

### [disableScroll](#disableScroll)

| 值   | 说明 |
|:------:|:----:|
| true
 |  |
| false
 |  |

### [disabled](#disabled)

| 值   | 说明 |
|:------:|:----:|
| true
 |  |
| false
 |  |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| change | 生成签名时触发 |  |
  
  