<template>
	<view class="ux-radio-group" :style="style">
		<slot></slot>
	</view>
</template>

<script setup>
	
	/**
	 * RadioGroup 单选组
	 * @demo pages/component/radio.uvue
	 * @tutorial https://www.uxframe.cn/component/radio.html
	 * @property {String}			direction = [row|col]						String | 方向 (默认 row ) 
	 * @value row 水平
	 * @value col 垂直
	 * @property {String} 			theme=[primary|warning|success|error|info]	String | 主题颜色 (默认 primary)
	 * @value primary 	主色
	 * @value warning 	警告
	 * @value success 	成功
	 * @value error 	错误
	 * @value info 		文本
	 * @property {String}			mode = [radio|button|cell]					String | 单选模式 (默认 radio )
	 * @value radio 单选
	 * @value button 按钮
	 * @value cell 单元格
	 * @property {String}			shape = [circle|square]						String | 形状，mode=radio时有效 (默认 circle )
	 * @value circle 圆形
	 * @value square 方形
	 * @property {Any} 				size										Any | 字体大小（默认 $ux.Conf.fontSize ）
	 * @property {String} 			color										String | 文本颜色（默认 $ux.Conf.fontColor ）
	 * @property {String} 			darkColor=[none|auto|color]					String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {String} 			checkedColor								String | 选择颜色（默认 #ffffff ）
	 * @property {String} 			checkedColorDark=[none|auto|color]			String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {String} 			backgroundColor								String | radio背景色（默认 $ux.Conf.backgroundColor ）
	 * @property {String} 			backgroundDark=[none|auto|color]			String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {String} 			activeBackgroundColor						String | radio背景选择颜色（默认 $ux.Conf.primaryColor ）
	 * @property {String} 			activeBackgroundDark=[none|auto|color]		String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {String} 			borderColor									String | radio边框色（默认 $ux.Conf.borderColor ）
	 * @property {String} 			activeBorderColor							String | radio边框选择颜色（默认 $ux.Conf.primaryColor ）
	 * @property {Boolean} 			inverse=[true|false]						Boolean | 反选（默认 true ）
	 * @property {Boolean} 			readonly=[true|false]						Boolean | 只读（默认 false ）
	 * @property {Boolean} 			disabled=[true|false]						Boolean | 是否禁用（默认 false ）
	 * @property {Array}			margin										Array | 边距 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}				mt											Any | 距上 单位px
	 * @property {Any}				mr											Any | 距右 单位px
	 * @property {Any}				mb											Any | 距下 单位px
	 * @property {Any}				ml											Any | 距左 单位px
	 * @property {Array}			padding										Array | 填充 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}				pt											Any | 上内边距 单位px
	 * @property {Any}				pr											Any | 右内边距 单位px
	 * @property {Any}				pb											Any | 下内边距 单位px
	 * @property {Any}				pl											Any | 左内边距 单位px
	 * @property {Array}			xstyle										Array<any> | 自定义样式
	 * @event {Function}			change										Function | 值改变时触发
	 * <AUTHOR>
	 * @date 2024-01-19 13:21:16
	 */
	
	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	
	defineOptions({
		name: 'ux-radio-group',
		mixins: [xstyleMixin]
	})
	
	const emit = defineEmits(['update:modelValue', 'change'])
	
	const props = defineProps({
		modelValue: {
			type: String,
			default: ''
		},
		direction: {
			type: String,
			default: 'row'
		},
		mode: {
			type: String,
			default: 'radio'
		},
		theme: {
			type: String,
			default: ''
		},
		shape: {
			type: String,
			default: ''
		},
		size: {
			default: 0
		},
		color: {
			type: String,
			default: ''
		},
		darkColor: {
			type: String,
			default: ''
		},
		checkedColor: {
			type: String,
			default: ''
		},
		checkedColorDark: {
			type: String,
			default: ''
		},
		backgroundColor: {
			type: String,
			default: ''
		},
		backgroundDark: {
			type: String,
			default: ''
		},
		activeBackgroundColor: {
			type: String,
			default: ''
		},
		activeBackgroundDark: {
			type: String,
			default: ''
		},
		borderColor: {
			type: String,
			default: ''
		},
		activeBorderColor: {
			type: String,
			default: ''
		},
		inverse: {
			type: Boolean,
			default: true
		},
		readonly: {
			type: Boolean,
			default: false
		},
		disabled: {
			type: Boolean,
			default: false
		},
	})
	
	const nodes = ref<Array<UxRadioComponentPublicInstance>>([] as Array<UxRadioComponentPublicInstance>) 
	
	const style = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('flex-direction', props.direction == 'row' ? 'row' : 'column')
		
		css = $ux.Util.xStyle(css, props.margin, props.mt, props.mr, props.mb, props.ml, props.padding, props.pt, props.pr, props.pb, props.pl)
		
		return css
	})
	
	const parentData = computed((): Map<string, any | null> => {
		let data = new Map<string, any | null>()
		
		data.set('mode', props.mode)
		data.set('theme', props.theme) 
		data.set('shape', props.shape)
		data.set('size', props.size)
		data.set('color', props.color)
		data.set('darkColor', props.darkColor)
		data.set('checkedColor', props.checkedColor)
		data.set('checkedColorDark', props.checkedColorDark)
		data.set('backgroundColor', props.backgroundColor)
		data.set('activeBackgroundColor', props.activeBackgroundColor)
		data.set('activeBackgroundDark', props.activeBackgroundDark)
		data.set('borderColor', props.borderColor)
		data.set('activeBorderColor', props.activeBorderColor)
		data.set('inverse', props.inverse)
		data.set('disabled', props.disabled)
		data.set('readonly', props.readonly)
		data.set('value', props.modelValue)  
		
		return data
	})
	
	provide('parentData', parentData)
	
	const modelValue = computed((): string => {
		return props.modelValue
	})
	
	function register(child : UxRadioComponentPublicInstance) : void {
		nodes.value.push(child)
	}
	
	function closeAll(child : UxRadioComponentPublicInstance, value: string) {
		nodes.value.forEach((instance: UxRadioComponentPublicInstance) => {
			if (instance == child) {
				emit('update:modelValue', value)
				emit('change', value)
			}
		})
	}
	
	onMounted(() => {
		
	})
	
	defineExpose({
		register,
		closeAll
	})
</script>

<style lang="scss" scoped>
	.ux-radio-group {
		display: flex;
		flex-direction: row;
	}
	
</style>