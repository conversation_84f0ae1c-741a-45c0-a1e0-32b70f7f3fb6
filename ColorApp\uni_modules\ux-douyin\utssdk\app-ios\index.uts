import { UxDouyinRegisterOptions, UxDouyinLoginOptions, UxDouyinShareOptions, UxDouyinMiniProgram, UxDouyinSuccess } from '../interface.uts'
import { UxDouyinFailImpl } from '../unierror';

var isAuth = false
var authResp: UxDouyinLoginOptions | null = null
var shareResp: UxDouyinShareOptions | null = null

/**
* 注册
* @param {UxDouyinRegisterOptions} options
* @tutorial https://www.uxframe.cn/api/douyin.html#register
*/
export const register = function (options : UxDouyinRegisterOptions) {
	// let ok = douyin.register(options.appid)
	// if(ok) {
	// 	options.success?.({
	// 		code: '0',
	// 		msg: 'register success'
	// 	} as UxDouyinSuccess)
	// } else {
	// 	options.fail?.(new UxDouyinFailImpl(9010001))
	// }
}

/**
* 登录
* @param {UxDouyinLoginOptions} options
* @tutorial https://www.uxframe.cn/api/douyin.html#login
*/
export const login = function (options : UxDouyinLoginOptions) {
	isAuth = true
	authResp = options
	// douyin.login(options.state)
}

/**
* 分享
* @param {UxDouyinShareOptions} options
* @tutorial https://www.uxframe.cn/api/douyin.html#share
*/
export const share = function (options : UxDouyinShareOptions) {
	isAuth = false
	shareResp = options
	
	// if(options.type == 0) {
	// 	if(options.imageUrl == null) {
	// 		options.fail?.(new UxDouyinFailImpl(9010002))
	// 		return
	// 	}
		
	// 	let url = UTSAndroid.convert2AbsFullPath(util.getPath(options.imageUrl!))
	// 	douyin.shareImage(url)
	// } else if(options.type == 1) {
	// 	douyin.shareHtml(options.title!, options.href!, options.summary!, options.imageUrl)
	// } else if(options.type == 2) {
	// 	douyin.shareProgram(options.miniProgram?.id!, options.miniProgram?.title!, options.miniProgram?.path!, options.miniProgram?.query, options.miniProgram?.imageId!, options.miniProgram?.state)
	// }
}