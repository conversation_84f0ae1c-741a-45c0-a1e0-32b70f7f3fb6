<template>
	<text class="ux-countto" :style="[style, xstyle]">
		{{ _value }}
	</text>
</template>

<script setup>
	
	/**
	 * CountTo 数字滚动
	 * @description 支持设置滚动时间，可显示小数
	 * @demo pages/component/countto.uvue
	 * @tutorial https://www.uxframe.cn/component/countto.html
	 * @property {String}			theme=[text|info|primary|success|warning|error]		String | 主题 (默认 info)
	 * @value text			文字
	 * @value info   		默认
	 * @value primary   	主要
	 * @value success  		成功
	 * @value warning   	警告
	 * @value error  		错误
	 * @property {Number}			startVal							Number | 开始的数值 (默认 0)
	 * @property {Number}			endVal								Number | 要滚动的目标数值 (默认 0)
	 * @property {Number}			duration							Number | 滚动到目标数值的动画持续时间，单位为毫秒 (ms） (默认 2000)
	 * @property {Boolean}			autoplay							Boolean | 设置数值后是否自动开始滚动  (默认 true)
	 * @property {Number}			decimals							Number | 要显示的小数位数 (默认 0)
	 * @property {String} 			format=[cmoney|qmoney|wmoney]		String | 格式化规则
	 * @value cmoney 大写金额
	 * @value qmoney 金额千分制
	 * @value wmoney 金额万分制
	 * @property {String}			color								String | 字体颜色 ( 默认 $ux.Conf.fontColor )
	 * @property {String} 			darkColor=[none|auto|color]			String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Any}				size								Any | 字体大小 ( 默认 $ux.Conf.fontSize)
	 * @property {Boolean}			bold								Boolean | 字体是否加粗 (默认 false)
	 * @property {Array}			margin								Array | 边距 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}				mt									Any | 距上 单位px
	 * @property {Any}				mr									Any | 距右 单位px
	 * @property {Any}				mb									Any | 距下 单位px
	 * @property {Any}				ml									Any | 距左 单位px
	 * @property {Array}			padding								Array | 填充 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}				pt									Any | 上内边距 单位px
	 * @property {Any}				pr									Any | 右内边距 单位px
	 * @property {Any}				pb									Any | 下内边距 单位px
	 * @property {Any}				pl									Any | 左内边距 单位px
	 * @property {Array}			xstyle								Array<any> | 自定义样式
	 * @event {Function} 			end 								Function | 数值滚动到目标值时触发
	 * <AUTHOR>
	 * @date 2024-01-11 11:02:28
	 */
	
	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	import { useThemeColor, useFontColor, useFontSize, UxThemeType } from '../../libs/use/style.uts'
	
	defineOptions({
		name: 'ux-countto',
		mixins: [xstyleMixin]
	})
	
	const emit = defineEmits(['end'])
	
	const props = defineProps({
		startVal: {
			type: Number,
			default: 0
		},
		endVal: {
			type: Number,
			default: 1000
		},
		duration: {
			type: Number,
			default: 2000
		},
		autoplay: {
			type: Boolean,
			default: true
		},
		decimals: {
			type: Number,
			default: 0
		},
		size: {
			default: 0
		},
		color: {
			type: String,
			default: ''
		},
		darkColor: {
			type: String,
			default: ''
		},
		bold: {
			type: Boolean,
			default: false
		},
		format: {
			type: String,
			default: ''
		}
	})
	
	const value = ref('')
	const timer  = ref(0)
	const paused = ref(false)
	
	const _value = computed((): string => {
		let text = value.value
		
		if(props.format == 'cmoney') {
			// 大写金额
			text = $ux.Fmt.upperMoney(text)
		} else if(props.format == 'qmoney') {
			// 金额千分制
			text = $ux.Fmt.fmtMoney(text, false)
		} else if(props.format == 'wmoney') {
			// 金额万分制
			text = $ux.Fmt.fmtMoney(text, true)
		}
		
		return text
	})
	
	const fontSize = computed(():number => {
		return useFontSize($ux.Util.getPx(props.size), 1)
	})
	
	const fontColor = computed(():string => {
		if(props.color != '') {
			return useFontColor(props.color, props.darkColor)
		} else {
			return useThemeColor(props.theme as UxThemeType)
		}
	})
	
	const style = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('color', fontColor.value)
		css.set('font-size', $ux.Util.addUnit(fontSize.value))
		css.set('font-weight', props.bold ? 'bold' : 'normal')
		
		css = $ux.Util.xStyle(css, props.margin, props.mt, props.mr, props.mb, props.ml, props.padding, props.pt, props.pr, props.pb, props.pl)
	
		return css
	})
	
	function pause() {
		if (!paused.value) {
			paused.value = true
			clearInterval(timer.value)
		}
	}
	
	function stop() {
		value.value = props.endVal.toFixed(props.decimals).toString()
		emit('end')
		clearInterval(timer.value)
	}
	
	function play() {
		const step : number = props.endVal / (props.duration / 10)
		timer.value = setInterval(() => {
			if (!paused.value) {
				const random : number = $ux.Random.random(0, step)
				const count : number = parseFloat(value.value) + random
				if (count >= props.endVal) {
					stop()
				} else {
					const factor = Math.pow(10, props.decimals)
					value.value = (count + $ux.Random.generateRandom(props.decimals) / factor).toFixed(props.decimals).toString()
				}
			}
		}, 10)
	}
	
	function start() {
		clearInterval(timer.value)
	
		paused.value = false
		value.value = props.startVal.toString()
		play()
	}
	
	function resume() {
		if (paused.value) {
			clearInterval(timer.value)
	
			paused.value = false
			play()
		}
	}
	
	function init() {
		value.value = props.startVal.toString()
	
		if (props.autoplay) {
			start()
		}
	}
	
	const startVal = computed((): number => {
		return props.startVal
	})
	
	const endVal = computed((): number => {
		return props.endVal
	})
	
	watch(startVal, () => {
		init()
	})
	
	watch(endVal, () => {
		init()
	})
	
	onMounted(() => {
		init()
	})
	
	onUnmounted(() => {
		clearInterval(timer.value)
	})
	
	defineExpose({
		start,
		stop,
		pause,
		resume
	})
</script>

<style lang="scss">
	
	.ux-countto {
		
	}
	
</style>