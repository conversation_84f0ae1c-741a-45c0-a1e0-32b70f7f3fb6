<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/unimoduleUxShimmer-Swift.h</key>
		<data>
		CjXktBt1enwDDhHe4w5Lf0Hk+Qk=
		</data>
		<key>Headers/unimoduleUxShimmer.h</key>
		<data>
		FH2SRnQAHIO8AbDZPa5/Bz/CcdA=
		</data>
		<key>Info.plist</key>
		<data>
		cKHnpi+QG9zrWCaJ5f/zOlZx5h0=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		VyCAHVpRybNIX3ASO/+LlsFC6xE=
		</data>
		<key>Modules/unimoduleUxShimmer.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		GAj7IEgY2+EXTiyKxy450Ynh1xs=
		</data>
		<key>Modules/unimoduleUxShimmer.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		SSyl/E3dOzVr2FEDIMWTbUUZIxY=
		</data>
		<key>Modules/unimoduleUxShimmer.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		yvSaLFde5AGNTUCkFVNnPNvm8Vc=
		</data>
		<key>Modules/unimoduleUxShimmer.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		D+kbF/33r5XCEIgm1REBTGt5/oU=
		</data>
		<key>Modules/unimoduleUxShimmer.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		yvSaLFde5AGNTUCkFVNnPNvm8Vc=
		</data>
		<key>Modules/unimoduleUxShimmer.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		j3x734xePMTrgoWMkN7KeB5sSag=
		</data>
		<key>config.json</key>
		<data>
		UPOAXvc7TiWwAAeuTOMIlyEf5u8=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/unimoduleUxShimmer-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			8fKxWTO856fTWiWsH536rOqxt3HTNapikxu5ztqwcTs=
			</data>
		</dict>
		<key>Headers/unimoduleUxShimmer.h</key>
		<dict>
			<key>hash2</key>
			<data>
			XAsze1m7Z4PhyI2gj+RafZVS4xyqWjgvqraTawdVPMY=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			67l89NvnMVxbwaThEKsyiN2klur4dVqPrQCuyD7bdWo=
			</data>
		</dict>
		<key>Modules/unimoduleUxShimmer.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			7JMyN8/ofWOWzuVtG/BrpTF7oQyvqW/Ec1OOpdD9kPA=
			</data>
		</dict>
		<key>Modules/unimoduleUxShimmer.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			GfGIGRcdsjiPYpjj1UK3a7uSrzstISR8biHOWFyeO6A=
			</data>
		</dict>
		<key>Modules/unimoduleUxShimmer.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			34ej70m1YpoezPfRRjbJqc1LuF2nZffeDz4SDwjxdcE=
			</data>
		</dict>
		<key>Modules/unimoduleUxShimmer.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			6VmdffV85snvg6gBLAZruqRwJuYKXTCi9/AutqVgwaY=
			</data>
		</dict>
		<key>Modules/unimoduleUxShimmer.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			34ej70m1YpoezPfRRjbJqc1LuF2nZffeDz4SDwjxdcE=
			</data>
		</dict>
		<key>Modules/unimoduleUxShimmer.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			/JAhaFxYDbyzLP6MKXGMMIGRXj1RnIRg6/87sLYhaqk=
			</data>
		</dict>
		<key>config.json</key>
		<dict>
			<key>hash2</key>
			<data>
			5qEA1rOVwnr38K2cEgB4/9ah8RL8jiMc85PywGxyt9g=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
