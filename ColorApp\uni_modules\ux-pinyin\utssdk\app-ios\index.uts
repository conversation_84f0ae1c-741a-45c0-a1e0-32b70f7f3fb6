import { ToPinyin, <PERSON><PERSON>hine<PERSON> } from '../interface.uts'

/**
 * 汉字转拼音，支持多音字
 * @param hanzi 汉字
 */
export const toPinyin: ToPinyin = (hanzi: string): string => {
	return UxPinyin.toPinyin(hanzi = hanzi) as string
}

/**
 * 是否是汉字
 * @param hanzi 汉字
 */
export const isChinese: IsChinese = (hanzi: string): boolean => {
	if(hanzi == '') {
		return false
	}
	
	return UxPinyin.isChinese(hanzi = hanzi)
}