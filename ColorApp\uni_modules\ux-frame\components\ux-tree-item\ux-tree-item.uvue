<template>
	<view class="ux-tree-item" :style="style">
		<view class="ux-tree-item__item" @click="click">
			<view class="ux-tree-item__row">
				<ux-icon v-if="(node.getArray<UTSJSONObject>('children') ?? []).length > 0" :xstyle="iconStyle" :type="open ? openIcon : closeIcon" :color="iconColor" :size="iconSize" :custom-family="customFamily"></ux-icon>
				<text class="ux-tree-item__label" :style="labelStyle">{{ node[labelKey] }}</text>
			</view>
			
			<view class="ux-tree-item__selected" @click="stop">
				<ux-radio v-if="showSelect" :checked="selected" :disabled="disabled" :color="color" :border-color="borderColor" :active-border-color="activeColor" :active-background-color="activeColor" shape="square" @change="change"></ux-radio>
			</view>
		</view>
		
		<ux-tree-item v-if="open && (node.getArray<UTSJSONObject>('children') ?? []).length > 0"
			:level="level + 1"
			:values="values"
			:id-key="idKey" 
			:label-key="labelKey"
			:node="n"
			:open-ids="openIds"
			:offset="offset"
			:color="color"
			:active-color="activeColor"
			:size="size"
			:size-type="sizeType"
			:bold="bold"
			:open-icon="openIcon"
			:close-icon="closeIcon"
			:icon-size="iconSize"
			:custom-family="customFamily"
			:include-child="includeChild"
			:only-child="onlyChild"
			@click="itemClick"
			@change="itemChange"
			v-for="(n, index) in children" :key="index">
		</ux-tree-item>
	</view>
</template>

<script setup>
	/**
	 * TreeItem 树子项
	 * @description 支持无限嵌套，支持异步加载
	 * @demo pages/component/tree.uvue
	 * @tutorial https://www.uxframe.cn/component/tree.html
	 * <AUTHOR>
	 * @date 2025-02-18 11:35:16
	 */
	
	import { $ux } from '../../index'
	import { useFontSize, useColor } from '../../libs/use/style.uts'
	
	defineOptions({
		name: 'ux-tree-item'
	})
	
	const emit = defineEmits(['click', 'change'])
	
	const props = defineProps({
		values: {
			type: Array as PropType<string[]>,
			default: () : string[] => [] as string[]
		},
		level: {
			type: Number,
			default: 0
		},
		idKey: {
			type: String,
			default: 'id'
		},
		labelKey: {
			type: String,
			default: 'label'
		},
		node: {
			type: Object as PropType<UTSJSONObject>,
			default: () : UTSJSONObject => {
				return {} as UTSJSONObject
			}
		},
		openIds: {
			type: Array as PropType<Array<string>>,
			default: () : string[] => {
				return [] as string[]
			}
		},
		offset: {
			type: Number,
			default: 15
		},
		color: {
			type: String,
			default: ''
		},
		activeColor: {
			type: String,
			default: ''
		},
		size: {
			default: 0
		},
		sizeType: {
			type: String,
			default: 'small'
		},
		bold: {
			type: Boolean,
			default: false
		},
		openIcon: {
			type: String,
			default: 'folderopen-filled'
		},
		closeIcon: {
			type: String,
			default: 'folderclose-outline'
		},
		iconSize: {
			default: 0
		},
		customFamily: {
			type: String,
			default: ''
		},
		includeChild: {
			type: Boolean,
			default: false
		},
		onlyChild: {
			type: Boolean,
			default: false
		},
	})
	
	const id = computed(() => {
		return props.node.getString(props.idKey) ?? ''
	})
	
	const children = computed(() => {
		return props.node.getArray<UTSJSONObject>('children') ?? []
	})
	
	const open = computed(() => {
		return props.openIds.indexOf(id.value) != -1
	})
	
	const disabled = computed(() => {
		return props.node.getBoolean('disabled') ?? false
	})
	
	const showSelect = computed(() => {
		if(props.onlyChild) {
			return (props.node.getArray<UTSJSONObject>('children') ?? []).length == 0
		} else {
			return true
		}
	})
	
	const selected = ref(false)
	
	const values = computed(() => {
		return props.values
	})
	
	watch(values, () => {
		selected.value = props.values.indexOf(id.value) != -1
	}, {immediate: true, deep: true})
	
	function itemChange(node: UTSJSONObject) {
		emit('change', node)
	}
	
	function change(e: string) {
		itemChange(props.node)
	}
	
	function itemClick(node: UTSJSONObject) {
		emit('click', node)
	}
	
	function click(e: MouseEvent) {
		itemClick(props.node)
		
		e.stopPropagation()
	}
	
	function stop(e: MouseEvent) {
		e.stopPropagation()
	}
	
	const fontSize = computed(() : number => {
		let type = props.sizeType == 'big' ? 2 : (props.sizeType == 'small' ? 1 : 0) 
		return useFontSize($ux.Util.getPx(props.size), type)
	})
	
	const iconColor = computed(() => {
		return open.value ? props.activeColor : props.color
	})
	
	const borderColor = computed(() => {
		return useColor('#c4c4c4', 'auto')
	})
	
	const style = computed(() => {
		let css = new Map<string, any>()
		
		css.set('marginLeft', `${props.level * props.offset}px`)
		
		return css
	})
	
	const labelStyle = computed(() => {
		let css = new Map<string, any>()
		
		css.set('color', selected.value ? props.activeColor : props.color)
		css.set('font-weight', props.bold ? 'bold' : 'normal')
		css.set('opacity', disabled.value ? 0.5 : 1)
		
		return css
	})
	
	const iconStyle = computed(() => {
		let css = new Map<string, any>()
		
		css.set('margin-right', '6px')
		css.set('opacity', disabled.value ? 0.5 : 1)
		
		return [css]
	})
	
</script>

<style lang="scss">

	.ux-tree-item {
		
		&__item {
			height: 40px;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: space-between;
			border-bottom: 1px solid #f6f6f6;
		}
		
		&__row {
			display: flex;
			flex-direction: row;
			align-items: center;
		}
			
		&__label {
			font-size: 14px;
		}
		
		&__selected {
			width: 30px;
			height: 30px;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
		}
	}

</style>