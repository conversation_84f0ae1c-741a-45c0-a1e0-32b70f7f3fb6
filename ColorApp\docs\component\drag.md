<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/drag?title=Drag"></Mobile>

# Drag
> 组件类型：UxDragComponentPublicInstance

支持列表式、宫格式拖动排序，支持设置列数，列表式支持边界滚动，支持动态新增删除项目

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| name | String | id | 唯一索引名称 |
| list | Array |  | 数据列表 |
| col | Number | 1 | 列数 |
| height | Any | 45 | 单项高度 |
| border | Boolean | false | 显示下边框 |
| longtouch | Boolean | true | 是否开启长按拖动 |
| touchtime | Number | 250 | 长按拖动触发时间开启长按拖动才有效 |
| speed | Number | 1 | 列表边界滚动速度 |
| disabled | Boolean | false | 是否禁用 |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| click | 点击项目时触发 |  |
| change | 拖拽完成时触发 |  |
| start | 拖拽开始时触发 |  |
| end | 拖拽结束时触发 |  |
  
  