<template>
	<view class="ux-skeleton" :style="[style, xstyle]">
		<view class="ux-skeleton__warp" :style="[warpStyle]">
			<view v-if="avatar" class="ux-skeleton__avatar" :style="[avatarStyle]"></view>
			<view class="ux-skeleton__rows">
				<view class="ux-skeleton__row" :style="[rowStyle(w, i)]" v-for="(w, i) in rows" :key="i"></view>
			</view>
		</view>
	</view>
</template>

<script setup>
	
	/**
	 * Skeleton 骨架屏
	 * @description 此组件仅是占位显示，需要辉光效果请用ux-shimmer插件包裹
	 * @demo pages/component/skeleton.uvue
	 * @tutorial https://www.uxframe.cn/component/skeleton.html
	 * @property {Array}			rows								String[] | 段落占位图行数, 值是宽度 (默认 '100%' )
	 * @property {Array}			height								Any[] | 段落高度 (默认 [18] )
	 * @property {Any}				spacing								Any | 段落间隔 (默认 5 )
	 * @property {Boolean}			avatar								Boolean | 显示头像 (默认 32 )
	 * @property {Any}				avatarWidth							Any | 头像宽度 (默认 32 )
	 * @property {Any}				avatarHeight						Any | 头像高度 (默认 32 )
	 * @property {String}			shape=[circle|square]				String | 头像占位图的形状 (默认 square )
	 * @value circle 圆形
	 * @value square 方形
	 * @property {String}			background							String | 段落背景色 (默认 #e9e9e9 )
	 * @property {Array}			margin								Array | 边距 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}				mt									Any | 距上 单位px
	 * @property {Any}				mr									Any | 距右 单位px
	 * @property {Any}				mb									Any | 距下 单位px
	 * @property {Any}				ml									Any | 距左 单位px
	 * @property {Array}			padding								Array | 填充 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}				pt									Any | 上内边距 单位px
	 * @property {Any}				pr									Any | 右内边距 单位px
	 * @property {Any}				pb									Any | 下内边距 单位px
	 * @property {Any}				pl									Any | 左内边距 单位px
	 * @property {Array}			xstyle								Array<any> | 自定义样式
	 * <AUTHOR>
	 * @date 2023-11-13 00:54:11
	 */
	
	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	
	defineOptions({
		name: 'ux-skeleton',
		mixins: [xstyleMixin]
	})
	
	const props = defineProps({
		rows: {
			type: Array as PropType<Array<string>>,
			default: () : Array<string> => [] as Array<string>
		},
		height: {
			default: () : number[] => [18] as number[]
		},
		spacing: {
			default: 5
		},
		avatar: {
			type: Boolean,
			default: false
		},
		avatarWidth: {
			default: 32
		},
		avatarHeight: {
			default: 32
		},
		shape: {
			type: String,
			default: 'square'
		},
		background: {
			type: String,
			default: '#e9e9e9'
		},
	})
	
	const style = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css = $ux.Util.xStyle(css, props.margin, props.mt, props.mr, props.mb, props.ml, props.padding, props.pt, props.pr, props.pb, props.pl)
		
		return css
	})
	
	const warpStyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('flex-direction', props.avatar ? 'row' : 'column')
		
		return css
	})
	
	const avatarStyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('width', $ux.Util.addUnit(props.avatarWidth))
		css.set('height', $ux.Util.addUnit(props.avatarHeight))
		css.set('border-radius', props.shape == 'square' ? $ux.Util.addUnit(5) : $ux.Util.addUnit(props.avatarWidth))
		css.set('margin-top', $ux.Util.addUnit(props.spacing))
		css.set('background-color', props.background)
		
		return css
	})
	
	function rowHeight(index: number): number {
		let _height: any[] = []
		if(props.height != null) {
			_height = props.height as any[]
		}
		
		if(_height.length == 0) {
			return 18
		}
		
		if(index <= _height.length - 1) {
			return $ux.Util.getPx(_height[index])
		}
		
		return $ux.Util.getPx(_height[0])
	}
	
	function rowStyle(width: string, index: number): Map<string, any> {
		let css = new Map<string, any>()
		
		css.set('width', $ux.Util.addUnit(width))
		css.set('height', $ux.Util.addUnit(rowHeight(index)))
		css.set('margin-top', $ux.Util.addUnit(props.spacing))
		css.set('background-color', props.background)
		
		return css
	}
</script>

<style lang="scss">
	.ux-skeleton {
		width: 100%;
		
		&__warp {
			display: flex;
			flex-direction: column;
		}
		
		&__rows {
			width: 100%;
			display: flex;
			flex-direction: column;
		}
		
		&__row {
			width: 100%;
			overflow: hidden;
			position: relative;
		}
		
		&__avatar {
			margin-right: 10px;
			opacity: 1;
		}
	}
</style>