<template>
	<view class="container">
		<!-- 头部标题 -->
		<view class="header">
			<text class="title">组件动态测试</text>
			<text class="subtitle">选择组件进行动态加载和测试</text>
		</view>

		<!-- 组件分类选择 -->
		<view class="category-section">
			<text class="section-title">组件分类</text>
			<scroll-view class="category-scroll" scroll-x="true">
				<view class="category-list">
					<view 
						v-for="(category, index) in categories" 
						:key="index"
						class="category-item"
						:class="{ active: selectedCategory === index }"
						@click="selectCategory(index)"
					>
						<text class="category-text">{{ category.name }}</text>
						<text class="category-count">({{ category.components.length }})</text>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 组件列表 -->
		<view class="component-section">
			<text class="section-title">{{ categories[selectedCategory].name }}</text>
			<scroll-view class="component-scroll" scroll-y="true">
				<view class="component-list">
					<view 
						v-for="(component, index) in categories[selectedCategory].components" 
						:key="index"
						class="component-item"
						:class="{ active: selectedComponent === component }"
						@click="loadComponent(component)"
					>
						<view class="component-info">
							<text class="component-name">{{ component.name }}</text>
							<text class="component-desc">{{ component.description }}</text>
						</view>
						<text class="component-tag">{{ component.tag }}</text>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 组件预览区域 -->
		<view class="preview-section">
			<text class="section-title">组件预览</text>
			<view class="preview-container">
				<view v-if="!selectedComponent" class="preview-placeholder">
					<text class="placeholder-text">请选择一个组件进行预览</text>
				</view>
				<view v-else class="preview-content">
					<!-- 动态组件渲染 - 使用条件渲染替代动态组件 -->
					<ux-button 
						v-if="selectedComponent.tag === 'ux-button'"
						:text="currentProps.text"
						:theme="currentProps.theme"
						:disabled="currentProps.disabled"
						:loading="currentProps.loading"
						:plain="currentProps.plain"
						@click="onComponentEvent('click', $event)"
					/>
					<ux-tag 
						v-else-if="selectedComponent.tag === 'ux-tag'"
						:text="currentProps.text"
						:theme="currentProps.theme"
						:closable="currentProps.closable"
						:disabled="currentProps.disabled"
						@click="onComponentEvent('click', $event)"
						@close="onComponentEvent('close', $event)"
					/>
					<ux-cell 
						v-else-if="selectedComponent.tag === 'ux-cell'"
						:title="currentProps.title"
						:value="currentProps.value"
						:icon="currentProps.icon"
						:arrow="currentProps.arrow"
						@click="onComponentEvent('click', $event)"
					/>
					<ux-radio 
						v-else-if="selectedComponent.tag === 'ux-radio'"
						:text="currentProps.text"
						:checked="currentProps.checked"
						:disabled="currentProps.disabled"
						@change="onComponentEvent('change', $event)"
					/>
					<ux-card 
						v-else-if="selectedComponent.tag === 'ux-card'"
						:title="currentProps.title"
						:shadow="currentProps.shadow"
						@click="onComponentEvent('click', $event)"
					>
						<text>卡片内容区域</text>
					</ux-card>
					<ux-loading 
						v-else-if="selectedComponent.tag === 'ux-loading'"
						:show="currentProps.show"
						:text="currentProps.text"
					/>
					<ux-badge 
						v-else-if="selectedComponent.tag === 'ux-badge'"
						:value="currentProps.value"
						:max="currentProps.max"
						:dot="currentProps.dot"
					>
						<text>徽标内容</text>
					</ux-badge>
					<mp-html 
						v-else-if="selectedComponent.tag === 'mp-html'"
						:content="currentProps.content"
						@load="onComponentEvent('load', $event)"
						@error="onComponentEvent('error', $event)"
					/>
					<view v-else class="unsupported-component">
						<text class="unsupported-text">暂不支持预览此组件: {{ selectedComponent.tag }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 属性配置面板 -->
		<view class="props-section" v-if="selectedComponent">
			<text class="section-title">属性配置</text>
			<scroll-view class="props-scroll" scroll-y="true">
				<view class="props-list">
					<view 
						v-for="(prop, key) in selectedComponent.props" 
						:key="key"
						class="prop-item"
					>
						<view class="prop-header">
							<text class="prop-name">{{ key }}</text>
							<text class="prop-type">{{ prop.type }}</text>
						</view>
						<text class="prop-desc">{{ prop.description }}</text>
						
						<!-- 不同类型的输入控件 -->
						<view class="prop-input">
							<!-- 布尔类型 -->
							<switch 
								v-if="prop.type === 'Boolean'" 
								:checked="currentProps[key]"
								@change="updateProp(key, $event.detail.value)"
							/>
							<!-- 数字类型 -->
							<input 
								v-else-if="prop.type === 'Number'" 
								type="number"
								:value="currentProps[key]"
								@input="updateProp(key, Number($event.detail.value))"
								class="prop-input-field"
							/>
							<!-- 字符串类型 -->
							<input 
								v-else 
								type="text"
								:value="currentProps[key]"
								@input="updateProp(key, $event.detail.value)"
								class="prop-input-field"
							/>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 事件日志 -->
		<view class="events-section">
			<view class="events-header">
				<text class="section-title">事件日志</text>
				<view class="events-actions">
					<text class="clear-btn" @click="clearEvents">清空</text>
				</view>
			</view>
			<scroll-view class="events-scroll" scroll-y="true">
				<view class="events-list">
					<view v-if="events.length === 0" class="events-empty">
						<text class="empty-text">暂无事件记录</text>
					</view>
					<view 
						v-for="(event, index) in events" 
						:key="index"
						class="event-item"
					>
						<view class="event-header">
							<text class="event-type">{{ event.type }}</text>
							<text class="event-time">{{ event.time }}</text>
						</view>
						<text class="event-data">{{ event.data }}</text>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script setup>
	import { ref, reactive, computed } from 'vue'
	
	// 当前选中的分类和组件
	const selectedCategory = ref(0)
	const selectedComponent = ref(null)
	const currentProps = reactive({})
	const events = ref([])
	
	// 组件分类数据 - 使用项目中实际存在的组件
	const categories = ref([
		{
			name: '基础组件',
			components: [
				{
					name: 'Button 按钮',
					tag: 'ux-button',
					description: '支持多种主题的按钮组件',
					defaultProps: {
						text: '按钮',
						theme: 'primary',
						disabled: false,
						loading: false,
						plain: false
					},
					props: {
						text: { type: 'String', description: '按钮文字' },
						theme: { type: 'String', description: '按钮主题', options: ['text', 'info', 'primary', 'success', 'warning', 'error'] },
						disabled: { type: 'Boolean', description: '是否禁用' },
						loading: { type: 'Boolean', description: '显示加载状态' },
						plain: { type: 'Boolean', description: '是否镂空' }
					}
				},
				{
					name: 'Tag 标签',
					tag: 'ux-tag',
					description: '用于标记和选择的标签组件',
					defaultProps: {
						text: '标签',
						theme: 'primary',
						closable: false,
						disabled: false
					},
					props: {
						text: { type: 'String', description: '标签文字' },
						theme: { type: 'String', description: '标签主题' },
						closable: { type: 'Boolean', description: '是否可关闭' },
						disabled: { type: 'Boolean', description: '是否禁用' }
					}
				},
				{
					name: 'Cell 单元格',
					tag: 'ux-cell',
					description: '列表单元格组件',
					defaultProps: {
						title: '单元格标题',
						value: '单元格内容',
						icon: '',
						arrow: true
					},
					props: {
						title: { type: 'String', description: '标题' },
						value: { type: 'String', description: '内容' },
						icon: { type: 'String', description: '图标' },
						arrow: { type: 'Boolean', description: '显示箭头' }
					}
				}
			]
		},
		{
			name: '表单组件',
			components: [
				{
					name: 'Radio 单选框',
					tag: 'ux-radio',
					description: '单选框组件',
					defaultProps: {
						text: '选项',
						checked: false,
						disabled: false
					},
					props: {
						text: { type: 'String', description: '选项文字' },
						checked: { type: 'Boolean', description: '是否选中' },
						disabled: { type: 'Boolean', description: '是否禁用' }
					}
				}
			]
		},
		{
			name: '布局组件',
			components: [
				{
					name: 'Card 卡片',
					tag: 'ux-card',
					description: '卡片容器组件',
					defaultProps: {
						title: '卡片标题',
						shadow: true
					},
					props: {
						title: { type: 'String', description: '卡片标题' },
						shadow: { type: 'Boolean', description: '显示阴影' }
					}
				}
			]
		},
		{
			name: '反馈组件',
			components: [
				{
					name: 'Loading 加载',
					tag: 'ux-loading',
					description: '加载状态组件',
					defaultProps: {
						show: true,
						text: '加载中...'
					},
					props: {
						show: { type: 'Boolean', description: '是否显示' },
						text: { type: 'String', description: '加载文字' }
					}
				},
				{
					name: 'Badge 徽标',
					tag: 'ux-badge',
					description: '徽标数字组件',
					defaultProps: {
						value: 5,
						max: 99,
						dot: false
					},
					props: {
						value: { type: 'Number', description: '徽标数值' },
						max: { type: 'Number', description: '最大值' },
						dot: { type: 'Boolean', description: '显示小红点' }
					}
				},
				{
					name: 'MP-HTML 富文本',
					tag: 'mp-html',
					description: '富文本解析组件',
					defaultProps: {
						content: '<p>这是一段<strong>富文本</strong>内容，支持<em>HTML</em>标签解析。</p><ul><li>列表项1</li><li>列表项2</li></ul>'
					},
					props: {
						content: { type: 'String', description: 'HTML内容' }
					}
				}
			]
		}
	])
	
	// 选择分类
	const selectCategory = (index) => {
		selectedCategory.value = index
		selectedComponent.value = null
		Object.keys(currentProps).forEach(key => {
			delete currentProps[key]
		})
	}
	
	// 加载组件
	const loadComponent = (component) => {
		selectedComponent.value = component
		
		// 重置属性为默认值
		Object.keys(currentProps).forEach(key => {
			delete currentProps[key]
		})
		Object.assign(currentProps, component.defaultProps)
		
		// 记录事件
		addEvent('load', `加载组件: ${component.name}`)
	}
	
	// 更新属性
	const updateProp = (key, value) => {
		currentProps[key] = value
		addEvent('prop-change', `${key}: ${value}`)
	}
	
	// 组件事件处理
	const onComponentEvent = (type, event) => {
		const eventData = {
			type: type,
			detail: event?.detail || event,
			timestamp: new Date().toLocaleTimeString()
		}
		addEvent(type, JSON.stringify(eventData.detail || 'triggered'))
	}
	
	// 添加事件记录
	const addEvent = (type, data) => {
		events.value.unshift({
			type: type,
			data: data,
			time: new Date().toLocaleTimeString()
		})
		
		// 限制事件记录数量
		if (events.value.length > 50) {
			events.value = events.value.slice(0, 50)
		}
	}
	
	// 清空事件记录
	const clearEvents = () => {
		events.value = []
		addEvent('system', '事件日志已清空')
	}
</script>

<style scoped>
	.container {
		flex: 1;
		background-color: #f5f5f5;
	}
	
	.header {
		background-color: #ffffff;
		padding: 20px;
		border-bottom: 1px solid #e5e5e5;
	}
	
	.title {
		font-size: 24px;
		font-weight: bold;
		color: #333333;
		margin-bottom: 5px;
	}
	
	.subtitle {
		font-size: 14px;
		color: #666666;
	}
	
	.category-section {
		background-color: #ffffff;
		margin-top: 10px;
		padding: 15px 20px;
		border-bottom: 1px solid #e5e5e5;
	}
	
	.section-title {
		font-size: 16px;
		font-weight: bold;
		color: #333333;
		margin-bottom: 10px;
	}
	
	.category-scroll {
		height: 50px;
	}
	
	.category-list {
		flex-direction: row;
		align-items: center;
	}
	
	.category-item {
		flex-direction: row;
		align-items: center;
		padding: 8px 16px;
		margin-right: 10px;
		background-color: #f8f9fa;
		border-radius: 20px;
		border: 1px solid #e9ecef;
	}
	
	.category-item.active {
		background-color: #007aff;
		border-color: #007aff;
	}
	
	.category-text {
		font-size: 14px;
		color: #333333;
		margin-right: 5px;
	}
	
	.category-item.active .category-text {
		color: #ffffff;
	}
	
	.category-count {
		font-size: 12px;
		color: #666666;
	}
	
	.category-item.active .category-count {
		color: #ffffff;
	}
	
	.component-section {
		background-color: #ffffff;
		margin-top: 10px;
		padding: 15px 20px;
		border-bottom: 1px solid #e5e5e5;
	}
	
	.component-scroll {
		height: 200px;
	}
	
	.component-list {
		flex-direction: column;
	}
	
	.component-item {
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		padding: 15px;
		margin-bottom: 10px;
		background-color: #f8f9fa;
		border-radius: 8px;
		border: 1px solid #e9ecef;
	}
	
	.component-item.active {
		background-color: #e3f2fd;
		border-color: #2196f3;
	}
	
	.component-info {
		flex: 1;
	}
	
	.component-name {
		font-size: 16px;
		font-weight: bold;
		color: #333333;
		margin-bottom: 5px;
	}
	
	.component-desc {
		font-size: 12px;
		color: #666666;
	}
	
	.component-tag {
		font-size: 12px;
		color: #007aff;
		background-color: #e3f2fd;
		padding: 4px 8px;
		border-radius: 4px;
	}
	
	.preview-section {
		background-color: #ffffff;
		margin-top: 10px;
		padding: 15px 20px;
		border-bottom: 1px solid #e5e5e5;
	}
	
	.preview-container {
		min-height: 100px;
		padding: 20px;
		background-color: #f8f9fa;
		border-radius: 8px;
		border: 1px dashed #dee2e6;
		margin-top: 10px;
	}
	
	.preview-placeholder {
		align-items: center;
		justify-content: center;
		height: 100px;
	}
	
	.placeholder-text {
		font-size: 14px;
		color: #999999;
	}
	
	.preview-content {
		align-items: center;
		justify-content: center;
	}
	
	.unsupported-component {
		align-items: center;
		justify-content: center;
		padding: 20px;
	}
	
	.unsupported-text {
		font-size: 14px;
		color: #ff6b6b;
	}
	
	.props-section {
		background-color: #ffffff;
		margin-top: 10px;
		padding: 15px 20px;
		border-bottom: 1px solid #e5e5e5;
	}
	
	.props-scroll {
		height: 200px;
	}
	
	.props-list {
		flex-direction: column;
	}
	
	.prop-item {
		padding: 15px;
		margin-bottom: 10px;
		background-color: #f8f9fa;
		border-radius: 8px;
		border: 1px solid #e9ecef;
	}
	
	.prop-header {
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 5px;
	}
	
	.prop-name {
		font-size: 14px;
		font-weight: bold;
		color: #333333;
	}
	
	.prop-type {
		font-size: 12px;
		color: #007aff;
		background-color: #e3f2fd;
		padding: 2px 6px;
		border-radius: 4px;
	}
	
	.prop-desc {
		font-size: 12px;
		color: #666666;
		margin-bottom: 10px;
	}
	
	.prop-input {
		flex-direction: row;
		align-items: center;
	}
	
	.prop-input-field {
		flex: 1;
		height: 40px;
		padding: 0 12px;
		background-color: #ffffff;
		border: 1px solid #dee2e6;
		border-radius: 4px;
		font-size: 14px;
	}
	
	.events-section {
		background-color: #ffffff;
		margin-top: 10px;
		padding: 15px 20px;
		flex: 1;
	}
	
	.events-header {
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 10px;
	}
	
	.events-actions {
		flex-direction: row;
	}
	
	.clear-btn {
		font-size: 14px;
		color: #007aff;
		padding: 5px 10px;
		background-color: #e3f2fd;
		border-radius: 4px;
	}
	
	.events-scroll {
		flex: 1;
	}
	
	.events-list {
		flex-direction: column;
	}
	
	.events-empty {
		align-items: center;
		justify-content: center;
		height: 100px;
	}
	
	.empty-text {
		font-size: 14px;
		color: #999999;
	}
	
	.event-item {
		padding: 10px;
		margin-bottom: 5px;
		background-color: #f8f9fa;
		border-radius: 4px;
		border-left: 3px solid #007aff;
	}
	
	.event-header {
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 5px;
	}
	
	.event-type {
		font-size: 12px;
		font-weight: bold;
		color: #007aff;
		background-color: #e3f2fd;
		padding: 2px 6px;
		border-radius: 4px;
	}
	
	.event-time {
		font-size: 12px;
		color: #666666;
	}
	
	.event-data {
		font-size: 12px;
		color: #333333;
	}
</style>