<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/keyboard?title=Keyboard"></Mobile>

# Keyboard
> 组件类型：UxKeyboardComponentPublicInstance

支持数字键盘、字母键盘、计算器键盘、密码键盘、安全键盘、车牌键盘

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| [theme](#theme) | String | primary | 主题 |
| [mode](#mode) | String | number | 模式 |
| showSub | Boolean | true | 显示负号数字键盘有效 |
| showPoint | Boolean | true | 显示点数字键盘有效 |
| showNum | Boolean | true | 显示数字键盘字母键盘有效 |
| showSymbol | Boolean | true | 显示标点符号字母键盘和密码键盘有效 |
| showSapce | Boolean | true | 等于false时，空格不起作用，但是占位还在 |
| encrypt | Boolean | true | 加密显示安全键盘有效 |
| verify | Boolean | true | 内容合理性校验 |
| upper | Boolean | false | 保持字母大写显示字母键盘有效 |
| scrollId | String |  | scoll-view或list-view的Id |
| adjustPosition | String |  | 键盘弹起时滚动到目标Id需配置父级IDscrollId |
| maxLength | Number | -1不限制 | 输入最大长度 |
| inputSize | Any | 15 | 输入内容大小 |
| inputColor | String | #999 | 输入内容颜色 |
| [inputDarkColor](#inputDarkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| placeholder | String |  | 占位内容 |
| size | Any | 20 | 按钮文字大小 |
| color | String | #333 | 按钮文字颜色 |
| [darkColor](#darkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| btnColor | String |  | 按钮背景颜色 |
| [btnDarkColor](#btnDarkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| confirmText | String |  | 确定按钮文字 |
| confirmColor | String |  | 确定按钮背景颜色 |
| [confirmDarkColor](#confirmDarkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| spaceText | String |  | 空格按钮文字 |
| background | String |  | 背景颜色 |
| [backgroundDark](#backgroundDark) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| showClose | Boolean | true | 显示关闭按钮 |
| radius | Any | 20 | 圆角 |
| opacity | Number | 0.2 | 遮罩透明度0-1 |
| [touchable](#touchable) | Boolean | false | 允许滑动关闭 |
| [maskClose](#maskClose) | Boolean | true | 遮罩层关闭 |
| disabled | Boolean | false | 是否禁用 |

### [theme](#theme)

| 值   | 说明 |
|:------:|:----:|
| text | 文字
 |
| info | 默认
 |
| primary | 主要
 |
| success | 成功
 |
| warning | 警告
 |
| error | 错误
 |

### [mode](#mode)

| 值   | 说明 |
|:------:|:----:|
| number | 数字键盘
 |
| letter | 英文字母键盘
 |
| calculator | 计算器键盘
 |
| safety | 安全键盘
 |
| platenumber | 车牌键盘
 |

### [inputDarkColor](#inputDarkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [darkColor](#darkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [btnDarkColor](#btnDarkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [confirmDarkColor](#confirmDarkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [backgroundDark](#backgroundDark)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [touchable](#touchable)

| 值   | 说明 |
|:------:|:----:|
| true | 
 |
| false | 
 |

### [maskClose](#maskClose)

| 值   | 说明 |
|:------:|:----:|
| true | 
 |
| false | 
 |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| change | 输入时触发 |  |
| confirm | 确定时触发 |  |
| close | 关闭时触发 |  |
| overlimit | 超出字数限制时触发 |  |
  
  