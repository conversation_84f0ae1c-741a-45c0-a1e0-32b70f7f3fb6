<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/datepicker?title=Datepicker"></Mobile>

# Datepicker
> 组件类型：UxDatepickerComponentPublicInstance

支持日历、年月日、年月日时分秒、年月日时分、时分秒、年月、年、周等日期选择，可限制选择范围

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| name | String |  | 标识符，在回调事件中返回 |
| [theme](#theme) | String |  | 主题颜色 |
| [mode](#mode) | String | date | 模式 |
| format | String |  | 格式化规则 |
| value | Array | now | 值 |
| range | Array |  | 范围(例如['2000-01-01','2023-12-10']) |
| max | Number | +1 | 默认年份最大值 |
| min | Number | -10 | 默认年份最小值 |
| [selectMode](#selectMode) | String | single | 选择模式仅calendar有效 |
| lunar | Boolean | true | 显示农历仅calendar有效 |
| [touchable](#touchable) | Boolean | true | 滑动翻页仅calendar有效 |
| title | String |  | 标题 |
| titleColor | String | `$ux.Conf.titleColor` | 标题颜色 |
| size | Any | `$ux.Conf.fontSize` | 内容大小 |
| color | String | `$ux.Conf.fontColor` | 内容颜色 |
| [darkColor](#darkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| selectColor | String | `$ux.Conf.fontColor` | 内容选中颜色 |
| [selectDarkColor](#selectDarkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| [selectStyle](#selectStyle) | Boolean | border | 选择区域遮罩样式 |
| confirm | String | 确定 | 确定文字 |
| confirmColor | String | `$ux.Conf.primaryColor` | 确定文字颜色 |
| btnSize | Any | `$ux.Conf.fontSize` | 按钮大小 |
| [btnType](#btnType) | String | normal | 按钮类型 |
| [btnPositon](#btnPositon) | String | bottom | 按钮位置 |
| radius | Any | `$ux.Conf.radius` | 圆角 |
| opacity | Number | `$ux.Conf.maskAlpha` | 遮罩透明度0-1 |
| [touchClose](#touchClose) | Boolean | false | 允许滑动关闭 |
| [maskClose](#maskClose) | Boolean | true | 遮罩层关闭 |
| disabled | Boolean | false | 是否禁用 |

### [theme](#theme)

| 值   | 说明 |
|:------:|:----:|
| primary | 主色
 |
| warning | 警告
 |
| success | 成功
 |
| error | 错误
 |
| info | 文本
 |

### [mode](#mode)

| 值   | 说明 |
|:------:|:----:|
| calendar日历
 |  |
| date日期
 |  |
| datetime日期时间
 |  |
| datehour日期小时
 |  |
| dateminute日期分钟
 |  |
| hourminute小时分钟
 |  |
| time时间
 |  |
| month月
 |  |
| year年
 |  |
| week周
 |  |

### [selectMode](#selectMode)

| 值   | 说明 |
|:------:|:----:|
| single单选
 |  |
| muti多选
 |  |
| range范围
 |  |

### [touchable](#touchable)

| 值   | 说明 |
|:------:|:----:|
| true
 |  |
| false
 |  |

### [darkColor](#darkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [selectDarkColor](#selectDarkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [selectStyle](#selectStyle)

| 值   | 说明 |
|:------:|:----:|
| border边框
 |  |
| box盒子
 |  |

### [btnType](#btnType)

| 值   | 说明 |
|:------:|:----:|
| normal正常
 |  |
| bigger大按钮
 |  |

### [btnPositon](#btnPositon)

| 值   | 说明 |
|:------:|:----:|
| top顶部按钮
 |  |
| bottom底部按钮
 |  |

### [touchClose](#touchClose)

| 值   | 说明 |
|:------:|:----:|
| true
 |  |
| false
 |  |

### [maskClose](#maskClose)

| 值   | 说明 |
|:------:|:----:|
| true
 |  |
| false
 |  |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| change | 确定选择时触发 |  |
| close | 关闭时触发 |  |
  
  