import { UxDouyinRegisterOptions, UxDouyinLoginOptions, UxDouyinShareOptions, UxDouyinMiniProgram, UxDouyinSuccess } from '../interface.uts'
import { UxDouyinFailImpl } from '../unierror';

/**
* 注册
* @param {UxDouyinRegisterOptions} options
* @tutorial https://www.uxframe.cn/api/douyin.html#register
*/
export const register = function (options : UxDouyinRegisterOptions) {
	
}

/**
* 登录
* @param {UxDouyinLoginOptions} options
* @tutorial https://www.uxframe.cn/api/douyin.html#login
*/
export const login = function (options : UxDouyinLoginOptions) {
	
}

/**
* 分享
* @param {UxDouyinShareOptions} options
* @tutorial https://www.uxframe.cn/api/douyin.html#share
*/
export const share = function (options : UxDouyinShareOptions) {
	
}