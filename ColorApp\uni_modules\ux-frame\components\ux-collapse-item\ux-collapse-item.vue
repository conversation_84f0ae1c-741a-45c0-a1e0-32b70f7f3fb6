<template>
	<view class="ux-collapse-item">
		<view class="ux-collapse-item__title" :style="style" hover-class="ux-collapse-item__hover" :hover-stay-time="200" @click="onOpen(!opend)">
			<text class="ux-collapse-item__title-text" :style="titleStyle">{{title}}</text>
			<ux-icon class="ux-collapse-item__arrow" :class="{'ux-collapse-item__arrow--active': opend}" type="arrowdown"></ux-icon>
		</view>
		
		<!-- #ifdef MP -->
		<view :id="myId" :class="height == 0 ? 'ux-collapse-item__temp' : 'ux-collapse-item__wrap'" :style="wrapStyle">
			<slot></slot>
		</view>
		<!-- #endif -->
		
		<!-- #ifndef MP -->
		<view class="ux-collapse-item__wrap" :style="wrapStyle">
			<slot></slot>
		</view>
		
		<view :id="myId" v-if="height == 0" class="ux-collapse-item__temp">
			<slot></slot>
		</view>
		<!-- #endif -->
	</view>
</template>

<script setup lang="ts">
	
	/**
	* 折叠面板孩子
	* @description 支持配置标题颜色、大小、加粗、背景色。折叠面板子项 需搭配 `ux-collapse` 使用
	* @demo pages/component/collapse.uvue
	* @tutorial https://www.uxframe.cn/component/collapse.html
	* @property {String} 			title									String | 标题
	* @property {String}			color									String | 标题文字颜色
	* @property {String} 			darkColor=[none|auto|color]				String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	* @value none 不显示
	* @value auto 自动适配深色模式
	* @value color 其他颜色
	* @property {Any}				size									Any | 标题文字大小 (默认 $ux.Conf.fontSize)
	* @property {Boolean}			bold = [true|false]						Boolean | 标题文字加粗 (默认 false)
	* @property {String}			titleBackground							String | 标题背景颜色
	* @property {String} 			titleBackgroundDark=[none|auto|color]	String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	* @value none 不显示
	* @value auto 自动适配深色模式
	* @value color 其他颜色
	* @property {String}			background								String | 背景颜色
	* @property {String} 			backgroundDark=[none|auto|color]		String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	* @value none 不显示
	* @value auto 自动适配深色模式
	* @value color 其他颜色
	* @property {Boolean} 			open=[true|false]						Boolean | 默认是否打开（默认 false ）
	* @value true
	* @value false
	* @property {Boolean} 			border=[true|false]						Boolean | 显示下边框（默认 true ）
	* @value true
	* @value false
	* @property {Boolean} 			disabled=[true|false]					Boolean | 是否禁用（默认 false ）
	* @value true
	* @value false
	* <AUTHOR>
	* @date 2023-11-22 10:20:18
	*/
	
	import { computed, getCurrentInstance, onMounted, ref, watch } from 'vue'
	import { $ux } from '../../index'
	import { useFontSize, useFontColor, useBorderColor, useForegroundColor } from '../../libs/use/style.uts'
	
	defineOptions({
		name: 'ux-collapse-item'
	})
	
	const props = defineProps({
		title: {
			type: String,
			default: ''
		},
		color: {
			type: String,
			default: ''
		},
		darkColor: {
			type: String,
			default: ''
		},
		size: {
			default: 0
		},
		bold: {
			type: Boolean,
			default: false
		},
		titleBackground: {
			type: String,
			default: ''
		},
		titleBackgroundDark: {
			type: String,
			default: ''
		},
		background: {
			type: String,
			default: ''
		},
		backgroundDark: {
			type: String,
			default: ''
		},
		open: {
			type: Boolean,
			default: false
		},
		border: {
			type: Boolean,
			default: true
		},
		disabled: {
			type: Boolean,
			default: false
		}
	})
	
	const tempRef = ref<Element | null>(null)
	const myId = `ux-collapse-item-${$ux.Random.uuid()}`
	const height = ref(0)
	const opend = ref(false)
	const ok = ref(false)
	const parentData = ref<Map<string, any>>(new Map<string, any>())
	const instance = getCurrentInstance()?.proxy
	
	const _color = computed(() : string => {
		let value = parentData.value.has('color') ? parentData.value.get('color') as string : ''
		return props.color != '' ? props.color : value
	})
	
	const _darkColor = computed(() : string => {
		let value = parentData.value.has('darkColor') ? parentData.value.get('darkColor') as string : ''
		return props.darkColor != '' ? props.darkColor : value
	})
	
	const _size = computed(() : number => {
		let value = parentData.value.has('size') ? parentData.value.get('size') as number : 0
		return $ux.Util.getPx(props.size) > 0 ? $ux.Util.getPx(props.size) : value
	})
	
	const _bold = computed(() : boolean => {
		return parentData.value.has('bold') ? parentData.value.get('bold') as boolean : false
	})
	
	const _titleBackground = computed(() : string => {
		let value = parentData.value.has('titleBackground') ? parentData.value.get('titleBackground') as string : '#ffffff'
		return props.titleBackground != '' ? props.titleBackground : value
	})
	
	const _titleBackgroundDark = computed(() : string => {
		let value = parentData.value.has('titleBackgroundDark') ? parentData.value.get('titleBackgroundDark') as string : '#333333'
		return props.titleBackgroundDark != '' ? props.titleBackgroundDark : value
	})
	
	const _background = computed(() : string => {
		let value = parentData.value.has('background') ? parentData.value.get('background') as string : ''
		return props.background != '' ? props.background : value
	})
	
	const _backgroundDark = computed(() : string => {
		let value = parentData.value.has('backgroundDark') ? parentData.value.get('backgroundDark') as string : ''
		return props.backgroundDark != '' ? props.backgroundDark : value
	})
	
	const _border = computed(() : boolean => {
		return parentData.value.has('border') ? parentData.value.get('border') as boolean : false
	})
	
	const fontSize = computed(():number => {
		return useFontSize(_size.value, 0)
	})
	
	const fontColor = computed(():string => {
		return useFontColor(_color.value, _darkColor.value)
	})
	
	const titleBackgroundColor = computed(():string => {
		return useForegroundColor(_titleBackground.value, _titleBackgroundDark.value)
	})
	
	const backgroundColor = computed(():string => {
		return useForegroundColor(_background.value, _backgroundDark.value)
	})
	
	const disabledColor = computed(():string => {
		return $ux.Color.getRgba(fontColor.value, 0.5)
	})
	
	const borderColor = computed((): string => {
		return useBorderColor('', '')
	})
	
	const style = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		if(_border.value) {
			css.set('border-bottom', `1rpx solid ${borderColor.value}`)
		}
		
		css.set('background-color', titleBackgroundColor.value)
		
		// #ifdef WEB
		css.set('cursor', props.disabled ? 'not-allowed' : 'pointer')
		// #endif
		
		return css
	})
	
	const titleStyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		if(props.disabled) {
			css.set('color', disabledColor.value)
		} else {
			css.set('color', fontColor.value)
		}
		
		css.set('font-size', fontSize.value)
		
		if(_bold.value) {
			css.set('font-weight', 'bold')
		}
		
		return css
	})
	
	const wrapStyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		if(_border.value && opend.value) {
			css.set('border-bottom', `1rpx solid ${borderColor.value}`)
		}
		
		// #ifdef MP
		if (height.value > 0) {
			css.set('height', `${opend.value ? height.value : 0}px`)
		}
		// #endif
		
		// #ifndef MP
		css.set('height', `${opend.value ? height.value : 0}px`)
		// #endif
		
		css.set('background-color', backgroundColor.value)
		
		return css
	})
	
	function calcHeight(callback: (() => void) | null) {
		uni.createSelectorQuery()
			.in(instance)
			.select(`#${myId}`)
			.boundingClientRect().exec((ret) => {
				if(ret.length > 0) {
					let node = ret[0] as NodeInfo;
					height.value = node.height! as number
				}
				
				ok.value = true
				callback?.()
			})
	}

	function onOpen(_open : boolean) {
		if (props.disabled) return
		
		$ux.Util.$dispatch(instance!, 'ux-collapse', 'close')
		
		opend.value = _open
	}
	
	function close() {
		if (opend.value) {
			opend.value = false
		}
	}
	
	function setData(data: Map<string, any>) {
		parentData.value = data
	}
	
	const open = computed(():boolean => {
		return props.open
	})
	
	watch(open, () => {
		onOpen(open.value)
	})
	
	onMounted(() => {
		$ux.Util.$dispatch(instance!, 'ux-collapse', 'register', instance)
		
		calcHeight(() => {
			onOpen(open.value)
		})
	})
	
	defineExpose({
		close,
		setData
	})
</script>

<style lang="scss">
	.ux-collapse-item {
		width: 100%;

		&__title {
			flex: 1;
			display: flex;
			flex-direction: row;
			align-items: center;
			padding: 12px;
		}

		&__title-text {
			flex: 1;
			color: #000;
			font-size: 14px;
		}

		&__wrap {
			overflow: hidden;
			width: 100%;
			transition-property: height;
			transition-duration: 300ms;
			transition-timing-function: cubic-bezier(.2, .8, .3, 1);
		}
		
		&__temp {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			opacity: 0;
		}
		
		&__arrow {
			transform: rotate(0deg);
			transition-property: transform;
			transition-duration: 0.2s;

			&--active {
				transform: rotate(180deg);
			}
		}
		
		&__hover {
			opacity: 0.7;
		}
	}

</style>