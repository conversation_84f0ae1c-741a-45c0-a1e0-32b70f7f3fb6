<template>
	<!-- #ifdef APP -->
	<scroll-view class="container" scroll-y="true">
	<!-- #endif -->
		<view class="page-container">
			<!-- 页面标题 -->
			<view class="header">
				<text class="title">PageRender 配置切换测试</text>
				<text class="subtitle">测试页面配置动态切换功能</text>
			</view>

			<!-- 配置选择区域 -->
			<view class="config-section">
				<text class="section-title">选择配置</text>
				<view class="config-buttons">
					<button 
						class="config-button"
						:class="{ active: selectedConfig === 0 }"
						@click="selectConfig(0)"
					>
						<text class="config-button-text">登录表单</text>
					</button>
					<button 
						class="config-button"
						:class="{ active: selectedConfig === 1 }"
						@click="selectConfig(1)"
					>
						<text class="config-button-text">信息展示</text>
					</button>
				</view>
			</view>

			<!-- 页面渲染区域 -->
			<view class="render-section">
				<text class="section-title">页面预览</text>
				<view class="render-container">
					<PageRender 
						v-if="currentConfig"
						:config="currentConfig.data"
						:auto-load="true"
						@loaded="onPageLoaded"
						@error="onPageError"
						@event="onPageEvent"
					/>
					<view v-else class="placeholder">
						<text class="placeholder-text">请选择一个配置进行预览</text>
					</view>
				</view>
			</view>

			<!-- 调试信息区域 -->
			<view class="debug-section">
				<text class="section-title">调试信息</text>
				<view class="debug-info">
					<text class="debug-text">当前配置索引: {{ selectedConfig }}</text>
					<text class="debug-text">当前配置名称: {{ currentConfig?.name || '无' }}</text>
					<text class="debug-text">配置切换次数: {{ switchCount }}</text>
					<text class="debug-text">最后切换时间: {{ lastSwitchTime }}</text>
				</view>
			</view>
		</view>
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script setup lang="uts">
	import { ref, computed } from 'vue'
	import PageRender from '../components/PageRender.uvue'
	
	// 响应式数据
	const selectedConfig = ref(1)  // 默认选择"信息展示"配置
	const switchCount = ref(0)
	const lastSwitchTime = ref('')
	
	// 配置选项
	const configOptions = ref([
		{
			name: '登录表单',
			data: {
				version: '1.0',
				pageId: 'loginPage',
				header: {
					title: '用户登录',
					showBackButton: true
				},
				dataSources: {
					loginForm: {
						id: 'loginForm',
						type: 'local',
						options: {},
						data: {
							username: '',
							password: ''
						},
						autoLoad: false
					}
				},
				pageLayout: {
					id: 'mainLayout',
					component: 'flex-layout',
					props: {
						flexDirection: 'column',
						justifyContent: 'center',
						alignItems: 'center',
						padding: '20px'
					}
				},
				components: [
					{
						id: 'formContainer',
						component: 'view',
						props: {
							backgroundColor: '#f8f9fa',
							borderRadius: '8px',
							padding: '20px',
							width: '100%'
						},
						dataSource: 'loginForm',
						children: [
							{
								id: 'formTitle',
								component: 'text',
								value: '用户登录'
							},
							{
								id: 'usernameField',
								component: 'input',
								props: {
									type: 'text',
									placeholder: '请输入用户名',
									dataBind: 'username'
								}
							},
							{
								id: 'passwordField',
								component: 'input',
								props: {
									type: 'password',
									placeholder: '请输入密码',
									dataBind: 'password'
								}
							},
							{
								id: 'submitBtn',
								component: 'button',
								value: '登录'
							}
						]
					}
				]
			}
		},
		{
			name: '信息展示',
			data: {
				version: '1.0',
				pageId: 'infoPage',
				header: {
					title: '用户信息',
					showBackButton: false
				},
				dataSources: {
					userInfo: {
						id: 'userInfo',
						type: 'local',
						options: {},
						data: {
							name: '张三',
							email: '<EMAIL>',
							phone: '13800138000'
						},
						autoLoad: false
					}
				},
				pageLayout: {
					id: 'mainLayout',
					component: 'flex-layout',
					props: {
						flexDirection: 'column',
						padding: '20px'
					}
				},
				components: [
					{
						id: 'userCard',
						component: 'view',
						props: {
							backgroundColor: '#667eea',
							borderRadius: '12px',
							padding: '20px'
						},
						dataSource: 'userInfo',
						children: [
							{
								id: 'cardTitle',
								component: 'text',
								value: '用户信息卡片'
							},
							{
								id: 'userName',
								component: 'text',
								value: '姓名: 张三'
							},
							{
								id: 'userEmail',
								component: 'text',
								value: '邮箱: <EMAIL>'
							},
							{
								id: 'userPhone',
								component: 'text',
								value: '电话: 13800138000'
							}
						]
					}
				]
			}
		}
	])
	
	// 计算属性：当前选中的配置
	const currentConfig = computed(() => {
		return configOptions.value[selectedConfig.value] || null
	})
	
	// 选择配置
	const selectConfig = (index: number) => {
		console.log('切换配置:', index, '配置名称:', configOptions.value[index].name)
		selectedConfig.value = index
		switchCount.value++
		lastSwitchTime.value = new Date().toLocaleTimeString()
	}
	
	// 页面加载完成事件
	const onPageLoaded = (config: any) => {
		console.log('页面加载完成:', config.pageId)
	}
	
	// 页面加载错误事件
	const onPageError = (error: string) => {
		console.log('页面加载错误:', error)
	}
	
	// 页面组件事件
	const onPageEvent = (eventData: any) => {
		console.log('页面组件事件:', eventData)
	}
</script>

<style scoped>
	.container {
		flex: 1;
		background-color: #f5f5f5;
	}
	
	.page-container {
		flex: 1;
		flex-direction: column;
	}
	
	.header {
		background-color: #ffffff;
		padding: 20px;
		border-bottom: 1px solid #e5e5e5;
	}
	
	.title {
		font-size: 20px;
		font-weight: bold;
		color: #333333;
		margin-bottom: 5px;
	}
	
	.subtitle {
		font-size: 14px;
		color: #666666;
	}
	
	.config-section {
		background-color: #ffffff;
		margin-top: 10px;
		padding: 15px 20px;
		border-bottom: 1px solid #e5e5e5;
	}
	
	.section-title {
		font-size: 16px;
		font-weight: bold;
		color: #333333;
		margin-bottom: 10px;
	}
	
	.config-buttons {
		flex-direction: row;
		flex-wrap: wrap;
	}
	
	.config-button {
		background-color: #f8f9fa;
		border: 1px solid #e9ecef;
		border-radius: 20px;
		padding: 8px 16px;
		margin-right: 10px;
		margin-bottom: 10px;
	}
	
	.config-button.active {
		background-color: #007aff;
		border-color: #007aff;
	}
	
	.config-button-text {
		font-size: 14px;
		color: #333333;
	}
	
	.config-button.active .config-button-text {
		color: #ffffff;
	}
	
	.render-section {
		background-color: #ffffff;
		margin-top: 10px;
		padding: 15px 20px;
		border-bottom: 1px solid #e5e5e5;
		flex: 1;
	}
	
	.render-container {
		flex: 1;
		border: 1px dashed #dee2e6;
		border-radius: 8px;
		margin-top: 10px;
		overflow: hidden;
		min-height: 300px;
	}
	
	.placeholder {
		align-items: center;
		justify-content: center;
		height: 200px;
	}
	
	.placeholder-text {
		font-size: 14px;
		color: #999999;
	}
	
	.debug-section {
		background-color: #ffffff;
		margin-top: 10px;
		padding: 15px 20px;
	}
	
	.debug-info {
		background-color: #f8f9fa;
		border-radius: 8px;
		padding: 15px;
		margin-top: 10px;
	}
	
	.debug-text {
		font-size: 12px;
		color: #666666;
		margin-bottom: 5px;
	}
</style>