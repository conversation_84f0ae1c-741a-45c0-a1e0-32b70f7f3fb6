<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/noticebar?title=Noticebar"></Mobile>

# Noticebar
> 组件类型：UxNoticebarComponentPublicInstance

支持水平滚动、垂直翻页，可配置滚动速度、右侧按钮

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| texts | Array |  | 通告内容 |
| [theme](#theme) | String |  | 主题颜色 |
| color | String | `$ux.Conf.fontColor` | 文字颜色 |
| [darkColor](#darkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| size | Any | `$ux.Conf.fontSize` | 内容文字大小 |
| icon | String | volumenotice-filled | 左侧图标 |
| rightIcon | String |  | 右侧图标 |
| iconSize | Any | `$ux.Conf.fontSize` | 图标尺寸 |
| iconColor | String | `$ux.Conf.fontColor` | 图标颜色 |
| background | String | `$ux.Conf.backgroundColor` | 背景颜色 |
| [backgroundDark](#backgroundDark) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| radius | Any | 5 | 圆角 |
| scrollable | Boolean | true | 开启滚动 |
| [direction](#direction) | String | row | 滚动方向 |
| speed | Number | 60px/秒 | 文字水平垂直滚动的速度 |
| mutiline | Boolean |  | 是否多行显示，多行则无滚动效果 |
| margin | Array |  | 边距[上右下左][上下左右][上下左右] |
| mt | Any |  | 距上单位px |
| mr | Any |  | 距右单位px |
| mb | Any |  | 距下单位px |
| ml | Any |  | 距左单位px |
| padding | Array |  | 填充[上右下左][上下左右][上下左右] |
| pt | Any |  | 上内边距单位px |
| pr | Any |  | 右内边距单位px |
| pb | Any |  | 下内边距单位px |
| pl | Any |  | 左内边距单位px |
| xstyle | Array |  | 自定义样式 |

### [theme](#theme)

| 值   | 说明 |
|:------:|:----:|
| primary | 主色
 |
| warning | 警告
 |
| success | 成功
 |
| error | 错误
 |
| info | 文本
 |

### [darkColor](#darkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [backgroundDark](#backgroundDark)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [direction](#direction)

| 值   | 说明 |
|:------:|:----:|
| row水平滚动
 |  |
| col垂直翻页
 |  |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| click | 点击时触发 |  |
| handle | 点击右侧按钮时触发 |  |
  
  