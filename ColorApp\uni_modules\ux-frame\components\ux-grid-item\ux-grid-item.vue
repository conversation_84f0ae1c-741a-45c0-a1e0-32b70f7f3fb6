<template>
	<view class="ux-grid-item" :style="style">
		<slot></slot>
	</view>
</template>

<script setup lang="ts">
	/**
	 * GridItem 网格项
	 * @demo pages/component/grid.uvue
	 * @tutorial https://www.uxframe.cn/component/grid.html
	 * <AUTHOR>
	 * @date 2023-12-31 20:49:56
	 */
	
	import { computed, getCurrentInstance, onMounted, ref } from 'vue'
	import { $ux } from '../../index'
	import { useBorderColor } from '../../libs/use/style.uts'
	
	defineOptions({
		name: 'ux-grid-item'
	})
	
	const instance = getCurrentInstance()?.proxy
		
	const col = ref(3)
	const border = ref(false)
	
	const borderColor = computed((): string => {
		return useBorderColor('', '')
	})
	
	const style = computed(() => {
		let css = {}
		
		css['width'] = `${100 / col.value}%`
		css['max-width'] = `150px`
		
		if(border.value) {
			css['border'] = `1px solid ${borderColor.value}`
		}
		
		return css
	})
	
	function setData(data: UTSJSONObject) {
		col.value = data['col'] ?? 3
		border.value = data['border'] ?? false
	}
	
	onMounted(() => {
		$ux.Util.$dispatch(instance!, 'ux-grid', 'register', instance)
	})
	
	defineExpose({
		setData
	})
</script>

<style lang="scss" scoped>
	
	.ux-grid-item {
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		box-sizing: border-box;
	}
</style>