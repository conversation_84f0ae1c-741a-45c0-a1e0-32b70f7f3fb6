<template>
	<!-- #ifdef APP -->
	<scroll-view class="container" scroll-y="true">
	<!-- #endif -->
		<view class="page">
			<view class="header">
				<text class="title">动态表单构建器演示</text>
				<text class="subtitle">基于FieldConfig配置动态渲染表单</text>
			</view>
			
			<!-- 配置选择 -->
			<view class="config-section">
				<text class="section-title">选择表单配置</text>
				<view class="config-buttons">
					<view 
						v-for="(config, index) in formConfigs" 
						:key="index"
						class="config-button"
						:class="{ active: currentConfigIndex === index }"
						@click="switchConfig(index)"
					>
						<text class="config-name">{{ config.name }}</text>
					</view>
				</view>
			</view>
			
			<!-- 动态表单 -->
			<view class="form-section">
				<text class="section-title">动态表单</text>
				<FormBuilder 
					:fields="currentConfig.fields"
					v-model="formData"
					:show-actions="true"
					:show-reset="true"
					@submit="handleSubmit"
					@change="handleFieldChange"
				/>
			</view>
			
			<!-- 表单数据预览 -->
			<view class="preview-section">
				<text class="section-title">表单数据预览</text>
				<view class="data-preview">
					<text class="data-text">{{ JSON.stringify(formData, null, 2) }}</text>
				</view>
			</view>
			
			<!-- 事件日志 -->
			<view class="log-section">
				<view class="log-header">
					<text class="section-title">事件日志</text>
					<text class="clear-btn" @click="clearLogs">清空</text>
				</view>
				<scroll-view class="log-scroll" scroll-y="true">
					<view class="log-list">
						<view v-if="logs.length === 0" class="log-empty">
							<text class="empty-text">暂无日志</text>
						</view>
						<view 
							v-for="(log, index) in logs" 
							:key="index"
							class="log-item"
						>
							<text class="log-time">{{ log.time }}</text>
							<text class="log-type">{{ log.type }}</text>
							<text class="log-content">{{ log.content }}</text>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script setup>
	import FormBuilder from '../components/FormBuilder.uvue'
	
	// 表单配置示例
	const formConfigs = ref([
		{
			name: '用户注册表单',
			fields: [
				{
					Id: 'username',
					Name: '用户名',
					Title: '用户名',
					Type: 'Text',
					DataType: 'string',
					Required: true,
					Placeholder: '请输入用户名',
					MinLength: 3,
					MaxLength: 20,
					Order: 1,
					ValidationType: 'pattern',
					Validation: '^[a-zA-Z0-9_]{3,20}$',
					ValidMesasge: '用户名只能包含字母、数字和下划线，长度3-20位'
				},
				{
					Id: 'email',
					Name: '邮箱',
					Title: '邮箱地址',
					Type: 'Text',
					DataType: 'string',
					InputType: 'email',
					Required: true,
					Placeholder: '请输入邮箱地址',
					Order: 2,
					ValidationType: 'pattern',
					Validation: '^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$',
					ValidMesasge: '请输入正确的邮箱格式'
				},
				{
					Id: 'password',
					Name: '密码',
					Title: '密码',
					Type: 'Text',
					DataType: 'string',
					InputType: 'password',
					Required: true,
					Placeholder: '请输入密码',
					MinLength: 6,
					MaxLength: 20,
					Order: 3
				},
				{
					Id: 'phone',
					Name: '手机号',
					Title: '手机号码',
					Type: 'PhoneFormat',
					DataType: 'string',
					InputType: 'mobile',
					Required: true,
					Placeholder: '请输入手机号码',
					Order: 4,
					ValidationType: 'pattern',
					Validation: '^1[3-9]\\d{9}$',
					ValidMesasge: '请输入正确的手机号码'
				},
				{
					Id: 'gender',
					Name: '性别',
					Title: '性别',
					Type: 'Select',
					DataType: 'string',
					Required: true,
					Order: 5,
					DataSourceType: 'Json',
					DefaultDataSource: '男,女,其他'
				},
				{
					Id: 'age',
					Name: '年龄',
					Title: '年龄',
					Type: 'Text',
					DataType: 'int',
					InputType: 'number',
					Required: true,
					Placeholder: '请输入年龄',
					MinDecimal: 1,
					MaxDecimal: 120,
					Order: 6
				},
				{
					Id: 'bio',
					Name: '个人简介',
					Title: '个人简介',
					Type: 'TextArea',
					DataType: 'string',
					Placeholder: '请输入个人简介',
					MaxLength: 500,
					WordCount: true,
					Order: 7
				},
				{
					Id: 'newsletter',
					Name: '订阅通知',
					Title: '订阅邮件通知',
					Type: 'Switch',
					DataType: 'bool',
					DefaultValue: 'false',
					Order: 8
				}
			]
		},
		{
			name: '产品信息表单',
			fields: [
				{
					Id: 'productName',
					Name: '产品名称',
					Title: '产品名称',
					Group: '基本信息',
					Type: 'Text',
					DataType: 'string',
					Required: true,
					Placeholder: '请输入产品名称',
					MaxLength: 100,
					Order: 1
				},
				{
					Id: 'category',
					Name: '产品分类',
					Title: '产品分类',
					Group: '基本信息',
					Type: 'Select',
					DataType: 'string',
					Required: true,
					Order: 2,
					DataSourceType: 'Json',
					DataSource: '[{"id":"1","text":"电子产品"},{"id":"2","text":"服装鞋帽"},{"id":"3","text":"家居用品"},{"id":"4","text":"食品饮料"}]'
				},
				{
					Id: 'price',
					Name: '价格',
					Title: '产品价格',
					Group: '基本信息',
					Type: 'Text',
					DataType: 'decimal',
					InputType: 'number',
					Required: true,
					Placeholder: '请输入价格',
					MinDecimal: 0.01,
					MaxDecimal: 999999.99,
					Precision: 2,
					Order: 3
				},
				{
					Id: 'description',
					Name: '产品描述',
					Title: '产品描述',
					Group: '详细信息',
					Type: 'TextArea',
					DataType: 'string',
					Placeholder: '请输入产品描述',
					MaxLength: 1000,
					WordCount: true,
					Order: 4
				},
				{
					Id: 'tags',
					Name: '产品标签',
					Title: '产品标签',
					Group: '详细信息',
					Type: 'Tag',
					DataType: 'string',
					IsArray: true,
					Placeholder: '请输入产品标签，用逗号分隔',
					Order: 5
				},
				{
					Id: 'inStock',
					Name: '是否有库存',
					Title: '是否有库存',
					Group: '库存信息',
					Type: 'Switch',
					DataType: 'bool',
					DefaultValue: 'true',
					Order: 6
				},
				{
					Id: 'stockCount',
					Name: '库存数量',
					Title: '库存数量',
					Group: '库存信息',
					Type: 'Text',
					DataType: 'int',
					InputType: 'number',
					Placeholder: '请输入库存数量',
					MinDecimal: 0,
					Order: 7
				}
			]
		}
	])
	
	const currentConfigIndex = ref(0)
	const formData = ref({} as UTSJSONObject)
	const logs = ref<UTSJSONObject[]>([])
	
	// 当前配置
	const currentConfig = computed(() => {
		return formConfigs.value[currentConfigIndex.value]
	})
	
	// 切换配置
	const switchConfig = (index: number) => {
		currentConfigIndex.value = index
		formData.value = {} as UTSJSONObject
		addLog('配置切换', `切换到配置: ${formConfigs.value[index].name}`)
	}
	
	// 处理表单提交
	const handleSubmit = (result: UTSJSONObject) => {
		if (result['valid'] as boolean) {
			addLog('表单提交', '表单验证通过，提交成功')
			uni.showToast({
				title: '提交成功',
				icon: 'success'
			})
		} else {
			const error = result['error'] as string
			const field = result['field'] as string
			addLog('表单提交', `表单验证失败: ${field} - ${error}`)
			uni.showToast({
				title: '验证失败',
				icon: 'error'
			})
		}
	}
	
	// 处理字段变化
	const handleFieldChange = (event: UTSJSONObject) => {
		const field = event['field'] as string
		const value = event['value']
		addLog('字段变化', `${field}: ${JSON.stringify(value)}`)
	}
	
	// 添加日志
	const addLog = (type: string, content: string) => {
		const now = new Date()
		const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
		
		logs.value.unshift({
			time: time,
			type: type,
			content: content
		} as UTSJSONObject)
		
		// 限制日志数量
		if (logs.value.length > 50) {
			logs.value = logs.value.slice(0, 50)
		}
	}
	
	// 清空日志
	const clearLogs = () => {
		logs.value = []
	}
	
	onMounted(() => {
		addLog('页面加载', '动态表单构建器演示页面加载完成')
	})
</script>

<style lang="scss">
	.container {
		flex: 1;
		background-color: #f5f5f5;
	}
	
	.page {
		padding: 20rpx;
	}
	
	.header {
		text-align: center;
		padding: 40rpx 20rpx;
		background-color: #fff;
		border-radius: 16rpx;
		margin-bottom: 20rpx;
	}
	
	.title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 10rpx;
	}
	
	.subtitle {
		font-size: 28rpx;
		color: #666;
		display: block;
	}
	
	.config-section,
	.form-section,
	.preview-section,
	.log-section {
		background-color: #fff;
		border-radius: 16rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
	}
	
	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 20rpx;
	}
	
	.config-buttons {
		display: flex;
		flex-direction: row;
		gap: 20rpx;
		flex-wrap: wrap;
	}
	
	.config-button {
		padding: 20rpx 30rpx;
		background-color: #f0f0f0;
		border-radius: 12rpx;
		border: 2rpx solid transparent;
	}
	
	.config-button.active {
		background-color: #007aff;
		border-color: #007aff;
	}
	
	.config-name {
		font-size: 28rpx;
		color: #333;
	}
	
	.config-button.active .config-name {
		color: #fff;
	}
	
	.data-preview {
		background-color: #f8f8f8;
		border-radius: 8rpx;
		padding: 20rpx;
		border: 1rpx solid #e0e0e0;
	}
	
	.data-text {
		font-size: 24rpx;
		color: #333;
		font-family: monospace;
		line-height: 1.5;
		white-space: pre-wrap;
		word-break: break-all;
	}
	
	.log-header {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}
	
	.clear-btn {
		font-size: 28rpx;
		color: #007aff;
		padding: 10rpx 20rpx;
		background-color: #f0f8ff;
		border-radius: 8rpx;
	}
	
	.log-scroll {
		height: 400rpx;
		border: 1rpx solid #e0e0e0;
		border-radius: 8rpx;
	}
	
	.log-list {
		padding: 10rpx;
	}
	
	.log-empty {
		text-align: center;
		padding: 60rpx 20rpx;
	}
	
	.empty-text {
		font-size: 28rpx;
		color: #999;
	}
	
	.log-item {
		padding: 15rpx;
		border-bottom: 1rpx solid #f0f0f0;
		display: flex;
		flex-direction: column;
		gap: 8rpx;
	}
	
	.log-time {
		font-size: 24rpx;
		color: #999;
	}
	
	.log-type {
		font-size: 26rpx;
		color: #007aff;
		font-weight: bold;
	}
	
	.log-content {
		font-size: 26rpx;
		color: #333;
		word-break: break-all;
	}
</style>