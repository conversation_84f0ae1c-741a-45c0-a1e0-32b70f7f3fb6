import { Review, AppStore } from '../interface.uts'

import { UIApplication } from 'UIKit';
import { URL } from 'Foundation';

/**
 * 跳转应用市场
 * @param id appstore appid
 */
export const appStore: AppStore = (id: string): boolean => {
	let market = `itms-apps://itunes.apple.com/app?id=${id}`
	
	let uri = new URL(string = market)
	if (uri != null && UIApplication.shared.canOpenURL(uri!)) {
		UIApplication.shared.open(uri!, options = new Map<UIApplication.OpenExternalURLOptionsKey, any>(), completionHandler = null)
		return true
	}
	
	return false
}

/**
 * 打开应用评分
 */
export const review: Review = () => {
	return UxStoreKit.review()
}