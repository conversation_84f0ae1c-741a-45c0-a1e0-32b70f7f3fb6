import PictureDrawable from "android.graphics.drawable.PictureDrawable";
import ImageView from 'android.widget.ImageView';
import File from 'java.io.File';
import FileInputStream from 'java.io.FileInputStream';
import Color from 'android.graphics.Color';
import PorterDuff from 'android.graphics.PorterDuff';

export class UxSvg {

	$element : UniNativeViewElement; 
	imageView : ImageView | null = null
	
	constructor(element : UniNativeViewElement) {
		this.$element = element
		this.imageView = new ImageView(UTSAndroid.getAppContext()!)
		this.$element.bindAndroidView(this.imageView!);
	}
	
	load(src: string, color: string) {
		if(src == "") {
			return
		}
		
		try {
			// uni-app x 正式打包会放在asset中，需要特殊处理
			let path = UTSAndroid.getResourcePath(src)
			
			if(path.startsWith("/android_asset")){
				let svg = com.caverock.androidsvg.SVG.getFromAsset(UTSAndroid.getAppContext()!.getAssets(), path.substring(15));
				this.render(svg, color)
			} else {
				let file = new File(path)
				if(file.exists()) {
					let svg = com.caverock.androidsvg.SVG.getFromInputStream(new FileInputStream(file));
					this.render(svg, color)
				}
			}
		} catch (e) {
		    e.printStackTrace();
		}
	}
	
	render(svg: com.caverock.androidsvg.SVG, color: string) {
		let drawable = new PictureDrawable(svg.renderToPicture());
		this.imageView?.setImageDrawable(drawable);
		
		if(color != '') {
			this.imageView?.setColorFilter(Color.parseColor(color), PorterDuff.Mode.OVERLAY)
		}
	}
}