/**
   @Name    :	格式化
   <AUTHOR>   UxFrame
   @Date    :   2024-01-09 22:07:11
*/

export class Format {
	
	/** 
	 * 脱敏加密
	 * 
	 * @param {String} text
	 * @returns {Boolean}
	 */
	encryptText(text : string) : string {
		if (text.length == 2) {
			return text[0] + '*'
		} else if (text.length > 2) {
			return text[0] + '*'.repeat(text.length - 2) + text[text.length - 1]
		} else {
			return text
		}
	}
	
	/** 
	 * 大写金额
	 * 
	 * @param {String} money
	 * @returns {String}
	 */
	upperMoney(money : string) : string {
		if (money == '') {
			return ''
		}
	
		// 汉字的数字
		let cnNums = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
		// 基本单位
		let cnIntRadice = ['', '拾', '佰', '仟']
		// 对应整数部分扩展单位
		let cnIntUnits = ['', '万', '亿', '兆']
		// 对应小数部分单位
		let cnDecUnits = ['角', '分', '毫', '厘']
		// 整数金额时后面跟的字符
		let cnInteger = ''
		// 整型完以后的单位
		let cnIntLast = '元'
		// 最大处理的数字
		let maxNum = 999999999999999.9999
		// 金额整数部分
		let integerNum: string
		// 金额小数部分
		let decimalNum: string
		// 输出的中文金额字符串
		let chineseStr = ''
		// 分离金额后用的数组，预定义
		let parts: string[]
	
		let _money = parseFloat(money.toString())
		// 超出最大处理数字
		if (_money >= maxNum) {
			return ''
		}
	
		if (_money == 0) {
			return `${cnNums[0]}${cnIntLast}${cnInteger}`
		}
	
		// 转换为字符串
		if (money.indexOf('.') == -1) {
			integerNum = money
			decimalNum = ''
		} else {
			parts = money.split('.')
			integerNum = parts[0]
			decimalNum = parts[1].substring(0, 4)
		}
	
		// 获取整型部分转换
		if (parseInt(integerNum, 10) > 0) {
			let zeroCount = 0
			let len = integerNum.length
	
			for (let i = 0; i < len; i++) {
				let n = integerNum.substring(i, i + 1)
				let p = len - i - 1
				let q = p / 4
				let m = p % 4
				if (n == '0') {
					zeroCount++
				} else {
					if (zeroCount > 0) {
						chineseStr += cnNums[0]
					}
	
					// 归零
					zeroCount = 0
					chineseStr += cnNums[parseInt(n)] + cnIntRadice[m]
				}
				if (m == 0 && zeroCount < 4) {
					chineseStr += cnIntUnits[q]
				}
			}
	
			chineseStr += cnIntLast
		}
	
		// 小数部分
		if (decimalNum != '') {
			let decLen = decimalNum.length
			for (let i = 0; i < decLen; i++) {
				let n = decimalNum.substring(i, i + 1)
				if (n != '0') {
					let index = parseInt(n)
					chineseStr += (cnNums[index] + cnDecUnits[i])
				}
			}
		}
	
		if (chineseStr == '') {
			chineseStr += cnNums[0] + cnIntLast + cnInteger
		} else if (decimalNum == '') {
			chineseStr += cnInteger
		}
	
		return chineseStr
	}
	
	/** 
	 * 格式化金额
	 * 
	 * @param {String} money
	 * @param {Boolean} wfz true 万分制 false 千分制
	 * @returns {String}
	 */
	fmtMoney(money : string, wfz : boolean) : string {
	
		try {
			let fIndex = wfz ? 4 : 3
	
			if (fIndex == 3) {
				let str = money.toString()
				if (str.indexOf('.') == -1) {
					return str.replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
				} else {
					let strs = str.split('.')
					let val = strs[0].replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
	
					for (let i = 1; i < strs.length; i++) {
						val += '.' + strs[i]
					}
	
					return val
				}
			} else {
				let str = money.toString()
	
				if (str.indexOf('.') == -1) {
					return str.replace(/(\d)(?=(?:\d{4})+$)/g, '$1,')
				} else {
					let strs = str.split('.')
					let val = strs[0].replace(/(\d)(?=(?:\d{4})+$)/g, '$1,')
	
					for (let i = 1; i < strs.length; i++) {
						val += '.' + strs[i]
					}
	
					return val
				}
			}
		} catch (e) {
			console.log(e);
		}
	
		return money.toString()
	}
}