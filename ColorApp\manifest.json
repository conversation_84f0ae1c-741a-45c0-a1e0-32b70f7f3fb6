{
	"name": "ColorApps",
	"appid": "__UNI__9370609",
	"description": "App、小程序、H5 SaaS平台",
	"versionName": "1.0.0",
	"versionCode": "100",
	"uni-app-x": {},
	/* 快应用特有相关 */
	"quickapp": {},
	/* 小程序特有相关 */
	"mp-weixin": {
		"appid": "",
		"setting": {
			"urlCheck": false,
			"minified": true
		},
		"usingComponents": true,
		"darkmode": true
	},
	"mp-alipay": {
		"usingComponents": true
	},
	"mp-baidu": {
		"usingComponents": true
	},
	"mp-toutiao": {
		"usingComponents": true
	},
	"uniStatistics": {
		"enable": false
	},
	"vueVersion": "3",
	"app": {
		"distribute": {
			"icons": {
				"android": {
					"hdpi": "unpackage/res/icons/72x72.png",
					"xhdpi": "unpackage/res/icons/96x96.png",
					"xxhdpi": "unpackage/res/icons/144x144.png",
					"xxxhdpi": "unpackage/res/icons/192x192.png"
				},
				"ios": {
					"appstore": "unpackage/res/icons/1024x1024.png"
				}
			},
			"splashScreens": {
				"ios": {
					"storyboard": "assets/UxFrameStoryboard.zip"
				},
				"android": {
					"xhdpi": "assets/720x1280.9.png",
					"xxhdpi": "assets/1080x1920.9.png",
					"xxxhdpi": "assets/2160x3840.9.png"
				},
				"android12": {
					"background": "",
					"icon": {
						"xhdpi": "",
						"xxhdpi": "",
						"xxxhdpi": ""
					},
					"brand": {
						"xhdpi": "",
						"xxxhdpi": "",
						"xxhdpi": ""
					}
				}
			}
		},
		"android": {
			"abiFilters": [
				"v7a",
				"arm64-v8a",
				"x86",
				"x86_64"
			]
		}
	},
	"web": {
		"title": "Colorful App",
		"template": "index.html",
		"router": {
			"mode": "hash"
		},
		"optimization": {
			"treeShaking": {
				"enable": true
			}
		},
		"devServer": {
			"https": false
		},
		"darkmode": true,
		"themeLocation": "theme.json"
	},
	"app-harmony": {
		"distribute": {
			"bundleName": "com.colorful.app",
			"icons": {
				"foreground": ""
			},
			"signingConfigs": {
				"default": {
					"certpath": "/Users/<USER>/Library/Application Support/HBuilder X/extensions/launcher/agc-certs/1749882998631.cer",
					"keyAlias": "debugKey",
					"keyPassword": "0000001ACD5684839601D37807F6D870A6D4733EAAAB1132F788207DE54D1693C7D2C251E535C7552359",
					"profile": "/Users/<USER>/Library/Application Support/HBuilder X/extensions/launcher/agc-certs/1749882998631.p7b",
					"signAlg": "SHA256withECDSA",
					"storeFile": "/Users/<USER>/Library/Application Support/HBuilder X/extensions/launcher/agc-certs/1749882998631.p12",
					"storePassword": "0000001ACD5684839601D37807F6D870A6D4733EAAAB1132F788207DE54D1693C7D2C251E535C7552359"
				}
			}
		}
	},
	"app-android": {
		"distribute": {
			"modules": {},
			"icons": {
				"hdpi": "unpackage/res/icons/72x72.png",
				"xhdpi": "unpackage/res/icons/96x96.png",
				"xxhdpi": "unpackage/res/icons/144x144.png",
				"xxxhdpi": "unpackage/res/icons/192x192.png"
			},
			"splashScreens": {
				"default": {
					"xhdpi": "assets/720x1280.9.png",
					"xxhdpi": "assets/1080x1920.9.png",
					"xxxhdpi": "assets/2160x3840.9.png"
				},
				"background": "",
				"icon": {
					"xhdpi": "",
					"xxhdpi": "",
					"xxxhdpi": ""
				},
				"brand": {
					"xhdpi": "",
					"xxxhdpi": "",
					"xxhdpi": ""
				}
			}
		}
	},
	"app-ios": {
		"distribute": {
			"modules": {},
			"icons": {
				"appstore": "unpackage/res/icons/1024x1024.png"
			},
			"splashScreens": {
				"storyboard": "assets/UxFrameStoryboard.zip"
			}
		}
	}
}