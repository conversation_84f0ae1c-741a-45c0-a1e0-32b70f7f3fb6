<template>
	<view :id="id"></view>
</template>

<script lang="uts">
	/**
	* 自定义相机预览拍照组件
	* @description 支持拍照、切换镜头、闪光灯、手电筒功能
	* @demo pages/component/camera-view.uvue
	* @tutorial https://www.uxframe.cn/component/camera-view.html
	* @event {Function} 			take 						Function | 拍照时触发
	* <AUTHOR>
	* @date 2025-04-07 21:02:28
	*/
   
	export default {
		name: 'ux-camera-view',
		data() {
			return {
				id: `ux-camera-view-${new Date().getTime()}`,
				video: null as null | HTMLElement,
				mediaStream: null as any,
				isFlash: false,
				isTorch: false,
				isFront: false
			}
		},
		NVLoad() : HTMLElement {
			return document.createElement('div') as HTMLElement
		},
		NVBeforeUnload() {
			this.close()
		},
		methods: {
			open() {
				this.close()
				
				if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
					this.video = document.createElement('video')
					this.video!.style.width = '100%'
					this.video!.style.height = '100%'
					this.video!.style.objectFit = 'cover'
					document.getElementById(this.id)!.appendChild(this.video)
					
					navigator.mediaDevices
						.getUserMedia({ audio: false, video: { width: 1920, height: 1080 } })
						.then((mediaStream) => {
							this.mediaStream = mediaStream
					
							this.video!.srcObject = mediaStream
							this.video!.onloadedmetadata = () => {
								this.video!.play()
							}
							
						})
						.catch((err) => {
							console.error('[ux-camera-view] ', err);
							this.close()
						})
				} else {
					console.error('[ux-camera-view] Your browser does not support getUserMedia API')
					this.close()
					
					uni.showToast({
						title: '您的浏览器不支持此功能！',
						icon: 'none'
					})
				}
			},
			close() {
				this.video?.pause()
				this.video = null

				this.mediaStream?.getTracks().forEach((track) => {
					track.stop()
				})
				this.mediaStreamm = null

				document.getElementById(this.id)!.innerHTML = ''
			},
			take(correctOrientation: boolean) {
				if (!this.video || !this.mediaStream) {
					console.error('[ux-camera-view] 组件未初始化')
					return
				}

				this.video?.pause()

				const canvas = document.createElement('canvas')
				canvas.width = this.video.videoWidth
				canvas.height = this.video.videoHeight

				const ctx = canvas.getContext('2d')
				ctx.drawImage(this.video, 0, 0, canvas.width, canvas.height)
				
				this.$emit('take', {
					'path': canvas.toDataURL('image/png')
				} as UTSJSONObject)
			},
			switch(isFront: boolean) {
				if (!this.video || !this.mediaStream) {
					console.error('[ux-camera-view] 组件未初始化')
					return
				}
				
				this.isFront = isFront
				this.open()
			},
			flash(isFlash: boolean) {
				if (!this.video || !this.mediaStream) {
					console.error('[ux-camera-view] 组件未初始化')
					return
				}
				
				if (!('flash' in navigator.mediaDevices.getSupportedConstraints())) {
					console.warn('Torch is not supported by this browser/device.')
					return
				}
				
				this.isFlash = isFlash
				this.open()
			},
			torch(isTorch: boolean) {
				if (!this.video || !this.mediaStream) {
					console.error('[ux-camera-view] 组件未初始化')
					return
				}

				if (!('torch' in navigator.mediaDevices.getSupportedConstraints())) {
					console.warn('Flashlight is not supported by this browser/device.')
					return
				}
				
				this.isTorch = isTorch
				this.open()
			},
		}
	}
</script>