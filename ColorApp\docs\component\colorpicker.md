<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/colorpicker?title=Colorpicker"></Mobile>

# Colorpicker
> 组件类型：UxColorpickerComponentPublicInstance

支持单色、渐变色、透明度

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| value | String |  | 值(rgba(255,0,0,1)) |
| colors | Array |  | 预设颜色值 |
| title | String |  | 标题 |
| titleColor | String | `$ux.Conf.titleColor` | 标题颜色 |
| size | Any | `$ux.Conf.fontSize` | 内容大小 |
| color | String | `$ux.Conf.fontColor` | 内容颜色 |
| [darkColor](#darkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| confirm | String | 确定 | 确定文字 |
| confirmColor | String | `$ux.Conf.primaryColor` | 确定文字颜色 |
| btnSize | Any | `$ux.Conf.fontSize` | 按钮大小 |
| [btnType](#btnType) | String | normal | 按钮类型 |
| [btnPositon](#btnPositon) | String | bottom | 按钮位置 |
| radius | Any | `$ux.Conf.radius` | 圆角 |
| opacity | Number | `$ux.Conf.maskAlpha` | 遮罩透明度0-1 |
| [touchable](#touchable) | Boolean | false | 允许滑动关闭 |
| [maskClose](#maskClose) | Boolean | true | 遮罩层关闭 |
| disabled | Boolean | false | 是否禁用 |

### [darkColor](#darkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [btnType](#btnType)

| 值   | 说明 |
|:------:|:----:|
| normal正常
 |  |
| bigger大按钮
 |  |

### [btnPositon](#btnPositon)

| 值   | 说明 |
|:------:|:----:|
| top顶部按钮
 |  |
| bottom底部按钮
 |  |

### [touchable](#touchable)

| 值   | 说明 |
|:------:|:----:|
| true
 |  |
| false
 |  |

### [maskClose](#maskClose)

| 值   | 说明 |
|:------:|:----:|
| true
 |  |
| false
 |  |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| change | 确定选择时触发 |  |
| changing | 选择时触发 |  |
| close | 关闭时触发 |  |
  
  