<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
	android:background="#FFFFFF"
    android:paddingHorizontal="12dp"
	android:minHeight="45dp"
    android:gravity="center_vertical">
	
	<ImageView
	    android:id="@+id/img"
	    android:layout_width="40dp"
	    android:layout_height="40dp"
	    android:layout_marginEnd="6dp"
	    android:scaleType="centerCrop"
	    android:visibility="gone" />
		
	<LinearLayout
	    android:layout_width="match_parent"
	    android:layout_height="match_parent"
	    android:orientation="vertical">
		
		<LinearLayout
		    android:layout_width="match_parent"
		    android:layout_height="0dp"
		    android:layout_weight="1"
		    android:orientation="horizontal"
		    android:gravity="center_vertical">
			
			<LinearLayout
			    android:layout_width="300dp"
			    android:layout_height="wrap_content"
			    android:layout_weight="1"
			    android:orientation="vertical">
			
			    <TextView
			        android:id="@+id/label"
			        android:layout_width="match_parent"
			        android:layout_height="wrap_content"
			        android:textSize="16sp"
			        android:textStyle="bold"
					android:maxLines="1"
					android:ellipsize="end"
					android:scrollHorizontally="true" />
			
			    <TextView
			        android:id="@+id/summary"
			        android:layout_width="match_parent"
			        android:layout_height="wrap_content"
			        android:textSize="14sp"
			        android:textColor="#808080"
			        android:layout_marginTop="2dp" 
					android:maxLines="1"
					android:ellipsize="end"
					android:scrollHorizontally="true"
					android:visibility="gone" />
			</LinearLayout>
			
			<ImageView
			    android:id="@+id/drag"
			    android:layout_width="24dp"
			    android:layout_height="24dp"
			    android:src="@drawable/ic_drag"
			    android:layout_marginStart="16dp"
			    android:contentDescription="Drag handle"
			    android:visibility="visible" />
		</LinearLayout>
		
		<View
			android:id="@+id/line"
		    android:layout_width="match_parent"
		    android:layout_height="1dp"
			android:minHeight="1dp"
		    android:background="#E5E5E5" />
	</LinearLayout>		
</LinearLayout>