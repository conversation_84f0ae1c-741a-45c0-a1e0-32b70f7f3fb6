export default {
	title: 'Uniapp-X 低代码高性能UI框架',
	notice: '感谢您使用并体验UxFrame框架！最新版现已发布，目前已开发组件总数：{0}、API总数：{1}、模版总数：{2}，了解更多请点我查看详情 😁',
	privacy: {
		title: "用户协议及隐私政策",
		content: "",
		agree: "同意",
		disagree: "不同意",
	},
	setting: {
		title: '设置',
		theme: '主题',
		darkMode: '深色模式',
		grayMode: '哀悼模式',
		followSys: '跟随系统',
		primaryColor: '主色调',
		selectPrimaryColor: '选择主色调',
		themeChooser: '主题选择器',
		language: '语言',
		CN: '中',
		US: '英',
	},
	component: {
		native: '原生组件',
		camera: '水印相机',
		blur: '高斯模糊',
		lottie: 'Lottie动画',
		movable: '拖拽组件',
		shimmer: '辉光组件',
		svg: 'SVG',
		sprite: '序列帧动画',
		
		basic: '基础组件',
		text: '文本',
		richtext: '富文本',
		icon: '图标',
		image: '图片',
		button: '按钮',
		cell: '单元格',
		transition: '过渡',
		layout: '布局',
		
		forms: '表单组件',
		form: '表单',
		input: '输入框',
		textarea: '文本域',
		keyboard: '键盘',
		colorpicker: '颜色选择器',
		dropdown: '下拉筛选',
		radio: '单选',
		switch: '开关',
		slider: '滑动选择器',
		picker: '选择器',
		datepicker: '日期选择器',
		areapicker: '地址选择器',
		calendar: '日历',
		rate: '评分',
		numberbox: '步进器',
		upload: '上传',
		
		prompt: '反馈组件',
		modal: '对话框',
		toast: '提示框',
		popover: '弹窗',
		swipeaction: '滑动操作',
		actionsheet: '底部操作',
		floatbutton: '悬浮按钮',
		
		display: '展示组件',
		echarts: 'ECharts',
		chartBar: '柱状图',
		chartLine: '折线图',
		chartRing: '环形图',
		table: '表格',
		tree: '树',
		badge: '角标',
		tag: '标签',
		loading: '加载',
		noticebar: '滚动通知',
		countdown: '倒计时',
		countto: '数字滚动',
		qrcode: '二维码',
		watermark: '水印',
		empty: '空状态',
		avatar: '头像',
		
		layouts: '布局组件',
		movableArea: '拖动区域',
		waterfall: '瀑布流',
		chatlist: '聊天列表',
		pages: '页面组',
		grid: '宫格布局',
		card: '卡片',
		scroll: '滚动列表',
		list: 'ListView',
		virtualList: '虚拟列表',
		drawer: '抽屉',
		skeleton: '骨架屏',
		divider: '分割线',
		collapse: '折叠面板',
		overlay: '遮罩层',
		placeholder: '空间占位',
		steps: '步骤',
		swiper: 'Swiper',
		
		tabs: '导航组件',
		navbar: '顶部导航',
		tabbar: '底部导航',
		tab: '选项卡',
		anchortabs: '锚点选项',
		sidebar: '侧边选项',
		indexbar: '索引',
		
		other: '其他组件',
		guide: '引导',
		signature: '签名',
		refresher: '下拉刷新',
		loadmore: '加载更多',
		backtop: '返回顶部',
		cut: '裁剪',
		readmore: '阅读更多'
	},
	api: {
		library: '核心库',
		conf: '配置',
		i18n: 'i18n',
		autocss: 'AutoCSS',
		color: '颜色库',
		util: '工具库',
		
		ext: '扩展库',
		encrypt: '加密库',
		toolkit: '函数库',
		store: '状态管理',
		date: '日期库',
		format: '格式化',
		random: '随机数',
		verify: '校验',
		
		device: '设备',
		soter: '生物认证',
		keyboard: '隐藏键盘',
		vibrate: '震动',
		clipboard: '复制黏贴',
		exit: '退出APP',
		call: '拨打电话',
		permission: '权限申请',
	
		network: '网络',
		networkStatus: '网络状态',
		mqtt: 'MQTT',
	
		media: '媒体',
		file: '文件',
		
		social: '社交',
		share: '系统分享',
		weixin: '微信SDK',
		wxwork: '企业微信SDK',
		douyin: '抖音SDK',
		kuaishou: '快手SDK',
		
		data: '数据存储',
		orm: 'ORM实体映射',
		sqlite: 'SQLite',
		
		other: '其他API',
		storekit: '应用市场',
		setGray: '哀悼模式',
		openUrl: '打开链接',
		openWeb: '打开网页',
		notification: '通知栏进度条',
		pinyin: '中文转拼音',
	},
	template: {
		media: '媒体',
		files: '文件选择器',
		
		login: '登录',
		
		other: '其他模版',
		privacy: '隐私政策',
		upgrade: 'APP升级',
	},
	ai: {
		
	},
	more: '更多',
	nomore: '真的没有了~',
	stayTuned: '计划中 敬请期待',
	test: {
		hello: "你好，UxFrame",
		time: "当前时间：{time} ",
	},
};