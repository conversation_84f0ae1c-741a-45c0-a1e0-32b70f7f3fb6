<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/unimoduleUxBlur-Swift.h</key>
		<data>
		TIpp13KAZpHs+8M02PuPJZG8HXA=
		</data>
		<key>Headers/unimoduleUxBlur.h</key>
		<data>
		FH2SRnQAHIO8AbDZPa5/Bz/CcdA=
		</data>
		<key>Info.plist</key>
		<data>
		RyHH0DL4Oq1Wki9J/4SloulJHHQ=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		EAgNmQv+nM3V+3bxIZlVqF42CHM=
		</data>
		<key>Modules/unimoduleUxBlur.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		ijfo2bcxupLuLJ5jUJndFzgL/RI=
		</data>
		<key>Modules/unimoduleUxBlur.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		GRX80MJB0TDtj0zpZsP8RVmrR2g=
		</data>
		<key>Modules/unimoduleUxBlur.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		6ksOgxxSRun6ETFwBYcj7O+h+VU=
		</data>
		<key>Modules/unimoduleUxBlur.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		C8Sc8lwI/WyC11KpHXQ/vbnssCM=
		</data>
		<key>Modules/unimoduleUxBlur.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		6ksOgxxSRun6ETFwBYcj7O+h+VU=
		</data>
		<key>Modules/unimoduleUxBlur.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		6d8h5j8ey3FgttHiEHs+ZWjPXAg=
		</data>
		<key>config.json</key>
		<data>
		Bgy5fCmxeqzdV4lx41+Yvz7XgmI=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/unimoduleUxBlur-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			6W8pT2FfLILXGYJUoH4JQUZW3nXtSBRMr+cFyBKFE1s=
			</data>
		</dict>
		<key>Headers/unimoduleUxBlur.h</key>
		<dict>
			<key>hash2</key>
			<data>
			XAsze1m7Z4PhyI2gj+RafZVS4xyqWjgvqraTawdVPMY=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			d5t0Qye/5PdveUOFpWTN+wY5fLQWi+xVm9TzoZRxE8U=
			</data>
		</dict>
		<key>Modules/unimoduleUxBlur.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			bqpoS2Ic+2CuDuM7gNyyBFeX5d7DZhNP/EwGUXgHibY=
			</data>
		</dict>
		<key>Modules/unimoduleUxBlur.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7nxdOlz+mpEfms40UHb1cvIVIHKaIZDI8J1j3bphOMg=
			</data>
		</dict>
		<key>Modules/unimoduleUxBlur.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			lqm5og9VjCLOkBR95NcveD0InJUROlHB2108yXEe7NE=
			</data>
		</dict>
		<key>Modules/unimoduleUxBlur.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			JQVdrJ+Y6vBOZhkIC6UTVw2V6jz5TfHzOb7NPZDytTI=
			</data>
		</dict>
		<key>Modules/unimoduleUxBlur.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			lqm5og9VjCLOkBR95NcveD0InJUROlHB2108yXEe7NE=
			</data>
		</dict>
		<key>Modules/unimoduleUxBlur.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			zQ/iNkHHNrYDW2EjqsN414zlSoAl07kBvGwcjswou+A=
			</data>
		</dict>
		<key>config.json</key>
		<dict>
			<key>hash2</key>
			<data>
			oju1KwusthIstUFVUy+H5P4Nv33eRQf0KVVWW0uWTLU=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
