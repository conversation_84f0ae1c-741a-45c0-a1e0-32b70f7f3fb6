import {
	AVCaptureSession,
	AVCaptureDevice,
	AVMediaType,
	AVCapturePhotoCaptureDelegate,
	AVCaptureDeviceInput,
	AVCaptureVideoPreviewLayer,
	AVLayerVideoGravity,
	AVCaptureVideoOrientation,
	AVCapturePhotoOutput,
	AVCapturePhoto,
	AVCapturePhotoSettings
} from 'AVFoundation'
import { UIView, UIImage } from 'UIKit'
import { PHPhotoLibrary, PHAuthorizationStatus } from 'Photos'
import { CGRect } from 'CoreFoundation'
import { FileManager, UUID } from 'Foundation'
import { DispatchQueue } from "Dispatch"

// 精华 定义全局委托 否则无法回调
var captureDelegate: CaptureDelegate | null = null

class CaptureDelegate implements AVCapturePhotoCaptureDelegate {
	callback : (path : string) => void

	constructor(callback : (path : string) => void) {
		this.callback = callback
		super()
	}

	photoOutput(output : AVCapturePhotoOutput, @argumentLabel("didFinishProcessingPhoto") photo : AVCapturePhoto, @argumentLabel("") error : NSError | null) {
		if (error == null) {
			const photoData = photo.fileDataRepresentation()
			if (photoData != null) {
				let image = new UIImage(data = photoData!)

				let userDir = FileManager.default.urls(for = FileManager.SearchPathDirectory.cachesDirectory, in = FileManager.SearchPathDomainMask.userDomainMask).first!
				let url = userDir.appendingPathComponent(UUID().uuidString + '.jpg')
				
				let imageData = image!.jpegData(compressionQuality = 1)
				if (imageData != null) {
					let ok = UTSiOS.try(imageData!.write(to = url), '?')
					if (ok != null) {
						this.callback(url.path)
					}
				}
			}
		} else {
			console.error('[ux-camera-view]', error)
		}
	}
}

export class UxCamera {
	previewer : UIView
	cameraView : UIView | null = null
	captureSession : AVCaptureSession | null = null
	inputDevice: AVCaptureDeviceInput | null = null
	cameraOutput : AVCapturePhotoOutput | null = null

	constructor(previewer : UIView) {
		this.previewer = previewer
		super()
	}

	checkPermissions(callback : (ok : boolean) => void) {
		PHPhotoLibrary.requestAuthorization((ok : PHAuthorizationStatus) => {
			if (ok != PHAuthorizationStatus.authorized) {
				callback(false)
				return
			}

			AVCaptureDevice.requestAccess(for = AVMediaType.video, completionHandler = (ok : boolean) => {
				callback(ok)
			})
		})
	}

	open() {
		this.checkPermissions((ok : boolean) => {
			if (ok) {
				this.close()
				this.openCamera()
			}
		})
	}

	close() {
		if (this.captureSession != null) {
			if (this.captureSession!.isRunning) {
				this.captureSession!.stopRunning()
			}
			for (input in this.captureSession!.inputs) {
				this.captureSession!.removeInput(input)
			}
			for (output in this.captureSession!.outputs) {
				this.captureSession!.removeOutput(output)
			}
		}
		this.cameraView?.removeFromSuperview()
		this.cameraView = null
		this.captureSession = null
	}

	take(correctOrientation: boolean, callback : (path : string) => void) {
		try {
			if(this.captureSession == null) {
				return
			}
			
			DispatchQueue.main.async(execute = () : void => {
				captureDelegate = new CaptureDelegate((path : string) => {
					callback(path)
					this.close()
				})
				
				let photoSettings = AVCapturePhotoSettings()
				this.cameraOutput?.capturePhoto(with = photoSettings, delegate = captureDelegate!)
			})
		} catch (error) {
		    console.error('[ux-camera-view]', error)
		}
	}

	switch(isFront : boolean) {
		try {
			if(this.inputDevice == null || this.captureSession == null) {
				return
			}
			
		    UTSiOS.try(this.captureSession?.beginConfiguration(), '?')
			
			this.captureSession?.removeInput(this.inputDevice!)
		    
			var newCamera: AVCaptureDevice | null
			if (isFront) {
				newCamera = AVCaptureDevice.default(AVCaptureDevice.DeviceType.builtInWideAngleCamera, for = AVMediaType.video, position = AVCaptureDevice.Position.front)
			} else {
			    newCamera = AVCaptureDevice.default(AVCaptureDevice.DeviceType.builtInWideAngleCamera, for = AVMediaType.video, position = AVCaptureDevice.Position.back)
			}
			
			var newCameraInput = UTSiOS.try(new AVCaptureDeviceInput(device = newCamera!), '?')
			
			if (this.captureSession!.canAddInput(newCameraInput!)) {
			    this.captureSession!.addInput(newCameraInput!)
			    this.inputDevice = newCameraInput!
			} else {
			    this.captureSession!.addInput(this.inputDevice!)
			}
			
			UTSiOS.try(this.captureSession?.commitConfiguration(), '?')
		} catch (error) {
		    console.error('[ux-camera-view]', error)
		}
	}

	flash(isFlash : boolean) {
		try {
			if(this.captureSession == null) {
				return
			}
			
			const device = AVCaptureDevice.default(for = AVMediaType.video)
			if (device == null) {
			    return
			}
			
			if (device!.hasFlash && device!.isFlashAvailable) {
			   UTSiOS.try(device!.lockForConfiguration(), '?')
			   
			   if (isFlash) {
			       device!.flashMode = AVCaptureDevice.FlashMode.on
			   } else {
			       device!.flashMode = AVCaptureDevice.FlashMode.off
			   }
			   
			   UTSiOS.try(device!.unlockForConfiguration(), '?')
			}
		} catch (error) {
		    console.error('[ux-camera-view]', error)
		}
	}

	torch(isTorch : boolean) {
		try {
			this.close()
			
			const device = AVCaptureDevice.default(for = AVMediaType.video)
			if (device == null) {
			    return
			}
			
			if (device!.hasTorch && device!.isTorchAvailable) {
			    UTSiOS.try(device!.lockForConfiguration(), '?')
			    
			    if (isTorch) {
			        device!.torchMode = AVCaptureDevice.TorchMode.on
			        UTSiOS.try(device!.setTorchModeOn(level = 1.0), '?')
			    } else {
			        device!.torchMode = AVCaptureDevice.TorchMode.off
			    }
			    
			    UTSiOS.try(device!.unlockForConfiguration(), '?')
			}
		} catch (error) {
		    console.error('[ux-camera-view]', error)
		}
	}

	private openCamera() {
		try {
			this.captureSession = new AVCaptureSession()
			this.captureSession!.sessionPreset = AVCaptureSession.Preset.photo
			this.inputDevice = UTSiOS.try(new AVCaptureDeviceInput(device = AVCaptureDevice.default(for = AVMediaType.video)!), '?')
			if (this.inputDevice != null) {
				this.captureSession!.addInput(this.inputDevice!)
			}
			
			this.cameraOutput = AVCapturePhotoOutput()
			if (this.captureSession!.canAddOutput(this.cameraOutput!)) {
				this.captureSession!.addOutput(this.cameraOutput!)
			}
			
			const bounds = this.previewer.layer.bounds
			const width = Int(bounds.width)
			const height = Int(bounds.height)
			
			this.cameraView = new UIView()
			this.cameraView!.frame = new CGRect(x = 0, y = 0, width = width, height = height)
			
			const previewLayer = new AVCaptureVideoPreviewLayer(session = this.captureSession!)
			previewLayer.videoGravity = AVLayerVideoGravity.resizeAspectFill
			previewLayer.connection!.videoOrientation = AVCaptureVideoOrientation.portrait
			previewLayer.frame = new CGRect(x = 0, y = 0, width = width, height = height)
			
			DispatchQueue.main.async(execute = () : void => {
				this.previewer.addSubview(this.cameraView!);
				this.cameraView!.layer.addSublayer(previewLayer)
				this.captureSession!.startRunning()
			})
		} catch (error) {
		    console.error('[ux-camera-view]', error)
		}
	}
}