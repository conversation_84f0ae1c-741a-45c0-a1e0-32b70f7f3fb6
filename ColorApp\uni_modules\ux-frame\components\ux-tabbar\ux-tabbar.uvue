<template>
	<view class="ux-tabbar" :style="tabbarStyle">
		<ux-blur v-if="showBlur && type != 'special'" :radius="blurRadius" :color="blurColor" class="ux-tabbar__blur"></ux-blur>
		
		<view ref="uxTabbarRef" class="ux-tabbar__content" :style="contentStyle">
			
			<!-- #ifndef APP-ANDROID || APP-IOS -->
			<canvas class="ux-tabbar__content__canvas" :id="canvasId" :canvas-id="canvasId"></canvas>
			<!-- #endif -->
			
			<view v-if="renderOk" :id="it.id!" :class="[`ux-tabbar__item__${anim}`, `transform__${anim}`]" :style="bodyStyle"
				v-for="(it, i) in tabs" :key="i" @click="onTab(i)" @touchstart="() => touchstart(i)"
				@touchend="() => touchend(i)" @touchcancel="() => touchend(i)">
			
				<view :id="`${myId}-hover-${i}`" class="ux-tabbar__hover" :style="hoverStyle(i)"></view>
			
				<view :id="`${myId}-icon-${i}`" :class="['ux-tabbar__icon', `transform__${anim}`]">
					<ux-icon :type="tabIndex == i?it.selectedIcon:it.unselectedIcon" :size="iconSize"
						:color="tabIndex == i?it.selectedColor:it.unselectedColor"
						:custom-family="customFamily">
					</ux-icon>
				</view>
			
				<text :id="`${myId}-name-${i}`" :class="['ux-tabbar__name', `transform__${anim}`]"
					:style="[textStyle(i)]">{{ it.name }}</text>
			
				<view :id="`${myId}-point-${i}`" :class="['ux-tabbar__point', `transform__${anim}`]"
					:style="[pointStyle(i)]"></view>
			
				<ux-badge :value="it.badge" :dot="it.reddot" :top="anim == 'push'? 0 : 10" :right="10"></ux-badge>
			</view>
		</view>

		<view v-if="type == 'special' && anim != 'roll'" class="ux-tabbar__center" :style="[centerStyle]" 
			hover-class="ux-tabbar__hover--center" :hover-start-time="0" :hover-stay-time="150" @click="onCenter()">
			<slot name="center"></slot>
		</view>
		
		<view v-if="type == 'special' && anim == 'roll' && tabs.length > 0" class="ux-tabbar__center" :style="[centerStyle]" hover-class="ux-tabbar__hover--center" :hover-start-time="0" :hover-stay-time="150" @click="onCenter()">
			<view :id="`${myId}-icon-roll`" :class="['ux-tabbar__center__icon', `transform__${anim}`]">
				<ux-icon :type="tabs[tabIndex]?.selectedIcon" :size="24" color="#e5e5e5" :custom-family="customFamily"></ux-icon>
			</view>
		</view>
		
		<ux-placeholder v-if="type == 'special'" :safearea="true" :background="backgroundColor"></ux-placeholder>
	</view>
</template>

<script setup>

	/**
	 * 底部tabbar
	 * @description 支持正常形状和凹形，支持上下滚动、左右推压、水滴动画效果
	 * @demo pages/component/tabbar.uvue
	 * @tutorial https://www.uxframe.cn/component/tabbar.html
	 * @property {Slot}				center						Slot | 中间凸起按钮插槽
	 * @property {String}			type = [default|special]		String | 类型 (默认 default)
	 * @value default 正常
	 * @value special 特殊
	 * @property {String}			anim = [none|scroll|roll|push|water]		String | 动效类型 (默认 none)
	 * @value none 无
	 * @value scroll 上下滚动
	 * @value roll 水平滚动
	 * @value push 左右推压
	 * @value water 水滴
	 * @property {Number}			index								Number | 默认选择下标 (默认 0)
	 * @property {UxTab[]}			data								UxTab[] | tab列表 (导入: import {UxTab} from '@/uni_modules/ux-frame/libs/types/types.uts')
	 * @property {String}			selectedColor						String | 选中颜色 (默认 $ux.Conf.fontColor)
	 * @property {String}			unselectedColor						String | 未选中颜色 (默认 #111111)
	 * @property {String}			selectedColorDark=[none|auto|color]	String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {String}			unselectedColorDark=[none|auto|color]	String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Any}				iconSize							Any | 图标大小 单位px (默认 18)
	 * @property {Any}				fontSize							Any | 字体大小 单位px (默认 12)
	 * @property {Boolean}			fontBold = [true|false]				Boolean | 字体加粗 (默认 false)
	 * @property {String}			customFamily						String | 自定义字体family
	 * @property {Boolean}			border = [true|false]				Boolean | 显示上边框 (默认 false)
	 * @property {String}			borderColor							String | 上边框颜色 (默认 $ux.Conf.borderColor)
	 * @property {Any}				corner								Any | 圆角 (默认 0)
	 * @property {String}			background							String | 背景色 (默认 #FFFFFF)
	 * @property {String} 			backgroundDark=[none|auto|color]	String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {String}			centerColor							String | 中间按钮背景色 (默认 #FFFFFF)
	 * @property {Boolean}			showBlur = [true|false]				Boolean | 开启模糊背景 仅支持normal类型 (默认 false )
	 * @property {Number}			blurRadius							Number | 模糊半径 (默认 10 )
	 * @property {Number}			blurAlpha							Number | 模糊透明度 (默认 0.5 )
	 * @property {Number}			zIndex								Number | 层级z-index (默认 10000)
	 * @property {Boolean}			hover = [true|false]				Boolean | 显示点击态 (默认 true)
	 * @property {Boolean}			fixed = [true|false]				Boolean | 固定位置 (默认 true)
	 * @event {Function}			click								Function | 按钮点击时触发
	 * @event {Function}			center								Function | 中间按钮点击时触发
	 * @event {Function}			change								Function | 当前选择下标改变时触发
	 * <AUTHOR>
	 * @date 2023-10-03 20:31:11
	 */
	
	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	import type { UxTab } from '../../libs/types/types.uts'
	import { useForegroundColor, useFontColor } from '../../libs/use/style.uts'
	import { useResize } from '../../libs/use/resize.uts'
	
	defineOptions({
		name: 'ux-tabbar',
		mixins: [xstyleMixin]
	})
	
	const emit = defineEmits(['click', 'center', 'change'])
	
	const props = defineProps({
		type: {
			type: String,
			default: 'default'
		},
		anim: {
			type: String,
			default: 'none'
		},
		index: {
			type: Number,
			default: 0
		},
		data: {
			type: Array,
			default: () : any[] => {
				return [] as any[]
			}
		},
		selectedColor: {
			type: String,
			default: ''
		},
		unselectedColor: {
			type: String,
			default: '#111111'
		},
		selectedColorDark: {
			type: String,
			default: ''
		},
		unselectedColorDark: {
			type: String,
			default: ''
		},
		iconSize: {
			default: 18
		},
		fontSize: {
			default: 12
		},
		fontBold: {
			type: Boolean,
			default: false
		},
		customFamily: {
			type: String,
			default: ''
		},
		border: {
			type: Boolean,
			default: false
		},
		borderColor: {
			type: String,
			default: ''
		},
		corner: {
			default: 0
		},
		radius: {
			type: Number,
			default: 30
		},
		background: {
			type: String,
			default: '#ffffff'
		},
		backgroundDark: {
			type: String,
			default: '#222222'
		},
		centerColor: {
			type: String,
			default: '#ffffff'
		},
		showBlur: {
			type: Boolean,
			default: false
		},
		blurRadius: {
			type: Number,
			default: 10
		},
		blurAlpha: {
			type: Number,
			default: 0.5
		},
		zIndex: {
			type: Number,
			default: 10000
		},
		fixed: {
			type: Boolean,
			default: true
		},
		hover: {
			type: Boolean,
			default: true
		},
	})
	
	const canvasId = `ux-tabbar-${$ux.Random.uuid()}`
	const uxTabbarRef = ref<Element | null>(null)
	const drawer = ref<any | null>(null)
	const canvas = ref<any | null>(null)
	const tabs = ref<UxTab[]>([] as UxTab[])
	const tabIndex = ref(0)
	const isPress = ref(false)
	const renderOk = ref(true)
	const _anim = ref('')
	const _type = ref('')
	
	const instance = getCurrentInstance()?.proxy
	
	const safeAreaHeight = uni.getWindowInfo().safeAreaInsets.bottom
	const dpr = uni.getWindowInfo().pixelRatio
	
	let timer = 0
	
	const myId = computed(():string => {
		return `ux-tabbar_${_type.value}_${$ux.Random.uuid()}`
	})
	
	const backgroundColor = computed(() : string => {
		return useForegroundColor(props.background, props.backgroundDark)
	})
	
	const selectedColor = computed(() : string => {
		return useForegroundColor(props.selectedColor, props.selectedColorDark)
	})
	
	const unselectedColor = computed(() : string => {
		return useForegroundColor(props.unselectedColor, props.unselectedColorDark)
	})
	
	const blurColor = computed(() : string => {
		return $ux.Color.getRgba(backgroundColor.value, props.blurAlpha)
	})
	
	const fontColor = computed(() : string => {
		return useFontColor('', '')
	})
	
	const corner = computed(() => {
		return $ux.Util.getPx(props.corner)
	})
	
	const tabbarStyle = computed(():Map<string, any> => {
		let css = new Map<string, any>()
		
		if (props.fixed) {
			css.set('position', 'fixed')
			css.set('bottom', 0)
		}
		
		const height = safeAreaHeight + 54
		const offset = _type.value == 'special' ? (props.radius + 8) : 0
		css.set('height', `${height + offset}px`)
		
		if(props.border && _type.value == 'default') {
			css.set('border-top', `0.5px solid ${props.borderColor}`)
		}
		
		if(!props.showBlur) {
			css.set('background-color', `${_type.value == 'default' ? backgroundColor.value : 'transparent'}`)
		} else {
			// #ifdef MP-WEIXIN
			css.set('background-color', `${_type.value == 'default' ? backgroundColor.value : 'transparent'}`)
			// #endif
		}
		
		css.set('z-index', props.zIndex)
		
		if (corner.value > 0 && _type.value == 'default') {
			css.set('border-top-left-radius', `${corner.value}px`)
			css.set('border-top-right-radius', `${corner.value}px`)
		}
		
		return css
	})
	
	const contentStyle = computed(():Map<string, any> => {
		let css = new Map<string, any>()
		
		if(_type.value == 'special') {
			css.set('margin-top', `${props.radius}px`)
		} else {
			css.set('margin-bottom', `${safeAreaHeight}px`)
		}
		
		css.set('height', `${54}px`)
		
		return css
	})
	
	const bodyStyle = computed(():Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('height', `${ _anim.value == 'push' ? 40 : 54}px`)
		
		return css
	})
	
	const centerStyle = computed(():Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('width', `${props.radius * 2}px`)
		css.set('height', `${props.radius * 2}px`)
		css.set('bottom', `${safeAreaHeight + 54 - props.radius - 5}px`)
		css.set('background-color', props.centerColor)
		
		if(_anim.value == 'roll') {
			const w = (uni.getWindowInfo().screenWidth ?? 0) / 5
			let width = props.radius + 5
			let startX = w * tabIndex.value + width / 2 + props.radius / 2 - 5
			
			css.set('left', `${startX}px`)
		}
		
		return css
	})
	
	const centerHoverStyle = computed(():string => {
		return 'background-color: #999999'
	})
	
	/**
	 * 样式
	 */
	function textStyle(i : number) : any | null {
		let color:string
		if(tabIndex.value == i) {
			color = tabs.value[i].selectedColor ?? fontColor.value
		} else {
			color = tabs.value[i].unselectedColor ?? fontColor.value
		}
		
		if(color == '') {
			color = fontColor.value
		}
		
		let css = new Map<string, any | null>()
		
		css.set('color', `${color}`)
		css.set('font-size', `${$ux.Util.getPx(props.fontSize)}px`)
		css.set('font-weight', `${props.fontBold ? 'bold' : 'normal'}`)
		
		return css
	}
	
	function pointStyle(i : number) : string {
		let color:string
		if(tabIndex.value == i) {
			color = tabs.value[i].selectedColor ?? fontColor.value
		} else {
			color = tabs.value[i].unselectedColor ?? fontColor.value
		}
		
		if(color == '') {
			color = fontColor.value
		}
		
		return `background-color: ${color}`
	}
	
	function hoverStyle(i : number) : string {
		return `background-color: ${tabs.value[i].selectedColor}`
	}
	
	/**
	 * hover
	 */
	function hoverAnim(id : string, show : boolean) {
		uni.getElementById(id)?.style?.setProperty('opacity', show ? 0.1 : 0)
		uni.getElementById(id)?.style?.setProperty('transform', show ? 'scale(1)' : 'scale(0)')
	}
	
	/**
	 * ctx
	 */
	// #ifdef APP-ANDROID || APP-IOS
	function getCtx(): DrawableContext {
		return drawer.value! as DrawableContext
	}
	// #endif
	
	// #ifndef APP-ANDROID || APP-IOS
	function getCtx(): any {
		return drawer.value!
	}
	// #endif
	
	// 清除绘制
	function resetDraw() {
		const _ctx = getCtx()
		
		// #ifdef APP-ANDROID || APP-IOS
		_ctx.reset()
		_ctx.update()
		// #endif
		
		// #ifndef APP-ANDROID || APP-IOS
		// @ts-ignore
		_ctx.clearRect(0, 0, canvas.value!.width, canvas.value!.height)
		// #endif
	}
	
	/**
	 * 正常
	 */
	function draw0() {
		
	}
	
	/**
	 * 凹陷
	 */
	async function draw1() {
		let rect = await uxTabbarRef.value!.getBoundingClientRectAsync()!
		
		const _ctx = getCtx()
		
		const h = safeAreaHeight + 54
		const sw = rect?.width ?? 0
		let width = props.radius + 10
		let height = props.radius + 10
		let startX = sw / 2 - width
		let offset = 20
		
		// 开始绘制
		_ctx.beginPath()
		
		if (corner.value > 0) {
			// 圆角
		
			// 起点
			let lc_s1 = 0
			let lc_s2 = corner.value * 2
		
			// 控制点1
			let lc_c1_1 = 0
			let lc_c1_2 = 0
		
			// 控制点2
			let lc_c2_1 = 0
			let lc_c2_2 = 0
		
			// 终点
			let lc_e1 = corner.value * 2
			let lc_e2 = 0
		
			_ctx.moveTo(lc_s1, lc_s2)
			_ctx.bezierCurveTo(lc_c1_1, lc_c1_2, lc_c2_1, lc_c2_2, lc_e1, lc_e2)
		} else {
			// 原点开始
			_ctx.moveTo(0, 0)
		}
		
		// 左边弧
		// 起点
		let l_s1 = startX - offset
		let l_s2 = 0
		
		// 控制点1
		let l_c1_1 = startX
		let l_c1_2 = l_s2
		
		// 控制点2
		let l_c2_x = width * (1 / 5)
		let l_c2_y = height
		let l_c2_1 = startX + l_c2_x
		let l_c2_2 = l_s2 + l_c2_y
		
		// 终点
		let l_e1 = startX + width
		let l_e2 = height
		
		_ctx.lineTo(l_s1, l_s2)
		_ctx.bezierCurveTo(l_c1_1, l_c1_2, l_c2_1, l_c2_2, l_e1, l_e2)
		
		// 右边弧
		// 起点是上一个终点
		
		// 控制点1
		let r_c1_1 = startX + width * 2 - l_c2_x
		let r_c1_2 = l_c2_2
		
		// 控制点2
		let r_c2_1 = startX + width * 2
		let r_c2_2 = l_c1_2
		
		// 终点
		let r_e1 = startX + width * 2 + offset
		let r_e2 = 0
		
		_ctx.bezierCurveTo(r_c1_1, r_c1_2, r_c2_1, r_c2_2, r_e1, r_e2)
		
		// 向右画线
		if (corner.value > 0) {
			// 圆角
			// 起点
			let lc_s1 = sw - corner.value
			let lc_s2 = 0
		
			// 控制点1
			let lc_c1_1 = sw
			let lc_c1_2 = 0
		
			// 控制点2
			let lc_c2_1 = sw
			let lc_c2_2 = 0
		
			// 终点
			let lc_e1 = sw
			let lc_e2 = corner.value
		
			_ctx.bezierCurveTo(lc_c1_1, lc_c1_2, lc_c2_1, lc_c2_2, lc_e1, lc_e2)
		} else {
			_ctx.lineTo(sw, 0)
		}
		
		// 向下画线
		_ctx.lineTo(sw, h)
		
		// 向左画线
		_ctx.lineTo(0, h)
		
		// 回到原点
		if (corner.value > 0) {
			// 圆角
			_ctx.lineTo(0, corner.value * 2)
		} else {
			_ctx.lineTo(0, 0)
		}
		
		_ctx.closePath()
		
		_ctx.fillStyle = backgroundColor.value
		_ctx.fill()
		
		// 上边框
		if (props.border) {
			// 开始绘制
			_ctx.beginPath()
		
			if (corner.value > 0) {
				// 圆角
		
				// 起点
				let lc_s1 = 0
				let lc_s2 = corner.value * 2
		
				// 控制点1
				let lc_c1_1 = 0
				let lc_c1_2 = 0
		
				// 控制点2
				let lc_c2_1 = 0
				let lc_c2_2 = 0
		
				// 终点
				let lc_e1 = corner.value * 2
				let lc_e2 = 0
		
				_ctx.moveTo(lc_s1, lc_s2)
				_ctx.bezierCurveTo(lc_c1_1, lc_c1_2, lc_c2_1, lc_c2_2, lc_e1, lc_e2)
			} else {
				// 原点开始
				_ctx.moveTo(0, 0)
			}
		
			// 左边弧
			// 起点
			l_s1 = startX - offset
			l_s2 = 0
		
			// 控制点1
			l_c1_1 = startX
			l_c1_2 = l_s2
		
			// 控制点2
			l_c2_x = width * (1 / 5)
			l_c2_y = height
			l_c2_1 = startX + l_c2_x
			l_c2_2 = l_s2 + l_c2_y
		
			// 终点
			l_e1 = startX + width
			l_e2 = height
		
			_ctx.lineTo(l_s1, l_s2)
			_ctx.bezierCurveTo(l_c1_1, l_c1_2, l_c2_1, l_c2_2, l_e1, l_e2)
		
			// 右边弧
			// 起点是上一个终点
		
			// 控制点1
			r_c1_1 = startX + width * 2 - l_c2_x
			r_c1_2 = l_c2_2
		
			// 控制点2
			r_c2_1 = startX + width * 2
			r_c2_2 = l_c1_2
		
			// 终点
			r_e1 = startX + width * 2 + offset
			r_e2 = 0
		
			_ctx.bezierCurveTo(r_c1_1, r_c1_2, r_c2_1, r_c2_2, r_e1, r_e2)
		
			// 向右画线
			if (corner.value > 0) {
				// 圆角
				// 起点
				let lc_s1 = sw - corner.value
				let lc_s2 = 0
		
				// 控制点1
				let lc_c1_1 = sw
				let lc_c1_2 = 0
		
				// 控制点2
				let lc_c2_1 = sw
				let lc_c2_2 = 0
		
				// 终点
				let lc_e1 = sw
				let lc_e2 = corner.value
		
				_ctx.bezierCurveTo(lc_c1_1, lc_c1_2, lc_c2_1, lc_c2_2, lc_e1, lc_e2)
			} else {
				_ctx.lineTo(sw, 0)
			}
		
			_ctx.strokeStyle = props.borderColor
			_ctx.stroke()
			_ctx.fillStyle = backgroundColor.value
		}
		
		// #ifdef APP-ANDROID || APP-IOS
		_ctx.update()
		// #endif
	}
	
	/**
	 * 凹陷2
	 */
	// #ifdef MP
	async function draw2(newIndex: number) {
	// #endif
	// #ifndef MP
	function draw2(newIndex: number) {
	// #endif
		// #ifdef MP
		let rect = await uxTabbarRef.value!.getBoundingClientRectAsync()!
		// #endif
		// #ifndef MP
		let rect = uxTabbarRef.value!.getBoundingClientRect()
		// #endif
		
		const _ctx = getCtx()
		
		const h = safeAreaHeight + 54
		const sw = rect?.width ?? 0
		let width = props.radius + 5
		let height = props.radius + 10
		let offset = 15
		let startX = sw / 5 * newIndex + width / 2
		
		// 开始绘制
		_ctx.beginPath()
		
		if (corner.value > 0) {
			// 圆角
		
			// 起点
			let lc_s1 = 0
			let lc_s2 = corner.value * 2
		
			// 控制点1
			let lc_c1_1 = 0
			let lc_c1_2 = 0
		
			// 控制点2
			let lc_c2_1 = 0
			let lc_c2_2 = 0
		
			// 终点
			let lc_e1 = corner.value * 2
			let lc_e2 = 0
		
			_ctx.moveTo(lc_s1, lc_s2)
			_ctx.bezierCurveTo(lc_c1_1, lc_c1_2, lc_c2_1, lc_c2_2, lc_e1, lc_e2)
		} else {
			// 原点开始
			_ctx.moveTo(0, 0)
		}
		
		// 左边弧
		// 起点
		let l_s1 = startX - offset
		let l_s2 = 0
		
		// 控制点1
		let l_c1_1 = startX
		let l_c1_2 = l_s2
		
		// 控制点2
		let l_c2_x = width * (1 / 5)
		let l_c2_y = height
		let l_c2_1 = startX + l_c2_x
		let l_c2_2 = l_s2 + l_c2_y
		
		// 终点
		let l_e1 = startX + width
		let l_e2 = height
		
		_ctx.lineTo(l_s1, l_s2)
		_ctx.bezierCurveTo(l_c1_1, l_c1_2, l_c2_1, l_c2_2, l_e1, l_e2)
		
		// 右边弧
		// 起点是上一个终点
		
		// 控制点1
		let r_c1_1 = startX + width * 2 - l_c2_x
		let r_c1_2 = l_c2_2
		
		// 控制点2
		let r_c2_1 = startX + width * 2
		let r_c2_2 = l_c1_2
		
		// 终点
		let r_e1 = startX + width * 2 + offset
		let r_e2 = 0
		
		_ctx.bezierCurveTo(r_c1_1, r_c1_2, r_c2_1, r_c2_2, r_e1, r_e2)
		
		let _l_s1 = l_s1
		let _l_s2 = l_s2
		let _l_c1_1 = l_c1_1
		let _l_c1_2 = l_c1_2
		let _l_c2_1 = l_c2_1
		let _l_c2_2 = l_c2_2
		let _l_e1 = l_e1
		let _l_e2 = l_e2
		let _r_c1_1 = r_c1_1
		let _r_c1_2 = r_c1_2
		let _r_c2_1 = r_c2_1
		let _r_c2_2 = r_c2_2
		let _r_e1 = r_e1
		let _r_e2 = r_e2
		
		// 向右画线
		if (corner.value > 0) {
			// 圆角
			// 起点
			let lc_s1 = sw - corner.value
			let lc_s2 = 0
		
			// 控制点1
			let lc_c1_1 = sw
			let lc_c1_2 = 0
		
			// 控制点2
			let lc_c2_1 = sw
			let lc_c2_2 = 0
		
			// 终点
			let lc_e1 = sw
			let lc_e2 = corner.value
		
			_ctx.bezierCurveTo(lc_c1_1, lc_c1_2, lc_c2_1, lc_c2_2, lc_e1, lc_e2)
		} else {
			_ctx.lineTo(sw, 0)
		}
		
		// 向下画线
		_ctx.lineTo(sw, h)
		
		// 向左画线
		_ctx.lineTo(0, h)
		
		// 回到原点
		if (corner.value > 0) {
			// 圆角
			_ctx.lineTo(0, corner.value * 2)
		} else {
			_ctx.lineTo(0, 0)
		}
		
		_ctx.closePath()
		
		_ctx.fillStyle = backgroundColor.value
		_ctx.fill()
		
		// 上边框
		if (props.border) {
			// 开始绘制
			_ctx.beginPath()
		
			if (corner.value > 0) {
				// 圆角
		
				// 起点
				let lc_s1 = 0
				let lc_s2 = corner.value * 2
		
				// 控制点1
				let lc_c1_1 = 0
				let lc_c1_2 = 0
		
				// 控制点2
				let lc_c2_1 = 0
				let lc_c2_2 = 0
		
				// 终点
				let lc_e1 = corner.value * 2
				let lc_e2 = 0
		
				_ctx.moveTo(lc_s1, lc_s2)
				_ctx.bezierCurveTo(lc_c1_1, lc_c1_2, lc_c2_1, lc_c2_2, lc_e1, lc_e2)
			} else {
				// 原点开始
				_ctx.moveTo(0, 0)
			}
		
			// 左边弧
			// 起点
			l_s1 = startX - offset
			l_s2 = 0
		
			// 控制点1
			l_c1_1 = startX
			l_c1_2 = l_s2
		
			// 控制点2
			l_c2_x = width * (1 / 5)
			l_c2_y = height
			l_c2_1 = startX + l_c2_x
			l_c2_2 = l_s2 + l_c2_y
		
			// 终点
			l_e1 = startX + width
			l_e2 = height
		
			_ctx.lineTo(l_s1, l_s2)
			_ctx.bezierCurveTo(l_c1_1, l_c1_2, l_c2_1, l_c2_2, l_e1, l_e2)
		
			// 右边弧
			// 起点是上一个终点
		
			// 控制点1
			r_c1_1 = startX + width * 2 - l_c2_x
			r_c1_2 = l_c2_2
		
			// 控制点2
			r_c2_1 = startX + width * 2
			r_c2_2 = l_c1_2
		
			// 终点
			r_e1 = startX + width * 2 + offset
			r_e2 = 0
		
			_ctx.bezierCurveTo(r_c1_1, r_c1_2, r_c2_1, r_c2_2, r_e1, r_e2)
		
			// 向右画线
			if (corner.value > 0) {
				// 圆角
				// 起点
				let lc_s1 = sw - corner.value
				let lc_s2 = 0
		
				// 控制点1
				let lc_c1_1 = sw
				let lc_c1_2 = 0
		
				// 控制点2
				let lc_c2_1 = sw
				let lc_c2_2 = 0
		
				// 终点
				let lc_e1 = sw
				let lc_e2 = corner.value
		
				_ctx.bezierCurveTo(lc_c1_1, lc_c1_2, lc_c2_1, lc_c2_2, lc_e1, lc_e2)
			} else {
				_ctx.lineTo(sw, 0)
			}
		
			_ctx.strokeStyle = props.borderColor
			_ctx.stroke()
			_ctx.fillStyle = backgroundColor.value
		}
		
		// 线条
		_ctx.beginPath()
		_ctx.lineTo(_l_s1, _l_s2)
		_ctx.bezierCurveTo(_l_c1_1, _l_c1_2, _l_c2_1, _l_c2_2, _l_e1, _l_e2)
		_ctx.bezierCurveTo(_r_c1_1, _r_c1_2, _r_c2_1, _r_c2_2, _r_e1, _r_e2)
		_ctx.lineWidth = 2
		_ctx.fillStyle = 'rgba(0,0,0,0.6)'
		_ctx.stroke()
		
		// #ifdef APP-ANDROID || APP-IOS
		_ctx.update()
		// #endif
	}
	
	/**
	 * 绘制背景
	 */
	// #ifdef APP-ANDROID || APP-IOS
	function draw() {
	// #endif
	// #ifndef APP-ANDROID || APP-IOS
	async function draw() {
	// #endif
		// #ifdef APP-ANDROID || APP-IOS
		drawer.value = uxTabbarRef.value!.getDrawableContext()
		// #endif
		
		// #ifndef APP-ANDROID || APP-IOS
		// @ts-ignore
		// canvas.value = uni.getElementById(canvasId) as UniCanvasElement
		// console.log(canvas.value);
		// canvas.value!.width = canvas.value!.offsetWidth * dpr
		// canvas.value!.height = canvas.value!.offsetHeight * dpr
		// drawer.value = canvas.value!.getContext('2d')
		// drawer.value!.scale(dpr, dpr)
		
		let context = await $ux.Util.createCanvasContext(canvasId, instance)
		
		drawer.value = context.getContext('2d')!
		canvas.value = drawer.value.canvas!
		
		// 处理高清屏逻辑
		const dpr = uni.getDeviceInfo().devicePixelRatio ?? 1
		canvas.value!.width = canvas.value!.offsetWidth * dpr
		canvas.value!.height = canvas.value!.offsetHeight * dpr
		drawer.value!.scale(dpr, dpr)
		
		// #endif
		
		// 清除绘制
		resetDraw()
		
		setTimeout(() => {
			if (_type.value == 'default') {
				draw0()
			} else if (_type.value == 'special') {
				if(_anim.value == 'roll') {
					draw2(tabIndex.value)
				} else {
					draw1()
				}
			}
		}, 50);
	}
	
	/**
	 * 无动效
	 */
	function noneAnim(_old : number, _new : number) {
		
	}
	
	/**
	 * 上下滚动动效
	 */
	function scrollAnim(_old : number, _new : number) {
		let anim = (i : number, show : boolean) : void => {
			if (tabs.value[i].btn!) {
				// 独立按钮不设置
				return
			}
		
			if (show) {
				// 上推
				uni.getElementById(`${myId.value}-icon-${i}`)?.style?.setProperty('transform', 'translate(0, -50%)')
				uni.getElementById(`${myId.value}-name-${i}`)?.style?.setProperty('transform', 'translate(0, -230%)')
		
				// 透明度
				uni.getElementById(`${myId.value}-icon-${i}`)?.style?.setProperty('opacity', 0)
				uni.getElementById(`${myId.value}-name-${i}`)?.style?.setProperty('opacity', 1)
		
				// 点
				uni.getElementById(`${myId.value}-point-${i}`)?.style?.setProperty('opacity', 1)
				uni.getElementById(`${myId.value}-point-${i}`)?.style?.setProperty('transform', 'scale(1)')
			} else {
				// 下压
				uni.getElementById(`${myId.value}-icon-${i}`)?.style?.setProperty('transform', 'translate(0, 0%)')
				uni.getElementById(`${myId.value}-name-${i}`)?.style?.setProperty('transform', 'translate(0, 100%)')
		
				// 透明度
				uni.getElementById(`${myId.value}-icon-${i}`)?.style?.setProperty('opacity', 1)
				uni.getElementById(`${myId.value}-name-${i}`)?.style?.setProperty('opacity', 0)
		
				// 点
				uni.getElementById(`${myId.value}-point-${i}`)?.style?.setProperty('opacity', 0)
				uni.getElementById(`${myId.value}-point-${i}`)?.style?.setProperty('transform', 'scale(0)')
			}
		}
		
		if (_old == _new) {
			// 重置
			anim(_new, true)
		
			for (let i = 0; i < tabs.value.length; i++) {
				if (i == _new) {
					continue
				}
		
				anim(i, false)
			}
		} else {
			// 上一个隐藏
			anim(_old, false)
		
			// 当前显示
			anim(_new, true)
		}
	}
	
	/**
	 * 左右滚动动效
	 */
	function rollAnim(_old : number, _new : number) {
		resetDraw()
		draw2(_new)
		
		let anim = (i : number, show : boolean) : void => {
			if (show) {
				// 上推
				uni.getElementById(`${myId.value}-icon-${i}`)?.style?.setProperty('transform', 'translate(0, -50%)')
		
				// 透明度
				uni.getElementById(`${myId.value}-icon-${i}`)?.style?.setProperty('opacity', 0)
				uni.getElementById(`${myId.value}-icon-roll`)?.style?.setProperty('opacity', 1)
			} else {
				// 下压
				uni.getElementById(`${myId.value}-icon-${i}`)?.style?.setProperty('transform', 'translate(0, 0%)')
		
				// 透明度
				uni.getElementById(`${myId.value}-icon-${i}`)?.style?.setProperty('opacity', 1)
				uni.getElementById(`${myId.value}-icon-roll`)?.style?.setProperty('opacity', 0)
			}
			
			uni.getElementById(`${myId.value}-icon-roll`)?.style?.setProperty('transition-duration', '200ms')
			uni.getElementById(`${myId.value}-icon-roll`)?.style?.setProperty('transform', 'translate(0, -100%)')
			uni.getElementById(`${myId.value}-icon-roll`)?.style?.setProperty('opacity', 0)
			setTimeout(() => {
				uni.getElementById(`${myId.value}-icon-roll`)?.style?.setProperty('transition-duration', '0ms')
				setTimeout(() => {
					uni.getElementById(`${myId.value}-icon-roll`)?.style?.setProperty('transform', 'translate(0, 100%)')
					setTimeout(() => {
						uni.getElementById(`${myId.value}-icon-roll`)?.style?.setProperty('transition-duration', '200ms')
						uni.getElementById(`${myId.value}-icon-roll`)?.style?.setProperty('opacity', 1)
						uni.getElementById(`${myId.value}-icon-roll`)?.style?.setProperty('transform', 'translate(0, 0%)')
					}, 10);
				}, 10);
			}, 150);
		}
		
		if (_old == _new) {
			// 重置
			anim(_new, true)
		
			for (let i = 0; i < tabs.value.length; i++) {
				if (i == _new) {
					continue
				}
		
				anim(i, false)
			}
		} else {
			// 上一个隐藏
			anim(_old, false)
		
			// 当前显示
			anim(_new, true)
		}
	}
	
	/**
	 * 左右推压动效
	 */
	async function pushAnim(_old : number, _new : number) {
		let rect = await uxTabbarRef.value!.getBoundingClientRectAsync()!
		let screenWidth = rect?.width ?? 0
		
		let anim = (i : number, show : boolean) : void => {
			// 平分宽度
			let per = 100 / tabs.value.length / 100 * screenWidth
		
			// 展开宽度
			let width = per + (5 / 100 * screenWidth)
		
			// 塌缩宽度
			let cwidth = (screenWidth - 20 * 2 - width) / (tabs.value.length - 1)
		
			if (show) {
				// 展开
				uni.getElementById(tabs.value[i].id!)?.style?.setProperty('width', `${width}px`)
		
				// 背景色
				let color = $ux.Color.getRgba(tabs.value[i].selectedColor!, 0.2)
				uni.getElementById(tabs.value[i].id!)?.style?.setProperty('background-color', color)
		
				// 透明度
				uni.getElementById(`${myId.value}-name-${i}`)?.style?.setProperty('opacity', 1)
			} else {
				// 塌缩
				uni.getElementById(tabs.value[i].id!)?.style?.setProperty('width', `${cwidth}px`)
		
				// 背景色
				uni.getElementById(tabs.value[i].id!)?.style?.setProperty('background-color', 'transparent')
		
				// 透明度
				uni.getElementById(`${myId.value}-name-${i}`)?.style?.setProperty('opacity', 0)
			}
		}
		
		anim(_new, true)
		
		for (let i = 0; i < tabs.value.length; i++) {
			if (i == _new) {
				continue
			}
		
			anim(i, false)
		}
	}
	
	/**
	 * 水滴动效
	 */
	async function waterAnim(_old : number, _new : number) {
		const _ctx = getCtx()
		
		// 宽高
		let rect = await uxTabbarRef.value!.getBoundingClientRectAsync()!
		let w = (rect?.width ?? 0) / props.data.length
		let width = w * 0.7
		let height = 12
		let _height = height
		
		// 居左
		let left = (w - width) / 2
		
		// 起点
		let startX = w * _old + left
		
		// 终点
		let endX = w * _new + left
		
		// 水滴
		let dropStart = height - 10
		let dropHeight = 35
		let dropEnd = dropStart + dropHeight
		// #ifdef APP-IOS
		let dropStep = 1
		// #endif
		// #ifndef APP-IOS
		let dropStep = 2
		// #endif
		let droprRadius = 5
		
		// 前进 后退
		let forward = startX < endX
		
		// 步长
		// #ifdef APP-IOS
		let step = 0.5 * Math.abs(_new - _old)
		// #endif
		// #ifndef APP-IOS
		let step = 5 * Math.abs(_new - _old)
		// #endif
		
		let moveAnim = (_ : boolean) : void => { }
		moveAnim = (drop : boolean) : void => {
			// 百分比
			let per = forward ? (startX / endX) : (endX / startX)
			per = per < 0.3 ? 0.3 : (per > 1 ? 1 : per)
		
			// 清除绘制
			// #ifdef APP-ANDROID || APP-IOS
			_ctx.reset()
			// #endif
			
			// #ifndef APP-ANDROID || APP-IOS
			// @ts-ignore
			_ctx.clearRect(0, 0, canvas.value!.width, canvas.value!.height)
			// #endif
			
			// 开始绘制
			_ctx.beginPath()
		
			// 水滴缓冲动效
			if (drop) {
				let dp = 1 - (dropEnd - dropStart) / dropHeight
				height = dp < 0.5 ? _height * (1 + dp) : _height * dp
		
				if (height < _height) {
					height = _height
				}
			}
		
			// 左边弧
			// 起点
			let l_s1 = startX
			let l_s2 = 0
		
			// 控制点1
			let l_c1_x = width / 2 * (2 / 3)
			let l_c1_y = height * (1 / 5)
			let l_c1_1 = l_s1 + l_c1_x
			let l_c1_2 = l_s2 + l_c1_y
		
			// 控制点2
			let l_c2_x = width / 2 * (4 / 5)
			let l_c2_y = height
			let l_c2_1 = l_s1 + l_c2_x
			let l_c2_2 = l_s2 + l_c2_y
		
			// 终点
			let l_e1 = l_s1 + width / 2
			let l_e2 = height
			
			_ctx.moveTo(l_s1, l_s2)
			_ctx.bezierCurveTo(l_c1_1, l_c1_2, l_c2_1, l_c2_2, l_e1, l_e2)
		
			// 右边弧
			// 起点是上一个终点
		
			// 控制点1
			let r_c1_1 = startX + width - l_c2_x
			let r_c1_2 = l_c2_2
		
			// 控制点2
			let r_c2_1 = startX + width - l_c1_x
			let r_c2_2 = l_c1_2
		
			// 终点
			let r_e1 = startX + width
			let r_e2 = 0
		
			_ctx.bezierCurveTo(r_c1_1, r_c1_2, r_c2_1, r_c2_2, r_e1, r_e2)
		
			// 结束绘制
			_ctx.closePath()
		
			// 填充
			_ctx.fillStyle = $ux.Color.getRgba(tabs.value[_new].selectedColor!, per)
			_ctx.fill()
		
			// 水滴动效
			if (drop) {
				let dp = (dropEnd - dropStart) / dropHeight
				dp = dp < 0 ? 0 : dp
		
				let dx = startX + (width / 2) - droprRadius
				let dy = dropStart
				
				// 开始绘制
				_ctx.beginPath()
		
				// 中点
				_ctx.moveTo(dx, dy)
		
				// 水滴
				_ctx.arc(dx + droprRadius, dy + droprRadius, droprRadius, 0, Math.PI * 2, false)
		
				// 结束绘制
				_ctx.closePath()
		
				// 填充
				_ctx.fillStyle = $ux.Color.getRgba(tabs.value[_new].selectedColor!, dp)
				_ctx.fill()
			}
		
			// 更新
			// #ifdef APP-ANDROID || APP-IOS
			_ctx.update()
			// #endif
		
			// 模拟动画
			if (forward) {
				if (startX < endX) {
					// 右移
					setTimeout(() => {
						moveAnim(false)
					}, 0)
		
					startX += step
		
					if (endX - startX < step) {
						startX = endX
					}
				} else {
					// 开始水滴动效
					if (dropStart < dropEnd) {
						setTimeout(() => {
							moveAnim(true)
						}, 0)
					}
		
					dropStart += dropStep
				}
			} else {
				if (startX > endX) {
					// 左移
					setTimeout(() => {
						moveAnim(false)
					}, 0)
		
					startX -= step
		
					if (startX - endX < step) {
						startX = endX
					}
				} else {
					// 开始水滴动效
					if (dropStart < dropEnd) {
						setTimeout(() => {
							moveAnim(true)
						}, 0)
					}
		
					dropStart += dropStep
				}
			}
		}
		
		moveAnim(false)
	}
	
	/**
	 * 动效
	 */
	function animTo(index : number) {
		if (_anim.value == 'none') {
			// 无动效
			noneAnim(tabIndex.value, index)
		} else if (_anim.value == 'scroll') {
			// 上下滚动动效
			scrollAnim(tabIndex.value, index)
		} else if (_anim.value == 'roll') {
			// 左右滚动动效
			rollAnim(tabIndex.value, index)
		} else if (_anim.value == 'push') {
			// 左右推压动效
			pushAnim(tabIndex.value, index)
		} else if (_anim.value == 'water') {
			// 水滴动效
			waterAnim(tabIndex.value, index)
		}
	}
	
	/**
	 * 点击态
	 */
	function touchstart(i : number) {
		if (!props.hover) {
			return
		}
		
		if (isPress.value) {
			return
		}
		
		timer = new Date().getTime()
		isPress.value = true
		
		hoverAnim(`${myId.value}-hover-${i}`, true)
	}
	
	function touchend(i : number) {
		let offset = new Date().getTime() - timer
		
		if (offset < 110) {
			setTimeout(() => {
				isPress.value = false
		
				hoverAnim(`${myId.value}-hover-${i}`, false)
			}, 110 - offset)
		} else {
			isPress.value = false
		
			hoverAnim(`${myId.value}-hover-${i}`, false)
		}
	}
	
	/**
	 * 重新渲染
	 */
	function reRender() {
		// 清除绘制
		resetDraw()
		
		renderOk.value = false
		nextTick(() => {
			renderOk.value = true
			
			// #ifdef MP
			setTimeout(async () => {
				await draw()
				animTo(tabIndex.value)
			}, 20)
			// #endif
			// #ifndef MP
			setTimeout(() => {
				draw()
				animTo(tabIndex.value)
			}, 20)
			// #endif
		})
	}
	
	/**
	 * 中间tab
	 */
	function onCenter() {
		emit('center')
	}
	
	/**
	 * 点击tab
	 */
	function onTab(i : number) {
		if (tabs.value[i].btn!) {
			// 独立按钮
			emit('click', tabs.value[i].index!)
			return
		}
		
		if (_type.value == 'special' && i == props.data.length / 2) {
			return
		}
		
		// 动效
		animTo(i)
		
		// 选择tab
		tabIndex.value = i
		emit('change', tabs.value[i].index!)
	}
	
	/**
	 * 初始tabs
	 */
	function initTabs() {
		let list = [] as UxTab[]
		
		props.data.forEach((e : any | null, i : number) => {
			let data = e! as UxTab
		
			let item = {
				id: data.id ?? `ux-tabbar-${_type.value}-${_anim.value}-${i}`,
				name: data.name,
				selectedIcon: data.selectedIcon ?? '',
				unselectedIcon: data.unselectedIcon ?? data.selectedIcon,
				selectedColor: data.selectedColor ?? selectedColor.value,
				unselectedColor: data.unselectedColor ?? unselectedColor.value,
				badge: data.badge ?? 0,
				reddot: data.reddot ?? false,
				btn: data.btn ?? false,
				index: i,
			} as UxTab
		
			list.push(item)
		})
		
		tabs.value = list as UxTab[]
		
		if (_anim.value == 'scroll') {
			let index = tabs.value.findIndex((e : UxTab) : boolean => e.selectedIcon == '')
		
			if (index != -1) {
				console.error('[ux-tabbar]配置错误: 动效[scroll]需配置selectedIcon')
				_anim.value = 'none'
			}
		} else {
			let a = tabs.value.findIndex((e : UxTab) : boolean => e.selectedIcon != '' && e.unselectedIcon == '')
			let b = tabs.value.findIndex((e : UxTab) : boolean => e.selectedIcon == '' && e.unselectedIcon != '')
		
			if (a != -1 || b != -1) {
				console.error(`[ux-tabbar]配置错误: 动效[${_anim.value}]需同时配置selectedIcon & unselectedIcon`)
			}
		}
		
		// 中间凹陷按钮
		if (_type.value == 'special' && _anim.value != 'roll') {
			if (_anim.value != 'none' && _anim.value != 'scroll') {
				_anim.value = 'none'
				console.error('[ux-tabbar]配置警告: 类型[special]仅支持动效[none/scroll]')
			}
		
			if (tabs.value.length % 2 != 0) {
				console.error('[ux-tabbar]配置错误: 类型[special]tabs数量必须为偶数')
				_type.value = 'default'
			}
			
			// #ifdef MP-WEIXIN
			console.error('[ux-tabbar]配置警告: 类型[special]不支持微信小程序。不支持原因：微信原生canvas层级太高')
			// #endif
		
			// 插入占位按钮到中间
			tabs.value.splice(tabs.value.length / 2, 0, {
				id: 'center',
				name: '',
				selectedIcon: '',
				unselectedIcon: '',
				selectedColor: '',
				unselectedColor: '',
				badge: 0,
				reddot: false,
				btn: false,
				index: -1
			} as UxTab)
		}
		
		// 初始化位置
		setTimeout(() => {
			animTo(tabIndex.value)
		}, 100);
	}
	
	const data = computed((): any[] => {
		return props.data as any[]
	})
	
	const type = computed(():string => {
		return props.type as string
	})
	
	const anim = computed(():string => {
		return props.anim as string
	})
	
	watch(data, () => {
		initTabs()
	})
	
	watch(type, () => {
		_type.value = type.value
	}, {immediate: true})
	
	watch(_type, () => {
		initTabs()
		reRender()
	})
	
	watch(backgroundColor, () => {
		reRender()
	})
	
	watch(selectedColor, () => {
		initTabs()
		reRender()
	})
	
	watch(unselectedColor, () => {
		initTabs()
		reRender()
	})
	
	watch(anim, () => {
		_anim.value = anim.value
	}, {immediate: true})
	
	watch(_anim, () => {
		if (_type.value == 'special') {
			if (_anim.value != 'none' && _anim.value != 'scroll' && _anim.value != 'roll') {
				_anim.value = 'none'
				console.warn('[ux-tabbar]配置警告: 类型[special]仅支持动效[none/scroll/roll]')
			}
		}
		
		reRender()
	})
	
	// #ifdef APP-ANDROID || APP-IOS
	function init() {
		draw()
		initTabs()
		reRender()
	}
	// #endif
	
	// #ifndef APP-ANDROID || APP-IOS
	async function init() {
		await draw()
		initTabs()
		reRender()
	}
	// #endif
	
	onMounted(() => {
		if (_type.value == 'special' && _anim.value != 'roll') {
			if(props.index >= props.data.length / 2) {
				tabIndex.value = props.index + 1
			} else {
				tabIndex.value = props.index
			}
		} else {
			tabIndex.value = props.index
		}
		
		setTimeout(() => {
			init()
		}, 100);
		
		// 监听尺寸变化重绘
		useResize(null, () => {
			draw()
		})
	})
	
	defineExpose({
		onTab,
		onCenter
	})
</script>

<style lang="scss">
	.ux-tabbar {
		position: relative;
		width: 100%;
		height: 54px;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: flex-end;
		z-index: 10000;
			
		&__blur {
			position: absolute;
			width: 100%;
			height: 100%;;
		}

		.ux-tabbar__center {
			position: absolute;
			width: 60px;
			height: 60px;
			border-radius: 60px;
			background-color: #fff;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			box-shadow: 0 0 10px 2px #ededed;
			transition-property: left;
			transition-duration: 200ms;
			transition-timing-function: linear;
			
			&__icon {
				width: 25px;
				height: 25px;
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: center;
			}
		}

		.ux-tabbar__content {
			position: relative;
			width: 100%;
			height: 54px;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: space-around;
			/* #ifdef WEB */
			cursor: pointer;
			/* #endif */
			
			&__canvas {
				position: absolute;
				z-index: 0;
				width: 100%;
				height: 100%;;
			}

			.ux-tabbar__item__none {
				flex: 1;
				height: 100%;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				z-index: 1 !important;
				position: relative;

				.ux-tabbar__icon {
					width: 25px;
					height: 25px;
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: center;
				}

				.ux-tabbar__name {
					color: black;
					font-size: 12px;
				}
			}

			.ux-tabbar__item__scroll {
				flex: 1;
				height: 100%;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				z-index: 1 !important;
				position: relative;
				
				.ux-tabbar__icon {
					position: absolute;
					width: 36px;
					height: 36px;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
				}

				.ux-tabbar__name {
					position: absolute;
					top: 100%;
					color: black;
					font-size: 18px;
				}
			}
			
			.ux-tabbar__item__roll {
				flex: 1;
				height: 100%;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				z-index: 1 !important;
				position: relative;
				
				.ux-tabbar__icon {
					width: 25px;
					height: 25px;
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: center;
				}
				
				.ux-tabbar__name {
					color: black;
					font-size: 12px;
				}
			}

			.ux-tabbar__item__push {
				width: 20%;
				height: 38px;
				border-radius: 54px;
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: flex-start;
				background-color: transparent;
				z-index: 1 !important;
				position: relative;

				.ux-tabbar__icon {
					height: 36px;
					margin-left: 20px;
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: flex-start;
				}

				.ux-tabbar__name {
					margin-left: 10px;
					color: black;
					font-size: 18px;
					opacity: 0;
				}

			}

			.ux-tabbar__item__water {
				flex: 1;
				height: 100%;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				z-index: 1 !important;
				position: relative;

				.ux-tabbar__icon {
					position: absolute;
					top: 30%;
					width: 36px;
					height: 36px;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
				}

				.ux-tabbar__name {
					position: absolute;
					top: 100%;
					color: black;
					font-size: 18px;
				}
			}

			.ux-tabbar__point {
				position: absolute;
				bottom: 5px;
				width: 5px;
				height: 5px;
				border-radius: 5px;
				opacity: 0;
			}
		}
	}

	.ux-tabbar__hover {
		position: absolute;
		width: 50px;
		height: 50px;
		opacity: 0;
		transform: scale(0);
		border-radius: 50px;
		transition-property: transform, opacity;
		transition-duration: 0.1s;
		transition-timing-function: linear;
	}
	
	.ux-tabbar__hover--center {
		opacity: 0.8;
	}

	.transform__none {}

	.transform__scroll {
		transition-property: transform, opacity;
		transition-duration: 0.3s;
		transition-timing-function: ease-in-out;
	}

	.transform__roll {
		transition-property: transform, opacity;
		transition-duration: 0.3s;
		transition-timing-function: ease-in-out;
	}
	
	.transform__push {
		transition-property: transform, opacity, width, background-color;
		transition-duration: 0.2s;
		transition-timing-function: linear;
	}

	.transform__water {
		transition-property: transform, opacity;
		transition-duration: 0.3s;
		transition-timing-function: ease-in-out;
	}
</style>