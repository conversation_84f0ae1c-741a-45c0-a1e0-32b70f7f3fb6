<template>
	<view v-if="show" class="ux-modal" :class="position">
		<view ref="uxModalMarkRef" class="ux-modal__mask" :style="{'background-color': `rgba(0, 0, 0, ${ maskOpacity })`}" @click="onMask()"></view>
		<ux-blur v-if="showBlur" v-show="show" :radius="blurRadius" :color="blurColor" class="ux-modal__blur" :style="{opacity: blurAlpha}" @click="onMask()"></ux-blur>
		
		<ux-popover v-if="mode == 'popover'" ref="uxPopoverRef" :z-index="100004" :show-mask="false" :width="popoverWidth" :targetId="popoverId">
			<view ref="uxModalRef" class="ux-modal__body" :style="style">
				<slot name="title">
					<view v-if="title != ''" class="ux-modal__title">
						<text class="ux-modal__title--text" :style="[_titleStyle, titleStyle]">{{ title }}</text>
					</view>
				</slot>
				<scroll-view class="ux-modal__content" :style="bodyStyle" direction="vertical">
					<view style="flex: 1;"></view>
					<slot name="content">
						<view v-if="editable" class="ux-modal__content--textarea">
							<textarea class="ux-modal__content--input" v-model="inputContent" :auto-focus="autoFocus" :placeholder="placeholder" :placeholder-style="placeholderStyle" @input="input"></textarea>
						</view>
						<text v-else class="ux-modal__content--text" :style="[_contentStyle, contentStyle]">{{ content }}</text>
					</slot>
					<view style="flex: 1;"></view>
				</scroll-view>
				<view v-if="showFooter" class="ux-modal__footer">
					<ux-button v-if="showCancel" :style="['flex: 1;', cancelStyle]" :theme="theme" :plain="true" :text="cancelText" :size="cancelSize" :color="cancelColor" @click="cancel()"></ux-button>
					<view v-if="showCancel" style="width: 15px;"></view>
					<ux-button :style="['flex: 1;', confirmStyle]" :theme="theme" :text="confirmText" :size="confirmSize" :color="confirmColor" @click="confirm()"></ux-button>
				</view>
				<view v-if="showClose" class="ux-modal__close" @click="close()">
					<ux-icon type="closecircle-outline" :color="titleColor" :size="24"></ux-icon>
				</view>
			</view>
		</ux-popover>
		<view v-else ref="uxModalRef" class="ux-modal__body" :class="`body-${position}`" :style="style">
			<slot name="title">
				<view v-if="title != ''" class="ux-modal__title">
					<text class="ux-modal__title--text" :style="[_titleStyle, titleStyle]">{{ title }}</text>
				</view>
			</slot>
			<scroll-view class="ux-modal__content" :style="bodyStyle" direction="vertical">
				<view style="flex: 1;"></view>
				<slot name="content">
					<view v-if="editable" class="ux-modal__content--textarea">
						<textarea class="ux-modal__content--input" v-model="inputContent" :auto-focus="autoFocus" :placeholder="placeholder" :placeholder-style="placeholderStyle" @input="input"></textarea>
					</view>
					<text v-else class="ux-modal__content--text" :style="[_contentStyle, contentStyle]">{{ content }}</text>
				</slot>
				<view style="flex: 1;"></view>
			</scroll-view>
			<view v-if="showFooter" class="ux-modal__footer">
				<ux-button v-if="showCancel" :style="['flex: 1;', cancelStyle]" :theme="theme" :plain="true" :text="cancelText" :size="cancelSize" :color="cancelColor" @click="cancel()"></ux-button>
				<view v-if="showCancel" style="width: 15px;"></view>
				<ux-button :style="['flex: 1;', confirmStyle]" :theme="theme" :text="confirmText" :size="confirmSize" :color="confirmColor" @click="confirm()"></ux-button>
			</view>
			<view v-if="showClose" class="ux-modal__close" @click="close()">
				<ux-icon type="closecircle-outline" :color="titleColor" :size="24"></ux-icon>
			</view>
		</view>
	</view>
</template>

<script setup>
	
	/**
	 * Modal 对话框
	 * @description 支持顶部对话框，底部对话框，内容输入对话框，弹出对话框，对话框嵌套
	 * @demo pages/component/modal.uvue
	 * @tutorial https://www.uxframe.cn/component/modal.html
	 * @property {String}			theme=[text|info|primary|success|warning|error]		String | 主题 (默认 primary)
	 * @value text			文字
	 * @value info   		默认
	 * @value primary   	主要
	 * @value success  		成功
	 * @value warning   	警告
	 * @value error  		错误
	 * @property {String}			mode=[modal|popover]					String | 模式 (默认 modal)
	 * @value modal 对话框
	 * @value popover 弹窗 暂只支持Web
	 * @property {String}			position=[center|top|bottom]			String | 显示位置 (默认 center)
	 * @value center 居中显示
	 * @value top 居上显示
	 * @value bottom 居下显示
	 * @property {String}			popoverId								String | 弹窗目标ID
	 * @property {String}			title									String | 标题
	 * @property {Any}				titleSize								Any | 标题大小 (默认 16)
	 * @property {String}			titleColor								String | 标题颜色 (默认 #000 )
	 * @property {String} 			titleDarkColor=[none|auto|color]		String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Boolean}			titleBold								Boolean | 标题加粗 (默认 true )
	 * @property {String}			titleStyle								String | 标题自定义样式
	 * @property {String}			content									String | 内容
	 * @property {Any}				contentSize								Any | 内容大小 (默认 15)
	 * @property {String}			contentColor							String | 内容颜色 (默认 #333 )
	 * @property {String} 			contentDarkColor=[none|auto|color]		String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {String} 			contentAlign=[left|center|right]		String | 对齐方式 (默认 center)
	 * @value left 左对齐
	 * @value center 居中
	 * @value right 右对齐
	 * @property {String}			bodyStyle								String | 主题内容自定义样式
	 * @property {String}			contentStyle							String | 内容自定义样式
	 * @property {String}			placeholder								String | 占位内容 (默认 请输入... )
	 * @property {Any}				placeholderSize							Any | 占位内容大小 (默认 15)
	 * @property {String}			placeholderColor						String | 占位内容颜色 (默认 #888 )
	 * @property {String}			confirmText								String | 确定按钮文字 (默认 确定 )
	 * @property {String}			confirmColor							String | 确定按钮文字颜色 (默认 $ux.Conf.primaryColor )
	 * @property {Any}				confirmSize								Any | 确定按钮文字大小 (默认 15)
	 * @property {String}			confirmStyle							String | 确定按钮自定义样式
	 * @property {String}			cancelText								String | 取消按钮文字 (默认 取消 )
	 * @property {String}			cancelColor								String | 取消按钮文字颜色 (默认 $ux.Conf.primaryColor )
	 * @property {Any}				cancelSize								Any | 取消按钮文字大小 (默认 15)
	 * @property {String}			cancelStyle								String | 取消按钮自定义样式
	 * @property {String}			background								String | 背景颜色
	 * @property {String} 			backgroundDark=[none|auto|color]		String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Boolean}			showBlur = [true|false]					Boolean | 开启模糊背景 (默认 false )
	 * @property {Number}			blurRadius								Number | 模糊半径 (默认 10 )
	 * @property {String}			blurColor								String | 模糊颜色  (默认 rgba(10, 10, 10, 0.3) )
	 * @property {Boolean}			showClose = [true|false]				Boolean | 显示关闭 (默认 false )
	 * @property {Boolean}			showCancel = [true|false]				Boolean | 点击关闭 (默认 false )
	 * @property {Boolean}			showFooter = [true|false]				Boolean | 显示底部 (默认 true )
	 * @property {Boolean}			editable = [true|false]					Boolean | 输入内容 (默认 false )
	 * @property {Boolean}			autoFocus = [true|false]				Boolean | 自动聚焦 (默认 false )
	 * @property {Any}				width									Any | 宽度 (默认 84%)
	 * @property {Any}				height									Any | 最小高度 (默认 240)
	 * @property {Any}				maxHeight								Any | 最大高度 (默认 500)
	 * @property {Any}				radius									Any | 圆角 (默认 20)
	 * @property {Number}			maskOpacity								Number | 遮罩层透明度 (默认 0.6)
	 * @property {Boolean}			maskClose=[true|false]					Boolean | 遮罩层关闭 (默认 false)
	 * @value true	
	 * @value false	
	 * @event {Function}			confirm									Function | 确定按钮点击时触发
	 * @event {Function}			cancel									Function | 取消按钮点击时触发
	 * @event {Function}			close									Function | 关闭按钮点击时触发发
	 * @event {Function}			input									Function | 输入时触发
	 * <AUTHOR>
	 * @date 2024-06-14 12:36:15
	 */
	
	import { $ux } from '../../index'
	import { useForegroundColor, useFontColor } from '../../libs/use/style.uts'
	
	defineOptions({
		name: 'ux-modal'
	})
	
	const emit = defineEmits(['confirm', 'cancel', 'close', 'input', 'update:modelValue'])
	
	const props = defineProps({
		modelValue: {
			type: Boolean,
			default: false
		},
		theme: {
			type: String,
			default: 'primary'
		},
		mode: {
			type: String,
			default: 'modal'
		},
		position: {
			type: String,
			default: 'center'
		},
		popoverId: {
			type: String,
			default: ''
		},
		title: {
			type: String,
			default: ''
		},
		titleSize: {
			default: 18
		},
		titleColor: {
			type: String,
			default: '#333'
		},
		titleDarkColor: {
			type: String,
			default: 'auto'
		},
		titleBold: {
			type: Boolean,
			default: true
		},
		titleStyle: {
			type: String,
			default: ''
		},
		content: {
			type: String,
			default: ''
		},
		contentSize: {
			default: 16
		},
		contentColor: {
			type: String,
			default: '#333'
		},
		contentDarkColor: {
			type: String,
			default: 'auto'
		},
		contentAlign: {
			type: String,
			default: 'center'
		},
		contentStyle: {
			type: String,
			default: ''
		},
		bodyStyle: {
			type: String,
			default: ''
		},
		placeholder: {
			type: String,
			default: '请输入...'
		},
		placeholderSize: {
			default: 15
		},
		placeholderColor: {
			type: String,
			default: '#888'
		},
		confirmText: {
			type: String,
			default: '确定'
		},
		confirmSize: {
			default: 15
		},
		confirmColor: {
			type: String,
			default: ''
		},
		confirmStyle: {
			type: String,
			default: ''
		},
		cancelText: {
			type: String,
			default: '取消'
		},
		cancelSize: {
			default: 15
		},
		cancelColor: {
			type: String,
			default: ''
		},
		cancelStyle: {
			type: String,
			default: ''
		},
		background: {
			type: String,
			default: ''
		},
		backgroundDark: {
			type: String,
			default: ''
		},
		showBlur: {
			type: Boolean,
			default: false
		},
		blurRadius: {
			type: Number,
			default: 10
		},
		blurColor: {
			type: String,
			default: 'rgba(10, 10, 10, 0.5)'
		},
		showClose: {
			type: Boolean,
			default: false
		},
		showCancel: {
			type: Boolean,
			default: false
		},
		showFooter: {
		    type: Boolean,
		    default: true
		},
		editable: {
			type: Boolean,
			default: false
		},
		autoFocus: {
			type: Boolean,
			default: false
		},
		width: {
			type: String,
			default: '92%'
		},
		height: {
			default: 240
		},
		maxHeight: {
			default: 500
		},
		radius: {
			default: 20
		},
		maskOpacity: {
			type: Number,
			default: 0.6
		},
		maskClose: {
			type: Boolean,
			default: false
		},
	})
	
	const uxModalRef = ref<Element | null>(null)
	const uxModalMarkRef = ref<Element | null>(null)
	const blurAlpha = ref(0)
	const uxPopoverRef = ref<UxPopoverComponentPublicInstance | null>(null)
	const show = ref(false)
	const ok = ref(false)
	
	const inputContent = ref('')
	
	const content = computed((): string => {
		return props.content
	})
	
	const modelValue = computed((): boolean => {
		return props.modelValue
	})
	
	const backgroundColor = computed((): string => {
		return useForegroundColor(props.background, props.backgroundDark)
	})
	
	const titleColor = computed((): string => {
		return useForegroundColor(props.titleColor, props.titleDarkColor)
	})
	
	const contentColor = computed((): string => {
		return useFontColor(props.contentColor, props.contentDarkColor)
	})
	
	const style = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('background-color', props.mode == 'popover' ? 'transparent' : backgroundColor.value)
		css.set('border-radius', $ux.Util.addUnit(props.radius))
		
		// #ifndef APP-HARMONY
		if(!$ux.Conf.darkMode.value) {
			css.set('box-shadow', '5px 10px 20px rgba(0, 0, 0, 0.1)')
		}
		// #endif
		
		css.set('width', `${props.width}`)
		
		if($ux.Util.getPx(props.height) > 0) {
			css.set('min-height', $ux.Util.addUnit(props.height))
		}
		
		if($ux.Util.getPx(props.maxHeight) > 0) {
			css.set('max-height', $ux.Util.addUnit(props.maxHeight))
		}
		
		if(props.contentAlign == 'left') {
			css.set('align-items', 'flex-start')
		} else if(props.contentAlign == 'center') {
			css.set('align-items', 'center')
		} else if(props.contentAlign == 'right') {
			css.set('align-items', 'flex-end')
		}
		
		if(props.position == 'top') {
			// #ifdef MP
			css.set('margin-top', `${uni.getWindowInfo().statusBarHeight + 50}px`)
			// #endif
			// #ifndef MP
			css.set('margin-top', `${uni.getWindowInfo().statusBarHeight + 20}px`)
			// #endif
		} else if(props.position == 'bottom') {
			css.set('margin-bottom', `${uni.getWindowInfo().safeAreaInsets.bottom + 20}px`)
		}
		
		css.set('opacity', ok.value ? 1 : 0)
		
		return css
	})
	
	const _titleStyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('font-size', $ux.Util.addUnit(props.titleSize))
		css.set('color', titleColor.value)
		css.set('font-weight', props.titleBold ? 'bold' : 'normal')
		
		return css
	})
	
	const _contentStyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('font-size', $ux.Util.addUnit(props.contentSize))
		css.set('color', props.mode == 'popover' ? '#fff' : contentColor.value)
		
		return css
	})
	
	const placeholderStyle = computed((): string => {
		return `font-size: ${$ux.Util.getPx(props.placeholderSize)}px;color: ${props.placeholderColor}`
	})
	
	const popoverWidth = computed((): number => {
		return $ux.Util.getPx(props.width)
	})
	
	function input(e: InputEvent) {
		inputContent.value = e.detail.value
		emit('input', e.detail.value)
	}
	
	function _close() {
		emit('update:modelValue', false)
	}
	
	function confirm() {
		_close()
		emit('confirm', inputContent.value)
	}
	
	function cancel() {
		_close()
		emit('cancel')
	}
	
	function close() {
		_close()
		emit('close')
	}
	
	function onMask() {
		if(props.maskClose) {
			close()
		}
	}
	
	watch(content, () => {
		inputContent.value = content.value
	}, {immediate: true})
	
	watch(modelValue, () => {
		if(modelValue.value) {
			show.value = true
			ok.value = false
			setTimeout(function() {
				ok.value = true
			}, 100);
			
			setTimeout(() => {
				blurAlpha.value = 0.7
				uxModalMarkRef.value?.style?.setProperty('opacity', 1)
				
				if(props.position == 'center') {
					uxModalRef.value?.style?.setProperty('transition-duration', '200ms')
					setTimeout(() => {
						uxModalRef.value?.style?.setProperty('transform', 'translateY(0%) scale(1.05)')
						setTimeout(() => {
							uxModalRef.value?.style?.setProperty('transition-duration', '300ms')
							setTimeout(() => {
								uxModalRef.value?.style?.setProperty('transform', 'translateY(0%) scale(1)')
							}, 10);
						}, 200);
					}, 10);
				} else {
					uxModalRef.value?.style?.setProperty('transition-duration', '400ms')
					uxModalRef.value?.style?.setProperty('transform', 'translateY(0%)')
					uxModalRef.value?.style?.setProperty('opacity', 1)
				}
				
				uxPopoverRef.value?.$callMethod('open')
			}, 30);
		} else {
			blurAlpha.value = 0
			uxModalMarkRef.value?.style?.setProperty('opacity', 0)
			uxModalRef.value?.style?.setProperty('opacity', 0)
			
			if(props.position == 'top') {
				uxModalRef.value?.style?.setProperty('transform', 'translateY(-100%)')
			} else if(props.position == 'bottom') {
				uxModalRef.value?.style?.setProperty('transform', 'translateY(100%)')
			}
			
			uxPopoverRef.value?.$callMethod('close')
			
			setTimeout(() => {
				show.value = false
				ok.value = false
			}, 400);
		}
	}, {immediate: true})
	
	onMounted(() => {
		
	})
	
	defineExpose({
		confirm,
		cancel,
		close
	})
</script>

<style lang="scss">

	.ux-modal {
		position: fixed;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		z-index: 100000;
		
		&__blur {
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			opacity: 0;
			z-index: 2;
			transition-duration: 400ms;
			transition-property: opacity;
		}
		
		&__mask {
			position: absolute;
			width: 100%;
			height: 100%;
			z-index:1;
			opacity: 0;
			background-color: rgba(0, 0, 0, 0.6);
			transition-duration: 400ms;
			transition-property: opacity;
		}
		
		&__body {
			z-index: 100003 !important;
			min-height: 240px;
			padding: 15px;
			border-radius: 20px;
			background-color: white;
			transform: scale(1);
			transition-duration: 400ms;
			transition-property: opacity, transform;
		}
		
		&__title {
			width: 100%;
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
			
			&--text {
				font-size: 18px;
			}
		}
		
		&__content {
			flex: 1;
			width: 100%;
			height: 100%;
			margin: 15px 0;
			display: flex;
			flex-direction: column;
			align-items: center;
			
			&--text {
				font-size: 16px;
			}
			
			&--textarea {
				width: 100%;
				height: 100%;
				border-radius: 5px;
				background-color: #f0f0f0;
			}
			
			&--input {
				padding: 15px;
				width: 100%;
				height: 100%;
				font-size: 16px;
				color: black;
			}
		}
		
		&__footer {
			width: 100%;
			height: 50px;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;
		}
		
		&__close {
			/* #ifdef WEB */
			cursor: pointer;
			/* #endif */
			position: absolute;
			top: 0;
			right: 0;
			padding: 10px;
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
		}
	}
	
	.top {
		justify-content: flex-start;
	}
	
	.center {
		justify-content: center;
	}
	
	.bottom {
		justify-content: flex-end;
	}
	
	.body-top {
		margin-top: var(--status-bar-height);
		padding-top: 20px;
		transform: translateY(-100%);
	}
	
	.body-center {
		opacity: 0;
		
		/* #ifndef APP-HARMONY */
		transform: translateY(0%) scale(0.2);
		/* #endif */
		
		/* #ifdef APP-HARMONY */
		transform: translateY(0%);
		/* #endif */
	}
	
	.body-bottom {
		padding-bottom: 20px;
		transform: translateY(100%);
	}
	
</style>