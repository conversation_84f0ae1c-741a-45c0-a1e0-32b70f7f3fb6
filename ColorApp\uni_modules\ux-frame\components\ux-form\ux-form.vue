<template>
	<view class="ux-form">
		<slot></slot>
	</view>
</template>

<script setup lang="ts">
	
	/**
	* Form 表单
	* @description 支持任何组件，只作为数据校验
	* @demo pages/component/form.uvue
	* @tutorial https://www.uxframe.cn/component/form.html
	* @property {String} 			direction=[horizontal|vertical]		String | 布局方向 (默认 horizontal)
	* @value horizontal 水平
	* @value vertical 垂直
	* @property {Any} 				width								Any | 宽度 (默认 自适应)
	* @property {Any} 				size								Any | 字体大小 (默认 $ux.Conf.fontSize)
	* @property {Any} 				errorSize							Any | 错误提示字体大小 (默认 11)
	* @property {String} 			color								String | 字体颜色 (默认 $ux.Conf.fontColor)
	* @property {String} 			darkColor=[none|auto|color]			String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	* @value none 不显示
	* @value auto 自动适配深色模式
	* @value color 其他颜色
	* @event {Function} 			submit 								Function | 提交数据时触发
	* <AUTHOR>
	* @date 2024-12-05 20:01:28
	*/
   
   import { PropType, computed, provide, ref, watch } from "vue"
   import { $ux } from '../../index'
   import { UxFormItem, UxFormResult, UxFormData } from "../../libs/types/types.uts"
   
   type FiledItem = {
		key : string,
		value : any,
   }
   
   defineOptions({
		name: 'ux-form'
   })
   
   const emit = defineEmits(['submit'])
   
   const props = defineProps({
		modelValue: {
			type: Object as PropType<UTSJSONObject>,
			default: () : UTSJSONObject => {
				return {} as UTSJSONObject
			}
		},
		direction: {
			type: String,
			default: 'horizontal'
		},
		width: {
			default: 0
		},
		size: {
			default: 0
		},
		errorSize: {
			default: 11
		},
		color: {
			type: String,
			default: ''
		},
		darkColor: {
			type: String,
			default: ''
		}
	})
	
	provide('direction', props.direction)
	provide('width', $ux.Util.getPx(props.width))
	provide('size',  $ux.Util.getPx(props.size))
	provide('errorSize',  $ux.Util.getPx(props.errorSize))
	provide('color', props.color)
	provide('darkColor', props.darkColor)
	
	const forms = ref<UxFormItem[]>([] as UxFormItem[])
	
	const modelValue = computed((): UTSJSONObject => {
		return props.modelValue
	})
	
	function submit() : UxFormResult {
		let result = {
			field: '',
			valid: true,
			error: '',
			formData: [] as UxFormData[]
		} as UxFormResult
		
		for (let key in modelValue.value) {
			let val = modelValue.value[key] as any
			let index = forms.value.findIndex((el : UxFormItem) : boolean => el.field == key)
			
			if (index != -1) {
				let res = forms.value[index].node._.exposed.verifyData(val)! as UxFormData
				if (!res.valid) {
					if(result.valid) {
						result.valid = false
						result.field = key
						result.error = res.error
					}
				}
				
				result.formData.push(res)
			}
		}
	
		emit('submit', result)
		
		return result
	}
	
	function verify(newVal : UTSJSONObject, oldVal : UTSJSONObject) {
		let fileds = [] as FiledItem[]
		
		for (let key in oldVal) {
			let val = newVal.getAny(key)! as any
			let old = oldVal.getAny(key)! as any
			
			if (val != old) {
				fileds.push({
					key,
					value: val
				} as FiledItem)
			}
		}
		
		forms.value.forEach((e : UxFormItem) => {
			let index = fileds.findIndex((el : FiledItem) : boolean => el.key == e.field)
			if (index != -1) {
				e.node._.exposed.verifyData(fileds[index].value)
			}
		})
	}
	
	watch(modelValue, (newVal: UTSJSONObject, oldVal: UTSJSONObject) => {
		verify(newVal, oldVal)
	})
	
	function register(item : UxFormItem) {
		let index = forms.value.findIndex((el : UxFormItem) : boolean => el.field == item.field)
		if (index != -1) {
			forms.value.splice(index, 1, item)
		} else {
			forms.value.push(item)
		}
	}
	
	function unregister(field : string) {
		if (forms.value.length == 0){
			return
		}
		
		let index = forms.value.findIndex((el : UxFormItem) : boolean => el.field == field)
		if (index != -1) {
			forms.value.splice(index, 1)
		}
	}
	
	defineExpose({
		register,
		unregister,
		submit
	})
</script>

<style lang="scss" scoped>
	/* #ifdef H5 */
	uni-view {
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: stretch;
		align-content: stretch;
		justify-content: flex-start;
		box-sizing: border-box;
		min-height: 0px;
		min-width: 0px;
		overflow: hidden;
	}
	/* #endif */
	
	.ux-form {
		
	}
	
</style>