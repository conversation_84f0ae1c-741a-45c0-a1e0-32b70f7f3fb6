<template>
	<view class="ux-image" :style="[style, xstyle]" @click="click">
		
		<image :id="myId" class="ux-image__img"
			:src="isSvg ? svgPng : src" 
			:mode="mode" 
			:fade-show="fade"
			@error="error"
			@load="load">
		</image>
		
		<view v-if="showLoading && isLoading" class="ux-image__loading">
			<slot name="loading">
				<ux-loading type="icon" :size="iconSize"></ux-loading>
			</slot>
		</view>
		<view v-if="showError && isError" class="ux-image__error">
			<slot name="error">
				<ux-icon :type="errorIcon" :size="iconSize" color="#a0a0a0"></ux-icon>
			</slot>
		</view>
		<view v-if="$slots['default'] != null" class="ux-image__mask" :style="maskStyle">
			<slot></slot>
		</view>
		
		<ux-svg-wv v-if="isSvg && isLoadSvg" class="ux-image__svg" :src="src" @error="svgError" @load="svgLoad"></ux-svg-wv>
		
		<!-- <ux-preview v-if="previewMode == 'hero' && isPreview" ref="uxPreview"></ux-preview> -->
	</view>
</template>

<script setup lang="ts">
	/**
	 * Image 图片
	 * @description 支持SVG格式，支持淡入动画、加载中、加载失败提示、圆角值和形状等功能
	 * @demo pages/component/image.uvue
	 * @tutorial https://www.uxframe.cn/component/image.html
	 * @property {String}			default 						Slot | default Slot
	 * @property {String}			loading 						Slot | loading Slot
	 * @property {String}			error 							Slot | error Slot
	 * @property {String}			src 							String | 图片地址
	 * @property {Array} 			previewList 					Array | 图片预览列表
	 * @property {String}			previewMode=[normal|hero]		String | 预览模式（默认 normal ）
	 * @value normal	正常
	 * @value hero		hero动效(暂不支持)
	 * @property {String}			mode=[scaleToFill|aspectFit|aspectFill|widthFix|heightFix|top|bottom|center|left|right|top left|top right|bottom left|bottom right] 	String | 裁剪模式 （默认 aspectFill）
	 * @value scaleToFill		不保持纵横比缩放图片，使图片的宽高完全拉伸至填满 image 元素
	 * @value aspectFit			保持纵横比缩放图片，使图片的长边能完全显示出来。也就是说，可以完整地将图片显示出来
	 * @value aspectFill		保持纵横比缩放图片，只保证图片的短边能完全显示出来。也就是说，图片通常只在水平或垂直方向是完整的，另一个方向将会发生截取
	 * @value widthFix			宽度不变，高度自动变化，保持原图宽高比不变
	 * @value heightFix			高度不变，宽度自动变化，保持原图宽高比不变
	 * @value top				不缩放图片，只显示图片的顶部区域
	 * @value bottom			不缩放图片，只显示图片的底部区域
	 * @value center			不缩放图片，只显示图片的中间区域
	 * @value left				不缩放图片，只显示图片的左边区域
	 * @value right				不缩放图片，只显示图片的右边区域
	 * @value top left			不缩放图片，只显示图片的左上边区域
	 * @value top right			不缩放图片，只显示图片的右上边区域
	 * @value bottom left		不缩放图片，只显示图片的左下边区域
	 * @value bottom right		不缩放图片，只显示图片的右下边区域
	 * @property {Any}				width 						Any | 宽度（默认 75 ）
	 * @property {Any}				height 						Any | 高度 （默认 75 ）
	 * @property {String}			shape=[circle|square] 		String | 图片形状 （默认 square ）
	 * @value circle	圆形
	 * @value square	方形
	 * @property {Any}				radius		 				Any | 圆角值（默认 0 ）
	 * @property {Boolean}			fade 						Boolean | 是否需要淡入效果 （默认 true ）
	 * @property {Boolean}			showLoading 				Boolean | 是否显示加载中的图标或者自定义的slot （默认 true ）
	 * @property {Boolean}			showError 					Boolean | 是否显示加载错误的图标或者自定义的slot （默认 true ）
	 * @property {String}			errorIcon 					String | 加载失败的图标或者图片 （默认 pic-outline ）
	 * @property {Any} 				iconSize 					Any | 图标大小 （默认 25 ）
	 * @property {Array}			margin						Array | 边距 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}				mt							Any | 距上 单位px
	 * @property {Any}				mr							Any | 距右 单位px
	 * @property {Any}				mb							Any | 距下 单位px
	 * @property {Any}				ml							Any | 距左 单位px
	 * @property {Array}			padding						Array | 填充 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}				pt							Any | 上内边距 单位px
	 * @property {Any}				pr							Any | 右内边距 单位px
	 * @property {Any}				pb							Any | 下内边距 单位px
	 * @property {Any}				pl							Any | 左内边距 单位px
	 * @property {Array}			xstyle						Array<any> | 自定义样式
	 * @event {Function}			error						Function | 图片加载失败时触发
	 * @event {Function} 			load 						Function | 图片加载成功时触发
	 * @event {Function} 			click 						Function | 被点击时触发
	 * <AUTHOR>
	 * @date 2024-11-28 00:39:45
	 */
	
	import { ref, computed, watch, watchEffect, PropType } from 'vue'
	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	
	defineOptions({
		name: 'ux-image',
		mixins: [xstyleMixin]
	})
	
	const emit = defineEmits(['click', 'load', 'error'])
	
	const props = defineProps({
		src: {
		    type: String,
		    default: ''
		},
		previewList: {
		    type: Array as PropType<Array<string>>,
		    default: ():string[] => {
				return [] as string[]
			}
		},
		previewMode: {
			type: String,
			default: 'normal'
		},
		mode: {
		    type: String,
		    default: 'aspectFill'
		},
		width: {
		    default: 75
		},
		height: {
		    default: 75
		},
		shape: {
		    type: String,
		    default: ''
		},
		radius: {
		    default: 0
		},
		showLoading: {
		    type: Boolean,
		    default: true
		},
		showError: {
		    type: Boolean,
		    default: true
		},
		errorIcon: {
		    type: String,
		    default: 'pic-outline'
		},
		iconSize: {
			default: 25
		},
		fade: {
		    type: Boolean,
		    default: true
		},
	})
	
	const myId = `ux-image-${$ux.Random.uuid()}`
	const isLoading = ref(true)
	const isError = ref(true)
	const isPreview = ref(false)
	const svgPng = ref('')
	const isLoadSvg = ref(false)
	
	watchEffect(() => {
		isLoading.value = true
		isError.value = false
	})
	
	const isSvg = computed(():boolean => {
		return props.src.toLocaleLowerCase().indexOf('<svg') != -1
	})
	
	watch(isSvg, () => {
		if(isSvg.value) {
			// #ifndef APP
			svgPng.value = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(props.src)}`
			// #endif
			
			// #ifdef APP
			isLoadSvg.value = true
			// #endif
		}
	}, {immediate: true})
	
	const radius = computed(() => {
		return $ux.Util.getPx(props.radius)
	})
	
	const style = computed(() => {
		let css = {}
		
		if(props.width > 0) {
			css['width'] = $ux.Util.addUnit(props.width)
		}
		
		if(props.height > 0) {
			css['height'] = $ux.Util.addUnit(props.height)
		}
		
		if (props.shape == 'circle') {
			css['border-radius'] = '500px'
		} else if (props.shape == 'square') {
			css['border-radius'] = `${$ux.Util.addUnit(radius.value == 0 ? 5 : radius.value)}`
		} else if (radius.value > 0) {
			css['border-radius'] = `${$ux.Util.addUnit(radius.value)}`
		}
		
		css = $ux.Util.xStyle(css, props.margin, props.mt, props.mr, props.mb, props.ml, props.padding, props.pt, props.pr, props.pb, props.pl)
	
		return css
	})
	
	const maskStyle = computed(() : string => {
		if (props.shape == 'circle') {
			return `border-radius: 500px`
		} else if (props.shape == 'square') {
			return `border-radius: ${$ux.Util.addUnit(radius.value == 0 ? 5 : radius.value)}`
		} else if (radius.value > 0) {
			return `border-radius: ${$ux.Util.addUnit(radius.value)}`
		}
		
		return ''
	})
	
	function click(e : MouseEvent) {
		if (props.previewList.length > 0 && !isError.value) {
			let urls = [] as string[]
			props.previewList.forEach((url : any | null) => {
				urls.push(url! as string)
			})
		
			// TODO 未完成
			if (props.previewMode == 'hero') {
				// isPreview.value = true
		
				// let ref = this.$refs['uxPreview'] as UxPreviewComponentPublicInstance
		
				// ref.preview({
				// 	current: props.src,
				// 	currentId: myId,
				// 	urls: urls,
				// 	close: () => {
				// 		isPreview.value = false
				// 	}
				// } as UxPreview)
			} else {
				uni.previewImage({
					current: props.src,
					urls: urls,
				})
			}
		}
		
		emit('click', e)
	}
	
	function error(e : ImageErrorEvent) {
		isLoading.value = false
		isError.value = true
		isLoadSvg.value = false
	
		emit('error', e)
	}
	
	function load(e : ImageLoadEvent) {
		isLoading.value = false
		isError.value = false
	
		emit('load', e)
	}
	
	function svgError() {
		isLoading.value = false
		isError.value = true
		isLoadSvg.value = false
		
		emit('error')
	}
	
	function svgLoad(e : string) {
		svgPng.value = e
		
		isLoading.value = false
		isError.value = false
		isLoadSvg.value = false
	}
	
</script>

<style lang="scss" scoped>
	.ux-image {
		display: flex;
		justify-content: center;
		align-items: center;
		overflow: hidden;
		position: relative;
		
		&__img {
			width: 100%;
			height: 100%;
			position: relative;
			
			&__fixed {
				width: 100%;
				height: 100%;
				position: absolute;
			}
		}
		
		&__svg {
			position: absolute;
			width: 100%;
			height: 100%;
			opacity: 0;
		}
		
		&__loading {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			display: flex;
			justify-content: center;
			align-items: center;
			overflow: hidden;
			background-color: #f0f0f0;
		}
		
		&__error {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			display: flex;
			justify-content: center;
			align-items: center;
			overflow: hidden;
			background-color: #f0f0f0;
		}
		
		&__mask {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
		}
	}
</style>