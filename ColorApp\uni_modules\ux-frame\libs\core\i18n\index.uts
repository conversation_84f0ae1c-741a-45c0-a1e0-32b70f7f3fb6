// #ifndef UNI-APP-X
import { EffectScope, effectScope } from 'vue';
// #endif
import { createComposer } from './composer'
import { error, warn, getAllKeys } from './util'
import { I18nErrorCodes } from './errors'
import type { <PERSON><PERSON><PERSON><PERSON>ull, NumberOrNull, StringOrNull, Composer } from './types'
type I18nMode = "legacy" | "composition"

// #ifndef APP
type VuePlugin = any
// #endif

var _i18n : UvueI18n | null = null

export class UvueI18n {
	private __global : Composer
	private __scope : EffectScope
	constructor(options : UTSJSONObject = {}, root : Composer | null = null) {
		this.__scope = effectScope()
		this.__global = this.__scope.run(() : Composer => createComposer(options, root))!
	}
	get mode() : I18nMode {
		return "composition"
	}
	get global() : Composer {
		return this.__global
	}
	get availableLocales():string[] {
		return getAllKeys(this.global.messages.value).sort()
	}
	dispose() {
		this.__scope.stop()
	}
	get install() : VuePlugin {
		const _install = (app : VueApp) => {
			app.config.globalProperties.$i18n = _i18n
			app.config.globalProperties.$t = function (key : string, values : AnyOrNull = null, locale : StringOrNull = null) : string {
				const isLocale = typeof values == 'string'
				const _values = isLocale ? null : values
				const _locale = isLocale ? values as string : locale
				return _i18n!.global.t(key, _values, _locale)
			}
			app.config.globalProperties.$tc = function (key : string, choice : NumberOrNull = null, values : AnyOrNull = null, locale : StringOrNull = null) : string {
				const isLocale = typeof values == 'string'
				const _values = isLocale ? null : values
				const _locale = isLocale ? values as string : locale
				return _i18n!.global.tc(key, choice, _values, _locale)
			}
			app.config.globalProperties.$d = function(date: any, key: StringOrNull = null, locale : StringOrNull = null, options: UTSJSONObject | null = null):string {
				return _i18n!.global.d(date, key, locale, options)
			}
			app.config.globalProperties.$n = function(number: number, key: StringOrNull = null, locale : AnyOrNull = null, options: UTSJSONObject | null = null):string {
				const _locale = typeof locale == 'string' ? locale as string : null
				const _options = typeof locale == 'object' && locale != null ? locale as UTSJSONObject : options
				return _i18n!.global.n(number, key, _locale, _options)
			}
			app.config.globalProperties.$locale = _i18n?.global.locale
		}
		
		//  #ifdef APP-ANDROID
		return definePlugin({
			install: _install
		})
		// #endif
		//  #ifndef APP-ANDROID
		return _install
		// #endif
	}
}

export class Locale {
	
	install(options : UTSJSONObject = {}) : UvueI18n {
		let locale = uni.getStorageSync('uVueI18nLocale')
		if(locale != null && locale != '') {
			// #ifdef UNI-APP-X
			options.set('locale', locale)
			// #endif
			// #ifndef UNI-APP-X
			options['locale'] = locale
			// #endif
		}
		
		_i18n = new UvueI18n(options)
		return _i18n!
	}
	
	useI18n(options : UTSJSONObject = {}) : Composer {
		const instance = getCurrentInstance()
		if (instance == null) {
			error(I18nErrorCodes.MUST_BE_CALL_SETUP_TOP)
		}
		return new UvueI18n(options, _i18n!.global).global
	}
	
	i18n() : Composer {
		if(_i18n == null) {
			error(I18nErrorCodes.NOT_INSTALLED)
		}
		return _i18n!.global
	}
}