
import { UxAnimationOptions } from '../../types/anim.uts'

export class Animation {

	/**
	 * 帧动画
	 */
	run(options : UxAnimationOptions) {
		// 速度
		let speed = options.speed ?? 1
		// 当前值
		let curVal = options.startVal
		// 结束值
		let endVal = options.endVal
		// 上次值
		let lastVal = options.lastVal ?? 0
		// 缓动效果
		let isOffset = options._isOffset ?? false
		// 缓动偏移
		let offset = options.offset ?? 0
		// 缓动速度
		let offsetSpeed = options.offsetSpeed ?? 0
		// 总进度
		let total = Math.abs(endVal - options.startVal)
		// 方向
		let isUp = options.startVal < endVal
		
		let anim = () => { }
		anim = () => {
			if (isUp) {
				if (curVal < endVal) {
					// 自增
					curVal += isOffset ? offsetSpeed : this.getSpeed(curVal - lastVal, total, speed)
					if(options.max != null) {
						curVal = Math.round(Math.min(options.max!, curVal))
					}
					
					// 桢动画回调
					options.frame?.(curVal)
					// #ifdef MP-WEIXIN
					setTimeout(function() {
						anim()
					}, 16.67);
					// #endif
					// #ifndef MP-WEIXIN
					requestAnimationFrame(() => {
						anim()
					})
					// #endif
				} else {
					// 动画结束
					curVal = endVal
					lastVal = endVal
					options.end?.(curVal)
					
					// 缓动
					if(offset > 0) {
						let target = endVal + offset
						this.run({
							startVal: endVal,
							endVal: target,
							offsetSpeed: offsetSpeed,
							_isOffset: true,
							frame: (value: number) => {
								options.frame?.(Math.round(value))
							},
							end: (value: number) => {
								this.run({
									startVal: target,
									endVal: endVal,
									offsetSpeed: offsetSpeed,
									_isOffset: true,
									frame: (value: number) => {
										options.frame?.(Math.round(value))
									},
								} as UxAnimationOptions)
							}
						} as UxAnimationOptions)
					}
				}
			} else {
				if (curVal > endVal) {
					// 自减
					curVal -= isOffset ? offsetSpeed : this.getSpeed(lastVal - curVal, total, speed)
					if(options.min != null) {
						curVal = Math.round(Math.max(options.min!, curVal))
					}
					
					// 桢动画回调
					options.frame?.(curVal)
					// #ifdef MP-WEIXIN
					setTimeout(function() {
						anim()
					}, 16.67);
					// #endif
					// #ifndef MP-WEIXIN
					requestAnimationFrame(() => {
						anim()
					})
					// #endif
				} else {
					// 动画结束
					curVal = endVal
					lastVal = endVal
					options.end?.(curVal)
					
					// 缓动
					if(offset > 0) {
						let target = endVal - offset
						this.run({
							startVal: endVal,
							endVal: target,
							offsetSpeed: offsetSpeed,
							_isOffset: true,
							frame: (value: number) => {
								options.frame?.(Math.round(value))
							},
							end: (value: number) => {
								this.run({
									startVal: target,
									endVal: endVal,
									offsetSpeed: offsetSpeed,
									_isOffset: true,
									frame: (value: number) => {
										options.frame?.(Math.round(value))
									},
								} as UxAnimationOptions)
							}
						} as UxAnimationOptions)
					}
				}
			}
		}
		anim()
	}
	
	/**
	 * 动画曲线
	 */
	private getSpeed(val: number, total: number, speed: number) {
		let per = val / total
		if(per < 0.4) {
			return speed * 2
		} else if(per < 0.7) {
			return speed * 1.2
		} else {
			return speed * 0.6
		}
	}
}