<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/row?title=Row"></Mobile>

# Row
> 组件类型：UxRowComponentPublicInstance



## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| flex | Boolean | false | 弹性盒子 |
| gutter | Any | 0 | 栅格间隔 |
| [justify](#justify) | String | left | 水平排列方式 |
| [align](#align) | String | center | 垂直对齐方式 |
| margin | Array |  | 边距[上右下左][上下左右][上下左右] |
| mt | Any |  | 距上单位px |
| mr | Any |  | 距右单位px |
| mb | Any |  | 距下单位px |
| ml | Any |  | 距左单位px |
| padding | Array |  | 填充[上右下左][上下左右][上下左右] |
| pt | Any |  | 上内边距单位px |
| pr | Any |  | 右内边距单位px |
| pb | Any |  | 下内边距单位px |
| pl | Any |  | 左内边距单位px |
| xstyle | Array |  | 自定义样式 |

### [justify](#justify)

| 值   | 说明 |
|:------:|:----:|
| left左对齐
 |  |
| right右对齐
 |  |
| center居中
 |  |
| around间隔相等
 |  |
| between两端对齐
 |  |

### [align](#align)

| 值   | 说明 |
|:------:|:----:|
| top上对齐
 |  |
| center居中
 |  |
| bottom下对齐
 |  |
| stretch填充
 |  |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| click | 被点击时触发 |  |
  
  