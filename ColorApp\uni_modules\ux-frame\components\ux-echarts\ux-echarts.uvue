<template>
	<view class="ux-echarts">
		<web-view class="web-view" :id="myId" 
			:src="src" 
			:progress="false" 
			@message="onMessage" 
			@error="onError"
			@load="onLoad" 
			@loading="onLoading"></web-view>
		<view v-if="showLoading && isLoading" class="ux-echarts__loading">
			<ux-loading>{{ loadingText }}</ux-loading>
		</view>
	</view>
</template>

<script setup>

	/**
	 * ECharts图表 https://echarts.apache.org/zh/index.html
	 * @description 基于ECharts开发的图表组件库
	 * @demo pages/component/echarts.uvue
	 * @tutorial https://www.uxframe.cn/component/echarts.html
	 * @property {UTSJSONObject}	option				UTSJSONObject | 图表配置数据
	 * @property {Boolean}			showLoading			Boolean | 显示加载中 (默认 true)
	 * @property {String}			loadingText			String | 加载中文案 (默认 加载中...)
	 * @event {Function}			message				Function | 回调消息
	 * @event {Function}			loading				Function | 加载中
	 * @event {Function}			load				Function | 加载完成
	 * @event {Function}			error				Function | 加载失败
	 * <AUTHOR>
	 * @date 2023-11-27 16:35:45
	 */
	
	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	
	defineOptions({
		name: 'ux-echarts',
		mixins: [xstyleMixin]
	})
	
	const emit = defineEmits(['message', 'loading', 'load', 'error'])
	
	const props = defineProps({
		option: {
			default: (): UTSJSONObject => {
				return {} as UTSJSONObject
			}
		},
		showLoading: {
			type: Boolean,
			default: true,
		},
		loadingText: {
			type: String,
			default: '加载中...',
		}
	})
	
	const src = '/uni_modules/ux-frame/hybrid/html/echarts.html'
	const myId = `ux-echarts-${$ux.Random.uuid()}`
	const webview = ref<WebviewContext | null>(null)
	const isLoading = ref(false)
	
	const data = computed((): any => {
		return props.option!
	})
	
	function init() {
		// #ifdef APP
		webview.value?.evalJS(`init(${JSON.stringify(data.value)})`)
		// #endif
		// #ifdef WEB
		let iframe = document.getElementById(myId) as HTMLElement;
		iframe.contentWindow['init'](data.value);
		// #endif
	}
	
	function change(data : any) {
		// #ifdef APP
		webview.value?.evalJS(`change(${JSON.stringify(data)})`)
		// #endif
		// #ifdef WEB
		let iframe = document.getElementById(myId) as HTMLElement;
		iframe.contentWindow['change'](data);
		// #endif
	}
	
	function clear() {
		// #ifdef APP
		webview.value?.evalJS(`clear()`)
		// #endif
		// #ifdef WEB
		let iframe = document.getElementById(myId) as HTMLElement;
		iframe.contentWindow['clear']();
		// #endif
	}
	
	function onMessage(e : WebViewMessageEvent) {
		// #ifdef APP
		if(e.detail.data.length > 0) {
			const _data = e.detail.data[0]
			
			if ((_data['error'] ?? '') != '') {
				console.error(`[ux-echarts]加载失败！${_data['error']}`);
			}
			
			emit('message', _data)
		}
		// #endif
	}
	
	function onLoading(e : WebViewLoadingEvent) {
		isLoading.value = true
		emit('loading')
	}
	
	function onLoad(e : WebViewLoadedEvent) {
		isLoading.value = false
		init()
		emit('load')
	}
	
	function onError(e : WebViewErrorEvent) {
		console.error('[ux-echarts]加载失败');
		emit('error')
	}
	
	watch(data, () => {
		clear()
		init()
	})
	
	onMounted(() => {
		// #ifdef APP
		webview.value = uni.createWebviewContext(myId, getCurrentInstance()?.proxy)
		// #endif
		// #ifdef WEB
		window.addEventListener('message', (e) => {
			if (e.data.iframeId == myId && e.data.action == 'onJSBridgeReady') {
				init()
			}
		});
		// #endif
	})
	
	defineExpose({
		change
	})
</script>

<style lang="scss">
	.ux-echarts {
		width: 100%;
		height: 100%;
		position: relative;

		.web-view {
			width: 100%;
			height: 100%;
		}

		&__loading {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
</style>