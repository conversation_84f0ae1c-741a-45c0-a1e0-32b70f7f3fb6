{"id": "ux-frame", "displayName": "【支持原生鸿蒙】UxFrame 低代码高性能UI框架", "version": "3.1.8", "description": "UxFrame UI框架基于`uniappx setup`模式构建，是一个功能强大、性能卓越、美观精致的跨平台解决方案", "keywords": ["uxframe", "鸿蒙", "ui库", "原生ui库", "uniappx"], "engines": {"HBuilderX": "^4.66", "uni-app": "", "uni-app-x": "^4.66"}, "dcloudext": {"type": "component-vue", "sale": {"regular": {"price": "598.00"}, "sourcecode": {"price": "599.00"}}, "contact": {"qq": "3106205417"}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "无"}, "npmurl": "", "darkmode": "√", "i18n": "√", "widescreen": "√"}, "uni_modules": {"encrypt": [], "platforms": {"cloud": {"tcb": "√", "aliyun": "√", "alipay": "√"}, "client": {"uni-app": {"vue": {"vue2": "-", "vue3": "-"}, "web": {"safari": "-", "chrome": "-"}, "app": {"vue": "-", "nvue": "-", "android": "-", "ios": "-", "harmony": "-"}, "mp": {"weixin": "-", "alipay": "-", "toutiao": "-", "baidu": "-", "kuaishou": "-", "jd": "-", "harmony": "-", "qq": "-", "lark": "-"}, "quickapp": {"huawei": "-", "union": "-"}}, "uni-app-x": {"web": {"safari": "√", "chrome": "√"}, "app": {"android": {"extVersion": "", "minVersion": "21"}, "ios": "√", "harmony": "√"}, "mp": {"weixin": "√"}}}}}, "name": "ux-frame", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"shelljs": "^0.8.5"}}