<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/page?title=Page"></Mobile>

# Page
> 组件类型：UxPageComponentPublicInstance



## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| navbar | Boolean | false | 标题栏高度占位 |
| tabbar | Boolean | false | 底部导航栏高度占位 |
| colors | Array |  | 背景色多个渐变 |
| backgroundImage | String |  | 背景图优先级高于背景色 |
| [backgroundClip](#backgroundClip) | String |  | 背景图裁剪 |
| stack | Boolean | false | 层叠 |

### [backgroundClip](#backgroundClip)

| 值   | 说明 |
|:------:|:----:|
| border-box
 |  |
| padding-box
 |  |
| content-box
 |  |

