
import { OpenFileManager, UxOpenFileManagerOptions, UxFile } from '../interface.uts'
import { SearchFiles, UxSearchFilesOptions } from '../interface.uts'
import { StopSearch } from '../interface.uts'

/**
 * 打开文件管理器
 * @param {UxOpenFileManagerOptions} options 参数
 */
export const openFileManager: OpenFileManager = (options: UxOpenFileManagerOptions) => {
	let mimeTypes = options.mimeTypes ?? []
	let multiple = options.multiple ?? false
	
	let callback = (files: UTSJSONObject[]) => {
		let _files: Map<string, any>[] = []
		
		files.forEach(e => {
			_files.push(e.toMap())
		})
		
		options.success?.(_files)
	}
	
	UxFiles.openFileManager(mimeTypes = mimeTypes, multiple = multiple, callback = callback)
}

/**
 * 搜索文件
 * @param {UxSearchFilesOptions} options 参数
 */
export const searchFiles: SearchFiles = (options: UxSearchFilesOptions) => {
	console.log('[ux-files] ios不支持 searchFiles');
}

/**
 * 停止搜索文件
 */
export const stopSearch: StopSearch = () => {
	console.log('[ux-files] ios不支持 stopSearch');
}