<template>
	<view v-if="height > 0" class="ux-placeholder" :style="style">
		<slot></slot>
	</view>
</template>

<script setup>
	/**
	 * 高度占位
	 * @description 支持状态栏、标题栏、底部导航栏、底部安全区域、任意高度
	 * @demo pages/component/placeholder.uvue
	 * @tutorial https://www.uxframe.cn/component/placeholder.html
	 * @property {Any}				height								Any | 占位高度 优先级更高
	 * @property {Boolean}			statusbar = [true|false]			Boolean | 状态栏高度占位 (默认 false)
	 * @property {Boolean}			navbar = [true|false]				Boolean | 标题栏高度占位 (默认 false)
	 * @property {Boolean}			tabbar = [true|false]				Boolean | 底部导航栏高度占位 (默认 false)
	 * @property {Boolean}			safearea = [true|false]				Boolean | 底部安全区高度占位 (默认 false)
	 * @property {String}			background							String | 背景色 (默认 transparent)
	 * @property {String} 			backgroundDark=[none|auto|color]	String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * <AUTHOR>
	 * @date 2023-11-03 18:32:45
	 */
	
	import { $ux } from '../../index'
	import { useBackgroundColor } from '../../libs/use/style.uts'
	
	defineOptions({
		name: 'ux-placeholder'
	})

	const props = defineProps({
		height: {
			default: 0
		},
		statusbar: {
			type: Boolean,
			default: false
		},
		navbar: {
			type: Boolean,
			default: false
		},
		tabbar: {
			type: Boolean,
			default: false
		},
		safearea: {
			type: Boolean,
			default: false
		},
		background: {
			type: String,
			default: 'transparent'
		},
		backgroundDark: {
			type: String,
			default: ''
		}
	})

	const height = computed(() : number => {
		if ($ux.Util.getPx(props.height) > 0) {
			return $ux.Util.getPx(props.height)
		}

		if (props.statusbar) {
			return uni.getWindowInfo().statusBarHeight
		}

		if (props.navbar) {
			return uni.getWindowInfo().statusBarHeight + 44
		}

		if (props.tabbar) {
			return uni.getWindowInfo().safeAreaInsets.bottom + 54
		}

		if (props.safearea) {
			return uni.getWindowInfo().safeAreaInsets.bottom
		}

		return 0
	})
	
	const backgroundColor = computed((): string => {
		return useBackgroundColor(props.background, props.backgroundDark)
	})

	const style = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('height', `${height.value}px`)
		
		if(backgroundColor.value != '')  {
			css.set('background-color', backgroundColor.value)
		}
		
		return css
	})
</script>

<style lang="scss">
	.ux-placeholder {
		width: 100%;
		height: 54px;
	}
</style>