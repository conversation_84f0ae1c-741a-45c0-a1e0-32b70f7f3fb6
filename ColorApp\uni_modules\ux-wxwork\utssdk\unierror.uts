
import { MyErrorCode, UxWxworkFail } from "./interface.uts"

export const UniErrorSubject = 'ux-wxwork';

  /**
   * 错误码及对应的错误信息
   */
export const UniErrors : Map<MyErrorCode, string> = new Map([
	[9010000, 'unregister'],
	[9010001, 'register fail'],
	[9010002, 'share fail'],
	[9010003, 'login fail'],
	[9010004, 'auth denied'],
	[9010005, 'user cancel'],
	[9010006, 'unsupport'],
	[9010007, 'uninstall wxwork'],
	[9010008, 'unknow error'],
]);

export class MyFailImpl extends UniError implements UxWxworkFail {

  constructor(errCode : MyErrorCode) {
    super();
	
    this.errSubject = UniErrorSubject;
    this.errCode = errCode;
    this.errMsg = UniErrors[errCode] ?? '';
  }
}