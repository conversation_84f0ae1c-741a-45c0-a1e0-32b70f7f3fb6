<template>
	<view></view>
</template>

<script lang="uts">
	/**
	* 自定义相机预览拍照组件
	* @description 支持拍照、切换镜头、闪光灯、手电筒功能
	* @demo pages/component/camera-view.uvue
	* @tutorial https://www.uxframe.cn/component/camera-view.html
	* @event {Function} 			mode=[take|watermark] 		Function | 模式，注意：watermark拍照模式性能没有take好
	* @value take 拍照模式
	* @value watermark 水印拍照模式
	* @event {Function} 			take 						Function | 拍照时触发
	* <AUTHOR>
	* @date 2025-04-07 21:02:28
	*/
   
	import FrameLayout from 'android.widget.FrameLayout'
	import { UxCamera } from './camera.uts'
	
	export default {
		name: 'ux-camera-view',
		emits: ['take'],
		props: {
			mode: {
				type: String,
				default: 'take'
			}
		},
		data() {
			return {
				previewer: null as null | FrameLayout,
				camera: null as null | UxCamera
			}
		},
		NVLoad() : FrameLayout {
			this.previewer = new FrameLayout($androidContext!)
			return this.previewer!
		},
		NVLayouted() {
			this.camera = new UxCamera(this.mode == 'watermark', this.previewer!, $androidContext!)
		},
		NVBeforeUnload() {
			
		},
		methods: {
			open() {
				this.camera?.open()
			},
			close() {
				this.camera?.close()
			},
			take(correctOrientation: boolean) {
				this.camera?.take(correctOrientation, (path: string) => {
					this.$emit('take', {
						'path': path
					} as UTSJSONObject)
				})
			},
			switch(isFront : boolean) {
				this.camera?.switch(isFront)
			},
			flash(isFlash : boolean) {
				this.camera?.flash(isFlash)
			},
			torch(isTorch : boolean) {
				this.camera?.torch(isTorch)
			}
		}
	}
</script>