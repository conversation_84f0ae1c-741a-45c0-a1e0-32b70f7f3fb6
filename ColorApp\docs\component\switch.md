<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/switch?title=Switch"></Mobile>

# Switch
> 组件类型：UxSwitchComponentPublicInstance

扩展官方组件

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| name | String |  | 表单的控件名称，作为键值对的一部分与表单(form组件)一同提交 |
| [theme](#theme) | String |  | 主题颜色 |
| checked | Boolean | false | 是否选中 |
| [size](#size) | String | normal | 尺寸 |
| color | String | `$ux.Conf.primaryColor` | 颜色 |
| [darkColor](#darkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| margin | Array |  | 边距[上右下左][上下左右][上下左右] |
| mt | Any |  | 距上单位px |
| mr | Any |  | 距右单位px |
| mb | Any |  | 距下单位px |
| ml | Any |  | 距左单位px |
| padding | Array |  | 填充[上右下左][上下左右][上下左右] |
| pt | Any |  | 上内边距单位px |
| pr | Any |  | 右内边距单位px |
| pb | Any |  | 下内边距单位px |
| pl | Any |  | 左内边距单位px |
| xstyle | Array |  | 自定义样式 |
| disabled | Boolean | false | 是否禁用 |

### [theme](#theme)

| 值   | 说明 |
|:------:|:----:|
| primary | 主色
 |
| warning | 警告
 |
| success | 成功
 |
| error | 错误
 |
| info | 文本
 |

### [size](#size)

| 值   | 说明 |
|:------:|:----:|
| normal正常
 |  |
| small小尺寸
 |  |
| large大尺寸
 |  |

### [darkColor](#darkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| change | 值改变时触发 |  |
  
  