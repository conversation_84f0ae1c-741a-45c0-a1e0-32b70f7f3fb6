<template>
	<scroll-view :id="myId"
		class="ux-scroll"
		:style="[style, xstyle]" 
		:scroll-x="scrollX"
		:scroll-y="scrollY"
		:upper-threshold="upperThreshold"
		:lower-threshold="lowerThreshold"
		:scroll-top="_scrollTop"
		:scroll-left="_scrollLeft"
		:scroll-into-view="scrollIntoView"
		:scroll-with-animation="scrollWithAnimation" 
		:enable-back-to-top="enableBackToTop"
		:show-scrollbar="showScrollbar"
		:refresher-enabled="refresherEnabled"
		:refresher-threshold="refresherThreshold"
		refresher-default-style="none"
		:refresher-background="refresherBackground"
		:refresher-triggered="refresherTriggered"
		@refresherpulling="refresherpulling"
		@refresherrefresh="refresherrefresh" 
		@refresherrestore="refresherrestore" 
		@refresherabort="refresherabort"
		@scrolltoupper="scrolltoupper" 
		@scrolltolower="scrolltolower" 
		@startnestedscroll="startnestedscroll"
		@nestedprescroll="nestedprescroll" 
		@stopnestedscroll="stopnestedscroll" 
		@scroll="scroll"
		@scrollend="scrollend">
		<ux-refresher v-if="refresherEnabled" class="ux-refresher" :style="refresherStyle" :states="refresherStates" :state="refresherState"></ux-refresher>
		
		<slot></slot>
	
		<ux-loadmore v-if="loadmoreEnabled" :states="loadmoreStates" :state="loadmoreState"></ux-loadmore>
	
		<ux-placeholder :tabbar="placeholder"></ux-placeholder>
		
		<view style="position: fixed;">
			<ux-backtop v-if="backtop && $slots['backtop'] == null" ref="uxBacktopRef" :parent="myId" @click="toTop"></ux-backtop>
			<ux-backtop v-if="backtop && $slots['backtop'] != null" ref="uxBacktopRef" :parent="myId" @click="toTop">
				<slot name="backtop"></slot>
			</ux-backtop>
		</view>
	</scroll-view>
</template>

<script setup lang="ts">
	/**
	 * scroll-view 滚动列表
	 * @description 扩展支持自定义下拉刷新、上拉加载自定义样式，可显示返回顶部按钮
	 * @demo pages/component/scroll.uvue
	 * @tutorial https://www.uxframe.cn/component/scroll.html
	 * @property {Slot}			default  								Slot | 默认插槽
	 * @property {Slot}			refresher  								Slot | refresher插槽
	 * @property {Slot}			loadmore  								Slot | loadmore插槽
	 * @property {Slot}			backtop  								Slot | backtop插槽
	 * @property {String}		type = [nested]							String | 渲染模式
	 * @value nested 嵌套模式。用于处理父子 scroll-view 间的嵌套滚动，子节点只能是 nested-scroll-header nested-scroll-body 组件或自定义 refresher
	 * @property {String}		direction = [none|all|horizontal|vertical]  String | 滚动方向，可取值 none、all、horizontal、vertical (默认 vertical)
	 * @value none 无
	 * @value all 全部
	 * @value horizontal 水平
	 * @value vertical 垂直
	 * @property {String}		associativeContainer = [nested-scroll-view]		String | 关联的滚动容器
	 * @value nested-scroll-view 嵌套滚动
	 * @property {Boolean}  	enableBackToTop = [true|false]  		Boolean | iOS点击顶部状态栏滚动条返回顶部，只支持竖向 (默认 false)
	 * @property {Boolean}  	bounces = [true|false]  				Boolean | 控制是否回弹效果 (默认 true)
	 * @property {Number}		upperThreshold  						Number | 距顶部/左边多远时（单位px），触发 scrolltoupper 事件（默认 50 ）
	 * @property {Number} 		lowerThreshold 							Number | 距底部/右边多远时（单位px），触发 scrolltolower 事件 （默认 50 ）
	 * @property {Number} 		scrollTop 								Number | 设置竖向滚动条位置 （默认 0 ）
	 * @property {Number}  		scrollLeft								Number | 设置横向滚动条位置（默认 0）
	 * @property {String}  		scrollIntoView							String | 值应为某子元素id（id不能以数字开头）。设置哪个方向可滚动，则在哪个方向滚动到该元素
	 * @property {Boolean}  	scrollWithAnimation = [true|false]  	Boolean | 是否在设置滚动条位置时使用滚动动画，设置false没有滚动动画 (默认 true)
	 * @property {Boolean}  	refresherEnabled = [true|false]  		Boolean | 开启下拉刷新 (默认 false)
	 * @property {Number}  		refresherThreshold  					Number | 设置下拉刷新阈值（默认 45 ）
	 * @property {Number}		refresherMaxDragDistance				Number | 设置下拉最大拖拽距离（单位px）（默认 80 ）
	 * @property {String}		refresherBackground						String | 设置下拉刷新区域背景颜色 (默认 transparent)
	 * @property {Boolean}		refresherTriggered = [true|false]		Boolean | 设置当前下拉刷新状态，true 表示下拉刷新已经被触发，false 表示下拉刷新未被触发 (默认 false)
	 * @property {Array}		refresherStates							Array | 下拉刷新状态文案
	 * @property {Boolean}  	loadmoreEnabled = [true|false]  		Boolean | 开启上拉加载 (默认 false)
	 * @property {Array}		loadmoreStates							Array | 上拉加载状态文案
	 * @property {Boolean}		showScrollbar = [true|false]			Boolean | 控制是否出现滚动条 (默认 false)
	 * @property {Boolean}		customNestedScroll = [true|false]		Boolean | 子元素是否开启嵌套滚动 将滚动事件与父元素协商处理 (默认 false)
	 * @property {Boolean}		openStartnestedscroll = [true|false]	Boolean | 与子元素开启滚动协商 (默认 false)
	 * @property {String}		nestedScrollChild						String | 嵌套滚动子元素的id属性，不支持ref，scroll-view惯性滚动时会让对应id元素视图进行滚动，子元素滚动时会触发scroll-view的nestedprescroll事件，嵌套子元素需要设置custom-nested-scroll = true 
	 * @property {Boolean}		backtop = [true|false]					Boolean | 显示返回顶部 (默认 false)
	 * @property {Boolean}		placeholder = [true|false]				Boolean | 底部导航栏高度占位 (默认 false)
	 * @property {Boolean}		disabled = [true|false]					Boolean | 禁止滚动 (默认 false)
	 * @property {String}		background								String | 背景颜色 (默认 transparent)
	 * @property {Array}		xstyle									Array<any> | 自定义样式
	 * @event {Function}		refresherpulling						Function | 下拉刷新控件被下拉
	 * @event {Function}		refresherrefresh						Function | 下拉刷新被触发
	 * @event {Function}		refresherrestore						Function | 下拉刷新被复位
	 * @event {Function}		refresherabort							Function | 下拉刷新被中止
	 * @event {Function}		loadmore								Function | 上拉加载触发
	 * @event {Function}		scrolltoupper							Function | 滚动到顶部/左边，会触发 scrolltoupper 事件
	 * @event {Function}		scrolltolower							Function | 滚动到底部/右边，会触发 scrolltolower 事件
	 * @event {Function}		scrollend								Function | 滚动结束时触发，event.detail = {scrollLeft, scrollTop, scrollHeight, scrollWidth, deltaX, deltaY}
	 * @event {Function}		scroll									Function | 滚动时触发，event.detail = {scrollLeft, scrollTop, scrollHeight, scrollWidth, deltaX, deltaY}
	 * @event {Function}		startnestedscroll						Function | 子元素开始滚动时触发, return ture表示与子元素开启滚动协商 默认return false! event = {node}
	 * @event {Function}		nestedprescroll							Function | 子元素滚动时触发，可执行event.consumed(x,y)告知子元素deltaX、deltaY各消耗多少。子元素将执行差值后的deltaX、deltaY滚动距离。不执行consumed(x,y)则表示父元素不消耗deltaX、deltaY。event = {deltaX, deltaY}
	 * @event {Function}		stopnestedscroll						Function | 子元素滚动结束或意外终止时触发
	 * <AUTHOR>
	 * @date 2023-11-02 15:20:35
	 */

	import { ref, computed, watch, PropType, provide, nextTick } from 'vue'
	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'

	defineOptions({
		name: 'ux-scroll',
		mixins: [xstyleMixin]
	})

	const emit = defineEmits([
		'refresherpulling',
		'refresherrefresh',
		'refresherrestore',
		'refresherabort',
		'loadmore',
		'scrolltoupper',
		'scrolltolower',
		'scroll',
		'scrollend',
		'startnestedscroll',
		'nestedprescroll',
		'stopnestedscroll'])
	
	const props = defineProps({
		type: {
			type: String,
			default: ''
		},
		direction: {
			type: String,
			default: 'vertical'
		},
		associativeContainer: {
			type: String,
			default: ''
		},
		enableBackToTop: {
			type: Boolean,
			default: false
		},
		bounces: {
			type: Boolean,
			default: true
		},
		upperThreshold: {
			type: Number,
			default: 50
		},
		lowerThreshold: {
			type: Number,
			default: 50
		},
		scrollTop: {
			type: Number,
			default: 0
		},
		scrollLeft: {
			type: Number,
			default: 0
		},
		scrollIntoView: {
			type: String,
			default: '',
		},
		scrollWithAnimation: {
			type: Boolean,
			default: true,
		},
		refresherEnabled: {
			type: Boolean,
			default: false,
		},
		refresherThreshold: {
			type: Number,
			default: 45,
		},
		refresherMaxDragDistance: {
			type: Number,
			default: 80,
		},
		refresherBackground: {
			type: String,
			default: 'transparent',
		},
		refresherTriggered: {
			type: Boolean,
			default: false,
		},
		refresherStates: {
			type: Array as PropType<Array<string>>,
			default: (): string[] => {
				return ['下拉刷新', '释放刷新', '刷新中...', '刷新成功']
			},
		},
		loadmoreEnabled: {
			type: Boolean,
			default: false,
		},
		loadmoreStates: {
			type: Array as PropType<Array<string>>,
			default: (): string[] => {
				return ['加载中...', '已加载', '-- 我也是有底线的 --']
			},
		},
		showScrollbar: {
			type: Boolean,
			default: false,
		},
		customNestedScroll: {
			type: Boolean,
			default: false,
		},
		nestedScrollChild: {
			type: String,
			default: '',
		},
		openStartnestedscroll: {
			type: Boolean,
			default: false,
		},
		backtop: {
			type: Boolean,
			default: false
		},
		placeholder: {
			type: Boolean,
			default: false
		},
		background: {
			type: String,
			default: 'transparent',
		}
	})

	const myId = `ux-scroll-${$ux.Random.uuid()}`
	const uxBacktopRef = ref(null)
	const y = ref(0)
	const refresherState = ref(0)
	const loadmoreState = ref(0)
	const pullingDis = ref(0)
	const _scrollTop = ref(0)
	const _scrollLeft = ref(0)
	
	provide('scrollY', y)
	
	const direction = computed((): string => {
		return props.disabled ? 'none' : props.direction
	})
	
	const scrollX = computed((): boolean => {
		return direction.value == 'horizontal'
	})
	
	const scrollY = computed((): boolean => {
		return direction.value == 'vertical'
	})
	
	const style = computed(() => {
		let css = {}
		
		if(props.direction == 'horizontal') {
			css['flex-direction'] = 'row'
			css['white-space'] = 'nowrap'
		}
		
		css['background-color'] = props.background
		
		css = $ux.Util.xStyle(css, props.margin, props.mt, props.mr, props.mb, props.ml, props.padding, props.pt, props.pr, props.pb, props.pl)
		
		return css
	})
	
	const refresherStyle = computed(() => {
		let css = {}
		
		css['height'] = `${pullingDis.value}px`
		
		return css
	})
	
	const refresherTriggered = computed((): boolean => {
		return props.refresherTriggered
	})
	
	watch(refresherTriggered, (a: boolean, b: boolean) => {
		if(a == false && b == true) {
			// 刷新完成
			refresherState.value = 3
		}
	})
	
	const __scrollTop = computed(() => {
		return props.scrollTop
	})
	
	watch(__scrollTop, () => {
		_scrollTop.value = __scrollTop.value
	})
	
	const __scrollLeft = computed(() => {
		return props.scrollLeft
	})
	
	watch(__scrollLeft, () => {
		_scrollLeft.value = __scrollLeft.value
	})
	
	function scroll(e: ScrollEvent) {
		
		y.value = e.detail.scrollTop
		
		// 显示置顶不卡顿
		if(props.backtop) {
			uxBacktopRef.value?.setShow(y.value > 300)
		}
		
		emit('scroll', e)
	}
	
	function scrollend(e: ScrollEvent) {
		emit('scrollend', e)
	}
	
	function refresherpulling(e: RefresherEvent) {
		// 释放刷新
		if(refresherState.value == 0 || refresherState.value == 1) {
			if(e.detail.dy > props.refresherThreshold / 2) {
				refresherState.value = 1
			} else {
				refresherState.value = 0
			}
		}
		
		pullingDis.value = e.detail.dy > props.refresherThreshold ? props.refresherThreshold : e.detail.dy
		
		emit('refresherpulling', e)
	}
	
	function refresherrefresh(e: RefresherEvent) {
		// 刷新中
		refresherState.value = 2
		
		emit('refresherrefresh', e)
	}
	
	function refresherrestore(e: RefresherEvent) {
		emit('refresherrestore', e)
		
		// 刷新结束
		setTimeout(() => {
			pullingDis.value = 0
			refresherState.value = 0
		}, 50);
	}
	
	function refresherabort(e: RefresherEvent) {
		emit('refresherabort', e)
		
		// 刷新结束
		setTimeout(() => {
			pullingDis.value = 0
			refresherState.value = 0
		}, 50);
	}
	
	function scrolltoupper(e: ScrollToUpperEvent) {
		emit('scrolltoupper', e)
	}
	
	function scrolltolower(e: ScrollToLowerEvent) {
		// 上拉触底加载
		if(props.loadmoreEnabled) {
			loadmoreState.value = 1
			emit('loadmore', e)
		}
		
		emit('scrolltolower', e)
	}
	
	function loadSuccess(lastPage: boolean) {
		loadmoreState.value = lastPage ? 2 : 0
	}
	
	function startnestedscroll(e: StartNestedScrollEvent):boolean {
		emit('startnestedscroll', e)
		return props.openStartnestedscroll
	}
	
	function nestedprescroll(e: NestedPreScrollEvent) {
		emit('nestedprescroll', e)
	}
	
	function stopnestedscroll(e: Event) {
		emit('stopnestedscroll', e)
	}
	
	function toTop() {
		if(direction.value == 'vertical') {
			_scrollTop.value = -1
			nextTick(() => {
				_scrollTop.value = 0
			})
		} else {
			_scrollLeft.value = -1
			nextTick(() => {
				_scrollLeft.value = 0
			})
		}
	}

	defineExpose({
		loadSuccess
	})
</script>

<style lang="scss" scoped>
	
	.ux-scroll {
		flex: 1;
		overflow: auto;
	}
	
	.ux-refresher {
		overflow: hidden;
		transition-property: height;
		transition-duration: 200ms;
	}
</style>