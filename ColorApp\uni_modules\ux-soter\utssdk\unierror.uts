
import { MyErrorCode, UxSoterFail } from "./interface.uts"

export const UniErrorSubject = 'ux-soter';

  /**
   * 错误码及对应的错误信息
   */
export const UniErrors : Map<MyErrorCode, string> = new Map([
	[0, 'sys error'],
	[1, 'device not support'],
	[2, 'device not enrolled'],
	[3, 'auth fail'],
	[4, 'auth error'],
	[5, 'challenge error'],
	[6, 'user cancel'],
	[7, 'use password'],
	[8, 'unset password'],
]);

export class UxSoterFailImpl extends UniError implements UxSoterFail {

  constructor(errCode : MyErrorCode) {
    super();
	
    this.errSubject = UniErrorSubject;
    this.errCode = errCode;
    this.errMsg = UniErrors[errCode] ?? '';
  }
}