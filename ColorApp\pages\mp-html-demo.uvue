<template>
<view class="page">
	<view class="header">
		<text class="title">mp-html 组件示例</text>
		<text class="subtitle">HTML解析与原生渲染</text>
	</view>

	<view class="demo-section">
		<text class="section-title">基础文本</text>
		<view class="demo-item">
        <text class="demo-title">自闭合标签测试</text>
        <mp-html 
          :content="selfClosingTest"
          :selectable="true"
          @link-tap="onLinkTap"
          @img-tap="onImgTap"
        />
      </view>
      
      <view class="demo-item">
			<mp-html :content="basicText"></mp-html>
		</view>
	</view>

	<view class="demo-section">
		<text class="section-title">标题和段落</text>
		<view class="demo-item">
			<mp-html :content="headingsText" />
		</view>
	</view>

	<view class="demo-section">
		<text class="section-title">文本样式</text>
		<view class="demo-item">
			<mp-html :content="styledText" />
		</view>
	</view>

	<view class="demo-section">
		<text class="section-title">列表</text>
		<view class="demo-item">
			<mp-html :content="listText" />
		</view>
	</view>

	<view class="demo-section">
		<text class="section-title">图片和链接</text>
		<view class="demo-item">
			<mp-html :content="mediaText" @link-tap="onLinkTap" @img-tap="onImgTap" />
		</view>
	</view>

	<view class="demo-section">
		<text class="section-title">复杂内容</text>
		<view class="demo-item">
			<mp-html :content="complexText" />
		</view>
	</view>
</view>
</template>
<script setup lang="uts">
	import { LinkTapEventType, ImgTapEventType } from '../uni_modules/mp-html/libs/types.uts'

	// 基础文本示例
	const basicText = '<p>这是一个简单的段落，包含<strong>粗体</strong>和<em>斜体</em>文本。</p>'

	// 标题示例
	const headingsText = `
  <h1>一级标题</h1>
  <h2>二级标题</h2>
  <h3>三级标题</h3>
  <p>这是一个普通段落，用来展示标题和段落的层次结构。</p>
`

	// 样式文本示例
	const styledText = `
  <p style="color: #007AFF; font-size: 18px;">蓝色大字体文本</p>
  <p style="background-color: #F0F0F0; padding: 10px; border-radius: 5px;">带背景色和内边距的段落</p>
  <p>包含<u>下划线</u>、<del>删除线</del>和<strong style="color: red;">红色粗体</strong>的文本。</p>
`

	// 列表示例
	const listText = `
  <h4>无序列表：</h4>
  <ul>
    <li>第一项</li>
    <li>第二项</li>
    <li>第三项</li>
  </ul>
  
  <h4>有序列表：</h4>
  <ol>
    <li>步骤一</li>
    <li>步骤二</li>
    <li>步骤三</li>
  </ol>
`

	// 媒体内容示例
	const mediaText = `
  <p>这是一个<a href="https://uniapp.dcloud.net.cn/">链接到uni-app官网</a>的示例。</p>
  <img src="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/uni-app.png" alt="uni-app logo" style="width: 200px; height: 200px;" />
  <p>点击图片或链接会触发相应的事件。</p>
`

	// 自闭合标签测试
	const selfClosingTest = `
      <div>
        <p>自闭合标签测试：</p>
        <img src="https://via.placeholder.com/150" alt="测试图片" />
        <br/>
        <hr/>
        <input type="text" placeholder="输入框" />
        <meta name="description" content="测试" />
        <link rel="stylesheet" href="test.css" />
      </div>
    `

	// 复杂内容示例
	const complexText = `
      <div style="padding: 10px; border: 1px solid #ddd;">
        <h2 style="color: #333;">复杂内容示例</h2>
        <p>这是一个包含<strong>多种元素</strong>的复杂示例。</p>
        <ul>
          <li>列表项 1</li>
          <li>列表项 2 <em>斜体文本</em></li>
          <li>列表项 3 <a href="https://example.com">链接</a></li>
        </ul>
        <img src="https://via.placeholder.com/200x100" alt="示例图片" style="max-width: 100%;" />
        <p style="margin-top: 10px;">
          <a href="https://github.com">GitHub</a> | 
          <a href="https://stackoverflow.com">Stack Overflow</a>
        </p>
      </div>
    `

	// 处理链接点击
	const onLinkTap = (event : LinkTapEventType) => {
		console.log('链接点击:', event.href)
		uni.showToast({
			title: `点击了链接: ${event.href}`,
			icon: 'none',
			duration: 2000
		})
	}

	// 处理图片点击
	const onImgTap = (event : ImgTapEventType) => {
		console.log('图片点击:', event.src)
		uni.showToast({
			title: '点击了图片',
			icon: 'none',
			duration: 2000
		})
	}
</script>

<style scoped>
	.container {
		background-color: #F8F9FA;
	}

	.page {
		flex: 1;
		flex-direction: column;
		padding: 20px;
		overflow: auto;
	}

	.header {
		flex-direction: column;
		align-items: center;
		margin-bottom: 30px;
		padding: 20px;
		background-color: #FFFFFF;
		border-radius: 10px;
	}

	.title {
		font-size: 24px;
		font-weight: bold;
		color: #333333;
		margin-bottom: 8px;
	}

	.subtitle {
		font-size: 16px;
		color: #666666;
	}

	.demo-section {
		flex-direction: column;
		margin-bottom: 25px;
	}

	.section-title {
		font-size: 18px;
		font-weight: bold;
		color: #333333;
		margin-bottom: 10px;
		padding-left: 5px;
	}

	.demo-item {
		background-color: #FFFFFF;
		padding: 15px;
		border-radius: 8px;
		border: 1px solid #E5E5E5;
	}
</style>