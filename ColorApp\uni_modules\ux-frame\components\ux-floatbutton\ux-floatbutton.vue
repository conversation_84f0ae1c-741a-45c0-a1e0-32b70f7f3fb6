<template>
	<view>
		<view :id="btnId" ref="uxFloatbuttonRef" class="transform" :class="`ux-floatbutton-${pos}`" :style="style"
				@touchstart="touchstart"
				@touchmove="touchmove" 
				@touchend="touchend"
				@mousedown="touchstartPc"
				@click="click">
			<slot></slot>
		</view>
		
		<view :id="myId" class="ux-floatbutton-bg"></view>
	</view>
</template>

<script setup lang="ts">
	
	/**
	 * FloatButton 悬浮按钮
	 * @description 支持四角定位，自动靠边吸咐，支持阻尼效果，支持按钮展开
	 * @demo pages/component/floatbutton.uvue
	 * @tutorial https://www.uxframe.cn/component/floatbutton.html
	 * @property {Array}			value								number[] | 位置值 (默认 右下角)
	 * @property {String}			pos=[rt|rc|rb|lt|lc|lb|ct|c|cb]		String | 默认位置 (默认 rb)
	 * @value rt 右上
	 * @value rc 右中
	 * @value rb 右下
	 * @value lt 左上
	 * @value lc 左中
	 * @value lb 左下
	 * @value ct 中上
	 * @value c 中心
	 * @value cb 中下
	 * @property {Boolean}			adsorb = [true|false]				Boolean | 自动吸附 (默认 true )
	 * @property {Any}				offset								Any | 距边偏移 (默认 15)
	 * @property {Any}				offsetX								Any | X轴偏移 优先级高于 offset (默认 0)
	 * @property {Any}				offsetY								Any | Y轴偏移 优先级高于 offset (默认 0)
	 * @property {Number}			duration							Number | 过渡时间 (默认 600)
	 * @property {Any}				width								Any | 宽度 (默认 50)
	 * @property {Any}				height								Any | 高度 (默认 50)
	 * @property {Any}				radius								Any | 圆角 (默认 25)
	 * @property {String}			background							String | 背景色 (默认 #fff)
	 * @property {String} 			backgroundDark=[none|auto|color]	String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色 (默认 #333)
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Boolean}			shadow = [true|false]				Boolean | 显示阴影 (默认 true )
	 * @property {Boolean}			disabled = [true|false]				Boolean | 禁止滑动 (默认 false)
	 * <AUTHOR>
	 * @date 2024-12-05 01:02:15
	 */
	
	import { PropType, computed, getCurrentInstance, onBeforeUnmount, onMounted, ref, watch } from 'vue'
	import { $ux } from '../../index'
	import { useResize } from '../../libs/use/resize'
	import { useForegroundColor } from '../../libs/use/style.uts'
	
	type Position = {
		x: number,
		y: number
	}
	
	defineOptions({
		name: 'ux-floatbutton'
	})
	
	const emit = defineEmits(['update:value', 'click'])
	
	const props = defineProps({
		value: {
			type: Array as PropType<number[]>,
			default: () : number[] => [-100, -100] as number[]
		},
		pos: {
			type: String,
			default: 'rb'
		},
		adsorb: {
			type: Boolean,
			default: true
		},
		offset: {
			default: 15
		},
		offsetX: {
			default: 0
		},
		offsetY: {
			default: 0
		},
		duration: {
			type: Number,
			default: 600
		},
		width: {
			default: 45
		},
		height: {
			default: 45
		},
		radius: {
			default: 25
		},
		background: {
			type: String,
			default: ''
		},
		backgroundDark: {
			type: String,
			default: ''
		},
		shadow: {
			type: Boolean,
			default: true
		},
		disabled: {
			type: Boolean,
			default: false
		}
	})
	
	let instance = getCurrentInstance()?.proxy
	
	const uxFloatbuttonRef = ref<Element | null>(null)
	const myId = `ux-floatbutton-${$ux.Random.uuid()}`
	const btnId = `ux-floatbutton-btn-${$ux.Random.uuid()}`
	const winHeight = ref(0)
	const winWidth = ref(0)
	const nowPos = ref<Position>({x: 0, y: 0} as Position)
	const curPos = ref<Position>({x: 0, y: 0} as Position)
	const startPos = ref<Position>({x: 0, y: 0} as Position)
	const time = ref(0)
	const isMoving = ref(false)
	
	const value = computed((): number[] => {
		return props.value
	})
	
	const pos = computed((): string => {
		return props.pos
	})
	
	const offsetX = computed((): number => {
		return $ux.Util.getPx(props.offsetX) == 0 ? $ux.Util.getPx(props.offset) : $ux.Util.getPx(props.offsetX)
	})
	
	const offsetY = computed((): number => {
		return $ux.Util.getPx(props.offsetY) == 0 ? $ux.Util.getPx(props.offset) : $ux.Util.getPx(props.offsetY)
	})
	
	const width = computed(() => {
		return $ux.Util.getPx(props.width)
	})
	
	const height = computed(() => {
		return $ux.Util.getPx(props.height)
	})
	
	const backgroundColor = computed((): string => {
		return useForegroundColor(props.background, props.backgroundDark)
	})
	
	const style = computed(() => {
		let css = {}
		
		css['opacity'] = 1
		css['width'] = `${width.value}px`
		css['height'] = `${height.value}px`
		css['border-radius'] = `${props.radius}px`
		css['background-color'] = backgroundColor.value
		
		if(props.shadow) {
			css['box-shadow'] = '0 0 8px 4px rgba(0, 0, 0, 0.05)'
		}
		
		css['transition-duration'] = `${ isMoving.value ? 0 : props.duration}ms`
		
		return css
	})
	
	function updatePos(x : number, y : number) {
		
		x = Math.max(offsetX.value, Math.min(winWidth.value - width.value - offsetX.value, x))
		y = Math.max(offsetY.value, Math.min(winHeight.value - height.value - offsetY.value, y))
	
		nowPos.value = {x: x, y: y} as Position
	
		uni.getElementById(btnId)?.style?.setProperty('left', `${x}px`)
		uni.getElementById(btnId)?.style?.setProperty('top', `${y}px`)
		
		emit('update:value', [x, y])
	}
	
	function _touchstart(x: number, y: number) {
		isMoving.value = true
		time.value = new Date().getTime()
		
		let left = uni.getElementById(btnId)?.style?.getPropertyValue('left')! as string
		let top = uni.getElementById(btnId)?.style?.getPropertyValue('top')! as string
		
		startPos.value = {x: x - parseInt(left), y: y - parseInt(top)} as Position
		curPos.value = {x: x, y: y} as Position
	}
	
	function _touchmove(x: number, y: number) {
		
		let _x = x - startPos.value.x
		let _y = y - startPos.value.y
		
		let disX = winWidth.value - width.value
		let disY = winHeight.value - height.value
	
		_x = Math.max(Math.min(disX, _x), 0)
		_y = Math.max(Math.min(disY, _y), 0)
		
		nowPos.value = {x: _x, y: _y} as Position
		
		uni.getElementById(btnId)?.style?.setProperty('left', `${_x}px`)
		uni.getElementById(btnId)?.style?.setProperty('top', `${_y}px`)
		
		emit('update:value', [_x, _y])
	}
	
	function _touchend(x: number, y: number) {
		isMoving.value = false
		
		let _x = x - startPos.value.x
		let _y = y - startPos.value.y
		
		let disX = winWidth.value - width.value
		let disY = winHeight.value - height.value
		
		_x = Math.max(Math.min(disX, _x), 0)
		_y = Math.max(Math.min(disY, _y), 0)
	
		if (props.adsorb) {
			_y = Math.max(Math.min(disY - offsetY.value, _y), offsetY.value)
			
			if (_x >= disX / 2) {
				_x = disX - offsetX.value
			} else {
				_x = offsetX.value
			}
			
			nowPos.value = {x: _x, y: _y} as Position
			
			setTimeout(() => {
				updatePos(_x, _y)
			}, 20);
		}
	}
	
	function touchstart(e : TouchEvent) {
		if (props.disabled) {
			return
		}
		
		_touchstart(e.changedTouches[0].clientX, e.changedTouches[0].clientY)
	}
	
	function touchmove(e : TouchEvent) {
		e.preventDefault()
		e.stopPropagation()
		
		if (props.disabled) {
			return
		}
		
		_touchmove(e.changedTouches[0].clientX, e.changedTouches[0].clientY)
	}
	
	function touchend(e : TouchEvent) {
		if (props.disabled) {
			return
		}
	
		_touchend(e.changedTouches[0].clientX, e.changedTouches[0].clientY)
	}
	
	function touchstartPc(e : MouseEvent) {
		if (props.disabled) {
			return
		}
		
		_touchstart(e.clientX, e.clientY)
	}
	
	function touchmovePc(e : MouseEvent) {
		e.preventDefault();
		e.stopPropagation();
		
		if (props.disabled || !isMoving.value){
			return
		}
		
		_touchmove(e.clientX, e.clientY)
	
	}
	
	function touchendPc(e : MouseEvent) {
		if (props.disabled || !isMoving.value){
			return
		}
		
		_touchend(e.clientX, e.clientY)
	}
	
	function click() {
		if (new Date().getTime() - time.value > 200){
			return
		}
		
		emit('click')
	}
	
	function defPos(): number[] {
		if(value.value.length == 2) {
			let x = value.value[0]
			let y = value.value[1]
			if(x == -100 || y == -100) {
				if(pos.value == 'rt') {
					x = winWidth.value - width.value - offsetX.value
					y = offsetY.value
				} else if(pos.value == 'rc') {
					x = winWidth.value - width.value - offsetX.value
					y = (winHeight.value - height.value) / 2
				} else if(pos.value == 'rb') {
					x = winWidth.value - width.value - offsetX.value
					y = winHeight.value - height.value - offsetY.value
				} else if(pos.value == 'lt') {
					x = offsetX.value
					y = offsetY.value
				} else if(pos.value == 'lc') {
					x = offsetX.value
					y = (winHeight.value - height.value) / 2
				} else if(pos.value == 'lb') {
					x = offsetX.value
					y = winHeight.value - height.value - offsetY.value
				} else if(pos.value == 'ct') {
					x = (winWidth.value - width.value) / 2
					y = offsetY.value
				} else if(pos.value == 'c') {
					x = (winWidth.value - width.value) / 2
					y = (winHeight.value - height.value) / 2
				} else if(pos.value == 'cb') {
					x = (winWidth.value - width.value) / 2
					y = winHeight.value - height.value - offsetY.value
				}
				
				return [x, y] as number[]
			}
		}
		
		return value.value
	}
	
	function init() {
		winWidth.value = uni.getWindowInfo().windowWidth
		winHeight.value = uni.getWindowInfo().windowHeight
		
		uni.createSelectorQuery()
			.in(instance)
			.select(`#${myId}`)
			.boundingClientRect()
			.exec((rect) => {
				let node = rect[0] as NodeInfo
				
				winWidth.value = node.width ?? 0
				winHeight.value = node.height ?? 0
				
				let _pos = defPos()
				
				updatePos(_pos[0], _pos[1])
			})
	}
	
	watch(value, () => {
		if (value.value.length == 2) {
			if(nowPos.value.x != value.value[0] && nowPos.value.y != value.value[1]) {
				updatePos(value.value[0], value.value[1])
			}
		}
	})
	
	watch(pos, () => {
		init()
	})
	
	onMounted(() => {
		// #ifdef WEB
		window.addEventListener('mouseup', touchendPc);
		window.addEventListener('mousemove', touchmovePc);
		// #endif
		
		setTimeout(function() {
			init()
		}, 200);
		
		useResize(uni.getElementById(btnId), () => {
			init()
		})
	})
	
	onBeforeUnmount(() => {
		// #ifdef WEB
		window.removeEventListener('mouseup', touchendPc);
		window.removeEventListener('mousemove', touchmovePc);
		// #endif
	})
	
</script>

<style lang="scss" scoped>
	.ux-floatbutton {
		z-index: 1000000;
		position: fixed;
		/* #ifdef WEB */
		cursor: grab;
		/* #endif */
		opacity: 0;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.transform {
		transition-duration: 0ms;
		transition-property: left, right, top, bottom, opacity;
		transition-timing-function: cubic-bezier(0, 0.55, 0.45, 1);
	}
	
	.ux-floatbutton-rt {
		@extend .ux-floatbutton;
		top: -100px;
		right: -100px;
	}
	
	.ux-floatbutton-rc {
		@extend .ux-floatbutton;
		top: 50%;
		left: 110%;
	}
	
	.ux-floatbutton-rb {
		@extend .ux-floatbutton;
		bottom: -100px;
		left: 110%;
	}
	
	.ux-floatbutton-lt {
		@extend .ux-floatbutton;
		top: -100px;
		left: -100px;
	}
	
	.ux-floatbutton-lc {
		@extend .ux-floatbutton;
		top: 50%;
		left: -100px;
	}
	
	.ux-floatbutton-lb {
		@extend .ux-floatbutton;
		bottom: -100px;
		left: -100px;
	}
	
	.ux-floatbutton-ct {
		@extend .ux-floatbutton;
		top: -100px;
		left: 50%;
	}
	
	.ux-floatbutton-c {
		@extend .ux-floatbutton;
		top: 50%;
		left: 50%;
	}
	
	.ux-floatbutton-cb {
		@extend .ux-floatbutton;
		top: 110%;
		left: 50%;
	}

	/* #ifdef WEB */
	.ux-floatbutton:active {
		cursor: grabbing;
	}
	/* #endif */
	
	.ux-floatbutton-bg {
		position: fixed;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		z-index: 1;
		opacity: 0;
		pointer-events: none;
		transform: translate(-100%, -100%);
		background-color: transparent;
	}
	
</style>