import { StartAuth, StartAuthOptions } from '../interface.uts'
import { Cancel } from '../interface.uts'
import { IsSupport } from '../interface.uts'
import { IsEnrolled } from '../interface.uts'
import { IsFingerprint } from '../interface.uts'
import { IsPIN } from '../interface.uts'
import { UxSoterFailImpl } from '../unierror.uts'

/**
 * 开始 SOTER 生物认证
 * @param options StartAuthOptions
 */
export const startAuth : StartAuth = (options : StartAuthOptions) => {
	if (!isSupport()) {
		options.fail?.(new UxSoterFailImpl(1))
		return
	}

	if (!isEnrolled()) {
		options.fail?.(new UxSoterFailImpl(2))
		return
	}
	
	const callback = (code: Int, msg: string): void => {
		if(code == 0) {
			options.success?.({
				authMode: 'faceid',
			});
		} else {
			options.fail?.(new UxSoterFailImpl(code as number))
		}
		
		options.complete?.()
	}
	
	let title = options.title ?? ''
	let content = options.content ?? ''
	let challenge = options.challenge ?? ''
	UxSoter.startAuth(title = title, content = content, challenge = challenge, callback = callback)
}

/**
 * 取消验证
 */
export const cancel : Cancel = () => {
	return UxSoter.cancel()
}

/**
 * 是否支持生物认证
 */
export const isSupport : IsSupport = () : boolean => {
	return UxSoter.isSupport()
}

/**
 * 设备是否录入如指纹等生物信息
 */
export const isEnrolled : IsEnrolled = () : boolean => {
	return UxSoter.isEnrolled()
}

/**
 * 设备是否录入指纹
 */
export const isFingerprint : IsFingerprint = () : boolean => {
	return false
}

/**
 * 设备是否设置登录密码
 */
export const isPIN : IsPIN = () : boolean => {
	return UxSoter.isKeyguardSecure()
}
