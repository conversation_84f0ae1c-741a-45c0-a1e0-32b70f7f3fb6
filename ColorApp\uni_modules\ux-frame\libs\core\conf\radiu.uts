// #ifndef UNI-APP-X
import { ref } from 'vue'
// #endif

export class RadiusBase {
	
	/**
	 * 圆角
	 */
	normal = ref(0)
	
	/**
	 * 圆角较小值
	 */
	small = ref(0)
	
	/**
	 * 圆角较大值
	 */
	large = ref(0)
	
	constructor(radius: number) {
		this.normal.value = radius
		this.small.value = radius - 2
		this.large.value = radius + 2
	}

	/**
	 * 重置圆角
	 */
	reset(radius: number) {
		this.normal.value = radius
		this.small.value = radius - 2
		this.large.value = radius + 2
	}
}
