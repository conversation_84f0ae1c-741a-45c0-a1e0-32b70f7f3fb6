import uts.sdk.modules.uxSoter.*
import io.dcloud.uts.UTSAndroid
import io.dcloud.uts.console
import android.content.Intent
import android.content.Context
import android.os.Bundle
import android.os.Build
import android.provider.Settings
import android.content.pm.PackageManager
import androidx.appcompat.app.AppCompatActivity
import androidx.biometric.BiometricManager
import androidx.biometric.BiometricManager.Authenticators
import android.hardware.fingerprint.FingerprintManager
import androidx.annotation.RequiresApi
import android.app.KeyguardManager
import android.os.CancellationSignal
import androidx.biometric.BiometricPrompt
import androidx.fragment.app.FragmentActivity
import androidx.core.content.ContextCompat
import java.util.concurrent.Executor
import android.security.keystore.KeyGenParameterSpec
import android.security.keystore.KeyProperties
import java.security.KeyStore
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey
import javax.crypto.spec.IvParameterSpec

object UxSoter {
	
	private var biometricPrompt: BiometricPrompt? = null
	
	fun isSupport(): Boolean {
		
		val biometricManager = BiometricManager.from(UTSAndroid.getAppContext()!!)
		val result = biometricManager.canAuthenticate(BiometricManager.Authenticators.BIOMETRIC_STRONG)
		
		var support = false
		when (result) {
		    BiometricManager.BIOMETRIC_SUCCESS -> {
		        // 支持生物认证且已录入信息
				support = true
		    }
		    BiometricManager.BIOMETRIC_ERROR_NO_HARDWARE -> {
		        // 无生物识别硬件
		    }
		    BiometricManager.BIOMETRIC_ERROR_HW_UNAVAILABLE -> {
		        // 硬件暂时不可用
		    }
		    BiometricManager.BIOMETRIC_ERROR_NONE_ENROLLED -> {
		        // 未录入生物信息，提示用户前往设置
		        support = true
		    }
		    else -> {
		        // 其他错误情况
		    }
		}
		
		return support
	}
	
	fun isEnrolled(): Boolean {
		
		val biometricManager = BiometricManager.from(UTSAndroid.getAppContext()!!)
		
		val allowedAuthenticators = Authenticators.BIOMETRIC_WEAK or // 包含低强度生物认证（如部分人脸）
		                Authenticators.BIOMETRIC_STRONG or // 高强度生物认证
		                Authenticators.DEVICE_CREDENTIAL // 设备密码/PIN
						
		val result = biometricManager.canAuthenticate(allowedAuthenticators)
		
		var enrolled = false
		when (result) {
		    BiometricManager.BIOMETRIC_SUCCESS -> {
		        // 支持生物认证且已录入信息
		        enrolled = true
		    }
		    BiometricManager.BIOMETRIC_ERROR_NONE_ENROLLED -> {
		        // 支持生物认证但未录入信息，跳转到设置页
		    }
		    BiometricManager.BIOMETRIC_ERROR_NO_HARDWARE -> {
		        // 设备无生物识别硬件
		    }
		    BiometricManager.BIOMETRIC_ERROR_HW_UNAVAILABLE -> {
		        // 生物识别硬件不可用
		    }
		    else -> {
		        // 未知错误
		    }
		}
		
		return enrolled
	}
	
	private fun hasFingerprintHardware(): Boolean {
	   return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
	       val fingerprintManager = UTSAndroid.getAppContext()!!.getSystemService(Context.FINGERPRINT_SERVICE) as FingerprintManager?
	       fingerprintManager?.isHardwareDetected ?: false
	   } else {
	       UTSAndroid.getAppContext()!!.packageManager.hasSystemFeature(PackageManager.FEATURE_FINGERPRINT)
	   }
	}
		
	fun isFingerprint(): Boolean {
		val hasFingerprintHardware = hasFingerprintHardware()
		if (!hasFingerprintHardware) {
		    return false
		}
		
		val biometricManager = BiometricManager.from(UTSAndroid.getAppContext()!!)
		val result = biometricManager.canAuthenticate(Authenticators.BIOMETRIC_WEAK or Authenticators.BIOMETRIC_STRONG)
		
		var enrolled = false
		when (result) {
		    BiometricManager.BIOMETRIC_SUCCESS -> {
		        enrolled = true
		    }
		    BiometricManager.BIOMETRIC_ERROR_NONE_ENROLLED -> {
		        // 未录入指纹
		    }
		    else -> {
		        // 其他错误
		    }
		}
		
		return enrolled
	}
	
	fun isPIN(): Boolean {
		var context = UTSAndroid.getAppContext()!!
		val keyguardManager = context.getSystemService(Context.KEYGUARD_SERVICE) as KeyguardManager
		return keyguardManager.isKeyguardSecure
	}
	
	fun startAuth(options : StartAuthOptions) {
		if (!isSupport()) {
			options.fail?.invoke(UxSoterFailImpl(1))
			return
		}
		
		if (!isEnrolled()) {
			options.fail?.invoke(UxSoterFailImpl(2))
			return
		}
		
		if (!isPIN()) {
		    options.fail?.invoke(UxSoterFailImpl(8))
		    return
		}
		
		if(options.mode == "PIN") {
			startPIN(options)
		} else {
			startFingerprint(options)
		}
	}
	
	fun startPIN(options : StartAuthOptions) {
		var context = UTSAndroid.getAppContext()!!
		
		val activity = context as? FragmentActivity ?: run {
		    options.fail?.invoke(UxSoterFailImpl(4))
		    return
		}
		
		val executor = ContextCompat.getMainExecutor(context)
		biometricPrompt = BiometricPrompt(activity, executor, object : BiometricPrompt.AuthenticationCallback() {
		    override fun onAuthenticationError(errorCode: Int, errString: CharSequence) {
		        options.fail?.invoke(UxSoterFailImpl(4))
		    }
		    override fun onAuthenticationSucceeded(result: BiometricPrompt.AuthenticationResult) {
		        options.success?.invoke(UxStartAuthSuccess(
		            authMode = "PIN"
		        ))
		    }
		    override fun onAuthenticationFailed() {
		        options.fail?.invoke(UxSoterFailImpl(3))
		    }
		})
		
		val promptInfo = BiometricPrompt.PromptInfo.Builder()
		    .setTitle(options.title ?: "验证设备密码")
		    .setSubtitle(options.content ?: "请输入您的PIN码、图案或密码")
		    .setAllowedAuthenticators(Authenticators.DEVICE_CREDENTIAL)
		    .build()
		biometricPrompt?.authenticate(promptInfo)
	}
	
	fun startFingerprint(options : StartAuthOptions) {
		var context = UTSAndroid.getAppContext()!!
		val activity = UTSAndroid.getUniActivity()!! as FragmentActivity

		val executor = ContextCompat.getMainExecutor(context)
		biometricPrompt = BiometricPrompt(activity, executor, object : BiometricPrompt.AuthenticationCallback() {
			override fun onAuthenticationSucceeded(result: BiometricPrompt.AuthenticationResult) {
				// 挑战因子
				if ((options.challenge ?: "") != "") {
					var cipher = result.getCryptoObject()?.getCipher();
					var usedIv = cipher?.getIV()
					
					if (!usedIv.contentEquals(options.challenge!!.toByteArray())) {
						options.fail?.invoke(UxSoterFailImpl(5))
						return
					}
				}
				
			    options.success?.invoke(UxStartAuthSuccess(
			        authMode = "Fingerprint"
			    ))
			}
		    override fun onAuthenticationError(errorCode: Int, errString: CharSequence) {
		       if (errorCode != BiometricPrompt.ERROR_CANCELED) {
					options.fail?.invoke(UxSoterFailImpl(4))
		       }
		    }
		    override fun onAuthenticationFailed() {
		        options.fail?.invoke(UxSoterFailImpl(3))
		    }
		})
		
		val promptInfo = BiometricPrompt.PromptInfo.Builder()
		    .setTitle(options.title ?: "指纹验证")
		    .setSubtitle(options.content ?: "请触摸指纹传感器")
            .setNegativeButtonText("取消")
			
		// 挑战因子
		if ((options.challenge ?: "") != "") {
		    promptInfo.setAllowedAuthenticators(Authenticators.BIOMETRIC_STRONG)
			var cryptoObject = BiometricPrompt.CryptoObject(createCipher(options.challenge!!))
			biometricPrompt?.authenticate(promptInfo.build(), cryptoObject);
		} else {
		    promptInfo.setAllowedAuthenticators(Authenticators.BIOMETRIC_STRONG or Authenticators.BIOMETRIC_WEAK)
			biometricPrompt?.authenticate(promptInfo.build());
		}
	}
	
	fun cancel() {
		biometricPrompt?.cancelAuthentication()
	}
	
	fun createCipher(challenge: String): Cipher {
	    generateKeyStoreKey("ux-soter")
	    return createKeyStoreCipher("ux-soter", challenge.toByteArray(Charsets.UTF_8))
	}
	
	@Throws(Exception::class)
	private fun generateKeyStoreKey(alias: String) {
	    // 最低 API 需求校验
	    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
	        throw IllegalStateException("Requires Android 6.0 (API 23) or higher")
	    }
	
	    val keyGenerator = KeyGenerator.getInstance(
	        KeyProperties.KEY_ALGORITHM_AES,
	        "AndroidKeyStore" // 核心：指定 KeyStore
	    )
	
	    // 安全参数配置（API 23+）
	    val builder = KeyGenParameterSpec.Builder(
	        alias,
	        KeyProperties.PURPOSE_ENCRYPT or KeyProperties.PURPOSE_DECRYPT
	    ).apply {
	        setBlockModes(KeyProperties.BLOCK_MODE_CBC)
	        setEncryptionPaddings(KeyProperties.ENCRYPTION_PADDING_PKCS7)
	        setRandomizedEncryptionRequired(false) // 必须设置以允许手动 IV
	        setUserAuthenticationRequired(true) // 生物认证要求
	    }
	
	    keyGenerator.init(builder.build())
	    keyGenerator.generateKey() // 密钥自动存储到 AndroidKeyStore
	}
	
	@Throws(Exception::class)
	private fun createKeyStoreCipher(alias: String, iv: ByteArray): Cipher {
	    // KeyStore 初始化
	    val keyStore = KeyStore.getInstance("AndroidKeyStore").apply {
	        load(null)
	    }
	
	    // 安全密钥获取（注意类型转换）
	    val key = keyStore.getKey(alias, null) as? SecretKey ?: throw IllegalStateException("Key not found")
	
	    // Cipher 配置（AES/CBC/PKCS7Padding）
	    val cipher = Cipher.getInstance("AES/CBC/PKCS7Padding")
	
	    // 使用自定义 IV (Initialization Vector)
	    cipher.init(Cipher.ENCRYPT_MODE, key, IvParameterSpec(iv))
	    return cipher
	}
}