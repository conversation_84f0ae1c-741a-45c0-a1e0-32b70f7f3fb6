<template>
	<view v-if="items.length > 0" id="uxSwiperRef" class="ux-swiper"
			@touchstart="touchStart"
			@touchmove="touchMove"
			@touchend="touchEnd"
			@touchcancel="touchEnd">
		<view class="ux-swiper-item" :style="style(item)" v-for="(item, index) in items" :key="index">
			<slot :data="item.data"></slot>
		</view>
	</view>
</template>

<script setup>
	/**
	 * Swiper 容器
	 * @description 支持左右滑动堆叠效果
	 * @demo pages/component/swiper.uvue
	 * @tutorial https://www.uxframe.cn/component/swiper.html
	 * @property {Array}		list								UTSJSONObject[] | 数据列表
	 * <AUTHOR>
	 * @date 2025-03-02 11:20:35
	 */
	
	type UxSwiperItem = {
		index : number,
		x : number,
		s : number,
		data : UTSJSONObject
	}

	defineOptions({
		name: 'ux-swiper'
	})
	
	const emit = defineEmits(['click'])
	
	const props = defineProps({
		list: {
			type: Array as PropType<Array<UTSJSONObject>>,
			default: (): UTSJSONObject[] => {
				return [] as UTSJSONObject[]
			}
		}
	})
	
	let offsets : number[] = []
	let maxOffsets : number[] = []
	let scales : number[] = []
	let maxScales : number[] = []

	const items = ref<UxSwiperItem[]>([])

	watch(() : Array<UTSJSONObject> => props.list, () => {
		items.value = []
		offsets = []
		maxOffsets = []
		scales = []
		maxScales = []

		let list = props.list

		if (list.length == 1) {
			offsets = [2]
			maxOffsets = [0]
			scales = [1]
			maxScales = [1]
		} else if (list.length == 2) {
			offsets = [10, 2, 2]
			maxOffsets = [8, 8, 0]
			scales = [1, 0.9, 0.8]
			maxScales = [1, 0.95, 0.86]
		} else if (list.length == 3) {
			offsets = [18, 10, 2, 2]
			maxOffsets = [8, 8, 8, 0]
			scales = [1, 0.9, 0.8, 0.7]
			maxScales = [1, 0.95, 0.86, 0.77]
		} else if (list.length == 4) {
			offsets = [26, 18, 10, 2, 2]
			maxOffsets = [8, 8, 8, 8, 0]
			scales = [1, 0.9, 0.8, 0.7, 0.6]
			maxScales = [1, 0.95, 0.86, 0.77, 0.68]
		} else {
			offsets = [26, 18, 10, 2, 2]
			maxOffsets = [8, 8, 8, 8, 0]
			scales = [1, 0.9, 0.8, 0.7, 0.6]
			maxScales = [1, 0.95, 0.86, 0.77, 0.68]

			for (let i = 4; i < list.length; i++) {
				offsets.push(2)
				maxOffsets.push(0)
				scales.push(0.6)
				maxScales.push(0.68)
			}
		}

		for (let i = 0; i < list.length; i++) {
			items.value.push({
				index: i,
				x: 0,
				s: Math.max(1 - 0.1 * i, 0.6),
				data: list[i]
			} as UxSwiperItem)
		}
	}, { immediate: true, deep: true })

	let initialX = 0
	let threshold = 60
	const isMove = ref(false)
	
	function style(item : UxSwiperItem) {
		return {
			transform: `scale(${item.s}) translateX(${item.x}px)`,
			transitionDuration: isMove.value ? '0ms' : '250ms',
			zIndex: items.value.length - item.index,
			right: `${offsets[item.index]}px`,
		}
	}
	
	function click() {
		let index = items.value.findIndex(e => e.index == 0)
		if (index == -1) {
			return
		}
		
		emit('click', items.value[index])
	}
	
	let lastTouch = 0
	function touchStart(event : UniTouchEvent) {
		event.preventDefault()
		
		// 上一次点击时间
		lastTouch = event.touches[0].clientX
		
		if (items.value.length <= 1) {
			return
		}
		
		// 最上方item
		let index = items.value.findIndex(e => e.index == 0)
		if (index == -1) {
			return
		}

		isMove.value = true
		initialX = event.touches[0].clientX - items.value[index].x
	}
	
	let width = 0
	let toLeft: boolean | null = null
	let addIndex = -1
	// #ifdef MP
	async function touchMove(event : UniTouchEvent) {
	// #endif
	// #ifndef MP
	function touchMove(event : UniTouchEvent) {
	// #endif
		event.preventDefault()
		
		if (items.value.length <= 1) {
			return
		}

		if (!isMove.value) return

		const touchX = event.touches[0].clientX
		
		// 滑动方向
		if(toLeft == null) {
			toLeft = touchX - initialX < 0
		}
		
		if(toLeft!) {
			const moveX = Math.min(touchX - initialX, 0)
			const offset = Math.abs(moveX / initialX / 3)
			
			// 左滑移出
			items.value.forEach((item, i) => {
				let idx = item.index
				if (idx != 0) {
					let scale = Math.min(scales[idx] + offset, maxScales[idx])
					item.x = Math.max(moveX * (scale / 4), -maxOffsets[idx])
					item.s = scale
				} else {
					item.x = moveX
				}
			})
		} else {
			if(width == 0) {
				// #ifdef MP
				let rect = await uni.getElementById('uxSwiperRef')?.getBoundingClientRectAsync()
				width = rect?.width ?? 0
				// #endif
				
				// #ifndef MP
				width = uni.getElementById('uxSwiperRef')?.getBoundingClientRect()?.width ?? 0
				// #endif
			}
			
			const moveX = Math.min(touchX - initialX, width)
			
			// 右滑添加
			if(addIndex == -1) {
				addIndex = items.value.findIndex(e => e.index == items.value.length - 1)
				items.value[addIndex].x = -width
				items.value[addIndex].s = scales[addIndex]
				
				for (let i = 0; i < items.value.length; i++) {
					if(i == addIndex) {
						items.value[i].index = 0
					} else {
						items.value[i].index++
					}
				}
			}
			
			setTimeout(function() {
				items.value.forEach((item, i) => {
					if (item.index == addIndex) {
						item.x = moveX - width
					}
				})
			}, 20);
		}
	}

	function touchEnd(event : UniTouchEvent) {
		event.preventDefault()
		
		let isLeft = toLeft ?? true
		toLeft = null
		addIndex = -1
		
		// 模拟点击
		if(Math.abs(event.changedTouches[0].clientX - lastTouch) < 5) {
			click()
			return
		}
		
		if (items.value.length <= 1) {
			return
		}

		if (!isMove.value) return
		isMove.value = false

		setTimeout(() => {
			const item = items.value[items.value.findIndex(e => e.index == 0)]
			
			let _threshold: number
			if(isLeft) {
				_threshold = -threshold
			} else {
				_threshold = threshold
			}
			if (item.x < _threshold) {
				if(isLeft) {
					// 移出后添加到最下方
					item.x = -(width + 100)
					
					setTimeout(() => {
						items.value.forEach((c, i) => {
							if (c.index == 0) {
								c.index = items.value.length - 1
							} else {
								c.index -= 1
							}
						})
					
						items.value.forEach((item, i) => {
							item.x = 0
							item.s = scales[item.index]
						})
					}, 300);
				} else {
					// 移入后放在最上方
					item.x = 0
					
					items.value.forEach((item, i) => {
						item.x = 0
						item.s = scales[item.index]
					})
				}
			} else {
				items.value.forEach((item, i) => {
					item.x = 0
					item.s = scales[item.index]
				})
			}
		}, 80);
	}

</script>

<style lang="scss">
	.ux-swiper {
		position: relative;
		height: 138px;
		margin: 6px 10px 6px 10px;
	}

	.ux-swiper-item {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 134px;
		background-color: white;
		border-radius: 10px;
		transition-duration: 250ms;
		transition-property: transform, right;
		transition-timing-function: ease;
		transform-origin: right center;
		/* #ifndef APP */
		// box-shadow: 0px 2px 2px #e5e5e5;
		/* #endif */
	}
</style>