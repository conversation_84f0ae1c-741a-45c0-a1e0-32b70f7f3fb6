<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/indexbar?title=Indexbar"></Mobile>

# Indexbar
> 组件类型：UxIndexbarComponentPublicInstance

支持右侧索引联动滚动到锚点

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| fixed | Boolean | true | fixed定位 |
| width | Any | auto | 宽度 |
| height | Any | auto | 高度 |
| right | Any | 10px | slider右偏移距离 |
| activeColor | String | `$ux.Conf.primaryColor` | 选中颜色 |
| indicateColor | String | `$ux.Conf.primaryColor` | 未选中颜色 |
| opacity | Number | 0.2 | 侧边栏背景透明度 |
| otherIcon | String |  | 其他项图标 |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| click | 点击时触发 |  |
| change | 当前选择下标改变时触发 |  |
  
  