import { BuilderNode } from "@kit.ArkUI"
import { BlurView } from "./builder.ets"

interface UxBlurOptions {
    color : string,
    radius : number,
	cornerRadius: number,
}

export class UxBlur {

	private $element : UniNativeViewElement; 
	private builder : BuilderNode<[UxBlurOptions]> | null = null
	
	private params : UxBlurOptions = {
	    color: '#ffffff',
	    radius: 10,
		cornerRadius: 0
	}
	
	constructor(element : UniNativeViewElement) {
		this.builder = element.bindHarmonyWrappedBuilder(wrapBuilder<[UxBlurOptions]>(BlurView), this.params)
		this.$element = element
		this.$element.bindHarmonyController(this)
	}
	
	initView(view: any | null, radius: number, cornerRadius: number, color: string) {
		this.params.color = color
		this.params.radius = radius
		this.params.cornerRadius = cornerRadius
		this.builder?.update(this.params)
	}
	
	destroy() {
		
	}
}