import LocalAuthentication
import DCloudUTSFoundation

public class UxSoter {

  static func startAuth(title: String, content: String, challenge: String, callback: @escaping (_ code: Int, _ msg: String) -> Void) {
    let context = LAContext()
    var policy: LAPolicy = .deviceOwnerAuthenticationWithBiometrics
    
    context.evaluatePolicy(
        policy,
        localizedReason: content
    ) { success, error in
        DispatchQueue.main.async {
            if success {
                callback(0, "ok")
            } else {
				guard let error = error as? LAError else { return }
				
				var code = -1
				var message = ""
				switch error.code {
				case .userCancel:
					code = 6
				    message = "用户取消了认证"
				case .userFallback:
					code = 7
				    message = "用户选择使用密码"
				case .biometryNotAvailable:
					code = 1
				    message = "设备不支持 Face ID"
				case .biometryNotEnrolled:
					code = 2
				    message = "用户未设置 Face ID"
				case .biometryLockout:
					code = 4
				    message = "认证失败次数过多，已被锁定"
				default:
					code = 3
				    message = "认证失败: \(error.localizedDescription)"
				}

               callback(code, message)
            }
        }
    }
  }
  
  static func cancel() {
    
  }
  
  static func isSupport() -> Bool {
    let context = LAContext()
    var error: NSError?
    
    let isSupported = context.canEvaluatePolicy(
        .deviceOwnerAuthenticationWithBiometrics,
        error: &error
    )
    
    return isSupported
  }
  
  static func isEnrolled() -> Bool {
    if isSupport() {
        if #available(iOS 11.0, *) {
			let context = LAContext()
            return context.biometryType != .none
        }
        return true
    }
	
    return false
  }
  
  static func isKeyguardSecure() -> Bool {
    let context = LAContext()
    return context.canEvaluatePolicy(
        .deviceOwnerAuthentication,
        error: nil
    )
  }
  
  static func generateKeyPair() -> SecKey? {
    let attributes: [String: Any] = [
        kSecAttrKeyType as String: kSecAttrKeyTypeECSECPrimeRandom,
        kSecAttrKeySizeInBits as String: 256,
        kSecPrivateKeyAttrs as String: [
            kSecAttrIsPermanent as String: true,
            kSecAttrApplicationTag as String: "ux.soter.authkey"
        ]
    ]
    
    var error: Unmanaged<CFError>?
    guard let privateKey = SecKeyCreateRandomKey(attributes as CFDictionary, &error) else {
        console.log("密钥生成失败: \(error!.takeRetainedValue())")
        return nil
    }
    return privateKey
  }
}