export * from '../interface.uts';
import { UxMqttOptions, UxMqttConnectOptions, UxMqttSubscribeOptions, UxMqttPublishOptions } from '../interface.uts';
import mqtt from 'mqtt';

export type MqttServerStatus = 'offline' | 'online'

export class UxMqtt {
	opts : UxMqttOptions
	client : mqtt.MqttClient | null = null
	status : MqttServerStatus = 'offline'

	constructor(opts : UxMqttOptions) {
		this.opts = opts
	}

	/**
	 * 连接
	 */
	connect(opts : UxMqttConnectOptions) {
		this.client = mqtt.connect(this.opts.brokerUrl, {
			clientId: this.opts.clientId, // 客户端id
			username: this.opts.username, // 用户名
			password: this.opts.password, // 密码
			clean: this.opts.cleanSession ?? true, //清除会话
			reconnectPeriod: (this.opts.automaticReconnect ?? true) ? 1000 : 0, // 自动重连
			connectTimeout: this.opts.connectionTimeout != null ? (this.opts.connectionTimeout * 1000) : (30 * 1000), // 超时
			keepalive: this.opts.keepAliveInterval ?? 60, // 心跳周期
		})

		this.client!.on('connect', () => {
			this.status = 'online'
			opts.statusListener?.(true, '')
		});

		this.client!.on('message', (topic, message) => {
			opts.messageListener?.(topic, message.toString())
		})

		this.client!.on('error', (error) => {
			this.status = 'offline'
			opts.statusListener?.(false, error.toString())
		})
	}

	/**
	 * 断开连接
	 */
	disconnect() {
		this.client?.end()
	}

	/**
	 * 订阅主题
	 */
	subscribe(opts : UxMqttSubscribeOptions) {
		this.client?.subscribe(opts.topic, { qos: opts.qos ?? 2 }, (err) => {
			if (err) {
				opts.fail?.(err.toString())
			} else {
				opts.success?.()
			}
		})
	}

	/**
	 * 取消订阅
	 */
	unsubscribe(topics : string[]) {
		this.client?.unsubscribe(topics)
	}

	/**
	 * 发布消息
	 */
	publish(opts : UxMqttPublishOptions) {
		let retained = opts.retained ?? false
		this.client?.publish(opts.topic, opts.msg, { qos: opts.qos ?? 2, retain: retained }, (err) => {
			if (err) {
				opts.fail?.(err.toString())
			} else {
				opts.success?.()
			}
		})
	}
}