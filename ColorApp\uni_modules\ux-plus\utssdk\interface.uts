
export type SetGray = (gray : number) => void

export type HideKeyboard = () => void

export type UxOpenURLOptions = {
	url : string
	complete ?: (res : UxPlusCallback) => void
	fail ?: (res : UxPlusCallback) => void
	success ?: (res : UxPlusCallback) => void
}

export type OpenURL = (options : UxOpenURLOptions) => void

export type UxOpenWebOptions = {
	title ?: string
	url : string
	blur ?: number
	complete ?: (res : UxPlusCallback) => void
	fail ?: (res : UxPlusCallback) => void
	success ?: (res : UxPlusCallback) => void
}

export type OpenWeb = (options : UxOpenWebOptions) => void

export type MakePhoneCall = (options : UxMakePhoneCallOptions) => void

export type UxMakePhoneCallOptions = {
	phoneNumber : string
	complete ?: (res : UxPlusCallback) => void
	fail ?: (res : UxPlusCallback) => void
	success ?: (res : UxPlusCallback) => void
}

export type UxVibrateType = 'heavy' | 'medium' | 'light' | 'lighter'

export type UxVibrateOptions = {
  type : UxVibrateType
  success ?: (res : UxPlusCallback) => void
  fail ?: (res : UxPlusCallback) => void
  complete ?: (res : UxPlusCallback) => void
}

export type Vibrate = (options : UxVibrateOptions) => void

export type SetClipboardData = (data : string) => void

export type GetClipboardData = () => string

export type Exit = () => void

export interface UxPlusCallback extends IUniError {
	
}