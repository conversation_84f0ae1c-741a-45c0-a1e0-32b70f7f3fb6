<template>
  <div
    class="form-group"
    :class="getGroupStyleClass(group.groupClass, false)"
    v-show="group.show"
  >
    <div 
      v-if="group.title" 
      class="form-group-title flex-h flex-v-center"
      :class="{ 'cursor-pointer': fold }"
      @click="toggleGroup"
    >
      <span class="flex1">{{ group.title }}</span>
      <Icon v-if="fold" :name="isGroupExpanded ? 'icon-mdi-chevron-down' : 'icon-mdi-chevron-right'" class="ml-2 transition-transform duration-200"/>
    </div>
    <div 
      v-show="!fold || isGroupExpanded"
      class="form-group-content w-full lg:flex-v xl:col-span-full"
    >
      <FormField
        v-for="field in group.fields"
        :key="field.Id"
        :field="field"
        :classes="getGroupItemClass(field)"
        :data-model="dataModel"
        :title-class="titleClass"
        :label-cell="labelCell"
        @change="handleFieldChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { getGroupStyleClass, getGroupItemClass } from '@/common/formGroupStyles';

const props = defineProps({
  /*
    {
        分组标题
        title: lastGroup,
        分组样式
        groupClass: lastGroupClass,
        该分组下的字段列表
        fields: items,
        所有分组都显示，不再根据fold属性控制
        show: true,
      }
  */
  group: {
    type: Object,
    required: true
  },
  // 分组索引
  groupIndex: {
    type: Number,
    default: 0
  },
  // 数据绑定对象
  dataModel: {
    type: Object,
    required: true
  },
  // 标题样式
  titleClass: {
    type: String,
    default: 'h-form-item'
  },
  // 24网络样式
  labelCell: {
    type: Number,
    default: 24
  },
  //是否折叠
  fold: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['change']);

// 响应式数据
// 如果开启了折叠功能，只有第一个分组默认展开
const isGroupExpanded = ref(props.fold ? props.groupIndex === 0 : true);

// 方法
const handleFieldChange = (e) => {
  emit('change', e);
};

const toggleGroup = () => {
  if (props.fold) {
    isGroupExpanded.value = !isGroupExpanded.value;
  }
};
</script>

<style>
/* 导入统一的分组样式 */
@import '@/styles/formGroupStyles.css';
</style>
