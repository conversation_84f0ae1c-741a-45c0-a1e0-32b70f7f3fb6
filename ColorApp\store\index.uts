import { $ux } from '@/uni_modules/ux-frame'

export type Config = {
	lang: string
}

/**
 * 状态管理
 */
export type State = {
	config: Config,
}

/**
 * 实例化State
 */
export const state = reactive<State>({
	config: {
		lang: 'zh-CN'
	},
} as State)

/**
 * 协议key
 */
export const privacyKey = 'UXFRAME-PRIVACY'

/**
 * 加载缓存数据
 */
export const loadData = () => {
	// 加载系统语言
	$ux.Locale.i18n().locale.value = state.config.lang
}