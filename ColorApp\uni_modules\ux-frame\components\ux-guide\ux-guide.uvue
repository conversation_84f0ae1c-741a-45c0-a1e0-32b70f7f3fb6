<template>
	<view v-show="show" :id="myId" class="ux-guide">
		<!-- #ifdef APP-ANDROID || APP-HARMONY -->
		<view class="transform" :style="top"></view>
		<view class="transform" :style="right"></view>
		<view class="transform" :style="bottom"></view>
		<view class="transform" :style="left"></view>
		<!-- #endif -->
		<!-- #ifndef APP-ANDROID || APP-HARMONY -->
		<view class="transform" :style="border"></view>
		<!-- #endif -->
		
		<view ref="bodyRef" class="ux-guide__body transform" :style="bodyStyle">
			<view v-if="arrowPosition == 'top'" ref="arrowRef" class="ux-guide__arrow transform" :style="arrowStyle">
				<!-- #ifndef APP-ANDROID || APP-HARMONY -->
				<canvas class="ux-guide__arrow__canvas" :id="arrowId" :canvas-id="arrowId"></canvas>
				<!-- #endif -->
			</view>
			<view class="ux-guide__content" :style="contentStyle">
				<slot>
					<text class="ux-guide__text">{{ datas[step].text }}</text>
					<view class="ux-guide__btns">
						<ux-button v-if="skip" theme="text" text="跳过" color="white" @click="onSkip()"></ux-button>
						<view class="ux-guide__row">
							<ux-button v-if="step > 0" :plain="true" :mr="10" theme="primary" text="上一步" @click="setStep(step-1)"></ux-button>
							<ux-button v-if="step < datas.length - 1" :plain="true" theme="primary" text="下一步" @click="setStep(step+1)"></ux-button>
							<ux-button v-else theme="primary" :text="confirmText" @click="onEnd()"></ux-button>
						</view>
					</view>
				</slot>
			</view>
			<view v-if="arrowPosition == 'bottom'" ref="arrowRef" class="ux-guide__arrow transform" :style="arrowStyle">
				<!-- #ifndef APP-ANDROID || APP-HARMONY -->
				<canvas class="ux-guide__arrow__canvas" :id="arrowId" :canvas-id="arrowId"></canvas>
				<!-- #endif -->
			</view>
		</view>
	</view>
</template>

<script setup>
	/**
	 * Guide 引导
	 * @description 支持动画效果，支持跳过和插槽自定义
	 * @demo pages/component/guide.uvue
	 * @tutorial https://www.uxframe.cn/component/guide.html
	 * @property {Slot}				default					Slot | 默认插槽
	 * @property {Array}			datas					UxGuideItem[] | 数据
	 * @property {Any}				width					Any | 宽度 (默认 300)
	 * @property {Boolean}			skip					Boolean | 跳过 (默认 true)
	 * @property {Any}				offset					Any | 偏移 (默认 5)
	 * @property {Number}			opacity					Number | 遮罩透明度 0-1 (默认 0.8)
	 * @property {String}			confirmText				String | 完成按钮文字 (默认 '完成')
	 * @property {Boolean}			autoscroll				Boolean | 自动滚动到锚点 (默认 true)
	 * @event {Function}			scroll					Function | 锚点无法触达需要滚动时触发
	 * @event {Function}			change					Function | 步骤改变时触发
	 * @event {Function}			skip					Function | 跳过时触发
	 * @event {Function}			end						Function | 结束时触发
	 * <AUTHOR>
	 * @date 2024-05-15 11:31:11
	 */
	
	import { $ux } from '../../index'
	import { useResize } from '../../libs/use/resize'
	import { UxGuideItem } from '../../libs/types/types.uts'
	
	type GuideNode = {
		left: number,
		top: number,
		right: number,
		bottom: number,
		width: number,
		height: number
	}
	
	const emit = defineEmits(['change', 'skip', 'end', 'scroll'])
	
	const props = defineProps({
		datas: {
			type: Array as PropType<UxGuideItem[]>,
			default: (): UxGuideItem[] => {
				return [] as UxGuideItem[]
			}
		},
		width: {
			default: 300
		},
		skip: {
			type: Boolean,
			default: true
		},
		offset: {
			default: 5
		},
		opacity: {
			type: Number,
			default: 0.8
		},
		confirmText: {
			type: String,
			default: '完成'
		},
		autoscroll: {
			type: Boolean,
			default: true
		},
	})
	
	const instance = getCurrentInstance()?.proxy
	const myId = `ux-guide-${$ux.Random.uuid()}`
	const bodyRef = ref<Element | null>(null)
	const arrowRef = ref<Element | null>(null)
	const arrowId = `ux-guide-arrow-${$ux.Random.uuid()}`
	let windowWidth = uni.getWindowInfo().windowWidth
	let windowHeight = uni.getWindowInfo().windowHeight
	const dpr = uni.getWindowInfo().pixelRatio
	const arrowLeft = ref(0)
	const arrowWidth = ref(12)
	const arrowHeight = ref(8)
	const arrowPosition = ref('top')
	const bodyWidth = ref(0)
	const bodyHeight = ref(0)
	const step = ref(0)
	const node = ref<GuideNode | null>(null)
	const show = ref(true)
	
	let scrollTop = 0
	
	const offset = computed(() => {
		return $ux.Util.getPx(props.offset)
	})
	
	const top = computed((): Map<string, any> => {
		let _left = 0
		let _top = 0
		let _height = (node.value?.top ?? 0) - offset.value
		
		let css = new Map<string, any>()
		
		css.set('position', `absolute`)
		css.set('left', `${_left}px`)
		css.set('top', `${_top}px`)
		css.set('width', `${windowWidth}px`)
		css.set('height', `${_height}px`)
		css.set('background-color', `rgba(0, 0, 0, ${props.opacity})`)
		
		return css
	})
	
	const right = computed((): Map<string, any> => {
		let _left = (node.value?.left ?? 0) + (node.value?.width ?? 0) + offset.value
		let _top = (node.value?.top ?? 0) - offset.value
		let _width = node.value?.right
		let _height = (node.value?.height ?? 0) + offset.value * 2
		
		let css = new Map<string, any>()
		
		css.set('position', `absolute`)
		css.set('left', `${_left}px`)
		css.set('top', `${_top}px`)
		css.set('width', `${_width}px`)
		css.set('height', `${_height}px`)
		css.set('background-color', `rgba(0, 0, 0, ${props.opacity})`)
		
		return css
	})
	
	const bottom = computed((): Map<string, any> => {
		let _left = 0
		let _top = (node.value?.top ?? 0) + (node.value?.height ?? 0) + offset.value
		let _height = node.value?.bottom
		
		let css = new Map<string, any>()
		
		css.set('position', `absolute`)
		css.set('left', `${_left}px`)
		css.set('top', `${_top}px`)
		css.set('width', `${windowWidth}px`)
		css.set('height', `${_height}px`)
		css.set('background-color', `rgba(0, 0, 0, ${props.opacity})`)
		
		return css
	})
	
	const left = computed((): Map<string, any> => {
		let _left = 0
		let _top = (node.value?.top ?? 0) - offset.value
		let _width = (node.value?.left ?? 0) - offset.value
		let _height = (node.value?.height ?? 0) + offset.value * 2
		
		let css = new Map<string, any>()
		
		css.set('position', `absolute`)
		css.set('left', `${_left}px`)
		css.set('top', `${_top}px`)
		css.set('width', `${_width}px`)
		css.set('height', `${_height}px`)
		css.set('background-color', `rgba(0, 0, 0, ${props.opacity})`)
		
		return css
	})
	
	const border = computed((): Map<string, any> => {
		let _left = (node.value?.left ?? 0) - offset.value
		let _top = (node.value?.top ?? 0) - offset.value
		let _width = (node.value?.width ?? 0 )+ offset.value * 2
		let _height = (node.value?.height ?? 0) + offset.value * 2
		
		let css = new Map<string, any>()
		
		css.set('position', `absolute`)
		css.set('left', `${_left}px`)
		css.set('top', `${_top}px`)
		css.set('width', `${_width}px`)
		css.set('height', `${_height}px`)
		css.set('border-radius', `${6}px`)
		css.set('outline', `${5000}px solid rgba(0, 0, 0, ${props.opacity})`)
		css.set('background-color', `rgba(0, 0, 0, 0)`)
		
		return css
	})
	
	async function darwArrow() {
		if(arrowRef.value == null) {
			return
		}
		
		// #ifdef APP-ANDROID || APP-HARMONY
		const ctx = arrowRef.value?.getDrawableContext()
		if(ctx == null) {
			return
		}
		
		ctx.reset()
		// #endif
		
		// #ifndef APP-ANDROID || APP-HARMONY
		const context = await $ux.Util.createCanvasContext(arrowId, instance)
		if(context == null) {
			return
		}
		
		const ctx = context.getContext('2d')!
		ctx.canvas.width = ctx.canvas.offsetWidth * dpr
		ctx.canvas.height = ctx.canvas.offsetHeight * dpr
		ctx.scale(dpr, dpr)
		// #endif
		
		ctx.beginPath()
			
		if (arrowPosition.value == 'top') {
			ctx.moveTo(0, arrowHeight.value)
			ctx.lineTo(arrowWidth.value, arrowHeight.value)
			ctx.lineTo(arrowWidth.value / 2, 0)
			ctx.lineTo(0, arrowHeight.value)
		} else {
			ctx.moveTo(0, 0)
			ctx.lineTo(arrowWidth.value, 0)
			ctx.lineTo(arrowWidth.value / 2, arrowHeight.value)
			ctx.lineTo(0, 0)
		}
			
		ctx.closePath()
		ctx.fillStyle = '#000'
		ctx.fill()
		
		// #ifdef APP-ANDROID || APP-HARMONY
		ctx.update()
		// #endif
	}
	
	const bodyStyle = computed(() => {
		let nodeLeft = node.value?.left ?? 0
		let nodeRight = node.value?.right ?? 0
		let nodeTop = node.value?.top ?? 0
		let nodeWidth = node.value?.width ?? 0
		let nodeHeight = node.value?.height ?? 0
		
		let _offset = windowWidth * 0.05
		let width = windowWidth / 3
		let isRight = nodeLeft + nodeWidth / 2 > width * 2
		
		let top = nodeTop + nodeHeight + 20
		
		arrowPosition.value = top + bodyHeight.value <= windowHeight ? 'top' : 'bottom'
		if(arrowPosition.value == 'bottom') {
			top = nodeTop - offset.value - bodyHeight.value - 20
		}
		arrowLeft.value = isRight ? (bodyWidth.value - _offset) : (nodeLeft + 10)
		darwArrow()
		
		let css = new Map<string, any>()
		
		if(isRight) {
			css.set('right', `${_offset}px`)
		} else {
			css.set('left', `${_offset}px`)
		}
		
		css.set('top', `${top}px`)
		css.set('width', `${$ux.Util.getPx(props.width)}px`)
		
		return css
	})
	
	const contentStyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('background-color', `rgba(0, 0, 0, 0.9)`)
		
		return css
	})
	
	const arrowStyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('width', `${arrowWidth.value}px`)
		css.set('height', `${arrowHeight.value}px`)
		css.set('margin-left', `${arrowLeft.value}px`)
		
		return css
	})
	
	function onSkip() {
		show.value = false
		emit('skip', step.value)
		step.value = 0
	}
	
	function onEnd() {
		show.value = false
		emit('end', step.value)
		step.value = 0
	}
	
	async function setStep(index: number) {
		const arect = await bodyRef.value?.getBoundingClientRectAsync()!
		bodyWidth.value = arect?.width ?? 0
		bodyHeight.value = arect?.height ?? 0
		
		uni.createSelectorQuery()
			.select(`#${props.datas[index].id}`)
			.boundingClientRect()
			.exec((rect) => {
				let _node = rect[0] as NodeInfo
				let top = _node.top ?? 0
				
				if(props.autoscroll) {
					let _offset = top + bodyHeight.value
					
					// 底部超出范围
					if(_offset > windowHeight) {
						scrollTop += _offset - windowHeight + bodyHeight.value / 2
						emit('scroll', scrollTop)
						
						setTimeout(() => {
							setStep(index)
						}, 500);
						return
					}
					
					// 顶部超出范围
					if(top < 100) {
						scrollTop = 0
						emit('scroll', scrollTop)
						
						setTimeout(() => {
							setStep(index)
						}, 500);
						return
					}
				}
				
				step.value = index
				
				node.value = {
					left: _node.left!,
					top: _node.top!,
					right: windowWidth - _node.left! - _node.width!,
					bottom: windowHeight - _node.top! - _node.height!,
					width: _node.width!,
					height: _node.height!
				} as GuideNode
				
				emit('change', step.value)
			});
	}
	
	onMounted(() => {
		setTimeout(() => {
			windowWidth = uni.getWindowInfo().windowWidth
			windowHeight = uni.getWindowInfo().windowHeight
			
			setStep(0)
		}, 50);
		
		useResize(uni.getElementById(myId), () => {
			setStep(step.value)
		})
	})
	
	defineExpose({
		onSkip,
		setStep,
		onEnd
	})
</script>

<style lang="scss">

	.ux-guide {
		position: fixed;
		width: 100%;
		height: 100%;
		z-index: 10000000;
		
		&__body  {
			position: absolute;
			display: flex;
			flex-direction: column;
		}
		
		&__content {
			display: flex;
			flex-direction: column;
			border-radius: 10px;
		}
		
		&__arrow {
			background-color: transparent;
			
			&__canvas {
				width: 100%;
				height: 100%;
			}
		}
		
		&__text {
			color: white;
			font-size: 14px;
			margin-top: 5px;
			margin-left: 15px;
			margin-right: 15px;
		}
		
		&__btns {
			flex: 1;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: space-between;
			margin: 15px 15px 15px 6px;
		}
		
		&__row {
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: flex-end;
		}
	}
	
	.transform {
		transition-property: left, top, margin-left, width, height;
		transition-duration: 200ms;
	}
	
</style>