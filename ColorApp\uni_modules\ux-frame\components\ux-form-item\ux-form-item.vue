<template>
	<view class="ux-form-item" :style="style">
		<view class="ux-form-item__wrap" :style="wrapStyle">
			<view class="ux-form-item__label" :style="labelStyle">
				<slot name="label">
					<text v-if="required && verify" class="ux-form-item__star">*</text>
					<text class="ux-form-item__label--text" :style="labelTextStyle">{{ label }}</text>
				</slot>
			</view>
			<view class="ux-form-item__content" :style="contentStyle">
				<slot></slot>
			</view>
		</view>
		<view v-if="!isValid && verify" class="ux-form-item__error">
			<slot name="error">
				<text class="ux-form-item__error--text" :style="errorStyle">{{ errorText }}</text>
			</slot>
		</view>
	</view>
</template>

<script setup lang="ts">
	/**
	* FormItem 表单子项
	* @description 支持任何组件，只作为数据校验
	* @demo pages/component/form.uvue
	* @tutorial https://www.uxframe.cn/component/form.html
	* @property {String} 			label								String | 表单名称
	* @property {String} 			field								String | 表单字段
	* @property {Boolean} 			verify								Boolean | 开启校验 (默认 true)
	* @property {Boolean} 			required							Boolean | 必填项 (默认 false)
	* @property {Array} 			rules								UxFormRule[] | 校验规则
	* @property {String} 			direction=[horizontal|vertical]		String | 布局方向 (默认 horizontal)
	* @value horizontal 水平
	* @value vertical 垂直
	* @property {Any} 				width								Any | 宽度 (默认 自适应)
	* @property {Any} 				size								Any | 字体大小 (默认 $ux.Conf.fontSize)
	* @property {Any} 				errorSize							Any | 错误提示字体大小 (默认 12)
	* @property {String} 			color								String | 字体颜色 (默认 $ux.Conf.fontColor)
	* @property {String} 			darkColor=[none|auto|color]			String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	* @value none 不显示
	* @value auto 自动适配深色模式
	* @value color 其他颜色
	* @property {Boolean} 			border								Boolean | 下边框 (默认 true)
	* @property {Any} 				padding								Any | 内边距 (默认 8)
	* @property {Any} 				vPadding							Any | 垂直方法内边距 (默认 12)
	* @property {Any} 				minHeight							Any | 最小高度 (默认 50)
	* @property {Boolean} 			disabled							Boolean | 是否禁用 (默认 false)
	* <AUTHOR>
	* @date 2024-12-05 20:01:28
	*/

	import { PropType, computed, getCurrentInstance, inject, onMounted, onUnmounted, ref } from 'vue'
	import { $ux } from '../../index'
	import { UxFormItem, UxFormRule, UxFormData } from "../../libs/types/types.uts"
	import { useFontSize, useFontColor, useBorderColor } from '../../libs/use/style.uts'

	defineOptions({
		name: 'ux-form-item'
	})

	const props = defineProps({
		label: {
			type: String,
			default: ''
		},
		field: {
			type: String,
			default: ''
		},
		verify: {
			type: Boolean,
			default: true
		},
		required: {
			type: Boolean,
			default: false
		},
		rules: {
			type: Array as PropType<UxFormRule[]>,
			default: () : UxFormRule[] => [] as UxFormRule[]
		},
		direction: {
			type: String,
			default: ''
		},
		width: {
			default: 0
		},
		size: {
			default: 0
		},
		errorSize: {
			default: 0
		},
		color: {
			type: String,
			default: ''
		},
		darkColor: {
			type: String,
			default: ''
		},
		border: {
			type: Boolean,
			default: true
		},
		padding: {
			default: 8
		},
		vPadding: {
			default: 12
		},
		minHeight: {
			default: 50
		}
	})

	const instance = getCurrentInstance()?.proxy
	const isValid = ref(true)
	const errorText = ref('请正确填写')

	const _direction = computed(() : string => {
		return props.direction != '' ? props.direction : inject('direction', 'horizontal') as string
	})

	const _width = computed(() : number => {
		return $ux.Util.getPx(props.width) > 0 ? $ux.Util.getPx(props.width) : inject('width', 0) as number
	})
	
	const _size = computed(() : number => {
		return $ux.Util.getPx(props.size) > 0 ? $ux.Util.getPx(props.size) : inject('size', 0) as number
	})
	
	const _errorSize = computed(() : number => {
		return $ux.Util.getPx(props.errorSize) > 0 ? $ux.Util.getPx(props.errorSize) : inject('errorSize', 0) as number
	})
	
	const _color = computed(() : string => {
		return props.color != '' ? props.color : inject('color', '') as string
	})
	
	const _darkColor = computed(() : string => {
		return props.darkColor != '' ? props.darkColor : inject('darkColor', '') as string
	})
	
	const fontSize = computed((): number => {
		return useFontSize(_size.value, 0)
	})
	
	const errorSize = computed((): number => {
		return useFontSize(_errorSize.value, 0)
	})
	
	const fontColor = computed((): string => {
		return useFontColor(_color.value, _darkColor.value)
	})

	const borderColor = computed(() : string => {
		return useBorderColor('', '')
	})

	const style = computed(() => {
		let css = {}

		if (props.border) {
			css['border-bottom'] = `1rpx solid ${borderColor.value}`
		}
		
		if(_direction.value == 'horizontal') {
			css['flex'] = 1
		}
		
		css['padding-top'] = $ux.Util.addUnit(props.padding)
		css['padding-bottom'] = $ux.Util.addUnit(props.padding)
		css['min-height'] = $ux.Util.addUnit(props.minHeight)
		
		return css
	})

	const wrapStyle = computed(() => {
		let css = {}

		if(_direction.value == 'horizontal') {
			css['flex-direction'] = 'row'
			css['justify-content'] = 'space-between'
			css['align-items'] = 'center'
		} else {
			css['flex-direction'] ='column'
			css['justify-content'] = 'center'
		}
		
		return css
	})

	const labelStyle = computed(() => {
		let css = {}
		
		if(_width.value > 0) {
			css['width'] = $ux.Util.addUnit(_width.value)
		}
		
		if(_direction.value != 'horizontal') {
			css['padding'] = `${props.vPadding}px 0px`
		}

		return css
	})
	
	const labelTextStyle = computed(() => {
		let css = {}

		css['font-size'] = $ux.Util.addUnit(fontSize.value)
		css['color'] = fontColor.value
		css['flex'] = 1
		
		return css
	})

	const contentStyle = computed(() => {
		let css = {}

		if( _direction.value == 'horizontal') {
			css['justify-content'] = 'flex-end'
		} else {
			css['justify-content'] = 'flex-start'
		}

		return css
	})

	const errorStyle = computed(() => {
		let css = {}

		css['font-size'] = $ux.Util.addUnit(errorSize.value)

		return css
	})

	function verifyData(val : any) : UxFormData {
		if (props.verify) {
			if (props.rules.length == 0 && props.required) {
				isValid.value = !$ux.Verify.isEmpty(val)
				errorText.value = isValid.value ? '' : `请填写${props.label}`
			} else {
				let rules = props.rules as UxFormRule[]
				for (let i = 0; i < rules.length; i++) {
					errorText.value = rules[i].valid(val)
					isValid.value = errorText.value == ''
					
					if (!isValid.value) {
						break
					}
				}
			}
		} else {
			isValid.value = true
			errorText.value = ''
		}
		
		return {
			valid: isValid.value,
			field: props.field,
			value: val,
			error: errorText.value
		} as UxFormData
	}

	function register() {
		$ux.Util.$dispatch(instance!, 'ux-form', 'register', {
			field: props.field,
			node: instance as UxFormItemComponentPublicInstance,
		} as UxFormItem)
	}

	function unregister() {
		$ux.Util.$dispatch(instance!, 'ux-form', 'unregister', props.field)
	}
	
	onMounted(() => {
		register()
	})

	onUnmounted(() => {
		unregister()
	})

	defineExpose({
		verifyData
	})
</script>

<style lang="scss" scoped>
	/* #ifdef H5 */
	uni-view {
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: stretch;
		align-content: stretch;
		justify-content: flex-start;
		box-sizing: border-box;
		min-height: 0px;
		min-width: 0px;
		overflow: hidden;
	}
	/* #endif */
	
	.ux-form-item {
		padding: 8px 0;
		min-height: 50px;
		display: flex;
		flex-direction: column;

		&__wrap {
			flex: 1;
			display: flex;
		}

		&__label {
			display: flex;
			flex-direction: row;
			justify-content: flex-start;
			align-items: center;

			&--text {
				font-size: 15px;
			}
		}

		&__star {
			margin-right: 5px;
			font-size: 16px;
			color: red;
		}

		&__content {
			// flex: 1;
			display: flex;
		}

		&__error {
			margin-top: 5px;

			&--text {
				color: red;
			}
		}
	}
</style>