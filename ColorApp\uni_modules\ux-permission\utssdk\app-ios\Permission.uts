import { UxRequestPermissionsOptions, UxOpenAppAuthorizeSettingOptions } from "../interface";

import { URL } from "Foundation";
import { UIApplication } from "UIKit";
import { CLAuthorizationStatus, CLLocationManager } from "CoreLocation";
import { UNAuthorizationOptions, UNAuthorizationStatus, UNNotificationSettings, UserNotifications } from "UserNotifications"
import { AVAuthorizationStatus, AVCaptureDevice, AVMediaType } from "AVFoundation";
import { PHAccessLevel, PHAuthorizationStatus, PHPhotoLibrary } from "Photos";
import { CBCentralManager, CBManagerAuthorization } from "CoreBluetooth"
import { CNContactStore, CNEntityType } from "Contacts";
import { EKEventStore , EKEntityType, EKAuthorizationStatus } from "EventKit";
import { AVAudioSession } from "AVFAudio";

export default class Permission {
	
	getPermissions(options : UxRequestPermissionsOptions): string[] {
		let permissions = [] as string[]
		
		if(options.permissions != null) {
			permissions = options.permissions!
		}
		
		if(options.name == 'network') {
			permissions.push('network')
		} else if(options.name == 'location') {
			permissions.push('location')
		} else if(options.name == 'push') {
			permissions.push('push')
		} else if(options.name == 'camera') {
			permissions.push('camera')
		} else if(options.name == 'photo') {
			permissions.push('photo')
		} else if(options.name == 'bluetooth') {
			permissions.push('bluetooth')
		} else if(options.name == 'microphone') {
			permissions.push('microphone')
		} else if(options.name == 'calendar') {
			permissions.push('calendar')
		}  else if(options.name == 'contact') {
			permissions.push('contact')
		} else if(options.name == 'sms') {
			
		} else if(options.name == 'phone') {
			permissions.push('phone')
		} else if(options.name == 'phone_state') {
			
		}
		
		return permissions
	}
	
	openAppAuthorizeSetting(options : UxOpenAppAuthorizeSettingOptions) {
		let settings = new URL(string = "app-settings:")
		UIApplication.shared.openURL(settings!)
	}

	requestPermissions(options : UxRequestPermissionsOptions) {
		let permissions = this.getPermissions(options)
		
		for (let i = 0; i < permissions.length; i++) {
			let permission = permissions[i]
			
			if(permission == 'network') {
				this.requestNetwork(options, permission)
			} else if(permission == 'location') {
				this.requestLocation(options, permission)
			} else if(permission == 'push') {
				this.requestPush(options, permission)
			} else if(permission == 'camera') {
				this.requestCamera(options, permission)
			} else if(permission == 'photo') {
				this.requestPhoto(options, permission)
			} else if(permission == 'bluetooth') {
				this.requestBluetooth(options, permission)
			} else if(permission == 'microphone') {
				this.requestMicrophone(options, permission)
			} else if(permission == 'calendar') {
				this.requestCalendar(options, permission)
			} else if(permission == 'contact') {
				this.requestContact(options, permission)
			} else if(permission == 'sms') {
				this.requestSms(options, permission)
			} else if(permission == 'phone') {
				this.requestPhone(options, permission)
			} else if(permission == 'phone_state') {
				this.requestPhoneState(options, permission)
			}
			
			break
		}
	}
	
	requestNetwork(options : UxRequestPermissionsOptions, permission: string) {
		options.complete?.(true, [permission], true)
	}
	
	requestLocation(options : UxRequestPermissionsOptions, permission: string) {
		var status = CLLocationManager.authorizationStatus();
		if(status == CLAuthorizationStatus.authorizedAlways || status == CLAuthorizationStatus.authorizedWhenInUse) {
			options.complete?.(true, [permission], true)
		} else {
			let locationManager = new CLLocationManager()
			locationManager.requestWhenInUseAuthorization()
		}
	}
	
	requestPush(options : UxRequestPermissionsOptions, permission: string) {
		let center = UserNotifications.UNUserNotificationCenter.current()
		center.getNotificationSettings(completionHandler = (settings: UNNotificationSettings) => {
			let status = settings.authorizationStatus == UNAuthorizationStatus.authorized
			if(UTSiOS.available("iOS 12.0, *")) {
				if(!status) {
					status = settings.authorizationStatus == UNAuthorizationStatus.provisional
				}
			} else if(UTSiOS.available("iOS 14.0, *")) {
				if(!status) {
					status = settings.authorizationStatus == UNAuthorizationStatus.ephemeral
				}
			}
			if(status) {
				options.complete?.(true, [permission], true)
			} else {
				center.requestAuthorization(options = [UNAuthorizationOptions.alert, UNAuthorizationOptions.sound, UNAuthorizationOptions.badge], completionHandler = (granted: boolean, _: any) => {
					if(granted) {
						// 权限授权后，注册远程通知
						DispatchQueue.main.async(execute=():void => {
							UIApplication.shared.registerForRemoteNotifications()
						})
						
						options.complete?.(true, [permission], true)
					} else {
						options.complete?.(false, [], false)
					}
				})
			}
		})
	}
	
	requestCamera(options : UxRequestPermissionsOptions, permission: string) {
		let status = AVCaptureDevice.authorizationStatus(for = AVMediaType.video)
		if(status == AVAuthorizationStatus.authorized) {
			options.complete?.(true, [permission], true)
		} else {
			 AVCaptureDevice.requestAccess(for = AVMediaType.video, completionHandler = (granted: boolean) => {
				 if (granted) {
				   options.complete?.(true, [permission], true)
				 } else {
				   options.complete?.(false, [], true)
				 }
			 })
		}
	}
	
	requestPhoto(options : UxRequestPermissionsOptions, permission: string) {
		let status = PHPhotoLibrary.authorizationStatus()
		if(status == PHAuthorizationStatus.authorized) {
			options.complete?.(true, [permission], true)
		} else {
			if(UTSiOS.available("iOS 14.0, *")) {
				PHPhotoLibrary.requestAuthorization(for = PHAccessLevel.readWrite, handler = (status: PHAuthorizationStatus) => {
					if (status == PHAuthorizationStatus.authorized) {
						options.complete?.(true, [permission], true)
					} else {
						options.complete?.(false, [], true)
					}
				})
			} else {
				PHPhotoLibrary.requestAuthorization((status: PHAuthorizationStatus) => {
					if (status == PHAuthorizationStatus.authorized) {
						options.complete?.(true, [permission], true)
					} else {
						options.complete?.(false, [], true)
					}
				})
			}
			
		}
	}
	
	requestBluetooth(options : UxRequestPermissionsOptions, permission: string) {
		if(UTSiOS.available("iOS 14.0, *")) {
			let bluetoothManager = new CBCentralManager()
			if(bluetoothManager.authorization == CBManagerAuthorization.allowedAlways) {
				options.complete?.(true, [permission], true)
			} else {
				options.complete?.(false, [], true)
			}
		} else {
			options.complete?.(true, [permission], true)
		}
	}
	
	requestMicrophone(options : UxRequestPermissionsOptions, permission: string) {
		let status = AVCaptureDevice.authorizationStatus(for = AVMediaType.audio)
		if(status == AVAuthorizationStatus.authorized) {
			options.complete?.(true, [permission], true)
		} else {
			AVAudioSession.sharedInstance().requestRecordPermission((granted: boolean) => {
				if (granted) {
				  options.complete?.(true, [permission], true)
				} else {
				  options.complete?.(false, [], true)
				}
			})
		}
	}
	
	requestCalendar(options : UxRequestPermissionsOptions, permission: string) {
		options.complete?.(false, [], true)
	}
	
	requestContact(options : UxRequestPermissionsOptions, permission: string) {
		let contactStore = new CNContactStore()
		contactStore.requestAccess(for = CNEntityType.contacts, completionHandler = (granted: boolean, _: any) => {
			if (granted) {
			  options.complete?.(true, [permission], true)
			} else {
			  options.complete?.(false, [], true)
			}
		})
	}
	
	requestSms(options : UxRequestPermissionsOptions, permission: string) {
		options.complete?.(true, [permission], true)
	}
	
	requestPhone(options : UxRequestPermissionsOptions, permission: string) {
		options.complete?.(true, [permission], true)
	}
	
	requestPhoneState(options : UxRequestPermissionsOptions, permission: string) {
		options.complete?.(true, [permission], true)
	}
}