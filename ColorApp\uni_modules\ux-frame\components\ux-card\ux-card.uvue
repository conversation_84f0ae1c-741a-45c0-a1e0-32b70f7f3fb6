<template>
	<view class="ux-card" :style="[style, xstyle]">
		<ux-blur v-if="showBlur" :radius="blurRadius" :color="blurColor" class="ux-card__blur" :style="blurStyle"></ux-blur>
		<view class="ux-card__body" :style="bodyStyle">
			<view v-if="title != ''" class="ux-card__body__title" :style="titlePadding">
				<view v-if="icon != ''" class="ux-card__body__title--icon">
					<ux-icon :type="icon" :size="iconSize" :color="iconColor" :custom-family="customFamily" :xstyle="[iconStyle]"></ux-icon>
				</view>
				<text class="ux-card__body__title--text" :style="[_titleStyle, titleStyle]">{{ title }}</text>
				<slot name="subtitle">
					<text v-if="subtitle != ''" class="ux-card__body__title--subtitle" :style="[_subtitleStyle, subtitleStyle]">{{ subtitle }}</text>
				</slot>
			</view>
			
			<view :class="`ux-card__body__${direction}`" :style="rowStyle">
				<slot></slot>
			</view>
		</view>
	</view>
</template>

<script setup>
	
	/**
	 * Card 卡片
	 * @description 支持图标、标题、副标题及样式自定义，支持内边距和外边距设置，可显示阴影和边框
	 * @demo pages/component/card.uvue
	 * @tutorial https://www.uxframe.cn/component/card.html
	 * @property {String}			direction=[row|column]				String | 布局方向
	 * @value row 水平
	 * @value column 垂直
	 * @property {String}			title								String | 标题
	 * @property {Any}				size								Any | 标题大小 (默认 $ux.Conf.fontSize)
	 * @property {String}			color								String | 标题颜色 (默认 $ux.Conf.fontColor)
	 * @property {String} 			darkColor=[none|auto|color]			String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Boolean}			bold = [true|false]					Boolean | 标题加粗 (默认 false)
	 * @property {String}			titleStyle							String | 标题样式
	 * @property {String}			subtitle							String | 副标题
	 * @property {String}			subtitleColor						String | 副标题颜色 (默认 $ux.Conf.secondaryColor)
	 * @property {Any}				subtitleSize						Any | 副标题大小 (默认 $ux.Conf.fontColor.small)
	 * @property {String}			subtitleStyle						String | 副标题样式
	 * @property {String}			icon								String | 标题图标
	 * @property {String}			iconColor							String | 标题图标颜色 (默认 $ux.Conf.fontColor)
	 * @property {Any}				iconSize							Any | 标题图标大小 (默认 $ux.Conf.fontSize)
	 * @property {String}			iconStyle							String | 标题图标样式
	 * @property {String}			customFamily						String | 自定义字体family
	 * @property {String}			background							String | 背景色 (默认 $ux.Conf.backgroundColor)
	 * @property {String} 			backgroundDark=[none|auto|color]	String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色 (默认 auto)
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Boolean}			showBlur = [true|false]				Boolean | 开启模糊背景 (默认 false )
	 * @property {Number}			blurRadius							Number | 模糊半径 (默认 10 )
	 * @property {String}			blurColor							String | 模糊颜色  (默认 rgba(255, 255, 255, 0.3) )
	 * @property {Boolean}			shadow = [true|false]				Boolean | 显示阴影 (默认 false )
	 * @property {Boolean}			border = [true|false]				Boolean | 显示边框 (默认 false )
	 * @property {String}			borderColor							String | 边框颜色  (默认 $ux.Conf.borderColor )
	 * @property {String} 			borderDarkColor=[none|auto|color]	String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色 (默认 auto)
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Any}				radius								Any | 圆角 (默认 $ux.Conf.radius)
	 * @property {Any}				padding								Any | 内部水平边距 (默认 $ux.Conf.padding)
	 * @property {Any}				margin								Any | 边距 (默认 $ux.Conf.margin)
	 * @property {Array}			xstyle								Array<any> | 自定义样式
	 * <AUTHOR>
	 * @date 2023-12-31 07:39:25
	 */
	
	import { $ux } from '../../index'
	import { useTitleColor, useSubTitleColor, useFontSize, useForegroundColor, useBorderColor, useHmargin, useHpadding, useRadius } from '../../libs/use/style.uts'
	
	defineOptions({
		name: 'ux-card'
	})
	
	const emit = defineEmits(['click'])
	
	const props = defineProps({
		direction: {
			type: String,
			default: 'row'
		},
		title: {
			type: String,
			default: ''
		},
		size: {
			default: 0
		},
		color: {
			type: String,
			default: ''
		},
		darkColor: {
			type: String,
			default: ''
		},
		bold: {
			type: Boolean,
			default: false
		},
		titleStyle: {
			type: String,
			default: ''
		},
		subtitle: {
			type: String,
			default: ''
		},
		subtitleColor: {
			type: String,
			default: ''
		},
		subtitleSize: {
			default: 0
		},
		subtitleStyle: {
			type: String,
			default: ''
		},
		icon: {
			type: String,
			default: ''
		},
		iconColor: {
			type: String,
			default: ''
		},
		iconSize: {
			default: 0
		},
		iconStyle: {
			type: String,
			default: ''
		},
		customFamily: {
			type: String,
			default: ''
		},
		background: {
			type: String,
			default: ''
		},
		backgroundDark: {
			type: String,
			default: ''
		},
		showBlur: {
			type: Boolean,
			default: false
		},
		blurRadius: {
			type: Number,
			default: 10
		},
		blurColor: {
			type: String,
			default: 'rgba(255, 255, 255, 0.3)'
		},
		shadow: {
			type: Boolean,
			default: false
		},
		border: {
			type: Boolean,
			default: false
		},
		borderColor: {
			type: String,
			default: ''
		},
		borderDarkColor: {
			type: String,
			default: 'auto'
		},
		radius: {
			default: -1
		},
		padding: {
			default: -1
		},
		margin: {
			default: -1
		},
		xstyle: {
			type: Array as PropType<Array<any>>,
			default: (): any[] => [] as any[]
		}
	})
	
	const margin = computed((): number => {
		return useHmargin($ux.Util.getPx(props.margin), 0)
	})
	
	const padding = computed((): number => {
		return useHpadding($ux.Util.getPx(props.padding), 0)
	})
	
	const backgroundColor = computed((): string => {
		return useForegroundColor(props.background, props.backgroundDark)
	})
	
	const borderColor = computed((): string => {
		return useBorderColor(props.borderColor, props.borderDarkColor)
	})
	
	const radius = computed((): number => {
		return useRadius($ux.Util.getPx(props.radius), 0)
	})
	
	const fontSize = computed((): number => {
		return useFontSize($ux.Util.getPx(props.size), 0)
	})
	
	const fontColor = computed((): string => {
		return useTitleColor(props.color, props.darkColor)
	})
	
	const subtitleSize = computed((): number => {
		return useFontSize($ux.Util.getPx(props.subtitleSize), 1)
	})
	
	const subtitleColor = computed((): string => {
		return useSubTitleColor(props.subtitleColor, '')
	})
	
	const style = computed((): Map<string, any> => {
		let m = $ux.Util.addUnit(margin.value)
		
		let css = new Map<string, any>()
		
		css.set('background-color', 'transparent')
		css.set('margin', `${m} ${m} 0 ${m}`)
		
		return css
	})
	
	const bodyStyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('background-color', props.showBlur? 'transparent' : backgroundColor.value)
		
		if(props.shadow && !$ux.Conf.darkMode.value) {
			css.set('box-shadow', `0 0 10px rgba(0, 0, 0, 0.5)`)
		}
		
		if(props.border) {
			css.set('border', `1rpx solid ${borderColor.value}`)
		} else {
			css.set('border', `none`)
		}
		
		css.set('border-radius', $ux.Util.addUnit(radius.value))
		
		return css
	})
	
	const blurStyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('position', 'absolute')
		css.set('top', 0)
		css.set('left', 0)
		css.set('right', 0)
		css.set('bottom', 0)
		css.set('width', '100%')
		css.set('height', '100%')
		css.set('border-radius', $ux.Util.addUnit(radius.value))
		
		return css
	})
	
	const rowStyle = computed(():  Map<string, any> => {
		let css = new Map<string, any>()
		
		let _padding = $ux.Util.addUnit(padding.value)
		css.set('padding', `${props.title != ''? 0 : _padding} ${_padding} ${_padding} ${_padding}`)
		
		return css
	})
	
	const titlePadding = computed((): string => {
		return `padding: 15px 15px 0 15px`
	})
	
	const _titleStyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('font-size', $ux.Util.addUnit(fontSize.value))
		css.set('color', fontColor.value)
		css.set('font-weight', props.bold?'bold':'normal')
		
		return css
	})
	
	const _subtitleStyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('font-size', $ux.Util.addUnit(subtitleSize.value))
		css.set('color', subtitleColor.value)
		
		return css
	})
	
</script>

<style lang="scss">

	.ux-card {
		display: flex;
		flex-direction: column;
		justify-content: center;
		position: relative;
			
		&__blur {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			
			&--body {
				flex: 1;
			}
		}
		
		&__body {
			flex: 1;
			display: flex;
			flex-direction: column;
			justify-content: flex-start;
			align-items: flex-start;
			
			&__title {
				width: 100%;
				margin-bottom: 18px;
				display: flex;
				flex-direction: row;
				justify-content: flex-start;
				align-items: center;
				
				&--icon {
					margin-right: 10px;
				}
				
				&--text {
					color: #333333;
					font-size: 14px;
				}
				
				&--subtitle {
					color: gray;
					font-size: 10px;
					margin-left: 5px;
				}
			}
			
			&__row {
				width: 100%;
				display: flex;
				flex-direction: row;
				justify-content: flex-start;
				flex-wrap: wrap;
			}
			
			&__column {
				width: 100%;
				display: flex;
				flex-direction: column;
			}
		}
	}
</style>