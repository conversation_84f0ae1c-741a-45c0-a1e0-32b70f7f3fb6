<template>
	<view class="container">
		<view class="header">
			<text class="title">FormBuilder 组件测试</text>
		</view>
		
		<view class="content">
			<FormBuilder
				:modelValue="formData"
				:fields="fields"
				:rules="rules"
				:cols="1"
				@change="handleFormChange"
				@fieldChange="handleFieldChange"
			>
				<template #top_form_item>
					<view class="form-header">
						<text class="form-title">用户信息表单</text>
						<text class="form-description">请填写完整的用户信息</text>
					</view>
				</template>
				
				<view class="form-actions">
					<ux-button type="primary" @click="handleSubmit">提交</ux-button>
					<ux-button @click="handleReset">重置</ux-button>
				</view>
			</FormBuilder>
		</view>
		
		<view class="debug-info">
			<text class="debug-title">表单数据：</text>
			<text class="debug-content">{{ JSON.stringify(formData, null, 2) }}</text>
		</view>
	</view>
</template>

<script setup lang="uts">
	import { ref } from "vue"
	import FormBuilder from "../components/FormBuilder.uvue"
	
	// 表单数据
	const formData = ref<UTSJSONObject>({
		name: '',
		email: '',
		phone: '',
		gender: '',
		birthday: '',
		address: '',
		description: '',
		isVip: false,
		hobbies: []
	} as UTSJSONObject)
	
	// 表单验证规则
	const rules = ref<UTSJSONObject>({
		name: [
			{ required: true, message: '请输入姓名' }
		],
		email: [
			{ required: true, message: '请输入邮箱' },
			{ pattern: '^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$', message: '邮箱格式不正确' }
		],
		phone: [
			{ required: true, message: '请输入手机号' },
			{ pattern: '^1[3-9]\\d{9}$', message: '手机号格式不正确' }
		]
	} as UTSJSONObject)
	
	// 字段配置
	const fields = ref<UTSJSONObject[]>([
		// 基本信息分组
		{
			Id: 'name',
			Name: 'name',
			Title: '姓名',
			Type: 'Text',
			InputType: 'text',
			Required: true,
			Placeholder: '请输入您的姓名',
			MaxLength: 50,
			Hide: false,
			Disabled: false,
			Readonly: false,
			Group: '基本信息',
			GroupClass: 'card'
		},
		{
			Id: 'email',
			Name: 'email',
			Title: '邮箱',
			Type: 'Text',
			InputType: 'email',
			Required: true,
			Placeholder: '请输入您的邮箱地址',
			MaxLength: 100,
			Hide: false,
			Disabled: false,
			Readonly: false,
			Group: '基本信息',
			GroupClass: 'card'
		},
		{
			Id: 'phone',
			Name: 'phone',
			Title: '手机号',
			Type: 'Text',
			InputType: 'mobile',
			Required: true,
			Placeholder: '请输入您的手机号',
			MaxLength: 11,
			Hide: false,
			Disabled: false,
			Readonly: false,
			Group: '基本信息',
			GroupClass: 'card'
		},
		{
			Id: 'gender',
			Name: 'gender',
			Title: '性别',
			Type: 'RadioGroup',
			Required: false,
			DataSource: [
				{ id: 'male', text: '男' },
				{ id: 'female', text: '女' }
			],
			Hide: false,
			Disabled: false,
			Group: '基本信息',
			GroupClass: 'card'
		},
		// 详细信息分组
		{
			Id: 'birthday',
			Name: 'birthday',
			Title: '生日',
			Type: 'Date',
			Required: false,
			Hide: false,
			Disabled: false,
			Group: '详细信息',
			GroupClass: 'border'
		},
		{
			Id: 'address',
			Name: 'address',
			Title: '地址',
			Type: 'Text',
			Required: false,
			Placeholder: '请输入您的地址',
			MaxLength: 200,
			Hide: false,
			Disabled: false,
			Readonly: false,
			Group: '详细信息',
			GroupClass: 'border'
		},
		{
			Id: 'description',
			Name: 'description',
			Title: '个人描述',
			Type: 'TextArea',
			Required: false,
			Placeholder: '请简单介绍一下自己',
			MaxLength: 500,
			Hide: false,
			Disabled: false,
			Readonly: false,
			Group: '详细信息',
			GroupClass: 'border'
		},
		// 偏好设置分组
		{
			Id: 'isVip',
			Name: 'isVip',
			Title: 'VIP会员',
			Type: 'Switch',
			Required: false,
			Hide: false,
			Disabled: false,
			Group: '偏好设置',
			GroupClass: 'background'
		},
		{
			Id: 'hobbies',
			Name: 'hobbies',
			Title: '兴趣爱好',
			Type: 'CheckboxGroup',
			Required: false,
			DataSource: [
				{ id: 'reading', text: '阅读' },
				{ id: 'music', text: '音乐' },
				{ id: 'sports', text: '运动' },
				{ id: 'travel', text: '旅行' },
				{ id: 'cooking', text: '烹饪' }
			],
			Hide: false,
			Disabled: false,
			Group: '偏好设置',
			GroupClass: 'background'
		}
	] as UTSJSONObject[])
	
	// 事件处理
	const handleFormChange = (data: UTSJSONObject) => {
		console.log('表单数据变化:', data)
	}
	
	const handleFieldChange = (data: UTSJSONObject) => {
		console.log('字段变化:', data)
	}
	
	const handleSubmit = () => {
		console.log('提交表单:', formData.value)
		uni.showToast({
			title: '提交成功',
			icon: 'success'
		})
	}
	
	const handleReset = () => {
		formData.value = {
			name: '',
			email: '',
			phone: '',
			gender: '',
			birthday: '',
			address: '',
			description: '',
			isVip: false,
			hobbies: []
		} as UTSJSONObject
		
		uni.showToast({
			title: '重置成功',
			icon: 'success'
		})
	}
</script>

<style lang="scss">
	.container {
		padding: 20px;
		background-color: #f5f5f5;
		min-height: 100vh;
	}
	
	.header {
		margin-bottom: 20px;
		text-align: center;
		
		.title {
			font-size: 24px;
			font-weight: bold;
			color: #333;
		}
	}
	
	.content {
		background-color: #fff;
		border-radius: 8px;
		padding: 20px;
		margin-bottom: 20px;
	}
	
	.form-header {
		margin-bottom: 24px;
		text-align: center;
		
		.form-title {
			display: block;
			font-size: 20px;
			font-weight: 600;
			color: #333;
			margin-bottom: 8px;
		}
		
		.form-description {
			font-size: 14px;
			color: #666;
		}
	}
	
	.form-actions {
		display: flex;
		justify-content: center;
		gap: 16px;
		margin-top: 24px;
		padding-top: 24px;
		border-top: 1px solid #e8e8e8;
	}
	
	.debug-info {
		background-color: #fff;
		border-radius: 8px;
		padding: 20px;
		
		.debug-title {
			display: block;
			font-size: 16px;
			font-weight: 600;
			color: #333;
			margin-bottom: 12px;
		}
		
		.debug-content {
			font-family: monospace;
			font-size: 12px;
			color: #666;
			white-space: pre-wrap;
			word-break: break-all;
		}
	}
</style>