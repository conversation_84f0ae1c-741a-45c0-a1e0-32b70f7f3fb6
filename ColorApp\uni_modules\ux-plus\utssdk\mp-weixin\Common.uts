import { UxOpenURLOptions } from '../interface.uts'
import { UxMakePhoneCallOptions } from '../interface.uts'
import { UxVibrateOptions } from '../interface.uts'
import { UxOpenWebOptions } from '../interface.uts'
import { UxPlusSuccessCallbackImpl, UxPlusErrorCallbackImpl } from '../unierror.uts'
import * as OpenWeb from "./OpenWeb.uts"

export default class Common {
	
	hideKeyboard() {
		uni.hideKeyboard()
	}
	
	setGray(gray: number) {
		// document.body.style.filter = `grayscale(${gray * 100}%)`
	}
	
	openURL(options : UxOpenURLOptions) {
		// window.location.href = options.url
		
		// const res = new UxPlusSuccessCallbackImpl('openURL:success')
		// options.success?.(res)
		// options.complete?.(res)
	}
	
	openWeb(options : UxOpenWebOptions) {
		OpenWeb.show(options)
	}
	
	makePhoneCall(options : UxMakePhoneCallOptions) {
		uni.makePhoneCall({
			phoneNumber: options.phoneNumber,
			success: () => {
				const res = new UxPlusSuccessCallbackImpl('makePhoneCall:ok')
				options.success?.(res)
				options.complete?.(res)
			}
		})
	}
	
	vibrate(options : UxVibrateOptions) {
		
	}
}
