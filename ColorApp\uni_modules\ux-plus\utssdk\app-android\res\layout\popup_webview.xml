<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="0dp"
    android:orientation="vertical"
    android:layout_weight="0.98">
	
	<RelativeLayout
	     android:layout_width="match_parent"
	     android:layout_height="wrap_content"
	     android:padding="10dp"
         android:background="#FFFFFF">

	     <TextView
	         android:id="@+id/title"
	         android:layout_width="wrap_content"
	         android:layout_height="wrap_content"
	         android:text=""
	         android:textSize="18sp"
			 android:textStyle="bold"
			 android:textColor="#000000"
	         android:layout_centerHorizontal="true"
	         android:layout_centerVertical="true" />

	     <Button
	         android:id="@+id/btn_close"
	         android:layout_width="22dp"
	         android:layout_height="22dp"
	         android:layout_alignParentRight="true"
	         android:layout_centerVertical="true"
			 android:background="@drawable/ic_close"/>

	 </RelativeLayout>
		
    <WebView
        android:id="@+id/webview"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</LinearLayout>