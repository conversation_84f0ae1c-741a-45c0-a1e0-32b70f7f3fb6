<template>
	<view>
	</view>
</template>

<script lang="uts">
	/**
	* 精灵动画组件
	* @demo pages/component/sprite.uvue
	* @tutorial https://www.uxframe.cn/component/sprite.html
	* @property {Array} 			frames					string[] | 精灵图集
	* @property {Number} 			duration				Number | 每帧周期 (默认 100)
	* @property {Boolean} 			play					Boolean | 是否播放 (默认 false)
	* @property {Boolean} 			loop					Boolean | 循环播放 (默认 false)
	* @property {Boolean} 			autoplay				Boolean | 自动播放 (默认 false)
	* @event {Function} 			start 					Function | 开始时触发
	* @event {Function} 			end 					Function | 结束时触发
	* <AUTHOR>
	* @date 2025-04-07 21:10:21
	*/
   
	import ImageView from "android.widget.ImageView";
	import File from "java.io.File"
	import Bitmap from 'android.graphics.Bitmap';
	import BitmapFactory from "android.graphics.BitmapFactory"
	import BitmapDrawable from "android.graphics.drawable.BitmapDrawable"
	import AnimationDrawable from "android.graphics.drawable.AnimationDrawable"
	import InputStream from 'java.io.InputStream';
	
	import { PropType } from "vue";

	export default {
		name: 'ux-sprite',
		emits: ['start', 'end'],
		props: {
			frames: {
				type: Array as PropType<string[]>,
				default: []
			},
			duration: {
				type: Number,
				default: 100
			},
			play: {
				type: Boolean,
				default: false
			},
			loop: {
				type: Boolean,
				default: false
			},
			autoplay: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				imageView: null as ImageView | null,
				animationDrawable: null as AnimationDrawable | null,
				durations: 0
			}
		},
		watch: {
			frames: {
				immediate: true,
				handler(newVal : string[], oldVal : string[]) {
					this.setResource()
				}
			},
			play: {
				immediate: true,
				handler(newVal : boolean, oldVal : boolean) {
					if (newVal) {
						this.playAnim()
					} else {
						this.stopAnim()
					}
				}
			},
			autoplay: {
				immediate: false,
				handler(newVal : boolean, oldVal : boolean) {
					if (newVal) {
						this.playAnim()
					}
				}
			}
		},
		expose: [],
		methods: {
			// 播放
			playAnim() {
				this.$emit('start')
				this.animationDrawable?.start()
				this.listenPlayEnd()
			},
			// 停止
			stopAnim() {
				this.animationDrawable?.stop()
			},
			// 监听播放结束
			listenPlayEnd() {
				setTimeout(() => {
					this.animationDrawable?.stop()
					this.$emit('end')

					if (this.loop) {
						this.playAnim()
					}
				}, this.durations as Int)
			},
			// 设置动画资源
			setResource() {
				try {
					let frames = this.frames
					
					if (frames.length == 0) {
						return
					}

					let animationDrawable = new AnimationDrawable()
					
					this.durations = 0
					frames.forEach(src => {
						// uni-app x 正式打包会放在asset中，需要特殊处理
						let path = UTSAndroid.getResourcePath(src)
						if(path.startsWith("/android_asset")){
							let bitmap = this.getBitmapFromAsset(path.substring(15))
							let drawable = new BitmapDrawable($androidContext!.resources, bitmap)
							animationDrawable.addFrame(drawable, this.duration as Int)
							this.durations += this.duration
						} else {
							let file = new File(path)
							if (file.exists()) {
								let bitmap = BitmapFactory.decodeFile(path)
								let drawable = new BitmapDrawable($androidContext!.resources, bitmap)
								animationDrawable.addFrame(drawable, this.duration as Int)
								this.durations += this.duration
							}
						}
					})

					this.animationDrawable = animationDrawable
					this.animationDrawable?.setOneShot(true)
					this.imageView?.setImageDrawable(animationDrawable)
				} catch (e) {
					//TODO handle the exception
				}
			},
			getBitmapFromAsset(filePath: string): Bitmap | null {
			    var assetManager = $androidContext!.getAssets();
			    var inputStream: InputStream | null = null;
			    var bitmap: Bitmap | null = null;
			    
			    try {
			        inputStream = assetManager.open(filePath)
			        bitmap = BitmapFactory.decodeStream(inputStream)
			    } catch (e) {
			        e.printStackTrace();
			    } finally {
			        if (inputStream != null) {
			            try {
			                inputStream?.close()
			            } catch (e) {
			                e.printStackTrace()
			            }
			        }
			    }
				
			    return bitmap;
			}
		},
		NVLoad() : ImageView {
			const image = new ImageView($androidContext!);
			this.imageView = image;
			return image;
		},
	}
</script>

<style>

</style>