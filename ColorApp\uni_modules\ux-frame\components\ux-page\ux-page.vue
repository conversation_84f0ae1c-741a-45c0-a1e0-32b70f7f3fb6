<template>
	<view ref="pageRef" class="ux-page" :style="style">
		<image v-if="backgroundImage != ''" class="ux-page__image" :src="backgroundImage" mode="heightFit"></image>
		<ux-placeholder :navbar="navbar"></ux-placeholder>
		<slot></slot>
		<ux-placeholder :tabbar="tabbar"></ux-placeholder>
	</view>
</template>

<script setup lang="ts">
	/**
	 * 页面容器
	 * @demo pages/component/page.uvue
	 * @tutorial https://www.uxframe.cn/component/page.html
	 * @property {Boolean}			navbar = [true|false]										Boolean | 标题栏高度占位 (默认 false)
	 * @property {Boolean}			tabbar = [true|false]										Boolean | 底部导航栏高度占位 (默认 false)
	 * @property {Array}			colors														String[] | 背景色 多个渐变
	 * @property {String}			backgroundImage												String | 背景图 优先级高于背景色
	 * @property {String}			backgroundClip = [border-box|padding-box|content-box]		String | 背景图裁剪
	 * @value border-box
	 * @value padding-box
	 * @value content-box
	 * @property {Boolean}			stack		Boolean | 层叠 (默认 false)
	 * <AUTHOR>
	 * @date 2023-10-09 00:35:45
	 */
	
	import { ref, computed, onMounted, PropType } from 'vue'
	import { $ux } from '../../index'
	
	defineOptions({
		name: 'ux-page'
	})
	
	const props = defineProps({
		navbar: {
			type: Boolean,
			default: false,
		},
		tabbar: {
			type: Boolean,
			default: false,
		},
		colors: {
			type: Array as PropType<string[]>,
			default: (): string[] => [],
		},
		backgroundImage: {
			type: String,
			default: '',
		},
		backgroundClip: {
			type: String,
			default: '',
		},
		stack: {
			type: Boolean,
			default: false,
		}
	})
	
	const pageRef = ref(null)

	const colors = computed((): string[] => {
		return props.colors
	})
	
	const backgroundColor = computed((): string[] => {
		if(colors.value.length == 0) {
			let color = $ux.Conf.darkMode.value ? $ux.Conf.backgroundColor.dark.value : $ux.Conf.backgroundColor.color.value
			return ['background-color', color]
		} else if(colors.value.length == 1) {
			return ['background-color', colors.value[0]]
		} else {
			return ['background', `linear-gradient(to bottom, ${colors.value.join(',')})`]
		}
	})
	
	const style = computed(() => {
		let css = {}
		
		if(props.backgroundImage != '') {
			// TODO 暂不支持
			// css['background-image'] = props.backgroundImage
			
			if(props.backgroundClip != '') {
				css['background-clip'] = props.backgroundClip
			}
			
		} else {
			css[backgroundColor.value[0]] = backgroundColor.value[1]
		}
		
		css['transform'] = props.stack ? 'translateY(30px) scale(0.98)' : 'translateY(0) scale(1)'
		
		return css
	})
	
	onMounted(() => {
		// TODO 防止深色模式下闪白
		// pageRef.value?.style[backgroundColor.value[0]] = backgroundColor.value[1]
		
		// 全局背景色
		$ux.Conf.setGlobalBackgroundColor()
		
		// #ifdef APP-ANDROID
		// android灰色模式只能作用于当前页
		if($ux.Conf.grayMode.value) {
			setTimeout(() => {
				$ux.Conf.setGrayMode(true)
			}, 100);
		}
		// #endif
	})
	
</script>

<style lang="scss" scoped>
	
	.ux-page {
		position: relative;
		display: flex;
		flex-direction: column;
		flex: 1;
		/* #ifndef APP-NVUE */
		width: 100%;
		height: 100%;
		/* #endif */
		transform: translateY(0);
		transition-property: transform;
		transition-duration: 300ms;
		transform-origin: bottom;
	}

	.ux-page__image {
		position: absolute;
		top: 0;
		bottom: 0;
		right: 0;
		left: 0;
		width: 100%;
		height: 100%;
	}
</style>