<template>
	<scroll-view :id="myId" class="ux-upload" :style="style" direction="horizontal" :show-scrollbar="false">
		<view class="ux-upload__wrap" :style="wrapStyle">
			<view v-if="frontBtn && files.length < count" class="ux-upload__btn" :style="btnStyle" @click="onChoose()">
				<slot name="upload">
					<ux-icon type="plus" color="#a9a9a9" :size="25"></ux-icon>
				</slot>
			</view>
			<view class="ux-upload__file" :style="[fileStyle, fileGutter(index)]" v-for="(file, index) in files" :key="index">
				<image class="ux-upload__file--img" :src="file.thumb" :mode="imageMode" @click="onClick(index)"></image>
				
				<view v-if="file.status == 'waiting' || file.status == 'uploading' || file.status == 'fail'" class="ux-upload__mask">
					<view class="ux-upload__mask--body">
						<text v-if="file.status == 'fail'" class="ux-upload__waiting">{{ failText }}</text>
						<text v-else-if="file.status == 'waiting'" class="ux-upload__waiting">{{ waitingText }}</text>
						<text v-else class="ux-upload__progress">{{ file.progress }}%</text>
					</view>
				</view>
				<view v-if="showDel && (!autoUpload || file.status == 'success' || file.status == 'fail')" class="ux-upload__del" @click="onDel(index)">
					<ux-icon type="close" color="white" :size="12"></ux-icon>
				</view>
			</view>
			<view v-if="!frontBtn && files.length < count" class="ux-upload__btn" :style="btnStyle" @click="onChoose()">
				<slot name="upload">
					<ux-icon type="plus" color="#a9a9a9" :size="25"></ux-icon>
				</slot>
			</view>
		</view>
	</scroll-view>
</template>

<script setup>
	/**
	* Uplaod 上传
	* @description 支持图片视频，支持删除、排序功能，支持进度显示
	* @demo pages/component/upload.uvue
	* @tutorial https://www.uxframe.cn/component/upload.html
	* @property {Array} 					list								UTSJSONObject[] | 默认列表
	* @property {String} 					name								String | 文件对应的 key , 开发者在服务器端通过这个 key 可以获取到文件二进制内容 (默认 file)
	* @property {String} 					url									String | 上传地址
	* @property {UTSJSONObject} 			header								UTSJSONObject | HTTP 请求 Header, header 中不能设置 Referer
	* @property {UTSJSONObject} 			formData							UTSJSONObject | HTTP 请求中其他额外的 form data
	* @property {Number} 					timeout								Number | 超时时间，单位 ms (默认 120000)
	* @property {Number} 					retry								Number | 重试次数 (默认 3)
	* @property {Number} 					successCode							Number | 上传成功状态码 (默认 200)
	* @property {String} 					mode=[image|video|media|file]		String | 模式 (默认 image)
	* @value image 图片
	* @value video 视频
	* @value media 媒体
	* @value file 文件
	* @property {Number} 					count								Number | 最大数量  (默认 9)
	* @property {Array} 					sizeType							String[] | original 原图，compressed 压缩图 Web不支持 (默认 ['original', 'compressed'])
	* @property {Array} 					sourceType							String[] | album 从相册选图，camera 使用相机 (默认 ['album','camera'])
	* @property {Array} 					extension 							String[] | 文件拓展名过滤 仅Web支持
	* @property {String} 					align=[left|right]					String | 对齐方向（默认 left ）
	* @value left 居左
	* @value right 居右
	* @property {Boolean} 					rows=[true|false]					Boolean | 多行（默认 true ）
	* @property {Number} 					col									Number | 列数，rows=true时生效（默认 4 ）
	* @property {Any} 						gutter								Any | 间隔 默认 5 ）
	* @property {Any} 						width								Any | 文件宽，rows=false时生效（默认 65 ）
	* @property {Any} 						height								Any | 文件高，rows=false时生效（默认 65 ）
	* @property {Any} 						radius								Any | 圆角（默认 8 ）
	* @property {String} 					background							String | 按钮背景色
	* @property {String} 					backgroundDark=[none|auto|color]	String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	* @value none 不显示
	* @value auto 自动适配深色模式
	* @value color 其他颜色
	* @property {String} 					borderColor							String | 按钮边框色
	* @property {String} 					borderColorDark=[none|auto|color]	String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	* @value none 不显示
	* @value auto 自动适配深色模式
	* @value color 其他颜色
	* @property {String} 					waitingText							String | 等待上传文案（ 默认 等待上传 ）
	* @property {String} 					failText							String | 等待失败文案（ 默认 上传失败 ）
	* @property {Boolean} 					frontBtn=[true|false]				Boolean | 前置上传按钮（默认 false ）
	* @property {Boolean} 					showDel=[true|false]				Boolean | 显示删除按钮（默认 true ）
	* @property {Function} 					beforeDel							Function | 删除钩子
	* @property {Function} 					beforeUpload						Function | 上传前钩子
	* @property {Function} 					afterUpload							Function | 上传完成钩子
	* @property {Boolean} 					autoUpload=[true|false]				Boolean | 自动上传（默认 true ）
	* @property {Boolean} 					disabled=[true|false]				Boolean | 禁用（默认 false ）
	* @event {Function} 					click 								Function | 点击时触发
	* @event {Function} 					change 								Function | 值改变时触发
	* @event {Function} 					error 								Function | 发生错误时触发
	* <AUTHOR>
	* @date 2024-07-18 01:33:25
	*/

	import { $ux } from '../../index'
	import { UxUploadFile } from '../../libs/types/types.uts'
	import { useColor } from '../../libs/use/style.uts'

	type UploadCallback = (data: string) => string
	type BeforeUploadCallback = (file: UxUploadFile) => UTSJSONObject | null
	type DelCallback = (index : number, file : UxUploadFile) => Promise<boolean>

	defineOptions({
		name: 'ux-upload'
	})

	const emit = defineEmits(['click', 'change', 'error', 'update:modelValue'])

	const props = defineProps({
		modelValue: {
			default: null
		},
		list: {
			type: Array as PropType<UTSJSONObject[]>,
			default: (): UTSJSONObject[] => [] as UTSJSONObject[]
		},
		name: {
			type: String,
			default: 'file'
		},
		url: {
			type: String,
			default: ''
		},
		header: {
			type: Object as PropType<UTSJSONObject>,
		},
		formData: {
			type: Object as PropType<UTSJSONObject>,
		},
		timeout: {
			type: Number,
			default: 120000
		},
		retry: {
			type: Number,
			default: 3
		},
		successCode: {
			type: Number,
			default: 200
		},
		mode: {
			type: String,
			default: 'image',
		},
		count: {
			type: Number,
			default: 9
		},
		sizeType: {
			type: Array as PropType<string[]>,
			default: (): string[] => {
				return ['original','compressed']
			}
		},
		sourceType: {
			type: Array as PropType<string[]>,
			default:(): string[] => {
				return ['album','camera']
			}
		},
		extension: {
			type: Array as PropType<string[]>,
		},
		align: {
			type: String,
			default: 'left'
		},
		rows: {
			type: Boolean,
			default: true
		},
		col: {
			type: Number,
			default: 5
		},
		gutter: {
			default: 5
		},
		width: {
			default: 65
		},
		height: {
			default: 65
		},
		radius: {
			default: 8
		},
		imageMode: {
			type: String,
			default: 'aspectFill',
		},
		frontBtn: {
			type: Boolean,
			default: false
		},
		background: {
			type: String,
			default: '#eaeaea',
		},
		backgroundDark: {
			type: String,
			default: '#d0d0d0',
		},
		borderColor: {
			type: String,
			default: '#dadada',
		},
		borderColorDark: {
			type: String,
			default: '#888888',
		},
		waitingText: {
			type: String,
			default: '等待上传',
		},
		failText: {
			type: String,
			default: '上传失败',
		},
		showDel: {
			type: Boolean,
			default: true
		},
		beforeDel: {
			type: Function as PropType<DelCallback | null>,
			default: null
		},
		beforeUpload: {
			type: Function as PropType<BeforeUploadCallback | null>,
			default: null
		},
		afterUpload: {
			type: Function as PropType<UploadCallback | null>,
			default: null
		},
		autoUpload: {
			type: Boolean,
			default: true
		},
		disabled: {
			type: Boolean,
			default: false
		}
	})
	
	let instance = getCurrentInstance()?.proxy
	
	const myId = `ux-upload-${$ux.Random.uuid()}`
	const files = ref<UxUploadFile[]>([] as UxUploadFile[])
	const uploading = ref(false)
	const totalWidth = ref(0)
	
	const gutter = computed(() => {
		return $ux.Util.getPx(props.gutter)
	})
	
	const width = computed((): number => {
		if(props.rows) {
			return Math.floor((totalWidth.value - (props.col - 1) * gutter.value) / props.col) - 1
		} else {
			return $ux.Util.getPx(props.width)
		}
	})
	
	const height = computed((): number => {
		if(props.rows) {
			return Math.floor((totalWidth.value - (props.col - 1) * gutter.value) / props.col) - 1
		} else {
			return $ux.Util.getPx(props.height)
		}
	})
	
	const backgroundColor = computed((): string => {
		return useColor(props.background, props.backgroundDark) 
	})
	
	const borderColor = computed((): string => {
		return useColor(props.borderColor, props.borderColorDark) 
	})
	
	const style = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		if(props.align == 'right') {
			css.set('justify-content', `flex-end`)
		}
		
		return css
	})
	
	const wrapStyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		if(!props.rows) {
			css.set('width', `${(width.value + gutter.value) * (files.value.length + 1)}px`)
			css.set('height', `${height.value + gutter.value * 2}px`)
			css.set('flex-wrap', `nowrap`)
		}
		
		if(props.align == 'right') {
			css.set('justify-content', `flex-end`)
		}
		
		return css
	})
	
	const fileStyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('width', `${width.value}px`)
		css.set('height', `${height.value}px`)
		css.set('radius', `${$ux.Util.getPx(props.radius)}px`)
		css.set('margin-bottom', `${gutter.value}px`)
		
		return css
	})
	
	function fileGutter(index: number): Map<string, any> {
		let css = new Map<string, any>()
		
		css.set('margin-right', `${props.count == 1 || (props.rows && (index + 1) % props.col == 0) ? 0 : gutter.value}px`)
		
		return css
	}
	
	const btnStyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('width', `${width.value}px`)
		css.set('height', `${height.value}px`)
		css.set('radius', `${$ux.Util.getPx(props.radius)}px`)
		css.set('background-color', backgroundColor.value)
		css.set('border-color', borderColor.value)
		css.set('margin-bottom', `${gutter.value}px`)
		
		if(props.frontBtn) {
			css.set('margin-right', `${gutter.value}px`)
		} else {
			let index = files.value.length - 1
			css.set('margin-left', `${props.rows && (index + 1) % props.col == 0 ? gutter.value : 0}px`)
		}
		
		return css
	})
	
	function change() {
		emit('change', files.value)
		
		if(props.count == 1) {
			emit('update:modelValue', files.value.length == 0 ? '' : files.value[0].path)
		} else {
			emit('update:modelValue', files.value.map(function (e): UTSJSONObject {
				return {
					id: e.id,
					url: e.path,
				} as UTSJSONObject
			}))
		}
	}
	
	function upload() {
		uploading.value = true
		
		let index = 0
		let f = ():void => {}
		f = ():void  => {
			if(index >= files.value.length) {
				uploading.value = false
				change()
				return
			}
			
			let path = files.value[index].path
			if(path == '') {
				index++
				f()
				return
			}
			
			let status = files.value[index].status
			if(status == 'success') {
				files.value[index].progress = 100
				files.value[index].retry = 0
				index++
				f()
				return
			}
			
			files.value[index].status = 'uploading'
			
			let formData = props.formData
			if(props.beforeUpload != null) {
				const bfun = props.beforeUpload! as BeforeUploadCallback
				formData = bfun(files.value[index])
			}
			
			const task = uni.uploadFile({
				url: props.url,
				filePath: path,
				name: props.name,
				header: props.header,
				formData: formData,
				timeout: props.timeout,
				success: (res) => {
					// console.log(res);
					if(res.statusCode == props.successCode) {
						files.value[index].status = 'success'
						files.value[index].data = res.data
						if(props.afterUpload != null) {
							const fun = props.afterUpload! as UploadCallback
							files.value[index].path = fun(res.data)
						}
					} else {
						files.value[index].status = 'fail'
					}
					
					index++
					f()
				},
				fail: (err) => {
					// console.log(err);
					let retry = files.value[index].retry as number
					
					if(props.retry > 0 && retry < props.retry) {
						files.value[index].retry = retry + 1
						setTimeout(() => {
							f()
						}, 200)
					} else {
						console.error('[ux-upload]', err);
						emit('error', err.errMsg)
						
						files.value[index].status = 'fail'
						index++
						f()
					}
				}
			})
			
			task.onProgressUpdate((res) => {
				// console.log(res);
				files.value[index].progress = res.progress
			})
		}
		
		f()
	}
	
	function chooseImage() {
		uni.chooseImage({
			count: props.count - files.value.length,
			sizeType: props.sizeType,
			sourceType: props.sourceType,
			extension: props.extension,
			success: (res) => {
				let paths = res.tempFilePaths
				
				let index = 0
				let f = ():void => {}
				f = ():void  => {
					if(index >= paths.length) {
						if(props.autoUpload) {
							setTimeout(() => {
								upload()
							},500);
						}
						return
					}
					
					uni.getImageInfo({
						src: paths[index],
						success: (ret) => {
							files.value.push({
								id: $ux.Random.uuid(),
								index: files.value.length,
								type: 'image',
								path: paths[index],
								thumb: paths[index],
								data: '',
								width: ret.width,
								height: ret.height,
								orientation: ret.orientation ?? '',
								size: 0,
								duration: 0,
								progress: 0,
								retry: 0,
								status: 'waiting'
							} as UxUploadFile)
							
							index++
							f()
						},
						fail: () => {
							files.value.push({
								id: $ux.Random.uuid(),
								index: files.value.length,
								type: 'image',
								path: paths[index],
								thumb: paths[index],
								data: '',
								width:0,
								height: 0,
								orientation: '',
								size: 0,
								duration: 0,
								progress: 0,
								retry: 0,
								status: 'waiting'
							} as UxUploadFile)
							
							index++
							f()
						}
					})
				}
				
				f()
			},
			fail: (err) => {
				emit('error', err.errMsg)
			}
		})
	}
	
	function chooseVideo() {
		uni.chooseVideo({
			sourceType: props.sourceType,
			success: (res) => {
				files.value.push({
					id: $ux.Random.uuid(),
					index: files.value.length,
					type: 'video',
					path: res.tempFilePath,
					thumb: '',
					data: '',
					width: res.width,
					height: res.height,
					size: res.size,
					duration: res.duration,
					orientation: '',
					progress: 0,
					retry: 0,
					status: 'waiting'
				} as UxUploadFile)
				
				if(props.autoUpload) {
					setTimeout(() => {
						upload()
					},500);
				}
			},
			fail: (err) => {
				emit('error', err.errMsg)
			}
		})
	}
	
	function onChoose() {
		if(props.disabled) {
			return
		}
		
		if(props.mode == 'file') {
			console.error('[ux-upload] 暂不支持文件上传');
			emit('error', '暂不支持文件上传')
			return
		}
		
		if(props.url == '') {
			console.error('[ux-upload] 请配置上传地址');
			emit('error', '未配置上传地址')
			return
		}
		
		if(uploading.value) {
			uni.showToast({
				title: '正在上传中...',
				icon: 'none'
			})
			return
		}
		
		if(files.value.length >= props.count) {
			return
		}
		
		if(props.mode == 'image') {
			chooseImage()
		} else if(props.mode == 'video') {
			chooseVideo()
		} else {
			uni.showActionSheet({
				itemList: ['选择照片', '选择视频'],
				success: (e) => {
					if(e.tapIndex == 0) {
						chooseImage()
					} else {
						chooseVideo()
					}
				}
			})
		}
	}
	
	async function onDel(index: number) {
		if(props.beforeDel == null) {
			files.value.splice(index, 1)
			change()
		} else {
			const f = props.beforeDel! as DelCallback
			const ok = await f(index, files.value[index])
			
			if(ok) {
				files.value.splice(index, 1)
				change()
			}
		}
	}
	
	function onClick(index: number) {
		let arr = files.value.filter((e):boolean => e.status == 'success').map((e): string => e.path)
		emit('click', index, files.value)
	}
	
	function getType(url: string): string {
		const extension = url.split('.').pop()?.toLowerCase() ?? ''
		const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'tiff', 'tif']
		const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv']
		
		if (imageExtensions.includes(extension)) {
		    return 'image'
		} else if (videoExtensions.includes(extension)) {
		    return 'video'
		} else {
		    return 'file'
		}
	}
	
	const list = computed((): UTSJSONObject[] => {
		return props.list
	})
	
	const modelValue = computed((): any | null => {
		return props.modelValue
	})
	
	function init(temps: UTSJSONObject[]) {
		temps.forEach((res: UTSJSONObject) => {
			let id = res.getString('id') ?? ''
			let url = res.getString('url') ?? ''
			
			if(id == '') {
				console.error('[ux-upload] 文件id不能为空');
				return
			}
			
			if(url == '') {
				return
			}
			
			let index = files.value.findIndex((e): boolean => e.id == id || e.path == url)
			if(index == -1) {
				files.value.push({
					id: id,
					index: files.value.length,
					type: getType(url),
					path: url,
					thumb: url,
					data: '',
					width: 0,
					height: 0,
					size: 0,
					duration: 0,
					orientation: '',
					progress: 100,
					retry: 0,
					status: 'success'
				} as UxUploadFile)
			} else {
				
			}
		})
	}
	
	function handleModel() {
		let obj = modelValue.value
		
		if(typeof obj == 'string') {
			init([{
				id: $ux.Random.uuid(),
				url: obj
			}] as UTSJSONObject[])
		} else if(obj instanceof Array) {
			let objs = [] as UTSJSONObject[]
			obj.forEach((e: any | null) => {
				if(e instanceof UTSJSONObject) {
					objs.push(e)
				}
			})
			
			init(objs)
		}
	}
	
	watch(list, () => {
		if(files.value.length == 0 && list.value.length > 0) {
			init(list.value)
		}
	}, {deep: true})
	
	watch(modelValue, () => {
		handleModel()
	})
	
	onMounted(() => {
		if(modelValue.value == null) {
			init(list.value)
		} else {
			handleModel()
		}
		
		uni.createSelectorQuery().in(instance).select(`#${myId}`).boundingClientRect((rect) => {
			totalWidth.value = (rect as NodeInfo).width as number
		}).exec()
	})
	
	defineExpose({
		upload
	})
</script>

<style lang="scss">
	.ux-upload {
		width: 100%;
		display: flex;
		flex-direction: row;
		position: relative;
		
		&__wrap {
			width: 100%;
			display: flex;
			flex-direction: row;
			align-items: center;
			flex-wrap: wrap;
		}
		
		&__file {
			/* #ifdef WEB */
			cursor: pointer;
			/* #endif */
			width: 65px;
			height: 65px;
			border-radius: 8px;
			
			&--img {
				width: 100%;
				height: 100%;
			}
		}
		
		&__mask {
			position: absolute;
			border-radius: 8px;
			width: 100%;
			height: 100%;
			display: flex;
			flex-direction: column;
			justify-content: flex-end;
			
			&--body {
				width: 100%;
				height: 22px;
				background-color: rgba(0, 0, 0, 0.55);
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: center;
			}
		}
		
		&__waiting { 
			color: white;
			font-size: 12px;
		}
		
		&__progress {
			color: white;
			font-size: 13px;
		}
		
		&__del {
			position: absolute;
			top: 0;
			right: 0;
			width: 18px;
			height: 18px;
			background-color: red;
			border-top-right-radius: 8px;
			border-bottom-left-radius: 8px;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		
		&__btn {
			width: 65px;
			height: 65px;
			background-color: #eaeaea;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
			border-radius: 8px;
			border: 1px solid #dadada;
		}
	}
</style>