<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/image?title=Image"></Mobile>

# Image
> 组件类型：UxImageComponentPublicInstance

支持SVG格式，支持淡入动画、加载中、加载失败提示、圆角值和形状等功能

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| default | String |  | defaultSlot |
| loading | String |  | loadingSlot |
| error | String |  | errorSlot |
| src | String |  | 图片地址 |
| previewList | Array |  | 图片预览列表 |
| [previewMode](#previewMode) | String | normal | 预览模式 |
| [mode](#mode) | String | aspectFill | 裁剪模式 |
| width | Any | 75 | 宽度 |
| height | Any | 75 | 高度 |
| [shape](#shape) | String | square | 图片形状 |
| radius | Any | 0 | 圆角值 |
| fade | Boolean | true | 是否需要淡入效果 |
| showLoading | Boolean | true | 是否显示加载中的图标或者自定义的slot |
| showError | Boolean | true | 是否显示加载错误的图标或者自定义的slot |
| errorIcon | String | pic-outline | 加载失败的图标或者图片 |
| iconSize | Any | 25 | 图标大小 |
| margin | Array |  | 边距[上右下左][上下左右][上下左右] |
| mt | Any |  | 距上单位px |
| mr | Any |  | 距右单位px |
| mb | Any |  | 距下单位px |
| ml | Any |  | 距左单位px |
| padding | Array |  | 填充[上右下左][上下左右][上下左右] |
| pt | Any |  | 上内边距单位px |
| pr | Any |  | 右内边距单位px |
| pb | Any |  | 下内边距单位px |
| pl | Any |  | 左内边距单位px |
| xstyle | Array |  | 自定义样式 |

### [previewMode](#previewMode)

| 值   | 说明 |
|:------:|:----:|
| normal | 正常
 |
| hero | hero动效(暂不支持)
 |

### [mode](#mode)

| 值   | 说明 |
|:------:|:----:|
| scaleToFill | 不保持纵横比缩放图片，使图片的宽高完全拉伸至填满image元素
 |
| aspectFit | 保持纵横比缩放图片，使图片的长边能完全显示出来。也就是说，可以完整地将图片显示出来
 |
| aspectFill | 保持纵横比缩放图片，只保证图片的短边能完全显示出来。也就是说，图片通常只在水平或垂直方向是完整的，另一个方向将会发生截取
 |
| widthFix | 宽度不变，高度自动变化，保持原图宽高比不变
 |
| heightFix | 高度不变，宽度自动变化，保持原图宽高比不变
 |
| top | 不缩放图片，只显示图片的顶部区域
 |
| bottom | 不缩放图片，只显示图片的底部区域
 |
| center | 不缩放图片，只显示图片的中间区域
 |
| left | 不缩放图片，只显示图片的左边区域
 |
| right | 不缩放图片，只显示图片的右边区域
 |
| topleft | 不缩放图片，只显示图片的左上边区域
 |
| topright | 不缩放图片，只显示图片的右上边区域
 |
| bottomleft | 不缩放图片，只显示图片的左下边区域
 |
| bottomright | 不缩放图片，只显示图片的右下边区域
 |

### [shape](#shape)

| 值   | 说明 |
|:------:|:----:|
| circle | 圆形
 |
| square | 方形
 |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| error | 图片加载失败时触发 |  |
| load | 图片加载成功时触发 |  |
| click | 被点击时触发 |  |
  
  