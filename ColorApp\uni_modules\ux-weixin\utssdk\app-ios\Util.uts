import { UIImage } from "UIKit";
import { Data } from "Foundation";
import { CGSize } from "CoreFoundation";
import { URL, URLSession, URLResponse } from "Foundation";

export default class Util {

	static getImage(path ?: string, width : number, height : number, callback : (image : UIImage | null) => void) {
		if (path == null || path == '') {
			callback(null)
		} else {
			if (this.isNetworkImage(path!)) {
				this.getNetworkImage(path!, (image : UIImage | null) => {
					if (image != null) {
						callback(image)
					} else {
						callback(null)
					}
				})
			} else {
				try {
					let imagePath = path!
					if(imagePath.indexOf('static/') != -1) {
						imagePath = UTSiOS.getResourcePath(path!)
					}
					
					const image = new UIImage(contentsOfFile = imagePath)

					if (image != null) {
						callback(image)
					} else {
						callback(null)
					}
				} catch (e) {
					callback(null)
				}
			}
		}
	}

	static getNetworkImage(path: string, callback : (image : UIImage | null) => void) {
		let url = new URL(string = path)
		
		let session = URLSession.shared
		
		let task = session.dataTask(with = url!, completionHandler = function (data: Data | null, response: URLResponse | null, error: NSError | null) {
			if(data == null) {
				callback(null)
			} else {
				callback(new UIImage(data = data!))
			}
		})
		
		task.resume()
	}

	static isNetworkImage(imagePath : String) : boolean {
		return imagePath.startsWith("http://") || imagePath.startsWith("https://")
	}
}