<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>mp-html 组件预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f9fa;
            line-height: 1.6;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #007AFF, #5856D6);
            color: white;
            padding: 30px;
            border-radius: 12px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0, 122, 255, 0.3);
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 8px;
        }
        
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .demo-section {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .demo-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .demo-content {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .feature-item {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007AFF;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .feature-item h3 {
            color: #007AFF;
            margin-bottom: 8px;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .highlight {
            color: #68d391;
        }
        
        .tag {
            color: #f6ad55;
        }
        
        .attr {
            color: #63b3ed;
        }
        
        .string {
            color: #fbb6ce;
        }
        
        .usage-example {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .api-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .api-table th,
        .api-table td {
            border: 1px solid #e2e8f0;
            padding: 12px;
            text-align: left;
        }
        
        .api-table th {
            background: #f7fafc;
            font-weight: 600;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-success {
            background: #c6f6d5;
            color: #22543d;
        }
        
        .footer {
            text-align: center;
            padding: 30px;
            color: #666;
            border-top: 1px solid #e2e8f0;
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>mp-html 组件</h1>
            <p>HTML解析与原生渲染组件 for uni-app x</p>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-title">🔧 最新修复 (v1.0.3)</h2>
            <div class="feature-list">
                <div class="feature-item">
                    <h3>✅ 自闭合标签检测逻辑优化</h3>
                    <p><strong>问题：</strong>自闭合标签修复导致复杂内容无法正确渲染</p>
                    <p><strong>原因：</strong>检测 <code>/</code> 字符时没有验证下一个字符是否为 <code>&gt;</code></p>
                    <p><strong>修复：</strong>严格检查 <code>/&gt;</code> 组合，避免误判普通标签</p>
                    <div class="code-block">
// 修复前（可能误判）
if (char == '/') {
  isSelfClosing = true
}

// 修复后（严格检查）
if (char == '/' && nextChar == '>') {
  isSelfClosing = true
}
                    </div>
                </div>
                <div class="feature-item">
                    <h3>✅ 自闭合标签识别优化 (v1.0.2)</h3>
                    <p>修复了 HtmlParser 未能正确识别以 <code>/&gt;</code> 结尾的自闭合标签问题</p>
                    <p>优化解析逻辑，支持标准的自闭合标签语法</p>
                    <ul>
                        <li>检测标签是否以 <code>/&gt;</code> 结尾</li>
                        <li>支持 <code>&lt;img /&gt;</code>、<code>&lt;br/&gt;</code>、<code>&lt;hr/&gt;</code> 等标准写法</li>
                        <li>兼容预定义的自闭合标签列表</li>
                    </ul>
                </div>
                <div class="feature-item">
                    <h3>✅ StyleUtils.mergeStyles 错误修复 (v1.0.1)</h3>
                    <p>修复了 <code>defaultStyle.keys is not a function</code> 错误</p>
                    <p>将 <code>UTSJSONObject.keys()</code> 方法替换为 <code>for...in</code> 循环</p>
                    <p>确保所有代码符合 UTS 语言规范</p>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-title">🧪 测试验证</h2>
            <div class="feature-list">
                <div class="feature-item">
                    <h3>自闭合标签测试</h3>
                    <div class="usage-example">
                        <p><strong>测试内容：</strong></p>
                        <div class="code-block">
&lt;p&gt;这是一个包含自闭合标签的测试：&lt;/p&gt;
&lt;img src="test.jpg" alt="测试图片" /&gt;
&lt;br/&gt;
&lt;p&gt;换行后的内容&lt;/p&gt;
&lt;hr/&gt;
&lt;p&gt;分割线后的内容&lt;/p&gt;
                        </div>
                        <p><strong>预期结果：</strong>所有标签都能正确解析和渲染</p>
                    </div>
                </div>
                <div class="feature-item">
                    <h3>复杂内容测试</h3>
                    <div class="usage-example">
                        <p><strong>测试内容：</strong></p>
                        <div class="code-block">
&lt;div&gt;
  &lt;h2&gt;标题&lt;/h2&gt;
  &lt;p&gt;包含 &lt;strong&gt;粗体&lt;/strong&gt; 和 &lt;em&gt;斜体&lt;/em&gt; 的段落&lt;/p&gt;
  &lt;img src="image.jpg" alt="图片" /&gt;
  &lt;ul&gt;
    &lt;li&gt;列表项1&lt;/li&gt;
    &lt;li&gt;列表项2&lt;/li&gt;
  &lt;/ul&gt;
&lt;/div&gt;
                        </div>
                        <p><strong>预期结果：</strong>嵌套标签和自闭合标签混合使用时正常渲染</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-title">🚀 组件特性</h2>
            <div class="feature-list">
                <div class="feature-item">
                    <h3>HTML解析</h3>
                    <p>支持常用HTML标签的解析，包括文本、图片、链接、列表等</p>
                </div>
                <div class="feature-item">
                    <h3>样式支持</h3>
                    <p>支持CSS样式解析，包括颜色、字体、布局等样式属性</p>
                </div>
                <div class="feature-item">
                    <h3>事件处理</h3>
                    <p>支持图片点击、链接点击等交互事件处理</p>
                </div>
                <div class="feature-item">
                    <h3>跨平台</h3>
                    <p>兼容App、小程序、H5等多个平台</p>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-title">📝 基础用法</h2>
            <div class="code-block">
<span class="tag">&lt;template&gt;</span>
  <span class="tag">&lt;mp-html</span> <span class="attr">:content</span>=<span class="string">"htmlContent"</span> <span class="tag">/&gt;</span>
<span class="tag">&lt;/template&gt;</span>

<span class="tag">&lt;script</span> <span class="attr">setup</span> <span class="attr">lang</span>=<span class="string">"uts"</span><span class="tag">&gt;</span>
<span class="highlight">const</span> htmlContent = <span class="string">'&lt;p&gt;Hello &lt;strong&gt;World&lt;/strong&gt;!&lt;/p&gt;'</span>
<span class="tag">&lt;/script&gt;</span>
            </div>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-title">🔧 高级用法</h2>
            <div class="code-block">
<span class="tag">&lt;template&gt;</span>
  <span class="tag">&lt;mp-html</span> 
    <span class="attr">:content</span>=<span class="string">"htmlContent"</span>
    <span class="attr">:selectable</span>=<span class="string">"true"</span>
    <span class="attr">:show-img-menu</span>=<span class="string">"true"</span>
    <span class="attr">@link-tap</span>=<span class="string">"onLinkTap"</span>
    <span class="attr">@img-tap</span>=<span class="string">"onImgTap"</span>
  <span class="tag">/&gt;</span>
<span class="tag">&lt;/template&gt;</span>

<span class="tag">&lt;script</span> <span class="attr">setup</span> <span class="attr">lang</span>=<span class="string">"uts"</span><span class="tag">&gt;</span>
<span class="highlight">const</span> onLinkTap = (event) => {
  console.log(<span class="string">'链接点击:'</span>, event.href)
}

<span class="highlight">const</span> onImgTap = (event) => {
  console.log(<span class="string">'图片点击:'</span>, event.src)
}
<span class="tag">&lt;/script&gt;</span>
            </div>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-title">📋 API 文档</h2>
            
            <h3>Props</h3>
            <table class="api-table">
                <thead>
                    <tr>
                        <th>属性名</th>
                        <th>类型</th>
                        <th>默认值</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>content</td>
                        <td>string</td>
                        <td>''</td>
                        <td>HTML内容字符串</td>
                    </tr>
                    <tr>
                        <td>selectable</td>
                        <td>boolean</td>
                        <td>false</td>
                        <td>是否可选择文本</td>
                    </tr>
                    <tr>
                        <td>show-img-menu</td>
                        <td>boolean</td>
                        <td>true</td>
                        <td>是否显示图片菜单</td>
                    </tr>
                </tbody>
            </table>
            
            <h3>Events</h3>
            <table class="api-table">
                <thead>
                    <tr>
                        <th>事件名</th>
                        <th>说明</th>
                        <th>参数</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>link-tap</td>
                        <td>链接点击事件</td>
                        <td>{href: string}</td>
                    </tr>
                    <tr>
                        <td>img-tap</td>
                        <td>图片点击事件</td>
                        <td>{src: string}</td>
                    </tr>
                    <tr>
                        <td>error</td>
                        <td>解析错误事件</td>
                        <td>{error: string}</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-title">✅ 支持的HTML标签</h2>
            <div class="feature-list">
                <div class="feature-item">
                    <h3>文本标签</h3>
                    <p><span class="status-badge status-success">✓</span> p, span, div, h1-h6, strong, b, em, i, u, del, s</p>
                </div>
                <div class="feature-item">
                    <h3>列表标签</h3>
                    <p><span class="status-badge status-success">✓</span> ul, ol, li</p>
                </div>
                <div class="feature-item">
                    <h3>媒体标签</h3>
                    <p><span class="status-badge status-success">✓</span> img, a</p>
                </div>
                <div class="feature-item">
                    <h3>其他标签</h3>
                    <p><span class="status-badge status-success">✓</span> br, hr</p>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-title">🎨 支持的CSS样式</h2>
            <div class="feature-list">
                <div class="feature-item">
                    <h3>文字样式</h3>
                    <p>color, font-size, font-weight, text-align</p>
                </div>
                <div class="feature-item">
                    <h3>布局样式</h3>
                    <p>margin, padding, width, height</p>
                </div>
                <div class="feature-item">
                    <h3>背景样式</h3>
                    <p>background-color</p>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-title">📱 平台兼容性</h2>
            <table class="api-table">
                <thead>
                    <tr>
                        <th>平台</th>
                        <th>支持状态</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>App (Android/iOS)</td>
                        <td><span class="status-badge status-success">✓ 支持</span></td>
                        <td>完整功能支持</td>
                    </tr>
                    <tr>
                        <td>小程序</td>
                        <td><span class="status-badge status-success">✓ 支持</span></td>
                        <td>微信、支付宝、百度等</td>
                    </tr>
                    <tr>
                        <td>H5</td>
                        <td><span class="status-badge status-success">✓ 支持</span></td>
                        <td>浏览器环境</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="footer">
            <p>mp-html 组件 - 为 uni-app x 提供强大的HTML解析能力</p>
            <p>使用 HBuilderX 打开项目查看完整示例</p>
        </div>
    </div>
</body>
</html>