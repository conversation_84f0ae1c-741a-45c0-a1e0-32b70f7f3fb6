export * from '../interface.uts';
import { UxMqttOptions, UxMqttConnectOptions, UxMqttSubscribeOptions, UxMqttPublishOptions } from '../interface.uts';

export type MqttServerStatus = 'offline' | 'online'

export class UxMqtt {
	opts : UxMqttOptions
	status : MqttServerStatus = 'offline'

	constructor(opts : UxMqttOptions) {
		this.opts = opts
	}

	/**
	 * 连接
	 */
	connect(opts : UxMqttConnectOptions) {
		
	}

	/**
	 * 断开连接
	 */
	disconnect() {
		
	}

	/**
	 * 订阅主题
	 */
	subscribe(opts : UxMqttSubscribeOptions) {
		
	}

	/**
	 * 取消订阅
	 */
	unsubscribe(topics : string[]) {
		
	}

	/**
	 * 发布消息
	 */
	publish(opts : UxMqttPublishOptions) {
		
	}
}