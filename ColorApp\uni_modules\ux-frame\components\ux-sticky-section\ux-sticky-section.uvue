<template>
	<!-- #ifndef MP -->
	<sticky-section :id="sectionId" :push-pinned-header="pushPinnedHeader" :padding="padding">
		<slot></slot>
	</sticky-section>
	<!-- #endif -->
	<!-- #ifdef MP -->
	<view :id="sectionId" class="ux-sticky-section" :style="style">
		<slot></slot>
	</view>
	<!-- #endif -->
</template>

<script setup>
	
	/**
	 * StickySection 吸顶布局容器
	 * @demo pages/component/sticky.uvue
	 * @tutorial https://www.uxframe.cn/component/sticky.html
	 * @property {String}		sectionId					String | id
	 * @property {String}		type						String | 类型，如果一个页面有多个本组件，可设置type区分
	 * @property {Number}		index						Number | 下标
	 * @property {Boolean}		pushPinnedHeader			Boolean | sticky-section元素重叠时是否继续上推 (默认 true)
	 * @property {Number[]}		padding						Number[] | 长度为 4 的数组，按 top、right、bottom、left 顺序指定内边距
	 * <AUTHOR>
	 * @date 2024-12-05 15:20:35
	 */
	
	import { $ux } from '../../index'

	defineOptions({
		name: 'ux-sticky-section'
	})

	const props = defineProps({
		sectionId: {
			type: String,
			default: ''
		},
		type: {
			type: String,
			default: ''
		},
		index: {
			type: Number,
			default: 0
		},
		pushPinnedHeader: {
			type: Boolean,
			default: false
		},
		padding: {
			type: Array as PropType<Array<number>>,
			default: (): number[] => {
				return [0,0,0,0] as number[]
			}
		}
	})
	
	// #ifdef MP
	const key = `ux-sticky-section-${props.type}`
	const top = ref(-1)
	const nodes = ref<UxStickyHeaderComponentPublicInstance[]>([]) 
	const scrollY = inject('scrollY', ref(0))
	
	provide('pushPinnedHeader', props.pushPinnedHeader)
	provide('index', props.index)
	provide('scrollTop', top)
	
	watch(scrollY, () => {
		nodes.value.forEach((e, index) => {
			e._.exposed.uptStatus(scrollY.value, index)
		})
	})
	
	const style = computed(() => {
		let css = {}
		
		if(props.padding.length > 0) {
			css['padding'] = props.padding.map(e => $ux.Util.addUnit(e))
		}
		
		return css
	})
	
	function register(child : UxStickyHeaderComponentPublicInstance, _top: number) {
		const data = uni.getStorageSync(key)
		if(data != '') {
			top.value = data as number
		} else {
			top.value = _top
			uni.setStorageSync(key, _top)
		}
		
		nodes.value.push(child)
	}
	
	onBeforeUnmount(() => {
		uni.removeStorageSync(key)
	})
	
	defineExpose({
		register
	})
	// #endif
</script>

<style lang="scss">
	.ux-sticky-section {
		position: relative;
	}
</style>