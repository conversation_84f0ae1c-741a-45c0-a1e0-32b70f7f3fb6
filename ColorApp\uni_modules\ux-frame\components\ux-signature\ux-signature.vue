<template>
	<view :id="myId" class="ux-signature transform" :style="[style]">
		<view class="ux-signature__area" :style="[areaStyle]">
			<text class="ux-signature__area--text" :style="textStyle">{{ text }}</text>
			
			<view :id="drawId" ref="uxSignatureRef" class="ux-signature__area--signature"
				@touchstart="touchstart"
				@touchmove="touchmove"
				@touchend="touchend"
				@touchcancel="touchend"
				@mousedown="touchstartPc"
				@mousemove="touchmovePc"
				@mouseup="touchend"
				@mouseleave="touchend">
				<canvas class="ux-signature__area--canvas" :id="canvasId" :canvas-id="canvasId"></canvas>
			</view>
		</view>
		<view v-if="showBtns" class="ux-signature__handle">
			<!-- <ux-button :mr="10" text="全屏" @click="fullscreen()"></ux-button> -->
			<ux-button :mr="10" text="清除" @click="clear()"></ux-button>
			<ux-button :mr="10" text="撤销" @click="undo()"></ux-button>
			<ux-button :mr="10" text="恢复" @click="redo()"></ux-button>
			<ux-button :mr="10" :theme="theme" :text="confirm" :color="confirmColor" @click="make()"></ux-button>
		</view>
	</view>
</template>

<script setup lang="ts">

	/**
	 * Signature 手写板签名
	 * @description 支持横竖屏切换、支持背景色、笔触大小、笔触颜色、背景颜色配置
	 * @demo pages/component/signature.uvue
	 * @tutorial https://www.uxframe.cn/component/signature.html
	 * @property {String}		text												String | 背景文字 （默认 签名区）
	 * @property {Number}		size												Number | 笔触大小 (默认 2)
	 * @property {String}		theme=[text|info|primary|success|warning|error]		String | 主题 (默认 info)
	 * @value text			文字
	 * @value info   		默认
	 * @value primary   	主要
	 * @value success  		成功
	 * @value warning   	警告
	 * @value error  		错误
	 * @property {String}		color												String | 笔触颜色 (默认 $ux.Conf.infoColor)
	 * @property {String}		background											String | 背景颜色 (默认 $ux.Conf.backgroundColor)
	 * @property {String}		confirm												String | 确定文字 (默认 确定)
	 * @property {String}		confirmColor										String | 确定文字颜色 (默认 $ux.Conf.primaryColor)
	 * @property {Number}		minWidth											Number | 最小宽度 (默认 2)
	 * @property {Number}		maxWidth											Number | 最大宽度 (默认 6)
	 * @property {Number}		minSpeed											Number | 最小速度 (默认 1.5)
	 * @property {Number}		maxHistory											Number | 最大可撤销记录 (默认 20)
	 * @property {Boolean}		fullScreen=[true|false]								Boolean | 全屏 (默认 false)
	 * @value true
	 * @value false
	 * @property {String}		format=[png|jpg]									String | 图片格式 (默认 png)
	 * @value png
	 * @value jpg
	 * @property {Boolean}		showBtns=[true|false]								Boolean | 显示操作按钮 (默认 true)
	 * @value true
	 * @value false
	 * @property {Boolean}		disableScroll=[true|false]							Boolean | 禁止屏幕滚动 (默认 true)
	 * @value true
	 * @value false
	 * @property {Boolean}		disabled=[true|false]								Boolean | 是否禁用 (默认 false)
	 * @value true
	 * @value false
	 * @property {Array}		margin												Array | 边距 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Number}		mt													Number | 距上 单位px
	 * @property {Number}		mr													Number | 距右 单位px
	 * @property {Number}		mb													Number | 距下 单位px
	 * @property {Number}		ml													Number | 距左 单位px
	 * @property {Array}		padding												Array | 填充 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Number}		pt													Number | 上内边距 单位px
	 * @property {Number}		pr													Number | 右内边距 单位px
	 * @property {Number}		pb													Number | 下内边距 单位px
	 * @property {Number}		pl													Number | 左内边距 单位px
	 * @property {Array}		xstyle												Array<any> | 自定义样式
	 * @event {Function}		change												Function | 生成签名时触发
	 * <AUTHOR>
	 * @date 2024-12-07 18:45:12
	 */
	
	import { computed, getCurrentInstance, onMounted, ref } from 'vue'
	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	import { UxSignature } from '../../libs/types/types.uts'
	import { usePlaceholderColor } from '../../libs/use/style.uts'
	import { useResize } from '../../libs/use/resize'
	
	type Point = {
		x: number
		y: number
		c?: string
		w?: number
		time ?: number
		dis ?: number
	}
	
	type Line = Point[]
	
	defineOptions({
		name: 'ux-signature',
		mixins: [xstyleMixin]
	})
	
	const emit = defineEmits(['change'])
	
	const props = defineProps({
		text: {
			type: String,
			default: '签字区'
		},
		size: {
			type: Number,
			default: 2
		},
		theme: {
			type: String,
			default: 'primary'
		},
		color: {
			type: String,
			default: ''
		},
		background: {
			type: String,
			default: ''
		},
		confirm: {
			type: String,
			default: '确定'
		},
		confirmColor: {
			type: String,
			default: ''
		},
		minWidth: {
			type: Number,
			default: 2
		},
		maxWidth: {
			type: Number,
			default: 6
		},
		minSpeed: {
			type: Number,
			default: 1.5
		},
		maxHistory: {
			type: Number,
			default: 20
		},
		disableScroll: {
			type: Boolean,
			default: true
		},
		disabled: {
			type: Boolean,
			default: false
		},
		fullScreen: {
			type: Boolean,
			default: true
		},
		format: {
			type: String,
			default: 'png'
		},
		showBtns: {
			type: Boolean,
			default: true
		}
	})
	
	let instance = getCurrentInstance()?.proxy
	
	const myId = `ux-signature-${$ux.Random.uuid()}`
	const canvasId = `ux-signature-canvas-${$ux.Random.uuid()}`
	const drawId = `ux-signature-draw-${$ux.Random.uuid()}`
	const dpr = uni.getWindowInfo().pixelRatio
	
	const width = ref(0)
	const height = ref(0)
	const canvasWidth = ref(0)
	const canvasHeight = ref(0)
	const isFullscreen = ref(false)
	const isEmpty = ref(true)
	const isDrawing = ref(false)
	
	const canvas = ref<any | null>(null)
	const ctx = ref<any | null>(null)
	const points = ref<Point[]>([] as Point[])
	const undoStack = ref<Line[]>([] as Line[])
	const redoStack = ref<Line[]>([] as Line[])
	const lastX = ref(0)
	const lastY = ref(0)
	
	const style = computed(() => {
		let css = {}
		
		if (isFullscreen.value) {
			css['width'] = `100%`
			css['height'] = `${width.value - 50}px`
			css['transform-origin'] = 'center center'
			css['transform'] = `rotate(90deg)`
		}
		
		css = $ux.Util.xStyle(css, props.margin, props.mt, props.mr, props.mb, props.ml, props.padding, props.pt, props.pr, props.pb, props.pl)
		
		return css
	})
	
	const areaStyle = computed(() => {
		let css = {}
		
		if (props.background != '') {
			css['background-color'] = props.background
		}
		
		return css
	})
	
	const textStyle = computed(()  => {
		let css = {}
		
		css['color'] = usePlaceholderColor('#e9e9e9', '#444444')
		
		return css
	})
	
	const options = computed(() : UxSignature => {
		return {
			size: props.size,
			color: props.color,
			minWidth: props.minWidth,
			maxWidth: props.maxWidth,
			minSpeed: props.minSpeed,
			maxHistory: props.maxHistory,
			disableScroll: props.disableScroll,
			disabled: props.disabled,
		} as UxSignature
	})
	
	function getLineWidth(): number {
		let	n = options.value.minSpeed
		let	o = options.value.minWidth
		let	a = options.value.maxWidth
		
		return Math.min(Math.max(a - (a - o) * 0.2 / Math.max(Math.min(n, 10), 1), o), a)
	}
	
	function drawLine(point : Point, last : Point, lineWidth : number, strokeStyle : string) {
		const _ctx = ctx.value
		
		_ctx.strokeStyle = strokeStyle
		_ctx.lineWidth = lineWidth
		_ctx.lineCap = 'round'
		_ctx.lineJoin = 'round'
		
		_ctx.beginPath()
		_ctx.moveTo(last.x, last.y)
		_ctx.lineTo(point.x , point.y)
		_ctx.stroke()
		_ctx.closePath()
		_ctx.draw(true)
	}
	
	async function getTouchPoint(x: number, y: number){
		const rect = await $ux.Util.getBoundingClientRect(`#${drawId}`, getCurrentInstance()?.proxy)
		
		return {
			x: x - rect.left,
			y: y - rect.top
		} as Point
	}
	
	function _touchstart(x: number, y: number) {
		isDrawing.value = true;
		isEmpty.value = false
		lastX.value = x
		lastY.value = y
		
		points.value.push({ x, y } as Point);
	}
	
	async function touchstart(e: TouchEvent) {
		if (options.value.disabled) {
			return
		}
		
		e.stopPropagation()
		
		if (options.value.disableScroll) {
			e.preventDefault()
		}
		
		const { x, y } = await getTouchPoint(e.changedTouches[0].clientX, e.changedTouches[0].clientY)
		
		_touchstart(x, y)
	}
	
	async function touchstartPc(e: MouseEvent) {
		if (options.value.disabled) {
			return
		}
		
		e.stopPropagation()
		
		if (options.value.disableScroll) {
			e.preventDefault()
		}
		
		const { x, y } = await getTouchPoint(e.clientX, e.clientY)
		
		_touchstart(x, y)
	}
	
	function _touchmove(x: number, y: number) {
		const lineWidth = getLineWidth()
		const strokeStyle = options.value.color
		
		const point = { x, y } as Point
		const last = { x: lastX.value, y: lastY.value } as Point
		
		drawLine(point, last, lineWidth, strokeStyle)
		
		lastX.value = x
		lastY.value = y
		points.value.push({ x, y, c: strokeStyle, w: lineWidth } as Point);
	}
	
	async function touchmove(e: TouchEvent) {
		if (options.value.disabled || !isDrawing.value) {
			return
		}
		
		e.stopPropagation()
		
		if (options.value.disableScroll) {
			e.preventDefault()
		}
		
		const { x, y } = await getTouchPoint(e.changedTouches[0].clientX, e.changedTouches[0].clientY)
		
		_touchmove(x, y)
	}
	
	async function touchmovePc(e: MouseEvent) {
		if (options.value.disabled || !isDrawing.value) {
			return
		}
		
		e.stopPropagation()
		
		if (options.value.disableScroll) {
			e.preventDefault()
		}
		
		const { x, y } = await getTouchPoint(e.clientX, e.clientY)
		
		_touchmove(x, y)
	}
	
	function touchend(e:  UniTouchEvent) {
		if (options.value.disabled || !isDrawing.value) {
			return
		}
		
		e.stopPropagation()
		
		if (options.value.disableScroll) {
			e.preventDefault()
		}
		
		isDrawing.value = false;
		undoStack.value.push(points.value);
		
		redoStack.value = [] as Line[];
		points.value = [] as Point[];
	}
	
	function clear() {
		const _ctx = ctx.value
		
		_ctx.clearRect(0, 0, canvasWidth.value, canvasHeight.value);
		_ctx.draw()
		
		isEmpty.value = true
		undoStack.value = [] as Line[];
		redoStack.value = [] as Line[];
		points.value = [] as Point[];
	}
	
	function redo() {
		if(redoStack.value.length == 0) return
		
		const lastPath : Line = redoStack.value.pop()!;
		undoStack.value.push(lastPath);
		
		isEmpty.value = false
		
		const _ctx = ctx.value
		
		_ctx.clearRect(0, 0, canvasWidth.value, canvasHeight.value);
		_ctx.draw()
		
		for (let l = 0; l < undoStack.value.length; l++) {
			for (let i = 1; i < undoStack.value[l].length; i++) {
				const last  = undoStack.value[l][i - 1]
				const point = undoStack.value[l][i]
				drawLine(point, last, point.w!, point.c!)
			}
		}
	}
	
	function undo() {
		if(redoStack.value.length == options.value.maxHistory && options.value.maxHistory != 0){
			return
		}
		
		const _ctx = ctx.value
		
		_ctx.clearRect(0, 0, canvasWidth.value, canvasHeight.value);
		_ctx.draw()
		
		if(undoStack.value.length > 0){
			const lastPath : Line = undoStack.value.pop()!;
			redoStack.value.push(lastPath);
			if(undoStack.value.length == 0){
				isEmpty.value = true
				return
			}
			
			for (let l = 0; l < undoStack.value.length; l++) {
				for (let i = 1; i < undoStack.value[l].length; i++) {
					const last  = undoStack.value[l][i - 1]
					const point = undoStack.value[l][i]
					drawLine(point, last, point.w!, point.c!)
				}
			}
		}
	}
	
	function fullscreen() {
		isFullscreen.value = !isFullscreen.value
	}
	
	function make() {
		if(isEmpty.value) {
		   emit('change', '')
		   return
		 }
		
		// #ifdef WEB
		// @ts-ignore
		let dom = uni.getElementById(canvasId)
		let base64Data = dom.querySelector('canvas')!.toDataURL('image/png')
		emit('change', base64Data)
		// #endif
	}
	
	async function init() {
		const rect = await $ux.Util.getBoundingClientRect(`#${myId}`, getCurrentInstance()?.proxy)
		width.value = rect.width
		height.value = rect.height
		
		const rect2 = await $ux.Util.getBoundingClientRect(`#${canvasId}`, getCurrentInstance()?.proxy)
		canvasWidth.value = rect2.width
		canvasHeight.value = rect2.height
		
		ctx.value = uni.createCanvasContext(canvasId, getCurrentInstance()?.proxy)
		
		// #ifdef WEB
		ctx.value!.width = canvasWidth.value * dpr
		ctx.value!.height = canvasHeight.value * dpr
		// ctx.value!.scale(dpr, dpr)
		// #endif
	}
	
	onMounted(() => {
		init()
		
		useResize(null, () => {
			init()
		})
		
		isFullscreen.value = props.fullScreen
	})
	
	defineExpose({
		clear,
		redo,
		undo,
		make,
		fullscreen
	})
</script>

<style lang="scss" scoped>
	.ux-signature {

		&__area {
			flex: 1;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			position: relative;

			&--signature {
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background-color: transparent;
			}

			&--text {
				font-size: 100px;
			}
			
			&--canvas {
				width: 100%;
				height: 100%;
			}
		}

		&__handle {
			width: 100%;
			height: 50px;
			display: flex;
			flex-direction: row;
			justify-content: flex-end;
			align-items: center;
		}
	}

	.transform {
		transition-property: transform;
		transition-duration: 200ms;
	}
</style>