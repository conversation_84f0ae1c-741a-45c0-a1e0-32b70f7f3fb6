import { UxRegisterOptions, UxLoginOptions, UxShareOptions, UxWeixinFail, UxWeixinSuccess } from '../interface.uts'

/**
* 注册
* @param {UxRegisterOptions} options
*/
export function register(options : UxRegisterOptions): void {
	
}

/**
* 登录
* @param {UxLoginOptions} options
*/
export function login(options : UxLoginOptions): void {
	
}

/**
* 分享
* @param {UxShareOptions} options
*/
export function share(options : UxShareOptions): void {
	
}

/**
* 图文
*/
function shareWebpage(options : UxShareOptions) {
	
}
	
/**
* 纯文本
*/
function shareText(options : UxShareOptions) {
	
}

/**
 * 纯图片
 */
function shareImage(options : UxShareOptions) {
	
}

/**
 * 视频
 */
function shareVideo(options : UxShareOptions) {
	
}

/**
 * 音乐
 */
function shareMusic(options : UxShareOptions) {
	
}

/**
 * 小程序
 */
function shareMp(options : UxShareOptions) {
	
}

/**
 * 微信客服
 */
function openCustomerService(options : UxShareOptions) {
	
}

/**
* 打开微信小程序
* @param {UxShareOptions} options
*/
export function openMiniProgram(options : UxShareOptions) {
	
}

/**
* 微信app是否安装
*/
export function isInstalled(): boolean {
	return false
}

/**
* 打开微信app
*/
export function openApp(): void {
	
}