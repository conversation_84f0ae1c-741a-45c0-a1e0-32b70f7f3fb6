<template>
	<web-view :id="myId" class="ux-svg" src="/uni_modules/ux-frame/hybrid/html/svg.html" @message="change"/>
</template>

<script setup>
	
	/**
	 * Svg 图片
	 * @description SVG加载组件
	 * @demo pages/component/svg.uvue
	 * @tutorial https://www.uxframe.cn/component/svg.html
	 * @event {Function} 			change 						Function | svg生成时触发
	 * <AUTHOR>
	 * @date 2024-04-19 14:17:45
	 */
	
	import { $ux } from '../../index'
	
	defineOptions({
		name: 'ux-svg-wv'
	})
	
	const emit = defineEmits(['load', 'error'])
	
	const props = defineProps({
		src: {
			type: String,
			default: ''
		},
	})
	
	const myId = `ux-svg-wv-${$ux.Random.uuid()}`
	let ctx: WebviewContext | null = null
	let isOk = false

	function init() {
		if(isOk) {
			if(ctx == null) {
				emit('error')
			} else {
				ctx?.evalJS(`onReceiveSvg('${encodeURIComponent(props.src)}')`);
			}
		}
	}
	
	function load(png: string) {
		// #ifdef APP-HARMONY
		// 鸿蒙无法传递太长的数据
		// 缓存到本地 返回key
		const key = `KEY-${$ux.Random.uuid()}`
		uni.setStorageSync(key, png)
		emit('load', key)
		// #endif
		
		// #ifndef APP-HARMONY
		emit('load', png)
		// #endif
	}
	
	function change(event : UniWebViewMessageEvent) {
		const data = event.detail.data![0]
		
		if ((data['init'] ?? false) == true) {
			isOk = true
			init()
		} else {
			const png = data['png'] as string
			
			if(png == '') {
				emit('error')
			} else {
				load(png)
			}
		};
	}
	
	onMounted(() => {
		ctx = uni.createWebviewContext(myId, getCurrentInstance()?.proxy);
		init()
	})
	
</script>

<style lang="scss">
	.ux-svg {
		display: flex;
	}

</style>