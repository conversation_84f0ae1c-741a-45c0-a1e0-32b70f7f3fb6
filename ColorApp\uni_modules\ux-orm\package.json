{"id": "ux-orm", "displayName": "UxFrame 零SQL本地数据库ORM SDK", "version": "1.0.1", "description": "专为uniappx打造的零SQL本地数据库ORM，支持android、ios", "keywords": ["uxframe", "uxorm", "orm", "sqlite", "本地数据库"], "repository": "", "engines": {"HBuilderX": "^3.6.8"}, "dcloudext": {"type": "component-vue", "sale": {"regular": {"price": "98.00"}, "sourcecode": {"price": "198.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": ["ux-sqlite"], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "y"}, "client": {"Vue": {"vue2": "u", "vue3": "u"}, "App": {"app-harmony": "n", "app-nvue": "y", "app-uvue": "y", "app-vue": "y"}, "H5-mobile": {"Safari": "n", "Android Browser": "n", "微信浏览器(Android)": "n", "QQ浏览器(Android)": "n"}, "H5-pc": {"Chrome": "n", "IE": "n", "Edge": "n", "Firefox": "n", "Safari": "n"}, "小程序": {"微信": "n", "阿里": "n", "百度": "n", "字节跳动": "n", "QQ": "n", "钉钉": "n", "快手": "n", "飞书": "n", "京东": "n"}, "快应用": {"华为": "n", "联盟": "n"}}}}}