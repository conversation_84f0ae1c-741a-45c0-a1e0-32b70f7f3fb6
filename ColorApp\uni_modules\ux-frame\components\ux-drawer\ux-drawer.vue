<template>
	<view :id="myId" ref="uxDrawerRef" class="ux-drawer" :class="[fixed?'fixed':'']" 
		@touchstart="touchstart" 
		@touchmove="touchmove" 
		@touchend="touchend" 
		@mousedown="touchstartPc" 
		@mousemove="touchmovePc" 
		@mouseup="touchendPc">
		<view :id="myMaskId" ref="uxOverlayRef" class="ux-drawer__mask masktransform" :style="maskStyle" @click="onClose"></view>
		<ux-blur v-if="showBlur" v-show="isOpened" class="ux-drawer__blur" :style="{opacity: blurAlpha}" :radius="blurRadius" :color="blurColor" @click="onClose"></ux-blur>
		
		<slot></slot>
		
		<view v-show="isOpened" :id="myBodyId" ref="uxDrawerBodyRef" :class="[`ux-drawer__slot--${direction}`, 'transform']" :style="slotStyle">
			<slot name="drawer"></slot>
		</view>
	</view>
</template>

<script setup lang="ts">

	/**
	 * 抽屉
	 * @description 支持点击、触摸滑动打开关闭
	 * @demo pages/component/drawer.uvue
	 * @tutorial https://www.uxframe.cn/component/drawer.html
	 * @property {Slot}				drawer									Slot | 侧边栏内容插槽
	 * @property {Boolean}			touchable = [true|false]				Boolean | 触摸拖动 (默认 true)
	 * @property {String}			direction = [top|right|bottom|left]		String | 方向 (默认 right)
	 * @value top 向上
	 * @value right 向右
	 * @value bottom 向下
	 * @value left 向左
	 * @property {Boolean}			fixed = [true|false]					Boolean | 固定定位 (默认 false)
	 * @property {String}			width									String | 宽度 (默认 85%)
	 * @property {String}			height									String | 高度 (默认 40%)
	 * @property {String}			background								String | 背景色 (默认 transparent)
	 * @property {Boolean}			border = [true|false]					Boolean | 显示边框 (默认 false)
	 * @property {Number}			opacity									Number | 遮罩透明度 0-1 (默认 $ux.Conf.maskAlpha)
	 * @property {Number}			duration								Number | 过渡时间 (默认 300ms)
	 * @property {Boolean}			showBlur = [true|false]				Boolean | 开启模糊背景 (默认 false )
	 * @property {Number}			blurRadius							Number | 模糊半径 (默认 10 )
	 * @property {String}			blurColor							String | 模糊颜色  (默认 rgba(10, 10, 10, 0.3) )
	 * @property {Number}			zIndex									Number | 层级z-index (默认 20000)
	 * @property {Boolean}			maskClose = [true|false]				Boolean | 遮罩层关闭 (默认 true)
	 * @property {Boolean}			interceptBack = [true|false]			Boolean | 拦截页面返回 (默认 false)
	 * @property {Boolean}			disabled = [true|false]					Boolean | 是否禁用 (默认 false)
	 * @event {Function}			change									Function | 状态改变时触发
	 * @event {Function}			close									Function | 关闭时触发
	 * <AUTHOR>
	 * @date 2024-12-01 20:32:46
	 */
	
	import { ref, computed, watch, onMounted, getCurrentInstance } from 'vue'
	import { $ux } from '../../index'
	import { useResize } from '../../libs/use/resize'
	
	defineOptions({
		name: 'ux-drawer'
	})
	
	const emit = defineEmits(['change', 'close'])
	
	const props = defineProps({
		touchable: {
			type: Boolean,
			default: true
		},
		direction: {
			type: String,
			default: 'right'
		},
		fixed: {
			type: Boolean,
			default: false
		},
		width: {
			type: String,
			default: '85%'
		},
		height: {
			type: String,
			default: '40%'
		},
		background: {
			type: String,
			default: 'transparent'
		},
		border: {
			type: Boolean,
			default: false
		},
		opacity: {
			type: Number,
			default: -1
		},
		duration: {
			type: Number,
			default: 300
		},
		showBlur: {
			type: Boolean,
			default: false
		},
		blurRadius: {
			type: Number,
			default: 10
		},
		blurColor: {
			type: String,
			default: 'rgba(10, 10, 10, 0.3)'
		},
		zIndex: {
			type: Number,
			default: 20000
		},
		maskClose: {
			type: Boolean,
			default: true
		},
		interceptBack: {
			type: Boolean,
			default: false
		},
		disabled: {
			type: Boolean,
			default: false
		},
	})
	
	const myId = `ux-drawer-${$ux.Random.uuid()}`
	const myMaskId = `ux-drawer-mask-${$ux.Random.uuid()}`
	const myBodyId = `ux-drawer-body-${$ux.Random.uuid()}`
	const uxDrawerRef = ref<Element | null>(null)
	const uxDrawerBodyRef = ref<Element | null>(null)
	const uxOverlayRef = ref<Element | null>(null)
	const opened = ref(false)
	const isOpened = ref(false)
	const touched = ref(false)
	const pos = ref(0)
	const dis = ref(-100)
	const blurAlpha = ref(0)
	let windowWidth = uni.getWindowInfo().windowWidth
	let windowHeight = uni.getWindowInfo().windowHeight
	let timer = 0
	
	const borderColor = computed(():string => {
		return $ux.Conf.darkMode.value ? $ux.Conf.borderColor.dark.value : $ux.Conf.borderColor.color.value
	})
	
	const slotLen = computed(() : number => {
		let w = ''
		let len = 0
		
		if (props.direction == 'right') {
			w = props.width.toString().toLowerCase()
			len = windowWidth
		} else if (props.direction == 'top') {
			w = props.height.toString().toLowerCase()
			len = windowHeight
		} else if (props.direction == 'left') {
			w = props.width.toString().toLowerCase()
			len = windowWidth
		} else if (props.direction == 'bottom') {
			w = props.height.toString().toLowerCase()
			len = windowHeight
		}
		
		if (w.includes('%')) {
			return len * (parseInt(w.replace('%', '')) / 100)
		}
		
		if (w.includes('rpx')) {
			let n = parseInt(w.replace('rpx', ''))
		
			return Math.floor(n / (750 / len))
		}
		
		if (w.includes('px')) {
			return parseInt(w.replace('px', ''))
		}
		
		return len
	})
	
	const slotStyle = computed(() => {
		let len = `${slotLen.value}px`
		
		let css = {}
		
		if(slotLen.value > 0) {
			if (props.direction == 'right') {
				css['width'] = len
			} else if (props.direction == 'top') {
				css['height'] = len
			} else if (props.direction == 'left') {
				css['width'] = len
			} else if (props.direction == 'bottom') {
				css['height'] = len
			}
		}
		
		if (props.border) {
			css[`border-${props.direction}`] = `1px solid ${borderColor.value}`
		}
		
		css['background-color'] = props.background
		
		return css
	})
	
	const defaultDis = computed(() : number => {
		if (props.direction == 'right') {
			return -slotLen.value / windowWidth * 100
		} else if (props.direction == 'top') {
			return -slotLen.value / windowHeight * 100
		} else if (props.direction == 'left') {
			return -slotLen.value / windowWidth * 100
		} else if (props.direction == 'bottom') {
			return -slotLen.value / windowHeight * 100
		}
		
		return 0
	})
	
	const opacity = computed(() : number => {
		if (props.opacity < 0) {
			return $ux.Conf.maskAlpha.value
		} else {
			return props.opacity
		}
	}) 
	
	const maskOpacity = computed(() : number => {
		if(defaultDis.value == 0) {
			return 0
		}

		let v = 1 - dis.value / defaultDis.value
		
		if (v > opacity.value) {
			v = opacity.value
		}
		
		if (v < 0) {
			v = 0
		}
		
		return v
	})
	
	const maskStyle = computed(() => {
		let css = {}
		
		css['z-index'] = props.zIndex
		css['opacity'] = maskOpacity.value
		
		return css
	})
	
	function showMask(show: boolean) {
		uni.getElementById(myMaskId)?.style?.setProperty('transition-duration', `${props.duration}ms`)
		if (show) {
			if(!opened.value) {
				uni.getElementById(myMaskId)?.style?.setProperty('width', `${windowWidth}px`)
				uni.getElementById(myMaskId)?.style?.setProperty('height', `${windowHeight}px`)
				uni.getElementById(myMaskId)?.style?.setProperty('opacity', maskOpacity.value)
				blurAlpha.value = 0.5
			}
		} else {
			if(opened.value) {
				blurAlpha.value = 0
				uni.getElementById(myMaskId)?.style?.setProperty('opacity', 0)
				setTimeout(() => {
					uni.getElementById(myMaskId)?.style?.setProperty('width', `${0}px`)
					uni.getElementById(myMaskId)?.style?.setProperty('height', `${0}px`)
				}, 200)
			}
		}
	}
	
	function setMask(show: boolean, value: number) {
		
	}
	
	async function open() {
		clearTimeout(timer)
		
		if (props.disabled) {
			return
		}
		
		if(isOpened.value && !touched.value) {
			return
		}
		
		dis.value = 0
		
		isOpened.value = true
		showMask(true)
		
		setTimeout(() => {
			uni.getElementById(myBodyId)?.style?.setProperty('transition-duration', `${props.duration}ms`)
			
			if (props.direction == 'right' || props.direction == 'left') {
				uni.getElementById(myBodyId)?.style?.setProperty('transform', `translateX(${0}%)`)
			} else if (props.direction == 'top' || props.direction == 'bottom') {
				uni.getElementById(myBodyId)?.style?.setProperty('transform', `translateY(${0}%)`)
			}
			
			setTimeout(() => {
				opened.value = true
				emit('change', opened.value)
			}, 100);
		}, 100);
	}
	
	function close() {
		if (props.disabled) {
			return
		}
		
		if(!isOpened.value && !touched.value) {
			return
		}
		
		clearTimeout(timer)
		
		showMask(false)

		uni.getElementById(myBodyId)?.style?.setProperty('transition-duration', `${props.duration}ms`)
		
		if (props.direction == 'right') {
			uni.getElementById(myBodyId)?.style?.setProperty('transform', `translateX(${-slotLen.value}px)`)
		} else if (props.direction == 'top') {
			uni.getElementById(myBodyId)?.style?.setProperty('transform', `translateY(${slotLen.value}px)`)
		} else if (props.direction == 'left') {
			uni.getElementById(myBodyId)?.style?.setProperty('transform', `translateX(${slotLen.value}px)`)
		} else if (props.direction == 'bottom') {
			uni.getElementById(myBodyId)?.style?.setProperty('transform', `translateY(${-slotLen.value}px)`)
		}
		
		dis.value = defaultDis.value
		
		timer = setTimeout(() => {
			opened.value = false
			setTimeout(() => {
				isOpened.value = false
			}, props.duration + 20);
			emit('close')
			emit('change', opened.value)
		}, props.duration);
	}
	
	function onClose(e: MouseEvent) {
		if(props.maskClose) {
			close()
		}
		
		e.stopPropagation()
	}
	
	function rightStart(x: number, y: number) {
		pos.value = x
		touched.value = true
	}
	
	function rightMove(x: number, y: number) {
		
		let _dis = x - pos.value
		
		uni.getElementById(myBodyId)?.style?.setProperty('transition-duration', `${0}ms`)
		if (opened.value) {
			let offset = pos.value - (slotLen.value - _dis)
			if(offset > 0) {
				return
			}
			setMask(false, 1- Math.abs(offset)/slotLen.value)
			uni.getElementById(myBodyId)?.style?.setProperty('transform', `translateX(${offset}px)`)
		} else {
			let offset = slotLen.value - _dis
			setMask(true, 1-offset/slotLen.value)
			uni.getElementById(myBodyId)?.style?.setProperty('transform', `translateX(-${offset}px)`)
		}
	}
	
	function rightEnd(x : number) {
		let _dis = x - pos.value
		
		if (opened.value) {
			if(_dis > 0 || Math.abs(_dis) < 20) {
				open()
				return
			}
			
			if (_dis > -slotLen.value * 0.3) {
				open()
			} else {
				close()
			}
		} else {
			if(_dis < 0 || Math.abs(_dis) < 20) {
				close()
				return
			}
			
			if (_dis < slotLen.value * 0.5) {
				close()
			} else {
				open()
			}
		}
	}
	
	function leftStart(x : number, y: number) {
		pos.value = windowWidth - x
		touched.value = true
	}
	
	function leftMove(x : number, y: number) {
		x = windowWidth - x
		
		let _dis = x - pos.value
		
		uni.getElementById(myBodyId)?.style?.setProperty('transition-duration', `${0}ms`)
		if (opened.value) {
			let offset = pos.value - (slotLen.value + _dis)
			if(offset < 0) {
				return
			}
			setMask(false, 1- Math.abs(offset)/slotLen.value)
			uni.getElementById(myBodyId)?.style?.setProperty('transform', `translateX(${offset}px)`)
		} else {
			let offset = slotLen.value - _dis
			setMask(true, 1-offset/slotLen.value)
			uni.getElementById(myBodyId)?.style?.setProperty('transform', `translateX(${offset}px)`)
		}
	}
	
	function leftEnd(x: number) {
		let _dis = windowWidth - x - pos.value
		
		if (opened.value) {
			if(_dis > 0 || Math.abs(_dis) < 20) {
				open()
				return
			}
			
			if (Math.abs(_dis) < slotLen.value * 0.3) {
				open()
			} else {
				close()
			}
		} else {
			if(_dis < 0 || Math.abs(_dis) < 20) {
				close()
				return
			}
			
			if (_dis < slotLen.value * 0.5) {
				close()
			} else {
				open()
			}
		}
	}
	
	function topStart(x: number, y: number) {
		pos.value = windowHeight - y
		touched.value = true
	}
	
	function topMove(x: number, y: number) {
		y = windowHeight - y
		
		let _dis = y - pos.value
		
		uni.getElementById(myBodyId)?.style?.setProperty('transition-duration', `${0}ms`)
		if (opened.value) {
			let offset = -_dis
			if(offset < 0) {
				return
			}
			uni.getElementById(myBodyId)?.style?.setProperty('transform', `translateY(${offset}px)`)
		} else {
			let offset = slotLen.value - _dis
			if(offset < 0) {
				return
			}
			uni.getElementById(myBodyId)?.style?.setProperty('transform', `translateY(${offset}px)`)
		}
	}
	
	function topEnd(y : number) {
		let _dis = windowHeight - y - pos.value
		
		if (opened.value) {
			if(_dis > 0 || Math.abs(_dis) < 20) {
				open()
				return
			}
			
			if (_dis > -80) {
				open()
			} else {
				close()
			}
		} else {
			if(_dis < 0 || Math.abs(_dis) < 20) {
				close()
				return
			}
			
			if (_dis < 100) {
				close()
			} else {
				open()
			}
		}
	}
	
	function bottomStart(x: number, y: number) {
		pos.value = y
		touched.value = true
	}
	
	function bottomMove(x: number, y: number) {
		let _dis = y - pos.value
		
		uni.getElementById(myBodyId)?.style?.setProperty('transition-duration', `${0}ms`)
		if (opened.value) {
			let offset = -_dis
			if(offset < 0) {
				return
			}
			uni.getElementById(myBodyId)?.style?.setProperty('transform', `translateY(-${offset}px)`)
		} else {
			let offset = slotLen.value - _dis
			if(offset < 0) {
				return
			}
			uni.getElementById(myBodyId)?.style?.setProperty('transform', `translateY(-${offset}px)`)
		}
	}
	
	function bottomEnd(y : number) {
		let _dis = y - pos.value
		
		if (opened.value) {
			if(_dis > 0 || Math.abs(_dis) < 20) {
				open()
				return
			}
			
			if (_dis > -80) {
				open()
			} else {
				close()
			}
		} else {
			if(_dis < 0 || Math.abs(_dis) < 20) {
				close()
				return
			}
			
			if (_dis < 80) {
				close()
			} else {
				open()
			}
		}
	}
	
	function touchstart(e : TouchEvent) {
		
		if (props.disabled) {
			return
		}
		
		if (!props.touchable) {
			return
		}
		
		timer = setTimeout(() => {
			isOpened.value = true
			
			if (props.direction == 'right') {
				rightStart(e.changedTouches[0].screenX, e.changedTouches[0].screenY)
			} else if (props.direction == 'top') {
				topStart(e.changedTouches[0].screenX, e.changedTouches[0].screenY)
			} else if (props.direction == 'left') {
				leftStart(e.changedTouches[0].screenX, e.changedTouches[0].screenY)
			} else if (props.direction == 'bottom') {
				bottomStart(e.changedTouches[0].screenX, e.changedTouches[0].screenY)
			}
		}, 100)
		
		e.stopPropagation()
	}
	
	function touchstartPc(e : MouseEvent) {
		if (props.disabled) {
			return
		}
		
		if (!props.touchable) {
			return
		}
		
		timer = setTimeout(() => {
			isOpened.value = true
			
			if (props.direction == 'right') {
				rightStart(e.clientX, e.clientY)
			} else if (props.direction == 'top') {
				topStart(e.clientX, e.clientY)
			} else if (props.direction == 'left') {
				leftStart(e.clientX, e.clientY)
			} else if (props.direction == 'bottom') {
				bottomStart(e.clientX, e.clientY)
			}
		}, 100)
		
		e.stopPropagation()
	}
	
	function preventDefault(e : TouchEvent) {
		const x = e.changedTouches[0].screenX
		const y = e.changedTouches[0].screenY
		const offset = 3
		
		if (props.direction == 'right') {
			if(Math.abs(x - pos.value) > offset) {
				e.preventDefault()
			}
		} else if (props.direction == 'top') {
			if(Math.abs(windowHeight - y - pos.value) > offset) {
				e.preventDefault()
			}
		} else if (props.direction == 'left') {
			if(Math.abs(windowWidth - x - pos.value) > offset) {
				e.preventDefault()
			}
		} else if (props.direction == 'bottom') {
			if(Math.abs(y - pos.value) > offset) {
				e.preventDefault()
			}
		}
	}
	
	function preventDefaultPc(e : MouseEvent) {
		const x = e.clientX
		const y = e.clientY
		const offset = 3
		
		if (props.direction == 'right') {
			if(Math.abs(x - pos.value) > offset) {
				e.preventDefault()
			}
		} else if (props.direction == 'top') {
			if(Math.abs(windowHeight - y - pos.value) > offset) {
				e.preventDefault()
			}
		} else if (props.direction == 'left') {
			if(Math.abs(windowWidth - x - pos.value) > offset) {
				e.preventDefault()
			}
		} else if (props.direction == 'bottom') {
			if(Math.abs(y - pos.value) > offset) {
				e.preventDefault()
			}
		}
	}
	
	function touchmove(e : TouchEvent) {
		
		if (props.disabled) {
			return
		}
		
		if (!props.touchable) {
			return
		}
		
		if(!touched.value) {
			return
		}
		
		clearTimeout(timer)
		
		preventDefault(e)
		
		if (props.direction == 'right') {
			rightMove(e.changedTouches[0].screenX, e.changedTouches[0].screenY)
		} else if (props.direction == 'top') {
			topMove(e.changedTouches[0].screenX, e.changedTouches[0].screenY)
		} else if (props.direction == 'left') {
			leftMove(e.changedTouches[0].screenX, e.changedTouches[0].screenY)
		} else if (props.direction == 'bottom') {
			bottomMove(e.changedTouches[0].screenX, e.changedTouches[0].screenY)
		}
	}
	
	function touchmovePc(e : MouseEvent) {
		if (props.disabled) {
			return
		}
		
		if (!props.touchable) {
			return
		}
		
		if(!touched.value) {
			return
		}
		
		clearTimeout(timer)
		
		preventDefaultPc(e)
		
		if (props.direction == 'right') {
			rightMove(e.clientX, e.clientY)
		} else if (props.direction == 'top') {
			topMove(e.clientX, e.clientY)
		} else if (props.direction == 'left') {
			leftMove(e.clientX, e.clientY)
		} else if (props.direction == 'bottom') {
			bottomMove(e.clientX, e.clientY)
		}
	}
	
	function touchend(e : TouchEvent) {
		
		if (props.disabled) {
			return
		}
		
		if (!props.touchable) {
			return
		}
		
		if (!touched.value) {
			return
		}
		
		clearTimeout(timer)
		
		if (props.direction == 'right') {
			rightEnd(e.changedTouches[0].screenX)
		} else if (props.direction == 'top') {
			topEnd(e.changedTouches[0].screenY)
		} else if (props.direction == 'left') {
			leftEnd(e.changedTouches[0].screenX)
		} else if (props.direction == 'bottom') {
			bottomEnd(e.changedTouches[0].screenY)
		}
		
		setTimeout(() => {
			touched.value = false
		}, 200);
	}
	
	function touchendPc(e : MouseEvent) {
		
		if (props.disabled) {
			return
		}
		
		if (!props.touchable) {
			return
		}
		
		if (!touched.value) {
			return
		}
		
		clearTimeout(timer)
		
		if (props.direction == 'right') {
			rightEnd(e.clientX)
		} else if (props.direction == 'top') {
			topEnd(e.clientY)
		} else if (props.direction == 'left') {
			leftEnd(e.clientX)
		} else if (props.direction == 'bottom') {
			bottomEnd(e.clientY)
		}
		
		setTimeout(() => {
			touched.value = false
		}, 200);
	}
	
	onMounted(() => {
		useResize(uxDrawerRef.value, () => {
			windowWidth = uni.getWindowInfo().windowWidth
			windowHeight = uni.getWindowInfo().windowHeight
		})
	})
	
	onBackPress((e): boolean => {
		return props.interceptBack && isOpened.value
	})
	
	defineExpose({
		open,
		close
	})
</script>

<style lang="scss" scoped>
	/* #ifdef H5 */
	uni-view {
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: stretch;
		align-content: stretch;
		justify-content: flex-start;
		box-sizing: border-box;
		min-height: 0px;
		min-width: 0px;
		overflow: hidden;
	}
	/* #endif */
	
	.ux-drawer {
		.ux-drawer__mask {
			position: fixed;
			z-index: 20000;
			top: 0;
			right: 0;
			width: 0;
			height: 0;
			opacity: 0;
			background-color: rgba(0, 0, 0, 1);
		}
		
		.ux-drawer__blur {
			position: fixed;
			z-index: 20002;
			width: 100%;
			height: 100%;
			top: 0;
			right: 0;
			left: 0;
			bottom: 0;
			opacity: 0;
			transition-property: opacity;
			transition-duration: 0.4s;
			transition-timing-function: linear;
		}

		.ux-drawer__slot--right {
			position: fixed;
			left: 0;
			top: 0;
			bottom: 0;
			width: 85%;
			transform: translate(-100%, 0);
			z-index: 200000;
		}

		.ux-drawer__slot--top {
			position: fixed;
			left: 0;
			right: 0;
			bottom: 0;
			transform: translate(0, 100%);
			z-index: 200000;
		}

		.ux-drawer__slot--left {
			position: fixed;
			right: 0;
			top: 0;
			bottom: 0;
			width: 85%;
			transform: translate(100%, 0);
			z-index: 200000;
		}

		.ux-drawer__slot--bottom {
			position: fixed;
			left: 0;
			right: 0;
			top: 0;
			height: 100%;
			transform: translate(0, -100%);
			z-index: 200000;
		}

		.transform {
			transition-property: transform;
			transition-duration: 0.3s;
			transition-timing-function: ease-in-out;
		}
		
		.masktransform {
			transition-property: opacity;
			transition-duration: 0.4s;
			transition-timing-function: linear;
		}
	}

	.fixed {
		position: fixed;
		left: 0;
		top: 0;
		bottom: 0;
		right: 0;
	}
</style>