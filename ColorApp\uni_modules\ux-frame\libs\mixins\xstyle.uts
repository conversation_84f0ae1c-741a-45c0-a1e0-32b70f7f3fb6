
// #ifdef UNI-APP-X
export const xstyleMixin = defineMixin({
	props: {
		// 组件标识符
		name: {
			type: String,
			default: ''
		},
		// 主题
		theme: {
			type: String,
			default: 'primary'
		},
		// 颜色 
		color: {
			type: String,
			default: ''
		},
		// 深色
		darkColor: {
			type: String,
			default: ''
		},
		// 背景色
		background: {
			type: String,
			default: ''
		},
		// 深色背景色
		backgroundDark: {
			type: String,
			default: ''
		},
		// 是否只读
		readonly: {
			type: Boolean,
			default: false
		},
		// 是否禁用
		disabled: {
			type: Boolean,
			default: false
		},
		// 边距 [上 右 下 左] [上下 左右] [上下左右]
		margin: {
			type: Array as PropType<Array<any>>,
			default: (): any[] => [] as any[]
		},
		mt: {
			type: Number,
			default: 0
		},
		mr: {
			type: Number,
			default: 0
		},
		mb: {
			type: Number,
			default: 0
		},
		ml: {
			type: Number,
			default: 0
		},
		// 填充 [上 右 下 左] [上下 左右] [上下左右]
		padding: {
			type: Array as PropType<Array<any>>,
			default: (): any[] => [] as any[]
		},
		pt: {
			type: Number,
			default: 0
		},
		pr: {
			type: Number,
			default: 0
		},
		pb: {
			type: Number,
			default: 0
		},
		pl: {
			type: Number,
			default: 0
		},
		// 自定义样式
		xstyle: {
			type: Array as PropType<Array<any>>,
			default: (): any[] => [] as any[]
		}
	},
})
// #endif

// #ifndef UNI-APP-X
export const xstyleMixin = {
	props: {
		// 组件标识符
		name: {
			type: String,
			default: ''
		},
		// 主题
		theme: {
			type: String,
			default: 'primary'
		},
		// 颜色 
		color: {
			type: String,
			default: ''
		},
		// 深色
		darkColor: {
			type: String,
			default: ''
		},
		// 背景色
		background: {
			type: String,
			default: ''
		},
		// 深色背景色
		backgroundDark: {
			type: String,
			default: ''
		},
		// 是否只读
		readonly: {
			type: Boolean,
			default: false
		},
		// 是否禁用
		disabled: {
			type: Boolean,
			default: false
		},
		// 边距 [上 右 下 左] [上下 左右] [上下左右]
		margin: {
			type: Array as PropType<Array<any>>,
			default: (): any[] => [] as any[]
		},
		mt: {
			type: Number,
			default: 0
		},
		mr: {
			type: Number,
			default: 0
		},
		mb: {
			type: Number,
			default: 0
		},
		ml: {
			type: Number,
			default: 0
		},
		// 填充 [上 右 下 左] [上下 左右] [上下左右]
		padding: {
			type: Array as PropType<Array<any>>,
			default: (): any[] => [] as any[]
		},
		pt: {
			type: Number,
			default: 0
		},
		pr: {
			type: Number,
			default: 0
		},
		pb: {
			type: Number,
			default: 0
		},
		pl: {
			type: Number,
			default: 0
		},
		// 自定义样式
		xstyle: {
			type: Array as PropType<Array<any>>,
			default: (): any[] => [] as any[]
		}
	},
}
// #endif

