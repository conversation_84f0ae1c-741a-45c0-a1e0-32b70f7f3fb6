<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/movable?title=Movable"></Mobile>

# Movable
> 组件类型：UxMovableComponentPublicInstance

支持宫格和列表布局，内置照片墙上传场景，支持多选、编辑、滑动删除、禁用

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| [layout](#layout) | String | grid | 布局 |
| column | Number | 4 | 列数 |
| spacing | Number | 8 | 间隔 |
| aspectRatio | Number | 1 | item宽高比仅grid有效 |
| multiple | Boolean | false | 多选 |
| editable | Boolean | false | 可编辑 |
| swipe | Boolean | false | 滑动删除 |
| disabled | Boolean | false | 禁用 |

### [layout](#layout)

| 值   | 说明 |
|:------:|:----:|
| grid宫格
 |  |
| list列表
 |  |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| init | 初始化时触发 |  |
| change | 值改变时触发 |  |
  
  