import io.dcloud.uts.UTSAndroid
import io.dcloud.uts.console
import android.content.Intent;
import android.net.Uri
import androidx.core.content.FileProvider
import java.io.File
import java.util.ArrayList
import com.bytedance.sdk.open.douyin.api.DouYinOpenApi;
import com.bytedance.sdk.open.douyin.DouYinOpenApiFactory
import com.bytedance.sdk.open.douyin.DouYinOpenConfig
import com.bytedance.sdk.open.aweme.authorize.model.Authorization;
import com.bytedance.sdk.open.douyin.ShareToContact;
import com.bytedance.sdk.open.aweme.base.ImageObject;
import com.bytedance.sdk.open.aweme.base.MediaContent;
import com.bytedance.sdk.open.aweme.base.MicroAppObject
import com.bytedance.sdk.open.douyin.model.ContactHtmlObject;
import com.bytedance.sdk.open.aweme.CommonConstants;

object UxDouyin {
	
	var douyinOpenApi: DouYinOpenApi? = null
	
	fun register(clientkey: String): Boolean {
		var ok = DouYinOpenApiFactory.init(DouYinOpenConfig(clientkey))
		if(ok) {
			douyinOpenApi = DouYinOpenApiFactory.create(UTSAndroid.getUniActivity()!!)
		}
		
		return ok
	}
	
	fun login(state: String?) {
		var request = Authorization.Request()
		request.scope = "user_info"
		request.state = state ?: ""
		douyinOpenApi?.authorize(request)
	}
	
	fun shareImage(url: String) {
		val mUri = ArrayList<String>()
		mUri.add(url)
		
		val imageObject = ImageObject()
		imageObject.mImagePaths = mUri
		
		val mediaContent = MediaContent()
		mediaContent.mMediaObject = imageObject
		
		val request = ShareToContact.Request()
		request.mMediaContent = mediaContent
		
		if (douyinOpenApi?.isAppSupportShareToContacts() ?: false) {
		    douyinOpenApi?.shareToContacts(request)
		}
	}
	
	fun shareHtml(title: String, html: String, discription: String, thumbUrl: String?) {
		var htmlObject = ContactHtmlObject()
		htmlObject.html = html
		htmlObject.discription = discription
		htmlObject.title = title
		
		if(thumbUrl != null) {
			htmlObject.thumbUrl = thumbUrl
		}
		
		var request = ShareToContact.Request()
		request.htmlObject = htmlObject
		
		if (douyinOpenApi?.isAppSupportShareToContacts() ?: false) {
		    douyinOpenApi?.shareToContacts(request)
		}
	}
	
	fun shareProgram(appId: String, title: String, path: String, query: String?, imageId: String, state: String?) {
		val microAppObject = MicroAppObject()
		microAppObject.appId = appId
		microAppObject.title = title
		microAppObject.path = path
		microAppObject.query = query ?: ""
		microAppObject.imageId = imageId
		
		val request = ShareToContact.Request()
		request.mMediaContent = MediaContent().apply {
		    mMediaObject = microAppObject
		}
		request.mState = state ?: ""
		
		if (douyinOpenApi?.isSupportApi(
		        CommonConstants.SUPPORT.SHARE_IM,
		        CommonConstants.SUPPORT.CONTACT_API.CONTACT_SUPPORT_MICRO_APP
		    ) == true
		) {
		    douyinOpenApi?.shareToContacts(request)
		}
	}
}