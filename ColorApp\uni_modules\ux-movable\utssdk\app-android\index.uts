
import Context from 'android.content.Context'
import GridLayoutManager from 'androidx.recyclerview.widget.GridLayoutManager'
import ItemTouchHelper from 'androidx.recyclerview.widget.ItemTouchHelper'
import LinearLayoutManager from 'androidx.recyclerview.widget.LinearLayoutManager'
import RecyclerView from 'androidx.recyclerview.widget.RecyclerView'
import LayoutInflater from 'android.view.LayoutInflater'
import View from 'android.view.View'
import ViewGroup from 'android.view.ViewGroup'
import TextView from 'android.widget.TextView'
import ImageView from 'android.widget.ImageView'
import BitmapFactory from 'android.graphics.BitmapFactory';
import R from 'uts.sdk.modules.uxMovable.R'
import Collections from 'java.util.Collections'
import Bitmap from 'android.graphics.Bitmap';
import bool from 'android.R.bool';
import Color from 'android.graphics.Color';
import Typeface from 'android.graphics.Typeface';
import Glide from 'com.bumptech.glide.Glide';
import Rect from 'android.graphics.Rect';
import Outline from 'android.graphics.Outline';
import ViewOutlineProvider from 'android.view.ViewOutlineProvider';
import EditText from 'android.widget.EditText';
import FrameLayout from 'android.widget.FrameLayout';
import ViewTreeObserver from 'android.view.ViewTreeObserver';
import InputStream from 'java.io.InputStream';

export type UxMovableLayout = 'list' | 'grid'

export type UxMovableOptions = {
	layout : UxMovableLayout,
	column : number,
	spacing: number,
	aspectRatio : number,
	multiple: boolean,
	editable: boolean,
	swipe: boolean,
	disabled : boolean,
	change : (data : UxMovableItem[]) => void
}

export type UxMovableStyle = {
	/**
	 * 字体颜色
	 */
	color ?: string,
	/**
	 * 字体大小
	 */
	fontSize ?: number,
	/**
	 * 粗体
	 */
	fontWeight ?: boolean,
	/**
	 * 宽
	 */
	width ?: number,
	/**
	 * 高
	 */
	height ?: number,
	/**
	 * 圆角
	 */
	radius ?: number,
	/**
	 * 背景色
	 */
	backgroundColor ?: string
}

export type UxMovableBorderType = 'solid' | 'dashed'

export type UxMovableBorder = {
	/**
	 * 边框类型
	 */
	type : UxMovableBorderType
	/**
	 * 边框宽度
	 */
	width : number,
	/**
	 * 颜色
	 */
	color : string
}

export type UxMovableCheckbox = {
	/**
	 * 选中状态
	 */
	checked: boolean,
	/**
	 * 选中图标
	 */
	selectedUrl: string,
	/**
	 * 未选中图标
	 */
	unselectedUrl: string,
	/**
	 * 宽
	 */
	width : number,
	/**
	 * 高
	 */
	height : number,
	/**
	 * 间距
	 */
	margin ?: number,
}

export type UxMovableUpload = {
	/**
	 * 占位图
	 */
	url : string,
	/**
	 * 背景色
	 */
	backgroundColor ?: string
	/**
	 * 隐藏
	 */
	hide ?: boolean,
	/**
	 * 禁用
	 */
	disabled ?: boolean,
}

export type UxMovableInput = {
	/**
	 * 内容
	 */
	content: string,
	/**
	 * 占位内容
	 */
	placeholder ?: string,
	/**
	 * 最大长度
	 */
	maxLength ?: number,
	/**
	 * 输入事件
	 */
	input ?: (content: string) => void
}

export type UxMovableLoading = {
	/**
	 * 显示loading
	 */
	show : boolean,
	/**
	 * 进度百分比
	 */
	progress : number,
	/**
	 * 等待上传文案
	 */
	waitingLabel ?: string,
	/**
	 * loading 样式
	 */
	style ?: UxMovableStyle,
}

export type UxMovablePlaceholder = {
	/**
	 * 占位图
	 */
	url : string,
	/**
	 * 宽
	 */
	width : number,
	/**
	 * 高
	 */
	height : number,
	/**
	 * 圆角
	 */
	radius ?: number,
	/**
	 * 间距
	 */
	margin ?: number,
	/**
	 * 点击事件
	 */
	click ?: (data : UxMovableItem) => void
}

export type UxMovableItem = {
	/**
	 * 下标
	 */
	index: number,
	/**
	 * 唯一id
	 */
	id : number,
	/**
	 * 文本
	 */
	label ?: string,
	/**
	 * 文本样式
	 */
	labelStyle ?: UxMovableStyle,
	/**
	 * 副文本
	 * 仅 list 有效
	 */
	summary ?: string,
	/**
	 * 副文本样式
	 * 仅 list 有效
	 */
	summaryStyle ?: UxMovableStyle,
	/**
	 * 图片地址
	 * gird 背景图
	 * list 左侧图
	 */
	url ?: string,
	/**
	 * 缩略图尺寸
	 */
	thumbSize ?: number,
	/**
	 * 左上占位
	 * 仅 grid 有效
	 */
	leftTop ?: UxMovablePlaceholder,
	/**
	 * 右上占位
	 * 仅 grid 有效
	 */
	rightTop ?: UxMovablePlaceholder,
	/**
	 * 左下占位
	 * 仅 grid 有效
	 */
	leftBottom ?: UxMovablePlaceholder,
	/**
	 * 右下占位
	 * 仅 grid 有效
	 */
	rightBottom ?: UxMovablePlaceholder,
	/**
	 * 上传按钮
	 * 仅 grid 有效
	 */
	upload ?: UxMovableUpload,
	/**
	 * loading
	 * 仅 grid 有效
	 */
	loading ?: UxMovableLoading,
	/**
	 * item 高度
	 * 仅 list 有效
	 */
	height ?: number,
	/**
	 * 圆角
	 */
	radius ?: number,
	/**
	 * 边框
	 */
	border ?: UxMovableBorder
	/**
	 * 选中状态
	 * 仅 grid 多选有效
	 */
	selected ?: UxMovableCheckbox
	/**
	 * 显示左侧拖拽图标
	 * 仅 list 有效
	 */
	showDrag ?: boolean,
	/**
	 * 输入事件
	 */
	input ?: UxMovableInput
	/**
	 * 点击事件
	 */
	click ?: (data : UxMovableItem) => void
}

export class UxMovable {

	$element : UniNativeViewElement
	movableView : UxMovableView

	constructor(element : UniNativeViewElement, options : UxMovableOptions) {
		this.$element = element
		this.movableView = new UxMovableView(this.$element.getAndroidActivity()! as Context, options)
		this.$element.bindAndroidView(this.movableView);
	}

	setData(data : UxMovableItem[]) {
		this.movableView.setData(data)
	}
	
	addData(data : UxMovableItem) {
		this.movableView.addData(data)
	}
	
	delData(data : UxMovableItem) {
		this.movableView.delData(data)
	}
	
	selectData(data: UxMovableItem) {
		this.movableView.selectData(data)
	}
	
	upload(data : UxMovableItem) {
		this.movableView.upload(data)
	}
	
	setLayout(layout : UxMovableLayout) {
		this.movableView.setLayout(layout)
	}

	setColumn(column : number) {
		this.movableView.setColumn(column)
	}
	
	setSpacing(spacing: number) {
		this.movableView.setSpacing(spacing)
	}
	
	setAspectRatio(aspectRatio: number) {
		this.movableView.setAspectRatio(aspectRatio)
	}
	
	setEditable(editable : boolean) {
		this.movableView.setEditable(editable)
	}
	
	setMultiple(multiple : boolean) {
		this.movableView.setMultiple(multiple)
	}
	
	setSwipe(swipe : boolean) {
		this.movableView.setSwipe(swipe)
	}

	setDisabled(disabled : boolean) {
		this.movableView.setDisabled(disabled)
	}
}

class UxMovableView extends RecyclerView {
	private context : Context
	private adapter : UxMovableAdapter
	private touchHelp : ItemTouchHelper
	private callback : UxMovableCallback
	private itemDecoration: UxMovableGridItemDecoration
	private options : UxMovableOptions

	constructor(ctx : Context, options : UxMovableOptions) {
		super(ctx)
		this.context = ctx
		this.options = options
		
		this.setLayoutParams(new ViewGroup.LayoutParams(
		    ViewGroup.LayoutParams.MATCH_PARENT,
		    ViewGroup.LayoutParams.WRAP_CONTENT
		))
		
		// 布局方式
		if (this.options.layout == 'list') {
			this.setLayoutManager(new LinearLayoutManager(this.context))
		} else {
			this.setLayoutManager(new GridLayoutManager(this.context, this.options.column.toInt()))
		}
		
		// 均分间距
		this.itemDecoration = UxMovableGridItemDecoration(this.options)
		this.setItemDecoration()

		// 适配器
		this.adapter = new UxMovableAdapter(this.context, this.options)
		setAdapter(this.adapter)

		// 拖拽事件
		this.callback = new UxMovableCallback(this.getDirection(), 0, this.adapter, this.options)
		this.touchHelp = new ItemTouchHelper(this.callback)
		this.touchHelp.attachToRecyclerView(this)
		
		// 布局尺寸监听
		let that = this
		class UxViewTreeObserver extends ViewTreeObserver.OnGlobalLayoutListener {
			override onGlobalLayout() {
			    if (that.width > 0) {
			        that.adapter.setRecyclerViewWidth(that.width)
			        that.viewTreeObserver.removeOnGlobalLayoutListener(this)
			    }
			}
		}
		this.viewTreeObserver.addOnGlobalLayoutListener(new UxViewTreeObserver())
	}
	
	override onLayout(changed: boolean, l: Int, t: Int, r: Int, b: Int) {
	    super.onLayout(changed, l, t, r, b)
	    if (changed) {
	        this.adapter.setRecyclerViewWidth(this.width)
	    }
	}

	setData(data : UxMovableItem[]) {
		this.adapter.setData(data)
	}
	
	addData(data : UxMovableItem) {
		this.adapter.addData(data)
	}
	
	delData(data : UxMovableItem) {
		this.adapter.delData(data)
	}
	
	selectData(data: UxMovableItem) {
		this.adapter.selectData(data)
	}
	
	upload(data : UxMovableItem) {
		this.adapter.upload(data)
	}
	
	setLayout(layout : UxMovableLayout) {
		this.options.layout = layout

		if (options.layout == 'list') {
			this.setLayoutManager(new LinearLayoutManager(this.context))
		} else {
			this.setLayoutManager(new GridLayoutManager(this.context, options.column.toInt()))
		}
		
		this.adapter.notifyDataSetChanged()
	}
	
	setColumn(column : number) {
		this.options.column = column

		if (options.layout != 'list') {
			this.setLayoutManager(new GridLayoutManager(this.context, options.column.toInt()))
			this.adapter.notifyDataSetChanged()
		}
	}
	
	setSpacing(spacing: number) {
		this.options.spacing = spacing
	}
	
	setAspectRatio(aspectRatio: number) {
		this.options.aspectRatio = aspectRatio
		this.adapter.notifyDataSetChanged()
	}
	
	setEditable(editable : boolean) {
		this.options.editable = editable
		this.adapter.notifyDataSetChanged()
	}
	
	setMultiple(multiple : boolean) {
		this.options.multiple = multiple
		this.adapter.notifyDataSetChanged()
	}

	setSwipe(swipe : boolean) {
		this.options.swipe = swipe
	}

	setDisabled(disabled : boolean) {
		this.options.disabled = disabled
	}
	
	setItemDecoration() {
		if (options.layout == 'list') {
			this.removeItemDecoration(this.itemDecoration)
		} else {
			this.addItemDecoration(this.itemDecoration)
		}
	}
	
	getDirection() : Int {
		if (this.options.layout == 'list') {
			return ItemTouchHelper.UP | ItemTouchHelper.DOWN
		} else {
			return ItemTouchHelper.UP | ItemTouchHelper.DOWN | ItemTouchHelper.LEFT | ItemTouchHelper.RIGHT
		}
	}
}

class UxMovableAdapter extends RecyclerView.Adapter<UxMovableHolder> {
	private context : Context
	private options : UxMovableOptions
	private items : UxMovableItem[] = []
	private recyclerViewWidth: number = 0.0
	private validWidth: boolean = false
	
	private static VIEW_TYPE_LINEAR : Int = 1;
	private static VIEW_TYPE_GRID : Int = 2;
	private static VIEW_TYPE_UPLOAD : Int = 3;

	constructor(context : Context, options : UxMovableOptions) {
		super()
		this.context = context
		this.options = options
	}
	
	setRecyclerViewWidth(width: number) {
	    if (width > 0 && width != this.recyclerViewWidth) {
	        this.recyclerViewWidth = width
			this.validWidth = true
	        this.notifyDataSetChanged()
	    }
	}

	setData(newItems : UxMovableItem[]) {
		this.items.clear();
		this.items.addAll(newItems);
		this.notifyDataSetChanged();
	}
	
	addData(data : UxMovableItem) {
		this.items.add(data)
		this.notifyItemInserted(this.items.length.toInt() - 1)
	}
	
	delData(data : UxMovableItem) {
		const position = this.items.findIndex(e => e.id == data.id)
		if(position < 0) {
			return
		}
		
		this.items.splice(position, 1)
		this.notifyItemRemoved(position.toInt())
	}
	
	selectData(data: UxMovableItem) {
		const position = this.items.findIndex(e => e.id == data.id)
		if(position < 0) {
			return
		}
		
		this.items.splice(position, 1, data)
		this.notifyDataSetChanged();
	}
	
	upload(data : UxMovableItem) {
		const position = this.items.findIndex(e => e.id == data.id)
		if(position < 0) {
			return
		}
		
		this.items.splice(position, 1, data)
		this.notifyDataSetChanged();
	}
	
	moveItem(from : Int, to : Int) {
		// 从数据源中移除 from 位置的元素
		let item = this.items.removeAt(from)
		
		// 将移除的元素插入到 to 位置
		this.items.add(to, item)

		this.notifyItemMoved(from, to);
	}
	
	removeItem(position : Int) {
		this.items.splice(position, 1)
		this.notifyItemRemoved(position.toInt())
		this.options.change(this.items)
	}

	endMove() {
		this.options.change(this.items)
	}

	override getItemViewType(position : Int) : Int {
		if(this.options.layout == 'list') {
			return VIEW_TYPE_LINEAR
		} else {
			let item : UxMovableItem = this.items.get(position);
			return item.upload != null ? VIEW_TYPE_UPLOAD : VIEW_TYPE_GRID
		}
	}

	override onCreateViewHolder(parent : ViewGroup, viewType : Int) : UxMovableHolder {
		let view : View;
		if (viewType == VIEW_TYPE_LINEAR) {
			view = LayoutInflater.from(this.context).inflate(R.layout.movable_list, parent, false);
		} else {
			view = LayoutInflater.from(this.context).inflate(R.layout.movable_grid, parent, false);
		}
		
		return new UxMovableHolder(view, this.options, viewType == VIEW_TYPE_UPLOAD);
	}

	override onBindViewHolder(holder : UxMovableHolder, position : Int) {
		if (!this.validWidth) return
		
		let item : UxMovableItem = this.items.get(position);
		let thumbSize = (item.thumbSize ?? 300).toInt()
		
		let itemWidth = (this.recyclerViewWidth - (this.options.column + 1) * (this.options.spacing * 2)) / this.options.column
		let itemHeight = itemWidth * this.options.aspectRatio
		let imgHeight = itemHeight
		if(this.options.editable) {
			itemHeight += 80
		}
		
		// grid 均分宽度
		if(this.options.layout != 'list') {
			let params = holder.itemView.layoutParams
			params.width = itemWidth.toInt()
			params.height = itemHeight.toInt()
			holder.itemView.layoutParams = params
			
			if(holder.wrap != null) {
				let params = holder.wrap!.layoutParams
				params.width = itemWidth.toInt()
				params.height = imgHeight.toInt()
				holder.wrap!.layoutParams = params
			}
		}
		
		// 上传按钮
		if(item.upload != null && this.options.layout == 'grid') {
			if (item.upload!.url != '' && holder.img != null) {
				holder.img?.setVisibility(View.VISIBLE)
				
				let params = holder.img!.layoutParams
				params.width = itemWidth.toInt()
				params.height = imgHeight.toInt()
				holder.img!.layoutParams = params
				
				if((item.upload!.backgroundColor ?? '') != '') {
					holder.img!.setBackgroundColor(Color.parseColor(item.upload!.backgroundColor!))
				}
				
				let res = this.getPath(item.upload!.url)
				if(res != null) {
					if(typeof res == 'string') {
						Glide.with(this.context)
							.load(res! as string)
							.into(holder.img!)
					} else {
						Glide.with(this.context)
							.load(res! as Bitmap)
							.into(holder.img!)
					}
				}
			} else {
				holder.img?.setVisibility(View.GONE)
			}
		} else {
			// 背景图
			if (item.url != null && holder.img != null) {
				holder.img?.setVisibility(View.VISIBLE)
				
				if(this.options.layout == 'grid') {
					let params = holder.img!.layoutParams
					params.width = itemWidth.toInt()
					params.height = imgHeight.toInt()
					holder.img!.layoutParams = params
				}
				
				let res = this.getPath(item.url!)
				if(res != null) {
					if(typeof res == 'string') {
						Glide.with(this.context)
							.load(res! as string)
							.override(thumbSize, thumbSize)
							.centerCrop()
							.into(holder.img!)
					} else {
						Glide.with(this.context)
							.load(res! as Bitmap)
							.override(thumbSize, thumbSize)
							.centerCrop()
							.into(holder.img!)
					}
				}
			} else {
				holder.img?.setVisibility(View.GONE)
			}
		}
		
		// 圆角
		if((item.radius ?? 0) > 0) {
			if(this.options.editable) {
				if(holder.img != null) {
					holder.img!.outlineProvider = new UxOutlineProvider(dpToFloat(item.radius!))
					holder.img!.clipToOutline = true
				}
			} else {
				holder.itemView.outlineProvider = new UxOutlineProvider(dpToFloat(item.radius!))
				holder.itemView.clipToOutline = true
			}
		}

		// 上传进度条
		let isLoading = false
		if (item.loading != null && item.loading!.show) {
			isLoading = true
			holder.overlay?.setVisibility(View.VISIBLE)
			holder.overlay?.setBackgroundColor(Color.argb((204 * (1 - item.loading!.progress / 100)).toInt(), 0, 0, 0))
			
			holder.progress?.setVisibility(View.VISIBLE)
			if(item.loading!.progress == 0 && (item.loading!.waitingLabel ?? '') != '') {
				holder.progress?.setText(item.loading!.waitingLabel!)
				holder.progress?.setTextSize((11).toFloat())
			} else {
				holder.progress?.setText(`${item.loading!.progress}%`)
				holder.progress?.setTextSize((15).toFloat())
			}
			
			if (item.loading!.style != null) {
				this.setTestStyle(holder.progress!, item.loading!.style!)
			}
		} else {
			holder.overlay?.setVisibility(View.GONE)
			holder.progress?.setVisibility(View.GONE)
		}
		
		// 文本
		if((item.label ?? '') != '' && !isLoading) {
			holder.label.setVisibility(View.VISIBLE)
			holder.label.setText(item.label ?? '')
			if (item.labelStyle != null) {
				this.setTestStyle(holder.label, item.labelStyle!)
			}
		}
		
		// 副文本
		if ((item.summary ?? '') != '' && !isLoading) {
			holder.summary?.setVisibility(View.VISIBLE)
			holder.summary?.setText(item.summary!)
			if (item.summaryStyle != null) {
				this.setTestStyle(holder.summary!, item.summaryStyle!)
			}
		} else {
			holder.summary?.setVisibility(View.GONE)
		}
		
		// 输入框
		if(this.options.editable && item.upload == null) {
			let input = item.input ?? {
				content: '',
				placeholder: '请输入',
				maxLength: 9,
			} as UxMovableInput
			holder.input?.setVisibility(View.VISIBLE)
			holder.input?.setHint(input.placeholder)
		}
		
		// 边框
		let borderColor = '#f0f0f0'
		if(item.border != null && item.border!.color != '') {
			borderColor = item.border!.color!
		}
		if(borderColor != '') {
			holder.line?.setBackgroundColor(Color.parseColor(borderColor))
		}
		
		if(this.options.layout == 'grid') {
			if(item.border != null) {
				
			}
		}

		// 拖拽
		if (item.showDrag ?? true) {
			holder.drag?.setVisibility(View.VISIBLE)
		} else {
			holder.drag?.setVisibility(View.GONE)
		}

		// 左上
		if(item.leftTop != null && holder.leftTop != null && !isLoading) {
			this.setPlaceholder(item.leftTop!, holder.leftTop!)
		}

		// 左下
		if (item.leftBottom != null && holder.leftBottom != null && !isLoading) {
			this.setPlaceholder(item.leftBottom!, holder.leftBottom!)
		}

		// 右上
		if (item.rightTop != null && holder.rightTop != null && !isLoading) {
			if(this.options.multiple && item.selected != null) {
				// 多选
				this.setPlaceholder({
					url: item.selected!.checked ? item.selected!.selectedUrl : item.selected!.unselectedUrl,
					width: item.selected!.width,
					height: item.selected!.height,
					margin: item.selected!.margin
				} as UxMovablePlaceholder, holder.rightTop!)
			} else {
				this.setPlaceholder(item.rightTop!, holder.rightTop!)
			}
		}
	
		// 右下
		if (item.rightBottom != null && holder.rightBottom != null && !isLoading) {
			this.setPlaceholder(item.rightBottom!, holder.rightBottom!)
		}
		
		// 绑定事件
		holder.itemView.setOnClickListener((e) => {
			item.click?.(item)
		})
		
		if (holder.leftTop != null) {
			holder.leftTop!.setOnClickListener((e) => {
				if(this.options.multiple) {
					item.click?.(item)
				} else {
					item.leftTop?.click?.(item)
				}
			})
		}
		
		if (holder.leftBottom != null) {
			holder.leftBottom!.setOnClickListener((e) => {
				if(this.options.multiple) {
					item.click?.(item)
				} else {
					item.leftBottom?.click?.(item)
				}
			})
		}
		
		if (holder.rightTop != null) {
			holder.rightTop!.setOnClickListener((e) => {
				if(this.options.multiple) {
					item.click?.(item)
				} else {
					item.rightTop?.click?.(item)
				}
			})
		}
		
		if (holder.rightBottom != null) {
			holder.rightBottom!.setOnClickListener((e) => {
				if(this.options.multiple) {
					item.click?.(item)
				} else {
					item.rightBottom?.click?.(item)
				}
			})
		}
	}

	override getItemCount() : Int {
		return this.items.size;
	}

	setTestStyle(holder : TextView, style : UxMovableStyle) {
		if (style.color != null) {
			holder.setTextColor(Color.parseColor(style.color!))
		}
		if (style.fontSize != null) {
			holder.setTextSize(style.fontSize!.toFloat())
		}
		if (style.fontWeight ?? false) {
			holder.setTypeface(null, Typeface.BOLD)
		}
		if (style.backgroundColor != null) {
			holder.setBackgroundColor(Color.parseColor(style.backgroundColor!))
		}
	}

	setPlaceholder(item : UxMovablePlaceholder, holder : ImageView) {
		if (item.url != '') {
			holder.setVisibility(View.VISIBLE)
			
			let res = this.getPath(item.url)
			if(res != null) {
				if(typeof res == 'string') {
					Glide.with(this.context)
						.load(res! as string)
						.override(200, 200)
						.into(holder)
				} else {
					Glide.with(this.context)
						.load(res! as Bitmap)
						.override(200, 200)
						.into(holder)
				}
			}
			
			// 宽高
			let params = holder.layoutParams as ViewGroup.MarginLayoutParams
			params.width = this.dpToInt(item.width)
			params.height = this.dpToInt(item.height)
			
			// 边距
			let margin = this.dpToInt(item.margin ?? 0)
			params.setMargins(margin, margin, margin, margin)
			holder.layoutParams = params
			
			// 圆角
			if((item.radius ?? 0) > 0) {
				holder.outlineProvider = new UxOutlineProvider(dpToFloat(item.radius!))
				holder.clipToOutline = true
			}
		} else {
			holder.setVisibility(View.GONE)
		}
	}

	getPath(url : string) : any | null {
		if(url == '' || url.startsWith("http://") || url.startsWith("https://")) {
			return url
		}
		
		try {
			// uni-app x 正式打包会放在asset中，需要特殊处理
			let path = UTSAndroid.getResourcePath(url)
			if(path.startsWith("/android_asset")){
				return this.getBitmapFromAsset(path.substring(15))
			}
		
			return path
		} catch (e) {
			console.error(e);
		}

		return url
	}
	
	getBitmapFromAsset(filePath: string): Bitmap | null {
	    var assetManager = UTSAndroid.getAppContext()!.getAssets();
	    var inputStream: InputStream | null = null;
	    var bitmap: Bitmap | null = null;
	    
	    try {
	        inputStream = assetManager.open(filePath)
	        bitmap = BitmapFactory.decodeStream(inputStream)
	    } catch (e) {
	        e.printStackTrace();
	    } finally {
	        if (inputStream != null) {
	            try {
	                inputStream?.close()
	            } catch (e) {
	                e.printStackTrace()
	            }
	        }
	    }
		
	    return bitmap;
	}
	
	dpToInt(dp : number) : Int {
		return (dp.toInt() * this.context.resources.displayMetrics.density).toInt()
	}
	
	dpToFloat(dp : number) : Float {
		return (dp.toFloat() * this.context.resources.displayMetrics.density).toFloat()
	}
}

export class UxMovableHolder extends RecyclerView.ViewHolder {
	label : TextView;
	summary ?: TextView = null;
	img ?: ImageView = null;
	wrap ?: FrameLayout = null
	overlay ?: View = null;
	progress ?: TextView = null;
	leftTop ?: ImageView = null;
	rightTop ?: ImageView = null;
	leftBottom ?: ImageView = null;
	rightBottom ?: ImageView = null;
	input ?: EditText = null;
	line ?: View = null;
	drag ?: ImageView = null;
	isUpload: boolean

	constructor(itemView : View, options : UxMovableOptions, isUpload: boolean) {
		super(itemView);
		
		// 上传按钮标识
		this.isUpload = isUpload
		
		// 绑定视图
		img = itemView.findViewById(R.id.img);
		label = itemView.findViewById(R.id.label);

		if (options.layout == 'list') {
			summary = itemView.findViewById(R.id.summary);
			line = itemView.findViewById(R.id.line);
			drag = itemView.findViewById(R.id.drag);
		} else {
			wrap = itemView.findViewById(R.id.wrap);
			overlay = itemView.findViewById(R.id.overlay);
			progress = itemView.findViewById(R.id.progress);
			leftTop = itemView.findViewById(R.id.leftTop);
			rightTop = itemView.findViewById(R.id.rightTop);
			leftBottom = itemView.findViewById(R.id.leftBottom);
			rightBottom = itemView.findViewById(R.id.rightBottom);
			input = itemView.findViewById(R.id.input); 
		}
	}
}

class UxMovableCallback extends ItemTouchHelper.SimpleCallback {

	adapter : UxMovableAdapter
	options : UxMovableOptions

	constructor(p1 : Int, p2 : Int, adapter : UxMovableAdapter, options : UxMovableOptions) {
		super(p1, p2)
		this.adapter = adapter
		this.options = options
	}

	override getMovementFlags(recyclerView : RecyclerView, viewHolder : RecyclerView.ViewHolder) : Int {
		let _viewHolder = viewHolder as UxMovableHolder
		if(_viewHolder.isUpload) {
			return makeMovementFlags(0, 0)
		} else {
			// 拖拽方向：上下左右（列表式只支持上下，宫格式支持上下左右）
			let dragFlags = ItemTouchHelper.UP | ItemTouchHelper.DOWN;
			if (recyclerView.getLayoutManager() instanceof GridLayoutManager) {
				dragFlags |= ItemTouchHelper.LEFT | ItemTouchHelper.RIGHT;
			}
			
			return makeMovementFlags(dragFlags, this.options.swipe ? ItemTouchHelper.LEFT : 0)
		}
	}

	override onSelectedChanged(viewHolder ?: RecyclerView.ViewHolder, actionState : Int) {
		super.onSelectedChanged(viewHolder, actionState)
		
		if (actionState == ItemTouchHelper.ACTION_STATE_DRAG) {
			// 长按拖拽时放大
			if(this.options.layout == 'list') {
				let scale = (1.05).toFloat()
				viewHolder?.itemView?.animate()?.scaleX(scale)?.scaleY(scale)?.setDuration(200)?.start()
			} else {
				let scale = (1.1).toFloat()
				let offset = (10).toFloat()
				viewHolder?.itemView?.animate()?.translationX(offset)?.translationY(offset).scaleX(scale)?.scaleY(scale)?.setDuration(200)?.start()
			}
		}
	}

	override clearView(recyclerView : RecyclerView, viewHolder : RecyclerView.ViewHolder) {
		super.clearView(recyclerView, viewHolder)
		
		// 拖拽结束后恢复大小
		let scale = (1).toFloat()
		let offset = (0).toFloat()
		if(this.options.layout == 'list') {
			viewHolder.itemView.animate()?.scaleX(scale)?.scaleY(scale)?.setDuration(200)?.start()
		} else {
			viewHolder.itemView.animate()?.translationX(offset)?.translationY(offset).scaleX(scale)?.scaleY(scale)?.setDuration(200)?.start()
		}
		
		// 结束滑动
		this.adapter.endMove()
	}
	
	@Suppress("DEPRECATION")
	override onMove(recyclerView : RecyclerView, viewHolder : RecyclerView.ViewHolder, target : RecyclerView.ViewHolder) : boolean {
		let from = viewHolder.getAdapterPosition();
		let to = target.getAdapterPosition();
		
		// 如果拖拽的是上传按钮，不允许移动
		let _viewHolder = viewHolder as UxMovableHolder
		if(_viewHolder.isUpload) {
			return false
		}
			
		// 如果目标位置是上传按钮的位置，不允许移动
		let _target = target as UxMovableHolder
		if(_target.isUpload) {
			return false
		}
		
		this.adapter.moveItem(from, to);
		return true;
	}
	
	@Suppress("DEPRECATION")
	override onSwiped(viewHolder : RecyclerView.ViewHolder, direction : Int) {
		// 滑动删除
		let position = viewHolder.adapterPosition
		this.adapter.removeItem(position)
	}

	override isLongPressDragEnabled() : boolean {
		// 长按触发拖拽
		return !this.options.disabled;
	}
}

class UxMovableGridItemDecoration extends RecyclerView.ItemDecoration {
	options: UxMovableOptions

	constructor(options: UxMovableOptions) {
		this.options = options
	}

	override getItemOffsets(
		outRect : Rect,
		view : View,
		parent : RecyclerView,
		state : RecyclerView.State
	) {
		if(this.options.layout == 'list') {
			return
		}
		
		let spacing = this.options.spacing.toInt() * 2
		let column = this.options.column.toInt()
		
		let position = parent.getChildAdapterPosition(view)
		if (position == RecyclerView.NO_POSITION) {
		    return
		}
		
		let row = Math.floor(position / column)
		let col = position % column

		outRect.left = spacing - col * spacing / column
		outRect.right = (col + 1) * spacing / column
		if (row > 0) {
			outRect.top = spacing
		}
	}
}

class UxOutlineProvider extends ViewOutlineProvider {
	radius: Float
	constructor(radius: Float) {
	   super()
	   this.radius = radius
	}
	
	override getOutline(view: View, outline: Outline) {
	    outline.setRoundRect(0, 0, view.width, view.height, this.radius)
	}
}