import Build from 'android.os.Build';
import Context from 'android.content.Context';
import NotificationManager from 'android.app.NotificationManager';
import NotificationChannel from 'android.app.NotificationChannel';
import Notification from 'android.app.Notification';
import Intent from 'android.content.Intent';
import ComponentName from 'android.content.ComponentName';
import PendingIntent from 'android.app.PendingIntent';
import Activity from "android.app.Activity";
import Bundle from 'android.os.Bundle';
import View from 'android.view.View';
import Color from 'android.graphics.Color';
import WindowManager from 'android.view.WindowManager';

import { UxCreateNotificationProgressOptions, UxFinishNotificationProgressOptions } from '../interface.uts';

const ACTION_DOWNLOAD_FINISH = "ACTION_DOWNLOAD_FINISH"
const ACTION_DOWNLOAD_PROGRESS = "ACTION_DOWNLOAD_PROGRESS"

const DOWNLOAD_PROGRESS_NOTIFICATION_ID : Int = 7890
const DC_DOWNLOAD_CHANNEL_ID = "下载文件"
const DC_DOWNLOAD_CHANNEL_NAME = "用于显示现在进度的渠道"

let globalNotificationProgressCallBack : (() => void) | null = () => { }
let globalNotificationProgressFinishCallBack = () => { }

let notificationBuilder : Notification.Builder | null = null

let timeId = -1

let histroyProgress = 0

let isProgress = false

export function createNotificationProgress(options : UxCreateNotificationProgressOptions) : void {
	const { content, progress, onClick } = options

	if (progress == 100) {
		clearTimeout(timeId)
		const context = UTSAndroid.getAppContext() as Context
		realCreateNotificationProgress(options.title ?? getAppName(context), content, progress, onClick)
		reset()
		return
	}

	histroyProgress = progress
	if (timeId != -1) {
		return
	}

	const context = UTSAndroid.getAppContext() as Context
	if (!isProgress) {
		realCreateNotificationProgress(options.title ?? getAppName(context), content, histroyProgress, onClick)
		isProgress = true
	} else {
		timeId = setTimeout(() => {
			realCreateNotificationProgress(options.title ?? getAppName(context), content, histroyProgress, onClick)
			timeId = -1
		}, 1000)
	}
}

export function cancelNotificationProgress() : void {
	const context = UTSAndroid.getAppContext() as Context
	const notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
	notificationManager.cancel(DOWNLOAD_PROGRESS_NOTIFICATION_ID)
	reset()
}

export function finishNotificationProgress(options : UxFinishNotificationProgressOptions) {
	globalNotificationProgressFinishCallBack = options.onClick
	const context = UTSAndroid.getAppContext() as Context
	const notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
	createDownloadChannel(notificationManager)
	const builder = createNotificationBuilder(context)
	builder.setProgress(0, 0, false)
	builder.setContentTitle(options.title ?? getAppName(context))
	builder.setContentText(options.content)
	//小米rom setOngoing未false的时候，会被通知管理器归为不重要通知
	// builder.setOngoing(false)
	builder.setAutoCancel(true);
	builder.setContentIntent(createPendingIntent(context, ACTION_DOWNLOAD_FINISH));
	notificationManager.notify(DOWNLOAD_PROGRESS_NOTIFICATION_ID, builder.build())
	reset()
}

function realCreateNotificationProgress(title : string, content : string, progress : number, cb : (() => void) | null) : void {
	globalNotificationProgressCallBack = cb
	const context = UTSAndroid.getAppContext() as Context
	const notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
	createDownloadChannel(notificationManager)
	const builder = createNotificationBuilder(context)
	builder.setProgress(100, progress.toInt(), false)
	builder.setContentTitle(title)
	builder.setContentText(content)
	builder.setContentIntent(createPendingIntent(context, ACTION_DOWNLOAD_PROGRESS));
	notificationManager.notify(DOWNLOAD_PROGRESS_NOTIFICATION_ID, builder.build())
}

function reset() {
	isProgress = false
	notificationBuilder = null
	histroyProgress = 0
	if (timeId != -1) {
		clearTimeout(timeId)
		timeId = -1
	}
}

function createPendingIntent(context : Context, action : string) : PendingIntent {
	const i = new Intent(action);
	i.setComponent(new ComponentName(context.getPackageName(), "uts.sdk.modules.uxNotification.TransparentActivity"));
	let flags = PendingIntent.FLAG_ONE_SHOT;
	if (Build.VERSION.SDK_INT >= 23) {
		flags = PendingIntent.FLAG_ONE_SHOT | PendingIntent.FLAG_IMMUTABLE;
	}
	return PendingIntent.getActivity(context, DOWNLOAD_PROGRESS_NOTIFICATION_ID, i, flags);
}

function createDownloadChannel(notificationManager : NotificationManager) {
	if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
		const channel = new NotificationChannel(
			DC_DOWNLOAD_CHANNEL_ID,
			DC_DOWNLOAD_CHANNEL_NAME,
			NotificationManager.IMPORTANCE_LOW
		)
		notificationManager.createNotificationChannel(channel)
	}
}

@Suppress("DEPRECATION")
function createNotificationBuilder(context : Context) : Notification.Builder {
	if (notificationBuilder == null) {
		if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
			notificationBuilder = new Notification.Builder(context, DC_DOWNLOAD_CHANNEL_ID)
		} else {
			notificationBuilder = new Notification.Builder(context)
		}
		notificationBuilder!.setSmallIcon(context.getApplicationInfo().icon)
		notificationBuilder!.setOngoing(true)
		notificationBuilder!.setSound(null)
	}
	return notificationBuilder!
}

@Suppress("DEPRECATION")
function getAppName(context : Context) : string {
	let appName = ""
	try {
		const packageManager = context.getPackageManager()
		const applicationInfo = packageManager.getApplicationInfo(context.getPackageName(), 0)
		appName = packageManager.getApplicationLabel(applicationInfo) as string
	} catch (e : Exception) {
		e.printStackTrace()
	}
	return appName
}

export class TransparentActivity extends Activity {
	constructor() {
		super()
	}

	@Suppress("DEPRECATION")
	override onCreate(savedInstanceState : Bundle | null) {
		super.onCreate(savedInstanceState)
		this.fullScreen(this)
		const action = this.getIntent().getAction()
		if (action == ACTION_DOWNLOAD_FINISH) {
			setTimeout(() => {
				globalNotificationProgressFinishCallBack()
				globalNotificationProgressFinishCallBack = () => { }
			}, 100)
			this.overridePendingTransition(0, 0)
		}

		if (action == ACTION_DOWNLOAD_PROGRESS) {
			setTimeout(() => {
				globalNotificationProgressCallBack?.()
				globalNotificationProgressCallBack = () => { }
			}, 100)
			this.overridePendingTransition(0, 0)
		}

		setTimeout(() => {
			this.finish()
		}, 20)
	}

	@Suppress("DEPRECATION")
	private fullScreen(activity : Activity) {
		if (Build.VERSION.SDK_INT >= 19) {
			if (Build.VERSION.SDK_INT >= 21) {
				const window = activity.getWindow();
				const decorView = window.getDecorView();
				const option = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_STABLE;
				decorView.setSystemUiVisibility(option);
				window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
				window.setStatusBarColor(Color.TRANSPARENT);
			} else {
				const window = activity.getWindow();
				const attributes = window.getAttributes();
				const flagTranslucentStatus = WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS;
				attributes.flags |= flagTranslucentStatus;
				window.setAttributes(attributes);
			}
		}
	}
}