---
description: 
globs: 
alwaysApply: true
---
The following rules must be strictly followed during project development:
1. Component Import Rule
All components in the project have been automatically imported on demand via the unplugin-vue-components/vite plugin. Manual re-import of any component is prohibited.
2. Component Storage and Documentation Requirements
Newly created Vue components must be placed in the @src/components directory.
Meanwhile, a documentation file for the component must be generated in the @docs/components directory (the documentation should include key information such as component functions, usage methods, and parameter descriptions).
3. Style Implementation Specification
All code involving styles in the project must be implemented using TailwindCSS. Other styling solutions (such as native CSS, Less, Sass, etc.) are not allowed.