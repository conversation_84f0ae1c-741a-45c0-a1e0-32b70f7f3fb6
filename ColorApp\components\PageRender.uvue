<template>
	<view v-if="pageConfig" class="page-render">
		<!-- 页面头部 -->
		<view v-if="pageConfig.header" class="page-header">
			<view class="header-content">
				<view v-if="pageConfig.header.showBackButton" class="back-button" @click="goBack">
					<text class="back-icon">←</text>
				</view>
				<text class="header-title">{{ pageConfig.header.title }}</text>
			</view>
		</view>
		
		<!-- 页面布局容器 -->
		<view 
			v-if="pageConfig.pageLayout"
			:class="getLayoutClass()"
			:style="getLayoutStyle()"
			class="page-layout"
		>
			<!-- 渲染页面组件 -->
			<DynamicComponent 
				v-for="(component, index) in pageConfig.components" 
				:key="component.id || index"
				:config="component"
				:data-source="getDataSource(component.dataSource)"
				@event="handleComponentEvent"
			/>
		</view>
		
		<!-- 加载状态 -->
		<view v-if="loading" class="loading-overlay">
			<view class="loading-content">
				<text class="loading-text">加载中...</text>
			</view>
		</view>
		
		<!-- 错误状态 -->
		<view v-if="error" class="error-overlay">
			<view class="error-content">
				<text class="error-text">{{ error }}</text>
				<button class="retry-button" @click="retryLoad">
					<text class="retry-text">重试</text>
				</button>
			</view>
		</view>
	</view>
</template>

<script setup lang="uts">
	import { ref, reactive, onMounted, computed, watch } from 'vue'
	import DynamicComponent from './DynamicComponent.uvue'
	
	// 页面配置类型定义
	type PageConfig = {
		version: string
		pageId: string
		header?: {
			title: string
			showBackButton: boolean
		}
		dataSources?: UTSJSONObject
		router?: {
			path: string
		}
		pageLayout?: {
			id: string
			component: string
			props?: UTSJSONObject
		}
		components?: ComponentConfig[]
	}
	
	type ComponentConfig = {
		id: string
		component: string
		props?: UTSJSONObject
		children?: ComponentConfig[]
		value?: UTSJSONObject | string
		dataSource?: string
		events?: UTSJSONObject
	}
	
	type DataSource = {
		id: string
		type: string
		options: UTSJSONObject
		data: UTSJSONObject
		autoLoad: boolean
	}
	
	// Props 定义
	const props = defineProps<{
		config?: PageConfig | string
		autoLoad?: boolean
	}>()
	
	// Emits 定义
	const emit = defineEmits<{
		loaded: [config: PageConfig]
		error: [error: string]
		event: [data: any]
	}>()
	
	// 响应式数据
	const pageConfig = ref<PageConfig | null>(null)
	const dataSources = reactive<Map<string, UTSJSONObject>>(new Map())
	const loading = ref(false)
	const error = ref('')
	
	// 计算属性：获取布局样式类
	const getLayoutClass = (): string => {
		const layout = pageConfig.value != null ? pageConfig.value.pageLayout : null
		if (layout != null && layout.props != null && layout.props['class'] != null) {
			return layout.props['class'] as string
		}
		return ''
	}
	
	// 获取布局样式
	const getLayoutStyle = (): UTSJSONObject => {
		const style: UTSJSONObject = {}
		const layout = pageConfig.value != null ? pageConfig.value.pageLayout : null
		
		if (layout != null && layout.props != null) {
			// 处理 flex 布局
			if (layout.component === 'flex-layout') {
				style['display'] = 'flex'
			}
			
			// 处理 flex 方向
			if (layout.props['flexDirection'] != null) {
				style['flex-direction'] = layout.props['flexDirection']
			}
			
			// 处理对齐方式
			if (layout.props['justifyContent'] != null) {
				style['justify-content'] = layout.props['justifyContent']
			}
			
			if (layout.props['alignItems'] != null) {
				style['align-items'] = layout.props['alignItems']
			}
			
			// 处理其他样式属性
			if (layout.props['padding'] != null) {
				style['padding'] = layout.props['padding']
			}
			
			if (layout.props['margin'] != null) {
				style['margin'] = layout.props['margin']
			}
			
			if (layout.props['backgroundColor'] != null) {
				style['background-color'] = layout.props['backgroundColor']
			}
			
			if (layout.props['height'] != null) {
				style['height'] = layout.props['height']
			} else {
				style['flex'] = '1'
			}
		}
		
		return style
	}
	
	// 获取数据源
	const getDataSource = (dataSourceId: string | null): UTSJSONObject | null => {
		if (dataSourceId != null && dataSources.has(dataSourceId)) {
			return dataSources.get(dataSourceId)
		}
		return null
	}
	
	// 加载页面配置
	const loadPageConfig = async (config: PageConfig | string) => {
		try {
			loading.value = true
			error.value = ''
			
			let configData: PageConfig
			
			if (typeof config === 'string') {
				// 从 URL 或路径加载配置
				const response = await uni.request({
					url: config,
					method: 'GET'
				})
				
				if (response.statusCode === 200) {
					configData = response.data as PageConfig
				} else {
					throw new Error(`加载配置失败: ${response.statusCode}`)
				}
			} else {
				// 直接使用传入的配置对象
				configData = config
			}
			
			pageConfig.value = configData
			
			// 初始化数据源
			await initDataSources(configData.dataSources)
			
			emit('loaded', configData)
		} catch (e) {
			const errorMsg = e instanceof Error ? e.message : '加载页面配置失败'
			error.value = errorMsg
			emit('error', errorMsg)
		} finally {
			loading.value = false
		}
	}
	
	// 初始化数据源
	const initDataSources = async (dataSourcesConfig: UTSJSONObject | null) => {
		if (dataSourcesConfig == null) return
		
		// 遍历数据源配置
		for (const key in dataSourcesConfig) {
			const dsConfig = dataSourcesConfig[key] as UTSJSONObject
			
			if (dsConfig['autoLoad'] === true) {
				await loadDataSource(key, dsConfig)
			} else {
				// 初始化空数据
				dataSources.set(key, dsConfig['data'] as UTSJSONObject || {})
			}
		}
	}
	
	// 加载数据源
	const loadDataSource = async (id: string, config: UTSJSONObject) => {
		try {
			const type = config['type'] as string
			const options = config['options'] as UTSJSONObject
			
			if (type === 'api') {
				const endpoint = options['endpoint'] as string
				const method = options['method'] as string || 'GET'
				const params = options['params'] as UTSJSONObject || {}
				
				const response = await uni.request({
					url: endpoint,
					method: method as any,
					data: params
				})
				
				if (response.statusCode === 200) {
					dataSources.set(id, response.data as UTSJSONObject)
				} else {
					console.error(`数据源 ${id} 加载失败:`, response.statusCode)
					dataSources.set(id, {})
				}
			} else {
				// 其他类型的数据源
				dataSources.set(id, config['data'] as UTSJSONObject || {})
			}
		} catch (e) {
			console.error(`数据源 ${id} 加载异常:`, e)
			dataSources.set(id, {})
		}
	}
	
	// 处理组件事件
	const handleComponentEvent = (eventData: any) => {
		console.log('页面组件事件:', eventData)
		
		// 处理特定事件类型
		switch (eventData.type) {
			case 'apiRequest':
				// 处理 API 请求事件
				handleApiRequest(eventData)
				break
				
			case 'redirect':
				// 处理页面跳转事件
				handleRedirect(eventData)
				break
				
			case 'validateForm':
				// 处理表单验证事件
				handleFormValidation(eventData)
				break
				
			default:
				// 转发其他事件
				emit('event', eventData)
		}
	}
	
	// 处理 API 请求
	const handleApiRequest = async (eventData: any) => {
		try {
			const config = eventData.config
			const events = config.events != null && config.events['click'] != null ? config.events['click'] : null
			
			if (events instanceof Array) {
				for (let i = 0; i < events.length; i++) {
					const event = events[i]
					if (event.type === 'apiRequest') {
						const options = event.options
						const payload = event.payload
						
						// 获取请求数据
						let requestData = {}
						if (payload != null && dataSources.has(payload)) {
							requestData = dataSources.get(payload) as UTSJSONObject
						}
						
						const response = await uni.request({
							url: options.endpoint,
							method: options.method || 'POST',
							data: requestData
						})
						
						if (response.statusCode === 200) {
							// 处理成功回调
							if (event.onSuccess != null) {
								if (event.onSuccess.type === 'redirect') {
									uni.navigateTo({
										url: event.onSuccess.path
									})
								}
							}
						}
					}
				}
			}
		} catch (e) {
			console.error('API 请求失败:', e)
		}
	}
	
	// 处理页面跳转
	const handleRedirect = (eventData: any) => {
		const path = (eventData.data != null && eventData.data['path'] != null) ? eventData.data['path'] : 
					 (eventData.config != null && eventData.config.router != null && eventData.config.router['path'] != null) ? eventData.config.router['path'] : null
		if (path != null) {
			uni.navigateTo({
				url: path
			})
		}
	}
	
	// 处理表单验证
	const handleFormValidation = (eventData: any) => {
		const target = eventData.data != null && eventData.data['target'] != null ? eventData.data['target'] : null
		console.log('验证表单:', target)
		// 这里可以实现具体的表单验证逻辑
	}
	
	// 返回上一页
	const goBack = () => {
		uni.navigateBack()
	}
	
	// 重试加载
	const retryLoad = () => {
		if (props.config != null) {
			loadPageConfig(props.config)
		}
	}
	
	// 组件挂载时自动加载配置
	onMounted(() => {
		if (props.config != null && (props.autoLoad !== false)) {
			loadPageConfig(props.config)
		}
	})
	
	// 监听 config prop 的变化
	watch(() => props.config, (newConfig, oldConfig) => {
		console.log('PageRender config 变化:', newConfig)
		if (newConfig != null && newConfig !== oldConfig) {
			loadPageConfig(newConfig)
		}
	}, { immediate: false })
	
	// 暴露方法给父组件
	defineExpose({
		loadPageConfig,
		loadDataSource,
		getDataSource
	})
</script>

<style scoped>
	.page-render {
		flex: 1;
		flex-direction: column;
		background-color: #f5f5f5;
	}
	
	.page-header {
		background-color: #ffffff;
		border-bottom: 1px solid #e0e0e0;
		padding-top: 44px; /* 状态栏高度 */
	}
	
	.header-content {
		height: 44px;
		flex-direction: row;
		align-items: center;
		padding: 0 15px;
		position: relative;
	}
	
	.back-button {
		width: 44px;
		height: 44px;
		align-items: center;
		justify-content: center;
		position: absolute;
		left: 0;
		top: 0;
	}
	
	.back-icon {
		font-size: 18px;
		color: #007aff;
	}
	
	.header-title {
		flex: 1;
		text-align: center;
		font-size: 17px;
		font-weight: bold;
		color: #333333;
	}
	
	.page-layout {
		flex: 1;
		flex-direction: column;
	}
	
	.loading-overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.3);
		align-items: center;
		justify-content: center;
		z-index: 1000;
	}
	
	.loading-content {
		background-color: #ffffff;
		padding: 20px;
		border-radius: 8px;
		align-items: center;
		justify-content: center;
	}
	
	.loading-text {
		font-size: 16px;
		color: #333333;
	}
	
	.error-overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.3);
		align-items: center;
		justify-content: center;
		z-index: 1000;
	}
	
	.error-content {
		background-color: #ffffff;
		padding: 20px;
		border-radius: 8px;
		align-items: center;
		justify-content: center;
		max-width: 300px;
		margin: 0 20px;
	}
	
	.error-text {
		font-size: 16px;
		color: #ff3b30;
		text-align: center;
		margin-bottom: 15px;
	}
	
	.retry-button {
		background-color: #007aff;
		border: none;
		border-radius: 4px;
		padding: 10px 20px;
		align-items: center;
		justify-content: center;
	}
	
	.retry-text {
		color: #ffffff;
		font-size: 14px;
	}
	
	/* 主题样式类 */
	.bg-primary {
		background-color: #007aff;
	}
	
	.text-white {
		color: #ffffff;
	}
	
	.text-center {
		text-align: center;
	}
</style>