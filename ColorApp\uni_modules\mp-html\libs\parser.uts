import { HtmlNodeType, CssStyleType } from './types.uts'

/**
 * HTML解析器类
 */
export class HtmlParser {
  private content: string = ''
  private position: number = 0
  
  constructor(content: string) {
    this.content = content
    this.position = 0
  }
  
  /**
   * 解析HTML内容
   */
  parse(): HtmlNodeType[] {
    const nodes: HtmlNodeType[] = []
    
    while (this.position < this.content.length) {
      const node = this.parseNode()
      if (node != null) {
        nodes.push(node)
      }
    }
    
    return nodes
  }
  
  /**
   * 解析单个节点
   */
  private parseNode(): HtmlNodeType | null {
    this.skipWhitespace()
    
    if (this.position >= this.content.length) {
      return null
    }
    
    if (this.content.charAt(this.position) == '<') {
      return this.parseElement()
    } else {
      return this.parseText()
    }
  }
  
  /**
   * 解析元素节点
   */
  private parseElement(): HtmlNodeType | null {
    if (this.content.charAt(this.position) != '<') {
      return null
    }
    
    this.position++ // 跳过 '<'
    
    // 检查是否是结束标签
    if (this.content.charAt(this.position) == '/') {
      // 跳过结束标签
      while (this.position < this.content.length && this.content.charAt(this.position) != '>') {
        this.position++
      }
      if (this.position < this.content.length) {
        this.position++ // 跳过 '>'
      }
      return null
    }
    
    // 解析标签名
    const tagName = this.parseTagName()
    if (tagName == '') {
      return null
    }
    
    // 解析属性
    const attrs = this.parseAttributes()
    
    // 检查是否是自闭合标签（以 /> 结尾）
    let isSelfClosing = false
    if (this.position < this.content.length && this.content.charAt(this.position) == '/') {
      // 检查下一个字符是否是 '>'，确保这是真正的自闭合标签
      if (this.position + 1 < this.content.length && this.content.charAt(this.position + 1) == '>') {
        isSelfClosing = true
        this.position++ // 跳过 '/'
      }
    }
    
    // 跳过 '>'
    if (this.position < this.content.length && this.content.charAt(this.position) == '>') {
      this.position++
    }
    
    // 自闭合标签（通过 /> 结尾或预定义的自闭合标签）
    if (isSelfClosing || this.isSelfClosingTag(tagName)) {
      return {
        type: 'element',
        name: tagName,
        attrs: attrs,
        style: this.parseStyle(attrs.getString('style') ?? ''),
        children: []
      }
    }
    
    // 解析子节点
    const children: HtmlNodeType[] = []
    while (this.position < this.content.length) {
      // 检查是否遇到结束标签
      if (this.isEndTag(tagName)) {
        break
      }
      
      const child = this.parseNode()
      if (child != null) {
        children.push(child)
      }
    }
    
    // 跳过结束标签
    this.skipEndTag(tagName)
    
    return {
      type: 'element',
      name: tagName,
      attrs: attrs,
      style: this.parseStyle(attrs.getString('style') ?? ''),
      children: children
    }
  }
  
  /**
   * 解析文本节点
   */
  private parseText(): HtmlNodeType | null {
    let text = ''
    
    while (this.position < this.content.length && this.content.charAt(this.position) != '<') {
      text += this.content.charAt(this.position)
      this.position++
    }
    
    text = text.trim()
    if (text == '') {
      return null
    }
    
    return {
      type: 'text',
      text: this.decodeHtmlEntities(text)
    }
  }
  
  /**
   * 解析标签名
   */
  private parseTagName(): string {
    let tagName = ''
    
    while (this.position < this.content.length) {
      const char = this.content.charAt(this.position)
      if (char == ' ' || char == '>' || char == '/') {
        break
      }
      tagName += char.toLowerCase()
      this.position++
    }
    
    return tagName
  }
  
  /**
   * 解析属性
   */
  private parseAttributes(): UTSJSONObject {
    const attrs: UTSJSONObject = {}
    
    this.skipWhitespace()
    
    while (this.position < this.content.length && this.content.charAt(this.position) != '>' && this.content.charAt(this.position) != '/') {
      const attrName = this.parseAttributeName()
      if (attrName == '') {
        break
      }
      
      this.skipWhitespace()
      
      let attrValue = ''
      if (this.position < this.content.length && this.content.charAt(this.position) == '=') {
        this.position++ // 跳过 '='
        this.skipWhitespace()
        attrValue = this.parseAttributeValue()
      }
      
      attrs[attrName] = attrValue
      this.skipWhitespace()
    }
    
    return attrs
  }
  
  /**
   * 解析属性名
   */
  private parseAttributeName(): string {
    let name = ''
    
    while (this.position < this.content.length) {
      const char = this.content.charAt(this.position)
      if (char == ' ' || char == '=' || char == '>' || char == '/') {
        break
      }
      name += char.toLowerCase()
      this.position++
    }
    
    return name
  }
  
  /**
   * 解析属性值
   */
  private parseAttributeValue(): string {
    let value = ''
    
    if (this.position < this.content.length) {
      const quote = this.content.charAt(this.position)
      if (quote == '"' || quote == "'") {
        this.position++ // 跳过开始引号
        while (this.position < this.content.length && this.content.charAt(this.position) != quote) {
          value += this.content.charAt(this.position)
          this.position++
        }
        if (this.position < this.content.length) {
          this.position++ // 跳过结束引号
        }
      } else {
        // 无引号的属性值
        while (this.position < this.content.length) {
          const char = this.content.charAt(this.position)
          if (char == ' ' || char == '>' || char == '/') {
            break
          }
          value += char
          this.position++
        }
      }
    }
    
    return this.decodeHtmlEntities(value)
  }
  
  /**
   * 解析CSS样式
   */
  private parseStyle(styleStr: string): UTSJSONObject {
    const style: UTSJSONObject = {}
    
    if (styleStr == '') {
      return style
    }
    
    const declarations = styleStr.split(';')
    for (let i = 0; i < declarations.length; i++) {
      const declaration = declarations[i].trim()
      if (declaration == '') {
        continue
      }
      
      const colonIndex = declaration.indexOf(':')
      if (colonIndex == -1) {
        continue
      }
      
      const property = declaration.substring(0, colonIndex).trim()
      const value = declaration.substring(colonIndex + 1).trim()
      
      // 转换CSS属性名为驼峰命名
      const camelProperty = this.toCamelCase(property)
      style[camelProperty] = value
    }
    
    return style
  }
  
  /**
   * 转换为驼峰命名
   */
  private toCamelCase(str: string): string {
    return str.replace(/-([a-z])/g, (match: string, letter: string): string => {
      return letter.toUpperCase()
    })
  }
  
  /**
   * 跳过空白字符
   */
  private skipWhitespace(): void {
    while (this.position < this.content.length) {
      const char = this.content.charAt(this.position)
      if (char != ' ' && char != '\t' && char != '\n' && char != '\r') {
        break
      }
      this.position++
    }
  }
  
  /**
   * 检查是否是自闭合标签
   */
  private isSelfClosingTag(tagName: string): boolean {
    const selfClosingTags = ['br', 'hr', 'img', 'input', 'meta', 'link']
    return selfClosingTags.includes(tagName)
  }
  
  /**
   * 检查是否是结束标签
   */
  private isEndTag(tagName: string): boolean {
    const currentPos = this.position
    
    if (currentPos + tagName.length + 3 >= this.content.length) {
      return false
    }
    
    if (this.content.charAt(currentPos) != '<' || this.content.charAt(currentPos + 1) != '/') {
      return false
    }
    
    const endTagName = this.content.substring(currentPos + 2, currentPos + 2 + tagName.length)
    return endTagName.toLowerCase() == tagName
  }
  
  /**
   * 跳过结束标签
   */
  private skipEndTag(tagName: string): void {
    if (this.isEndTag(tagName)) {
      while (this.position < this.content.length && this.content.charAt(this.position) != '>') {
        this.position++
      }
      if (this.position < this.content.length) {
        this.position++ // 跳过 '>'
      }
    }
  }
  
  /**
   * 解码HTML实体
   */
  private decodeHtmlEntities(text: string): string {
    return text
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&amp;/g, '&')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&nbsp;/g, ' ')
  }
}