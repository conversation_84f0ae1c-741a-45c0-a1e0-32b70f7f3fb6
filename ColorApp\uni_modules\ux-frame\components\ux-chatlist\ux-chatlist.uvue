<template>
	<view class="ux-chatlist">
		<!-- #ifdef APP -->
		<list-view
			:id="myId" 
			class="ux-chatlist__list" 
			:style="style"
			direction="vertical"
			:bounces="false"
			:upper-threshold="upperThreshold"
			:lower-threshold="lowerThreshold"
			:scroll-top="_scrollTop"
			:scroll-into-view="scrollIntoView"
			:scroll-with-animation="true" 
			:show-scrollbar="false"
			@scrolltoupper="scrolltoupper"
			@scrolltolower="scrolltolower"
			@scroll="scroll">
			<list-item style="flex: 1;"></list-item>
			
			<slot></slot>
			
			<ux-chatlist-item v-if="loadmoreState != 0" type="99">
				<ux-loadmore :states="loadmoreStates" :state="loadmoreState"></ux-loadmore>
			</ux-chatlist-item>
		</list-view>
		<!-- #endif -->
		<!-- #ifndef APP -->
		<scroll-view
			:id="myId" 
			class="ux-chatlist__list" 
			:style="style"
			direction="vertical"
			:upper-threshold="upperThreshold"
			:lower-threshold="lowerThreshold"
			:scroll-top="_scrollTop"
			:scroll-into-view="scrollIntoView"
			:scroll-with-animation="true" 
			:show-scrollbar="false"
			@scrolltoupper="scrolltoupper"
			@scrolltolower="scrolltolower"
			@scroll="scroll">
			<view style="flex: 1;"></view>
			
			<slot></slot>
			
			<ux-chatlist-item v-if="loadmoreState != 0">
				<ux-loadmore :states="loadmoreStates" :state="loadmoreState"></ux-loadmore>
			</ux-chatlist-item>
		</scroll-view>
		<!-- #endif -->
	</view>
</template>

<script setup>
	
	/**
	 * chatlist 聊天列表
	 * @description 支持上拉加载聊天记录
	 * @demo pages/component/chatlist.uvue
	 * @tutorial https://www.uxframe.cn/component/chatlist.html
	 * @property {Slot}			default  								Slot | 默认slot
	 * @property {Slot}			loadmore  								Slot | loadmore slot
	 * @property {Number}		upperThreshold  						Number | 距顶部/左边多远时（单位px），触发 scrolltoupper 事件（默认 50 ）
	 * @property {Number} 		lowerThreshold 							Number | 距底部/右边多远时（单位px），触发 scrolltolower 事件 （默认 50 ）
	 * @property {Number} 		scrollTop 								Number | 设置竖向滚动条位置 （默认 0 ）
	 * @property {String}  		scrollIntoView							String | 值应为某子元素id（id不能以数字开头）。设置哪个方向可滚动，则在哪个方向滚动到该元素
	 * @property {Array}		loadmoreStates							Array | 上拉加载状态文案
	 * @property {String}		background								String | 背景色
	 * @property {Array}		xstyle									Array<any> | 自定义样式
	 * @event {Function}		loadmore								Function | 上拉加载触发
	 * @event {Function}		scrolltoupper							Function | 滚动到顶部/左边，会触发 scrolltoupper 事件
	 * @event {Function}		scrolltolower							Function | 滚动到底部/右边，会触发 scrolltolower 事件
	 * @event {Function}		scroll									Function | 滚动时触发
	 * <AUTHOR>
	 * @date 2024-05-28 14:12:28
	 */
	
	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	
	defineOptions({
		name: 'ux-chatlist',
		mixins: [xstyleMixin]
	})
	
	const emit = defineEmits([
		'loadmore',
		'scrolltoupper', 
		'scrolltolower', 
		'scroll'])
	
	const props = defineProps({
		upperThreshold: {
			type: Number,
			default: 50
		},
		lowerThreshold: {
			type: Number,
			default: 50
		},
		scrollTop: {
			type: Number,
			default: 0
		},
		scrollIntoView: {
			type: String,
			default: '',
		},
		background: {
			type: String,
			default: 'transparent',
		},
		loadmoreStates: {
			type: Array as PropType<Array<string>>,
			default: (): string[] => {
				return ['已加载', '加载中...', '没有更多消息了 ^_^']
			},
		},
	})
	
	const myId = `ux-chatlist-${$ux.Random.uuid()}`
	const _scrollTop = ref(0)
	const loadmoreState = ref(0)
	
	const scrollTop = computed(() => {
		return props.scrollTop
	})
	
	watch(scrollTop, () => {
		_scrollTop.value = scrollTop.value
	}, {immediate: true})
	
	const style = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('background-color', props.background)
		
		css = $ux.Util.xStyle(css, props.margin, props.mt, props.mr, props.mb, props.ml, props.padding, props.pt, props.pr, props.pb, props.pl)
		
		return css
	})

	function scroll(e: ScrollEvent) {
		emit('scroll', e)
	}

	function scrolltoupper(e: ScrollToUpperEvent) {
		emit('scrolltoupper', e)
	}
	
	function scrolltolower(e: ScrollToLowerEvent) {
		loadmoreState.value = 1
		emit('loadmore', e)
		
		emit('scrolltolower', e)
	}
	
	function loadSuccess(lastPage: boolean) {
		loadmoreState.value = lastPage ? 2 : 0
	}
	
	function scrollTo() {
		_scrollTop.value = -1
		nextTick(() => {
			_scrollTop.value = 0
		})
	}
	
	defineExpose({
		loadSuccess,
		scrollTo
	})
</script>

<style lang="scss">
	.ux-chatlist {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: flex-end;
		transform: rotate(180deg);
		
		&__list {
			flex: 1;
		}
	}
</style>