<template>
	<view :id="myId" ref="uxSliderRef" class="ux-slider" :style="style" 
		@touchstart="touchstart" @touchmove="touchmove" @touchend="touchend"
		@mousedown="touchstartPc" @mousemove="touchmovePc" @mouseup="touchendPc" @mouseleave="mouseleave">
		<!-- #ifndef APP -->
			<canvas :id="canvasId" :canvas-id="canvasId" class="ux-slider__canvas"></canvas>
		<!-- #endif -->
	</view>
</template>

<script setup>
	
	/**
	* Slider 滑动选择器
	* @description 支持双滑块、竖向滑动，可设置步长和最小值、最大值
	* @demo pages/component/slider.uvue
	* @tutorial https://www.uxframe.cn/component/slider.html
	* @property {String} 			name												String | 标识符，在回调事件中返回
	* @property {String} 			theme=[primary|warning|success|error|info]			String | 主题颜色
	* @value primary 	主色
	* @value warning 	警告
	* @value success 	成功
	* @value error 		错误
	* @value info 		文本
	* @property {String} 			mode=[normal|range]					String | 模式 (默认 normal)
	* @value normal 正常
	* @value range 范围
	* @property {String} 			direction=[horizontal|vertical]		String | 方向 (默认 horizontal)
	* @value horizontal 水平
	* @value vertical 垂直
	* @property {Number} 			min									Number | 最小值 (默认 0)
	* @property {Number} 			max									Number | 最大值 (默认 100)
	* @property {Number} 			step								Number | 步长，取值必须大于 0，并且可被(max - min)整除 (默认 1)
	* @property {Number} 			value								Number | 当前取值
	* @property {String} 			activeColor							String | 已选择部分的线条颜色 (默认 $ux.Conf.primaryColor)
	* @property {String} 			activeDark=[none|auto|color]		String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色 (默认 none)
	* @value none 不显示
	* @value auto 自动适配深色模式
	* @value color 其他颜色
	* @property {String} 			backgroundColor						String | 背景条颜色 (默认 #e0e0e0)
	* @property {String} 			backgroundDark=[none|auto|color]	String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色 (默认 #a9a9a9)
	* @value none 不显示
	* @value auto 自动适配深色模式
	* @value color 其他颜色
	* @property {Any} 				blockSize							Any | 滑块的大小 (默认 18)
	* @property {String} 			blockColor							String | 滑块颜色 (默认 #ffffff)
	* @property {String} 			blockDark=[none|auto|color]			String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色 (默认 none)
	* @value none 不显示
	* @value auto 自动适配深色模式
	* @value color 其他颜色
	* @property {String} 			borderColor							String | 滑块边框颜色 (默认 #e6e6e6)
	* @property {Boolean} 			showValue							Boolean | 是否显示当前 value (默认 false)
	* @property {String} 			showMode=[up|bottom]				String | 显示方式 (默认 top)
	* @value up 上方跟随
	* @value bottom 下方跟随
	* @property {Any} 				size								Any | 尺寸 (默认 2)
	* @property {Any} 				corner								Any | 圆角 仅web支持 (默认 0)
	* @property {String} 			unit								String | 单位 (默认 %)
	* @property {Boolean} 			disabled							Boolean | 是否禁用
	* @property {Array}				margin								Array | 边距 [上 右 下 左] [上下 左右] [上下左右]
	* @property {Any}				mt									Any | 距上 单位px
	* @property {Any}				mr									Any | 距右 单位px
	* @property {Any}				mb									Any | 距下 单位px
	* @property {Any}				ml									Any | 距左 单位px
	* @property {Array}				padding								Array | 填充 [上 右 下 左] [上下 左右] [上下左右]
	* @property {Any}				pt									Any | 上内边距 单位px
	* @property {Any}				pr									Any | 右内边距 单位px
	* @property {Any}				pb									Any | 下内边距 单位px
	* @property {Any}				pl									Any | 左内边距 单位px
	* @event {Function} 			change 								Function |  完成一次拖动后触发的事件
	* @event {Function} 			changing 							Function |  拖动过程中触发的事件
	* <AUTHOR>
	* @date 2023-11-19 22:56:28
	*/
	
	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	import { UxSliderEvent } from "../../libs/types/types.uts"
	import { useResize } from '../../libs/use/resize'
	import { useDarkmode } from '../../libs/use/darkmode.uts'
	import { useFontColor, useFontSize, useForegroundColor, useThemeColor, useBorderColor, UxThemeType } from '../../libs/use/style.uts'
	
	defineOptions({
		name: 'ux-slider',
		mixins: [xstyleMixin]
	})
	
	type EmitNames = 'change' | 'changing'
	
	const emit = defineEmits(['update:modelValue', 'change', 'changing', 'touchstart', 'touchmove', 'touchend'])
	
	const props = defineProps({
		modelValue: {
			default: null
		},
		name: {
			type: String,
			default: '',
		},
		theme: {
			type: String,
			default: 'primary',
		},
		mode: {
			type: String,
			default: 'normal',
		},
		direction: {
			type: String,
			default: 'horizontal',
		},
		min: {
			type: Number,
			default: 0,
		},
		max: {
			type: Number,
			default: 100,
		},
		step: {
			type: Number,
			default: 1,
		},
		value: {
			type: Number,
			default: 0
		},
		values: {
			type: Array as PropType<Array<number>>,
			default: () : number[] => {
				return [0, 10] as number[]
			}
		},
		activeColor: {
			type: String,
			default: ''
		},
		activeDark: {
			type: String,
			default: ''
		},
		backgroundColor: {
			type: String,
			default: '#e0e0e0'
		},
		backgroundDark: {
			type: String,
			default: '#a9a9a9'
		},
		blockSize: {
			default: 22,
		},
		blockColor: {
			type: String,
			default: '#ffffff'
		},
		blockDark: {
			type: String,
			default: ''
		},
		borderColor: {
			type: String,
			default: ''
		},
		showValue: {
			type: Boolean,
			default: false
		},
		showMode: {
			type: String,
			default: 'top'
		},
		size: {
			default: 2,
		},
		corner: {
			default: 0,
		},
		unit: {
			type: String,
			default: '%'
		},
	})
	
	let instance = getCurrentInstance()?.proxy
	
	const uxSliderRef = ref<Element | null>(null)
	const myId = `ux-slider-${$ux.Random.uuid()}`
	const canvasId = `ux-slider-canvas-${$ux.Random.uuid()}`
	let ctx: any | null = null
	const dpr = uni.getWindowInfo().pixelRatio
	const _value = ref(0)
	const _values = ref<number[]>([] as number[])
	const sliderWidth = ref(0)
	const sliderHeight = ref(0)
	const firstPos = ref(0)
	const secondPos = ref(0)
	const firstSelected = ref(false)
	const secondSelected = ref(false)
	const isPressed = ref(false)
	const text1 = ref('')
	const text2 = ref('')
	
	const blockSize = computed(() => {
		return $ux.Util.getPx(props.blockSize)
	})
	
	const progressWidth = computed((): number => {
		return sliderWidth.value - blockSize.value
	})
	
	const progressHeight = computed((): number => {
		return $ux.Util.getPx(props.size)
	})
	
	function getValue(x : number) : number {
		let value = Math.round((x / progressWidth.value) * (props.max - props.min))
		return props.min + Math.round(value / props.step) * props.step
	}
	
	function getPos(value : number) : number {
		return Math.round((value - props.min) / (props.max - props.min) * progressWidth.value)
	}
	
	function emits(event : EmitNames) {
		if (props.mode == 'range') {
			let val = [getValue(firstPos.value), getValue(secondPos.value)].sort((a : number, b : number) : number => a - b)
			
			emit('update:modelValue', val)
			
			emit(event, {
				name: props.name,
				value: 0,
				values: val,
			} as UxSliderEvent)
		} else {
			let val = getValue(firstPos.value)
			
			emit('update:modelValue', val)
			
			emit(event, {
				name: props.name,
				value: val,
				values: [] as number[],
			} as UxSliderEvent)
		}
	}
	
	const fontSize = computed((): number => {
		return useFontSize(12, 1)
	})
	
	const fontColor = computed((): string => {
		return useFontColor('', '')
	})
	
	const backgroundColor = computed(() : string => {
		return useForegroundColor(props.backgroundColor, props.backgroundDark)
	})
	
	const activeColor = computed(() : string => {
		if(props.activeColor != '') {
			return useForegroundColor(props.activeColor, props.activeDark)
		} else {
			return useThemeColor(props.theme as UxThemeType)
		}
	})
	
	const blockColor = computed(() : string => {
		return useForegroundColor(props.blockColor, props.blockDark)
	})
	
	const borderColor = computed(() : string => {
		return useBorderColor(props.borderColor, '')
	})
	
	const style = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		if (props.direction == 'vertical') {
			css.set('width', '40px')
			css.set('height', '100%')
		} else {
			css.set('width', '100%')
			if(props.showValue && props.showMode == 'bottom') {
				css.set('height', '52px')
			} else {
				css.set('height', '46px')
			}
		}
		
		css = $ux.Util.xStyle(css, props.margin, props.mt, props.mr, props.mb, props.ml, props.padding, props.pt, props.pr, props.pb, props.pl)
		
		return css
	})
	
	const bgStyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('background-color', backgroundColor.value)
		
		if (props.direction == 'vertical') {
			css.set('left', $ux.Util.addUnit((sliderHeight.value - progressHeight.value) / 2))
			css.set('top', $ux.Util.addUnit(blockSize.value / 2))
			css.set('width', $ux.Util.addUnit(progressHeight.value))
			css.set('height', $ux.Util.addUnit(progressWidth.value))
		} else {
			css.set('left', $ux.Util.addUnit(blockSize.value / 2))
			css.set('top', $ux.Util.addUnit((sliderHeight.value - progressHeight.value) / 2))
			css.set('width', $ux.Util.addUnit(progressWidth.value))
			css.set('height', $ux.Util.addUnit(progressHeight.value))
		}
		
		css.set('border-radius', $ux.Util.addUnit(props.corner))
		
		return css
	})
	
	const progressStyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('background-color', activeColor.value)
		
		if (props.direction == 'vertical') {
			if (props.mode == 'range') {
				const width = Math.abs(secondPos.value - firstPos.value)
				const left = Math.min(firstPos.value, secondPos.value) + blockSize.value / 2
				
				css.set('left', $ux.Util.addUnit((sliderHeight.value - progressHeight.value) / 2))
				css.set('top', $ux.Util.addUnit(left))
				css.set('width', $ux.Util.addUnit(progressHeight.value))
				css.set('height', $ux.Util.addUnit(width))
			} else {
				css.set('left', $ux.Util.addUnit((sliderHeight.value - progressHeight.value) / 2))
				css.set('top', $ux.Util.addUnit(blockSize.value / 2))
				css.set('width', $ux.Util.addUnit(progressHeight.value))
				css.set('height', $ux.Util.addUnit(firstPos.value))
			}
		} else {
			if (props.mode == 'range') {
				const width = Math.abs(secondPos.value - firstPos.value)
				const left = Math.min(firstPos.value, secondPos.value) + blockSize.value / 2
				
				css.set('left', $ux.Util.addUnit(left))
				css.set('top', $ux.Util.addUnit((sliderHeight.value - progressHeight.value) / 2))
				css.set('width', $ux.Util.addUnit(width))
				css.set('height', $ux.Util.addUnit(progressHeight.value))
			} else {
				css.set('left', $ux.Util.addUnit(blockSize.value / 2))
				css.set('top', $ux.Util.addUnit((sliderHeight.value - progressHeight.value) / 2))
				css.set('width', $ux.Util.addUnit(firstPos.value))
				css.set('height', $ux.Util.addUnit(progressHeight.value))
			}
		}
		
		return css
	})
	
	const block1Style = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		const size = blockSize.value / 2 - 2
		const top = (sliderHeight.value  - size) / 2
		
		css.set('background-color', blockColor.value)
		css.set('width', $ux.Util.addUnit(size * 2))
		css.set('height', $ux.Util.addUnit(size * 2))
		css.set('border-radius', $ux.Util.addUnit(size * 2))
		css.set('border-color', borderColor.value)
		
		if (props.direction == 'vertical') {
			css.set('left', $ux.Util.addUnit(top - size / 2))
			css.set('top', $ux.Util.addUnit(firstPos.value))
		} else {
			css.set('left', $ux.Util.addUnit(firstPos.value))
			css.set('top', $ux.Util.addUnit(top - size / 2))
		}
		
		return css
	})
	
	const block2Style = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		const size = blockSize.value / 2 - 2
		const top = (sliderHeight.value  - size) / 2
		
		css.set('background-color', blockColor.value)
		css.set('width', $ux.Util.addUnit(size * 2))
		css.set('height', $ux.Util.addUnit(size * 2))
		css.set('border-radius', $ux.Util.addUnit(size * 2))
		css.set('border-color', borderColor.value)
		
		if (props.direction == 'vertical') {
			css.set('left', $ux.Util.addUnit(top - size / 2))
			css.set('top', $ux.Util.addUnit(secondPos.value))
		} else {
			css.set('left', $ux.Util.addUnit(secondPos.value))
			css.set('top', $ux.Util.addUnit(top - size / 2))
		}
		
		return css
	})
	
	const text1Style = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		const size = blockSize.value
		const top = (sliderHeight.value - size) / 2
		
		let textLeft = firstPos.value
		let textTop = top - size / 2
		if (props.showMode == 'bottom') {
			textTop = top + size
		}
		
		if (getValue(firstPos.value ) >= props.max) {
			textLeft = firstPos.value  - 5
		}
		
		css.set('left', $ux.Util.addUnit(textLeft))
		css.set('top', $ux.Util.addUnit(textTop))
		css.set('color', fontColor.value)
		css.set('font-size', $ux.Util.addUnit(fontSize.value))
		
		text1.value = `${getValue(firstPos.value )}${props.unit}`
		
		return css
	})
	
	const text2Style = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		const size = blockSize.value
		const top = (sliderHeight.value - size) / 2
		
		let textLeft = secondPos.value
		let textTop = top - size / 2
		if (props.showMode == 'bottom') {
			textTop = top + size
		}
		
		if (getValue(secondPos.value ) >= props.max) {
			textLeft = secondPos.value - 5
		}
		
		css.set('left', $ux.Util.addUnit(textLeft))
		css.set('top', $ux.Util.addUnit(textTop))
		css.set('color', fontColor.value)
		css.set('font-size', $ux.Util.addUnit(fontSize.value))
		
		text2.value = `${getValue(secondPos.value )}${props.unit}`
		
		return css
	})
	
	// #ifdef APP
	function getCtx(): DrawableContext {
		return ctx! as DrawableContext
	}
	// #endif
	
	// #ifndef APP
	function getCtx(): CanvasRenderingContext2D {
		return ctx! as CanvasRenderingContext2D
	}
	// #endif
	
	function drawBg() {
		const _ctx = getCtx()
		
		_ctx.fillStyle = backgroundColor.value
			
		if (props.direction == 'vertical') {
			_ctx.fillRect((sliderHeight.value - progressHeight.value) / 2, blockSize.value / 2, progressHeight.value, progressWidth.value)
		} else {
			_ctx.fillRect(blockSize.value / 2, (sliderHeight.value - progressHeight.value) / 2, progressWidth.value, progressHeight.value)
		}
	}
	
	function drawProgress() {
		const _ctx = getCtx()
		
		_ctx.fillStyle = activeColor.value
			
		if (props.direction == 'vertical') {
			if (props.mode == 'range') {
				const width = Math.abs(secondPos.value - firstPos.value)
				const left = Math.min(firstPos.value, secondPos.value) + blockSize.value / 2
			
				_ctx.fillRect((sliderHeight.value - progressHeight.value) / 2, left, progressHeight.value, width)
			} else {
				_ctx.fillRect((sliderHeight.value - progressHeight.value) / 2, blockSize.value / 2, progressHeight.value, firstPos.value)
			}
		} else {
			if (props.mode == 'range') {
				const width = Math.abs(secondPos.value - firstPos.value)
				const left = Math.min(firstPos.value, secondPos.value) + blockSize.value / 2
			
				_ctx.fillRect(left, (sliderHeight.value - progressHeight.value) / 2, width, progressHeight.value)
			} else {
				_ctx.fillRect(blockSize.value / 2, (sliderHeight.value - progressHeight.value) / 2, firstPos.value, progressHeight.value)
			}
		}
	}
	
	function drawBlock() {
		const size = blockSize.value / 2 - 2
		const top = (sliderHeight.value - size) / 2
		
		const _ctx = getCtx()
			
		_ctx.beginPath()
		if (props.direction == 'vertical') {
			_ctx.arc((top + size / 2), (firstPos.value + size + 2), size, 0, Math.PI * 2)
		} else {
			_ctx.arc((firstPos.value + size + 2), (top + size / 2), size, 0, Math.PI * 2)
		}
		_ctx.closePath()
			
		_ctx.fillStyle = blockColor.value
		_ctx.fill()
		
		_ctx.strokeStyle = borderColor.value
		_ctx.stroke()
			
		if (props.showValue && props.direction != 'vertical') {
			let textLeft = firstPos.value + 4
			let textTop = top - 7
			if (props.showMode == 'bottom') {
				textTop = top + blockSize.value + 8
			}
			
			if (getValue(firstPos.value) >= props.max) {
				textLeft = firstPos.value - 12
			}
			
			// #ifdef APP
			_ctx.font = $ux.Util.addUnit(fontSize.value)
			// #endif
			// #ifndef APP
			_ctx.font = `${$ux.Util.addUnit(fontSize.value)} Arial`
			// #endif
			_ctx.fillStyle = fontColor.value
			_ctx.fillText(`${getValue(firstPos.value)}${props.unit}`, textLeft, textTop)
		}
			
		if (props.mode == 'range') {
			_ctx.beginPath()
			if (props.direction == 'vertical') {
				_ctx.arc((top + size / 2), (secondPos.value + size + 2), size, 0, Math.PI * 2)
			} else {
				_ctx.arc((secondPos.value + size + 2), (top + size / 2), size, 0, Math.PI * 2)
			}
			_ctx.closePath()
			
			_ctx.fillStyle = blockColor.value
			_ctx.fill()
			
			_ctx.strokeStyle = borderColor.value
			_ctx.stroke()
			
			if (props.showValue && props.direction != 'vertical') {
				let textLeft = secondPos.value + 4
				let textTop = top - 7
				if (props.showMode == 'bottom') {
					textTop = top + blockSize.value + 8
				}
			
				if (getValue(secondPos.value) >= props.max) {
					textLeft = secondPos.value - 12
				}
				
				// #ifdef APP
				_ctx.font = $ux.Util.addUnit(fontSize.value)
				// #endif
				// #ifndef APP
				_ctx.font = `${$ux.Util.addUnit(fontSize.value)} Arial`
				// #endif
				_ctx.fillStyle = fontColor.value
				_ctx.fillText(`${getValue(secondPos.value)}${props.unit}`, textLeft, textTop)
			}
		}
	}
	
	function draw() {
		const _ctx = getCtx()
		
		// #ifdef APP
		_ctx.reset()
		// #endif
		
		// #ifndef APP
		_ctx.clearRect(0, 0, ctx!.canvas.width, ctx!.canvas.height)
		// #endif
		
		drawBg()
		drawProgress()
		drawBlock()
			
		// #ifdef APP
		_ctx.update()
		// #endif
	}
	
	function _touchstart(pos : number) {
		firstSelected.value = Math.abs(pos - firstPos.value) <= blockSize.value
		
		if (!firstSelected.value && props.mode == 'range') {
			secondSelected.value = Math.abs(pos - secondPos.value) <= blockSize.value
		}
		
		isPressed.value = true
	}
	
	async function touchstart(e : TouchEvent) {
		
		if (props.disabled) {
			return
		}
		
		// #ifdef MP
		let rect = await uni.getElementById(e.target!.id).getBoundingClientRectAsync()!
		// #endif
		// #ifndef MP
		let rect = await e.target!.getBoundingClientRectAsync()!
		// #endif
		
		let pos: number
		
		if (props.direction == 'vertical') {
			pos = e.changedTouches[0].clientY - rect.top
		} else {
			pos = e.changedTouches[0].clientX - rect.left
		}
		
		_touchstart(pos)
		
		emit('touchstart', e)
	}
	
	async function touchstartPc(e : MouseEvent) {
		if (props.disabled) {
			return
		}
		
		// #ifdef MP
		let rect = await uni.getElementById(e.target!.id).getBoundingClientRectAsync()!
		// #endif
		// #ifndef MP
		let rect = await e.target!.getBoundingClientRectAsync()!
		// #endif
		
		let pos: number
		
		if (props.direction == 'vertical') {
			pos = e.clientY - rect.top
		} else {
			pos = e.clientX - rect.left
		}
		
		_touchstart(pos)
		
		emit('touchstart', e)
	}
	
	function _touchmove(pos : number) {
		
		if (firstSelected.value) {
			firstPos.value = Math.ceil(pos / props.step) * props.step
		
			if (firstPos.value >= progressWidth.value) {
				firstPos.value = progressWidth.value
			} else if (firstPos.value <= 0) {
				firstPos.value = 0
			}
			
			draw()
			emits('changing')
		}
		
		if (props.mode == 'range') {
			if (secondSelected.value) {
				secondPos.value = Math.ceil(pos / props.step) * props.step
		
				if (secondPos.value >= progressWidth.value) {
					secondPos.value = progressWidth.value
				} else if (secondPos.value <= 0) {
					secondPos.value = 0
				}
		
				draw()
				emits('changing')
			}
		}
	}
	
	async function touchmove(e : TouchEvent) {
		e.preventDefault()
		
		if (props.disabled || !isPressed.value) {
			return
		}
		
		// #ifdef MP
		let rect = await uni.getElementById(e.target!.id).getBoundingClientRectAsync()!
		// #endif
		// #ifndef MP
		let rect = await e.target!.getBoundingClientRectAsync()!
		// #endif
		
		let pos: number
		
		if (props.direction == 'vertical') {
			pos = e.changedTouches[0].clientY - rect.top
		} else {
			pos = e.changedTouches[0].clientX - rect.left
		}
		
		_touchmove(pos)
		
		emit('touchmove', e)
	}
	
	async function touchmovePc(e : MouseEvent) {
		e.preventDefault()
		
		let rect = await e.target!.getBoundingClientRectAsync()!
		
		if (props.disabled || !isPressed.value) {
			return
		}
		
		let pos: number
		
		if (props.direction == 'vertical') {
			pos = e.clientY - rect.top
		} else {
			pos = e.clientX - rect.left
		}
		
		_touchmove(pos)
		
		emit('touchmove', e)
	}
	
	function _touchend() {
		
		if (!props.disabled && isPressed.value) {
			emits('change')
		}
		
		firstSelected.value = false
		secondSelected.value = false
		isPressed.value = false
	}
	
	function touchend(e : TouchEvent) {
		e.preventDefault()
		
		_touchend()
		
		emit('touchend', e)
	}
	
	function touchendPc(e : MouseEvent) {
		e.preventDefault()
		
		_touchend()
		
		emit('touchend', e)
	}
	
	function mouseleave(e : MouseEvent) {
		e.preventDefault()
		
		if(!isPressed.value) {
			_touchend()
		}
	}
	
	const modelValue = computed((): any | null => {
		return props.modelValue
	})
	
	async function init() {
		if(modelValue.value == null) {
			_value.value = props.value
			_values.value = props.values as number[]
		} else {
			if (props.mode == 'range') {
				_values.value = modelValue.value as number[]
			} else {
				_value.value = modelValue.value as number
			}
		}
		
		// #ifdef APP
		ctx = uxSliderRef.value!.getDrawableContext()
		// #endif
		// #ifndef APP
		const context = await $ux.Util.createCanvasContext(canvasId, instance)
		ctx = context.getContext('2d')
		ctx!.canvas.width = ctx!.canvas.offsetWidth * dpr
		ctx!.canvas.height = ctx!.canvas.offsetHeight * dpr
		ctx!.scale(dpr, dpr)
		// #endif
		
		const rect = await uxSliderRef.value!.getBoundingClientRectAsync()!
		
		if (props.direction == 'vertical') {
			sliderWidth.value = rect.height
			sliderHeight.value = rect.width
		} else {
			sliderWidth.value = rect.width
			sliderHeight.value = rect.height
		}
		
		if (props.mode == 'range') {
			if (_values.value.length == 2) {
				_values.value.sort(function (a : any | null, b : any | null) : number {
					return (a! as number) - (b! as number)
				})
		
				firstPos.value = getPos(_values.value[0] as number)
				secondPos.value = getPos(_values.value[1] as number)
			} else {
				_values.value = Array(0, 10)
			}
		} else {
			firstPos.value = getPos(_value.value)
		}
		
		draw()
	}
	
	watch(activeColor, () => {
		init()
	})
	
	watch(modelValue, () => {
		if (props.mode == 'range') {
			_values.value = modelValue.value as number[]
		} else {
			_value.value = modelValue.value as number
		}
		
		init()
	})
	
	onMounted(() => {
		
		setTimeout(function() {
			uni.createSelectorQuery().in(instance).select('.ux-slider').boundingClientRect().exec((_: any) => { init() })
		}, 30);
		
		useResize(uxSliderRef.value, () => {
			init()
		})
		
		useDarkmode(() => {
			init()
		})
	})
</script>

<style lang="scss">
	.ux-slider {
		width: 100%;
		height: 40px;
		position: relative;
		
		&__bg {
			position: absolute;
			width: 100%;
		}
		
		&__progress {
			position: absolute;
			z-index: 1;
		}
		
		&__block {
			position: absolute;
			border: 1px solid #e6e6e6;
			z-index: 2;
		}
		
		&__text {
			position: absolute;
			z-index: 3;
			font-size: 12px;
		}
		
		&__canvas {
			width: 100%;
			height: 100%;
		}
	}
</style>