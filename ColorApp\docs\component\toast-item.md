<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/toast-item?title=Toast-item"></Mobile>

# Toast-item
> 组件类型：UxToastItemComponentPublicInstance



## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| itemId | String |  | 唯一id |
| index | Number |  | 下标 |
| logo | String |  | Logo |
| logoWidth | Any | 30 | Logo宽 |
| logoHeight | Any | 30 | Logo高 |
| logoRadius | Any | 10 | Logo圆角 |
| content | String |  | 内容 |
| color | String |  | 文本颜色优先级高于主题 |
| [darkColor](#darkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| size | Any | 14 | 字体大小 |
| [bold](#bold) | Boolean | false | 字体加粗 |
| background | String | `$ux.Conf.backgroundColor` | 背景色 |
| [backgroundDark](#backgroundDark) | String | auto | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| borderColor | String | `$ux.Conf.borderColor` | 边框颜色 |
| [borderDarkColor](#borderDarkColor) | String | auto | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| radius | Any | 30 | 圆角 |
| shadow | Boolean | true | 显示阴影 |
| border | Boolean | false | 显示边框 |
| scale | Boolean | true | 缩放动画 |

### [darkColor](#darkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [bold](#bold)

| 值   | 说明 |
|:------:|:----:|
| true加粗
 |  |
| false正常
 |  |

### [backgroundDark](#backgroundDark)

| 值   | 说明 |
|:------:|:----:|
| none不显示 | 
 |
| auto自动适配深色模式 | 
 |
| color其他颜色
 |  |

### [borderDarkColor](#borderDarkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

