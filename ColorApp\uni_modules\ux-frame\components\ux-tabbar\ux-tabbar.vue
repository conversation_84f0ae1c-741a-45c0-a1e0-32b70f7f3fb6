<template>
	<view :id="myId" ref="uxTabbarRef" class="ux-tabbar" :style="tabbarStyle">
		<!-- #ifdef APP-VUE -->
		<view :render="render" :change:render="tabbarjs.receiveMsg"></view>
		<!-- #endif -->
		<ux-blur v-if="showBlur && type != 'special'" :radius="blurRadius" :color="blurColor" class="ux-tabbar__blur" :style="blurStyle"></ux-blur>

		<view class="ux-tabbar__content" :style="contentStyle">

			<!-- #ifdef APP-NVUE -->
			<web-view class="ux-tabbar__content__canvas" :style="canvasStyle" :id="canvasId" ref="webview" src="/uni_modules/ux-frame/hybrid/html/tabbar.html" @pagefinish="reRender"></web-view>
			<!-- #endif -->
			<!-- #ifndef APP-NVUE -->
			<canvas class="ux-tabbar__content__canvas" :style="canvasStyle" :id="canvasId" :canvas-id="canvasId"></canvas>
			<!-- #endif -->

			<view v-if="renderOk" :id="it.id" :ref="it.id" :class="[`ux-tabbar__item__${anim}`, `transform__${anim}`]"
				:style="bodyStyle" v-for="(it, i) in tabs" :key="i" @click="onTab(i)" @touchstart="() => touchstart(i)"
				@touchend="() => touchend(i)" @touchcancel="() => touchend(i)">

				<view :id="`${myId}-hover-${i}`" :ref="`${myId}-hover-${i}`" class="ux-tabbar__hover--b" :style="itemStyle">
					<view class="ux-tabbar__hover" :style="hoverStyle(i)"></view>
				</view>

				<view :id="`${myId}-icon-${i}`" :ref="`${myId}-icon-${i}`" :class="['ux-tabbar__icon', `transform__${anim}`]" :style="itemStyle">
					<ux-icon :type="tabIndex == i?it.selectedIcon:it.unselectedIcon" :size="iconSize"
						:color="tabIndex == i?it.selectedColor:it.unselectedColor"
						:custom-family="customFamily">
					</ux-icon>
				</view>
				
				<view :id="`${myId}-name-${i}`" :ref="`${myId}-name-${i}`" :class="['ux-tabbar__name--b', `transform__${anim}`]" :style="itemStyle">
					<text class="ux-tabbar__name" :style="[textStyle(i)]">{{ it.name }}</text>
				</view>
				
				<view :id="`${myId}-point-${i}`" :ref="`${myId}-point-${i}`" :class="['ux-tabbar__point--b', `transform__${anim}`]" :style="itemStyle">
					<view class="ux-tabbar__point" :style="[pointStyle(i)]"></view>
				</view>

				<ux-badge :value="it.badge" :dot="it.reddot" :top="anim == 'push'? 0 : 10" :right="10"></ux-badge>
			</view>
		</view>

		<view v-if="type == 'special'" class="ux-tabbar__center" :style="[centerStyle]" hover-class="ux-tabbar__hover--center" :hover-start-time="0" :hover-stay-time="150" @click="onCenter()">
			<slot name="center"></slot>
		</view>

		<ux-placeholder v-if="type == 'special'" :safearea="true" :background="backgroundColor"></ux-placeholder>
	</view>
</template>

<script>
	/**
	 * 底部tabbar
	 * @description 支持正常形状和凹形，支持上下滚动、左右推压、水滴动画效果
	 * @demo pages/component/tabbar.uvue
	 * @tutorial https://www.uxframe.cn/component/tabbar.html
	 * @property {Slot}				center						Slot | 中间凸起按钮插槽
	 * @property {String}			type = [default|special]		String | 类型 (默认 default)
	 * @value default 正常
	 * @value special 特殊
	 * @property {String}			anim = [none|scroll|push|water]		String | 动效类型 (默认 none)
	 * @value none 无
	 * @value scroll 上下滚动
	 * @value push 左右推压
	 * @value water 水滴
	 * @property {Number}			index								Number | 默认选择下标 (默认 0)
	 * @property {UxTab[]}			data								UxTab[] | tab列表 (导入: import {UxTab} from '@/uni_modules/ux-frame/libs/types/types.uts')
	 * @property {String}			selectedColor						String | 选中颜色 (默认 $ux.Conf.fontColor)
	 * @property {String}			unselectedColor						String | 未选中颜色 (默认 #111111)
	 * @property {String}			selectedColorDark=[none|auto|color]	String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {String}			unselectedColorDark=[none|auto|color]	String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Any}				iconSize							Any | 图标大小 单位px (默认 18)
	 * @property {Any}				fontSize							Any | 字体大小 单位px (默认 12)
	 * @property {Boolean}			fontBold = [true|false]				Boolean | 字体加粗 (默认 false)
	 * @property {String}			customFamily						String | 自定义字体family
	 * @property {Boolean}			border = [true|false]				Boolean | 显示上边框 (默认 false)
	 * @property {String}			borderColor							String | 上边框颜色 (默认 $ux.Conf.borderColor)
	 * @property {Any}				corner								Any | 圆角 (默认 0)
	 * @property {String}			background							String | 背景色 (默认 #FFFFFF)
	 * @property {String} 			backgroundDark=[none|auto|color]	String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {String}			centerColor							String | 中间按钮背景色 (默认 #FFFFFF)
	 * @property {Boolean}			showBlur = [true|false]				Boolean | 开启模糊背景 仅支持normal类型 (默认 false )
	 * @property {Number}			blurRadius							Number | 模糊半径 (默认 10 )
	 * @property {Number}			blurAlpha							Number | 模糊透明度 (默认 0.5 )
	 * @property {Number}			zIndex								Number | 层级z-index (默认 10000)
	 * @property {Boolean}			hover = [true|false]				Boolean | 显示点击态 (默认 true)
	 * @property {Boolean}			fixed = [true|false]				Boolean | 固定位置 (默认 true)
	 * @event {Function}			click								Function | 按钮点击时触发
	 * @event {Function}			center								Function | 中间按钮点击时触发
	 * @event {Function}			change								Function | 当前选择下标改变时触发
	 * <AUTHOR>
	 * @date 2024-12-05 20:31:11
	 */

	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	// import type { UxTab } from '../../libs/types/types.uts'
	import { useForegroundColor, useFontColor } from '../../libs/use/style.uts'
	import { useResize } from '../../libs/use/resize.uts'
	
	// #ifdef APP-NVUE
	const animation = weex.requireModule('animation')
	// #endif

	export default {
		name: 'ux-tabbar',
		emits: ['click', 'center', 'change'],
		mixins: [xstyleMixin],
		data() {
			return {
				canvasId: `ux-tabbar-${$ux.Random.uuid()}`,
				ctx: null,
				width: 0,
				height: 54,
				tabs: [],
				tabIndex: 0,
				colors: [],
				isPress: false,
				renderOk: true,
				safeAreaHeight: uni.getWindowInfo().safeAreaInsets.bottom,
				dpr: uni.getWindowInfo().pixelRatio,
				timer: 0,
				radius: 30,
				render: {}
			}
		},
		props: {
			type: {
				type: String,
				default: 'default'
			},
			anim: {
				type: String,
				default: 'none'
			},
			index: {
				type: Number,
				default: 0
			},
			data: {
				type: Array,
				default: () => {
					[]
				}
			},
			selectedColor: {
				type: String,
				default: ''
			},
			unselectedColor: {
				type: String,
				default: '#111111'
			},
			selectedColorDark: {
				type: String,
				default: ''
			},
			unselectedColorDark: {
				type: String,
				default: ''
			},
			iconSize: {
				default: 18
			},
			fontSize: {
				default: 12
			},
			fontBold: {
				type: Boolean,
				default: false
			},
			customFamily: {
				type: String,
				default: ''
			},
			border: {
				type: Boolean,
				default: false
			},
			borderColor: {
				type: String,
				default: ''
			},
			corner: {
				default: 0
			},
			background: {
				type: String,
				default: '#ffffff'
			},
			backgroundDark: {
				type: String,
				default: '#222222'
			},
			centerColor: {
				type: String,
				default: '#ffffff'
			},
			showBlur: {
				type: Boolean,
				default: false
			},
			blurRadius: {
				type: Number,
				default: 10
			},
			blurAlpha: {
				type: Number,
				default: 0.5
			},
			zIndex: {
				type: Number,
				default: 10000
			},
			fixed: {
				type: Boolean,
				default: true
			},
			hover: {
				type: Boolean,
				default: true
			},
		},
		computed: {
			myId() {
				return `ux-tabbar_${this.type}_${$ux.Random.uuid()}`
			},
			backgroundColor() {
				return useForegroundColor(this.background, this.backgroundDark)
			},
			_selectedColor() {
				return useForegroundColor(this.selectedColor, this.selectedColorDark)
			},
			_unselectedColor() {
				return useForegroundColor(this.unselectedColor, this.unselectedColorDark)
			},
			blurColor() {
				return $ux.Color.getRgba(this.backgroundColor, this.blurAlpha)
			},
			fontColor() {
				return useFontColor('', '')
			},
			_corner() {
				return $ux.Util.getPx(this.corner)
			},
			tabbarStyle() {
				let css = {}

				if (this.fixed) {
					css['position'] = 'fixed'
					css['bottom'] = 0
				}
				
				if(this.width > 0) {
					css['width'] = `${this.width}px`
				}

				const height = this.safeAreaHeight + 54
				const offset = this.type == 'special' ? (this.radius + 8) : 0
				css['height'] = `${height + offset}px`
				css['min-height'] = `${height + offset}px`

				if (this.border && this.type == 'default') {
					css['border-top'] = `0.5px solid ${this.borderColor}`
				}

				if (!this.showBlur) {
					css['background-color'] = `${this.type == 'default' ? this.backgroundColor : 'transparent'}`
				}

				css['z-index'] = this.zIndex

				if (this._corner > 0 && this.type == 'default') {
					css['border-top-left-radius'] = `${this._corner}px`
					css['border-top-right-radius'] = `${this._corner}px`
				}

				return css
			},
			contentStyle() {
				let css = {}

				if (this.type == 'special') {
					css['margin-top'] = `${this.radius}px`
				} else {
					css['margin-bottom'] = `${this.safeAreaHeight}px`
				}

				if(this.width > 0) {
					css['width'] = `${this.width}px`
				}
				
				css['height'] = `${54}px`

				return css
			},
			bodyStyle() {
				let css = {}
				
				if(this.width > 0) {
					css['width'] = `${this.width}px`
				}
				
				css['height'] = `${ this.anim == 'push' ? 40 : 54}px`

				return css
			},
			blurStyle() {
				let css = {}
				
				if(this.width > 0) {
					css['width'] = `${this.width}px`
				}
				
				return css
			},
			canvasStyle() {
				let css = {}
				
				if(this.width > 0) {
					css['width'] = `${this.width}px`
				}
				
				return css
			},
			itemStyle() {
				let css = {}
				
				if(this.anim != 'push') {
					css['width'] = `${this.width / this.tabs.length}px`
				}
				
				return css
			},
			centerStyle() {
				let css = {}

				css['width'] = `${this.radius * 2}px`
				css['height'] = `${this.radius * 2}px`
				css['bottom'] = `${this.safeAreaHeight + 54 - this.radius - 5}px`
				css['background-color'] = this.centerColor

				return css
			},
			centerHoverStyle() {
				return 'background-color: #999999'
			}
		},
		watch: {
			data(a, b) {
				this.initTabs()
			},
			type: {
				immediate: true,
				handler() {
					this.initTabs()
					setTimeout(() => {
						this.reRender()
					}, 100);
				}
			},
			anim: {
				immediate: true,
				handler() {
					if (this.type == 'special') {
						if (this.anim != 'none' && this.anim != 'scroll') {
							console.error('[ux-tabbar]配置警告: 类型[special]仅支持动效[none/scroll]')
						}
					}

					setTimeout(() => {
						this.reRender()
					}, 100);
				}
			},
			backgroundColor(a, b) {
				this.reRender()
			},
			_selectedColor(a, b) {
				this.initTabs()
				this.reRender()
			},
			_unselectedColor(a, b) {
				this.initTabs()
				this.reRender()
			},
		},
		mounted() {
			if (this.type == 'special') {
				if (this.index >= this.data.length / 2) {
					this.tabIndex = this.index + 1
				} else {
					this.tabIndex = this.index
				}
			} else {
				this.tabIndex = this.index
			}
			
			let f = async () => {
				// 绘制
				await this.draw()
				this.initTabs()
				this.reRender()
			}
			
			f()

			// 监听尺寸变化重绘
			useResize(null, () => {
				this.draw()
			})
		},
		methods: {
			/**
			 * 样式
			 */
			textStyle(i) {
				let color = ''
				if (this.tabIndex == i) {
					color = this.tabs[i].selectedColor ?? this.fontColor
				} else {
					color = this.tabs[i].unselectedColor ?? this.fontColor
				}

				if (color == '') {
					color = this.fontColor
				}

				let css = {}
				
				css['color'] = `${color}`
				css['font-size'] = `${$ux.Util.getPx(this.fontSize)}px`
				css['font-weight'] = `${this.fontBold ? 'bold' : 'normal'}`

				return css
			},

			pointStyle(i) {
				let color = ''
				if (this.tabIndex == i) {
					color = this.tabs[i].selectedColor ?? this.fontColor
				} else {
					color = this.tabs[i].unselectedColor ?? this.fontColor
				}

				if (color == '') {
					color = this.fontColor
				}

				return `background-color: ${color}`
			},

			hoverStyle(i) {
				return `background-color: ${this.tabs[i].selectedColor}`
			},
			
			/**
			 * 初始tabs
			 */
			initTabs() {
				let list = []
			
				this.data.forEach((e, i) => {
					let data = e
			
					let item = {
						id: data.id ?? `ux-tabbar-${this.type}-${this.anim}-${i}`,
						name: data.name,
						selectedIcon: data.selectedIcon ?? '',
						unselectedIcon: data.unselectedIcon ?? data.selectedIcon,
						selectedColor: data.selectedColor ?? this._selectedColor,
						unselectedColor: data.unselectedColor ?? this._unselectedColor,
						badge: data.badge ?? 0,
						reddot: data.reddot ?? false,
						btn: data.btn ?? false,
						index: i,
					}
			
					list.push(item)
				})
			
				this.tabs = list
			
				if (this.anim == 'scroll') {
					let index = this.tabs.findIndex((e) => e.selectedIcon == '')
			
					if (index != -1) {
						console.error('[ux-tabbar]配置错误: 动效[scroll]需配置selectedIcon')
					}
				} else {
					let a = this.tabs.findIndex((e) => e.selectedIcon != '' && e.unselectedIcon == '')
					let b = this.tabs.findIndex((e) => e.selectedIcon == '' && e.unselectedIcon != '')
			
					if (a != -1 || b != -1) {
						console.error(`[ux-tabbar]配置错误: 动效[${this.anim}]需同时配置selectedIcon & unselectedIcon`)
					}
				}
			
				// 中间凹陷按钮
				if (this.type == 'special') {
					if (this.anim != 'none' && this.anim != 'scroll') {
						console.error('[ux-tabbar]配置警告: 类型[special]仅支持动效[none/scroll]')
					}
			
					if (this.tabs.length % 2 != 0) {
						console.error('[ux-tabbar]配置错误: 类型[special]tabs数量必须为偶数')
					}
					
					// #ifdef MP-WEIXIN
					console.error('[ux-tabbar]配置警告: 类型[special]不支持微信小程序。不支持原因：原生canvas层级太高')
					// #endif
			
					// 插入占位按钮到中间
					this.tabs.splice(this.tabs.length / 2, 0, {
						id: 'center',
						name: '',
						selectedIcon: '',
						unselectedIcon: '',
						selectedColor: '',
						unselectedColor: '',
						badge: 0,
						reddot: false,
						btn: false,
						index: -1
					})
				}
			
				// 初始化位置
				setTimeout(() => {
					this.animTo(this.tabIndex)
				}, 100)
			},

			/**
			 * 绘制背景
			 */
			async draw() {
				// const el = uni.getElementById(this.myId)
				// if(el.parentElement.classList.length > 0) {
				// 	const name = el.parentElement.classList[0]
				// 	const rect = await $ux.Util.getBoundingClientRect(`.${name}`)
				// 	this.width = rect.width
				// }
				
				const rect = await $ux.Util.getBoundingClientRect(`#${this.myId}`)
				this.width = rect.width == 0 ? uni.getWindowInfo().windowWidth : rect.width
				
				this.ctx = uni.createCanvasContext(this.canvasId, this)
				
				// #ifndef APP-NVUE
				// 处理高清屏逻辑
				this.ctx.width = this.width * this.dpr
				this.ctx.height = this.height * this.dpr
				this.ctx.scale(this.dpr, this.dpr)
				// #endif
			
				// 清除绘制
				this.resetDraw()
			
				if (this.type == 'default') {
					this.draw0()
				} else if (this.type == 'special') {
					this.draw1()
				}
			},

			// 清除绘制
			resetDraw() {
				// #ifdef APP-NVUE
				this.$refs['webview'].evalJS(`resetDraw()`)
				return
				// #endif
				
				if(this.ctx == null) {
					return
				}

				// #ifndef APP
				// @ts-ignore
				this.ctx.clearRect(0, 0, this.width, this.height)
				// #endif
				
				this.ctx.draw()
			},

			/**
			 * 正常
			 */
			draw0() {

			},

			/**
			 * 凹陷
			 */
			draw1() {
				// #ifdef APP-NVUE
				let data = {
					dpr: this.dpr,
					safeAreaHeight: this.safeAreaHeight,
					radius: this.radius,
					corner: this._corner,
					backgroundColor: this.backgroundColor,
					borderColor: this.borderColor,
				}
				this.$refs.webview.evalJS(`draw1(${JSON.stringify(data)})`)
				return
				// #endif
				
				if(this.ctx == null) {
					return
				}
				
				const fixed = 8
				const h = this.safeAreaHeight + 54 + fixed
				const sw = this.width
				let width = this.radius + 10
				let height = this.radius + 10 + fixed
				let startX = sw / 2 - width
				let offset = 20

				// 开始绘制
				this.ctx.beginPath()

				if (this._corner > 0) {
					// 圆角

					// 起点
					let lc_s1 = 0
					let lc_s2 = this._corner * 2

					// 控制点1
					let lc_c1_1 = 0
					let lc_c1_2 = 0

					// 控制点2
					let lc_c2_1 = 0
					let lc_c2_2 = 0

					// 终点
					let lc_e1 = this._corner * 2
					let lc_e2 = 0

					this.ctx.moveTo(lc_s1, lc_s2)
					this.ctx.bezierCurveTo(lc_c1_1, lc_c1_2, lc_c2_1, lc_c2_2, lc_e1, lc_e2)
				} else {
					// 原点开始
					this.ctx.moveTo(0, 0)
				}

				// 左边弧
				// 起点
				let l_s1 = startX - offset
				let l_s2 = 0

				// 控制点1
				let l_c1_1 = startX
				let l_c1_2 = l_s2

				// 控制点2
				let l_c2_x = width * (1 / 5)
				let l_c2_y = height
				let l_c2_1 = startX + l_c2_x
				let l_c2_2 = l_s2 + l_c2_y

				// 终点
				let l_e1 = startX + width
				let l_e2 = height

				this.ctx.lineTo(l_s1, l_s2)
				this.ctx.bezierCurveTo(l_c1_1, l_c1_2, l_c2_1, l_c2_2, l_e1, l_e2)

				// 右边弧
				// 起点是上一个终点

				// 控制点1
				let r_c1_1 = startX + width * 2 - l_c2_x
				let r_c1_2 = l_c2_2

				// 控制点2
				let r_c2_1 = startX + width * 2
				let r_c2_2 = l_c1_2

				// 终点
				let r_e1 = startX + width * 2 + offset
				let r_e2 = 0

				this.ctx.bezierCurveTo(r_c1_1, r_c1_2, r_c2_1, r_c2_2, r_e1, r_e2)

				// 向右画线
				if (this._corner > 0) {
					// 圆角
					// 起点
					let lc_s1 = sw - this._corner
					let lc_s2 = 0

					// 控制点1
					let lc_c1_1 = sw
					let lc_c1_2 = 0

					// 控制点2
					let lc_c2_1 = sw
					let lc_c2_2 = 0

					// 终点
					let lc_e1 = sw
					let lc_e2 = this._corner

					this.ctx.bezierCurveTo(lc_c1_1, lc_c1_2, lc_c2_1, lc_c2_2, lc_e1, lc_e2)
				} else {
					this.ctx.lineTo(sw, 0)
				}

				// 向下画线
				this.ctx.lineTo(sw, h)

				// 向左画线
				this.ctx.lineTo(0, h)

				// 回到原点
				if (this._corner > 0) {
					// 圆角
					this.ctx.lineTo(0, this._corner * 2)
				} else {
					this.ctx.lineTo(0, 0)
				}

				this.ctx.closePath()

				// #ifdef APP-NVUE
				this.ctx.setFillStyle(this.backgroundColor)
				// #endif
				// #ifndef APP-NVUE
				this.ctx.fillStyle = this.backgroundColor
				// #endif
				
				this.ctx.fill()

				// 上边框
				if (this.border) {
					// 开始绘制
					this.ctx.beginPath()

					if (this._corner > 0) {
						// 圆角

						// 起点
						let lc_s1 = 0
						let lc_s2 = this._corner * 2

						// 控制点1
						let lc_c1_1 = 0
						let lc_c1_2 = 0

						// 控制点2
						let lc_c2_1 = 0
						let lc_c2_2 = 0

						// 终点
						let lc_e1 = this._corner * 2
						let lc_e2 = 0

						this.ctx.moveTo(lc_s1, lc_s2)
						this.ctx.bezierCurveTo(lc_c1_1, lc_c1_2, lc_c2_1, lc_c2_2, lc_e1, lc_e2)
					} else {
						// 原点开始
						this.ctx.moveTo(0, 0)
					}

					// 左边弧
					// 起点
					l_s1 = startX - offset
					l_s2 = 0

					// 控制点1
					l_c1_1 = startX
					l_c1_2 = l_s2

					// 控制点2
					l_c2_x = width * (1 / 5)
					l_c2_y = height
					l_c2_1 = startX + l_c2_x
					l_c2_2 = l_s2 + l_c2_y

					// 终点
					l_e1 = startX + width
					l_e2 = height

					this.ctx.lineTo(l_s1, l_s2)
					this.ctx.bezierCurveTo(l_c1_1, l_c1_2, l_c2_1, l_c2_2, l_e1, l_e2)

					// 右边弧
					// 起点是上一个终点

					// 控制点1
					r_c1_1 = startX + width * 2 - l_c2_x
					r_c1_2 = l_c2_2

					// 控制点2
					r_c2_1 = startX + width * 2
					r_c2_2 = l_c1_2

					// 终点
					r_e1 = startX + width * 2 + offset
					r_e2 = 0

					this.ctx.bezierCurveTo(r_c1_1, r_c1_2, r_c2_1, r_c2_2, r_e1, r_e2)

					// 向右画线
					if (this._corner > 0) {
						// 圆角
						// 起点
						let lc_s1 = sw - this._corner
						let lc_s2 = 0

						// 控制点1
						let lc_c1_1 = sw
						let lc_c1_2 = 0

						// 控制点2
						let lc_c2_1 = sw
						let lc_c2_2 = 0

						// 终点
						let lc_e1 = sw
						let lc_e2 = this._corner

						this.ctx.bezierCurveTo(lc_c1_1, lc_c1_2, lc_c2_1, lc_c2_2, lc_e1, lc_e2)
					} else {
						this.ctx.lineTo(sw, 0)
					}
					
					// #ifdef APP-NVUE
					this.ctx.setStrokeStyle(this.borderColor)
					// #endif
					// #ifndef APP-NVUE
					this.ctx.strokeStyle = this.borderColor
					// #endif
					
					this.ctx.stroke()
					
					// #ifdef APP-NVUE
					this.ctx.setFillStyle(this.backgroundColor)
					// #endif
					// #ifndef APP-NVUE
					this.ctx.fillStyle = this.backgroundColor
					// #endif
				}

				this.ctx.draw()
			},

			/**
			 * 无动效
			 */
			noneAnim(_old, _new) {

			},
			
			/**
			 * 水滴动效
			 */
			waterAnim(_old, _new) {
				// #ifdef APP-NVUE
				let data = {
					dpr: this.dpr,
					_old: _old,
					_new: _new,
					size: this.data.length,
					color: this.tabs[_new].selectedColor, // TODO 计算透明颜色
					isIos: uni.getSystemInfoSync().platform == 'ios'
				}
				this.$refs.webview.evalJS(`waterAnim(${JSON.stringify(data)})`)
				return
				// #endif
				
				if(this.ctx == null) {
					return
				}

				// 宽高
				let w = this.width / this.data.length
				let width = w * 0.7
				let height = 12
				let _height = height

				// 居左
				let left = (w - width) / 2

				// 起点
				let startX = w * _old + left

				// 终点
				let endX = w * _new + left

				// 水滴
				let dropStart = height - 10
				let dropHeight = 35
				let dropEnd = dropStart + dropHeight
				// #ifdef APP-IOS
				let dropStep = 3
				// #endif
				// #ifndef APP-IOS
				let dropStep = 2
				// #endif
				let droprRadius = 5

				// 前进 后退
				let forward = startX < endX

				// 步长
				// #ifdef APP-IOS
				let step = 0.5 * Math.abs(_new - _old)
				// #endif
				// #ifndef APP-IOS
				let step = 5 * Math.abs(_new - _old)
				// #endif

				let moveAnim = (_) => {}
				moveAnim = (drop) => {
					// 百分比
					let per = forward ? (startX / endX) : (endX / startX)
					per = per < 0.3 ? 0.3 : (per > 1 ? 1 : per)

					// 清除绘制
					// #ifndef APP
					// @ts-ignore
					this.ctx.clearRect(0, 0, this.width, this.height)
					// #endif

					// 开始绘制
					this.ctx.beginPath()

					// 水滴缓冲动效
					if (drop) {
						let dp = 1 - (dropEnd - dropStart) / dropHeight
						height = dp < 0.5 ? _height * (1 + dp) : _height * dp

						if (height < _height) {
							height = _height
						}
					}

					// 左边弧
					// 起点
					let l_s1 = startX
					let l_s2 = 0

					// 控制点1
					let l_c1_x = width / 2 * (2 / 3)
					let l_c1_y = height * (1 / 5)
					let l_c1_1 = l_s1 + l_c1_x
					let l_c1_2 = l_s2 + l_c1_y

					// 控制点2
					let l_c2_x = width / 2 * (4 / 5)
					let l_c2_y = height
					let l_c2_1 = l_s1 + l_c2_x
					let l_c2_2 = l_s2 + l_c2_y

					// 终点
					let l_e1 = l_s1 + width / 2
					let l_e2 = height

					this.ctx.moveTo(l_s1, l_s2)
					this.ctx.bezierCurveTo(l_c1_1, l_c1_2, l_c2_1, l_c2_2, l_e1, l_e2)

					// 右边弧
					// 起点是上一个终点

					// 控制点1
					let r_c1_1 = startX + width - l_c2_x
					let r_c1_2 = l_c2_2

					// 控制点2
					let r_c2_1 = startX + width - l_c1_x
					let r_c2_2 = l_c1_2

					// 终点
					let r_e1 = startX + width
					let r_e2 = 0

					this.ctx.bezierCurveTo(r_c1_1, r_c1_2, r_c2_1, r_c2_2, r_e1, r_e2)

					// 结束绘制
					this.ctx.closePath()

					// 填充
					let _color = $ux.Color.getRgba(this.tabs[_new].selectedColor, per)
					// #ifdef APP-NVUE
					this.ctx.setFillStyle(_color)
					// #endif
					// #ifndef APP-NVUE
					this.ctx.fillStyle = _color
					// #endif
					
					this.ctx.fill()

					// 水滴动效
					if (drop) {
						let dp = (dropEnd - dropStart) / dropHeight
						dp = dp < 0 ? 0 : dp

						let dx = startX + (width / 2) - droprRadius
						let dy = dropStart

						// 开始绘制
						this.ctx.beginPath()

						// 中点
						this.ctx.moveTo(dx, dy)

						// 水滴
						this.ctx.arc(dx + droprRadius, dy + droprRadius, droprRadius, 0, Math.PI * 2, false)

						// 结束绘制
						this.ctx.closePath()

						// 填充
						let _color = $ux.Color.getRgba(this.tabs[_new].selectedColor, dp)
						// #ifdef APP-NVUE
						this.ctx.setFillStyle(_color)
						// #endif
						// #ifndef APP-NVUE
						this.ctx.fillStyle = _color
						// #endif
						
						this.ctx.fill()
					}

					// 更新
					this.ctx.draw()

					// 模拟动画
					if (forward) {
						if (startX < endX) {
							// 右移
							setTimeout(() => {
								moveAnim(false)
							}, 0)

							startX += step

							if (endX - startX < step) {
								startX = endX
							}
						} else {
							// 开始水滴动效
							if (dropStart < dropEnd) {
								setTimeout(() => {
									moveAnim(true)
								}, 0)
							}

							dropStart += dropStep
						}
					} else {
						if (startX > endX) {
							// 左移
							setTimeout(() => {
								moveAnim(false)
							}, 0)

							startX -= step

							if (startX - endX < step) {
								startX = endX
							}
						} else {
							// 开始水滴动效
							if (dropStart < dropEnd) {
								setTimeout(() => {
									moveAnim(true)
								}, 0)
							}

							dropStart += dropStep
						}
					}
				}

				moveAnim(false)
			},

			/**
			 * 动效
			 */
			animTo(index) {
				if (this.anim == 'none') {
					// 无动效
					this.noneAnim(this.tabIndex, index)
				} else if (this.anim == 'scroll') {
					// 上下滚动动效
					// #ifdef APP-VUE
					this.render = {
						event: 'scrollAnim',
						_old: this.tabIndex,
						_new: index,
						myId: this.myId,
						tabs: this.tabs
					}
					// #endif
					
					// #ifdef APP-NVUE
					this.scrollAnimNvue(this.tabIndex, index)
					// #endif
					
					// #ifdef H5
					this.tabbarjs.scrollAnim(this.tabIndex, index)
					// #endif
				} else if (this.anim == 'push') {
					this.colors = this.tabs.map(e => $ux.Color.getRgba(e.selectedColor, 0.2))
					
					// 左右推压动效
					// #ifdef APP-VUE
					this.render = {
						event: 'pushAnim',
						_old: this.tabIndex,
						_new: index,
						myId: this.myId,
						tabs: this.tabs,
						colors: this.colors
					}
					// #endif
					
					// #ifdef APP-NVUE
					this.pushAnimNvue(this.tabIndex, index)
					// #endif
					
					// #ifdef H5
					this.tabbarjs.pushAnim(this.tabIndex, index)
					// #endif
				} else if (this.anim == 'water') {
					// 水滴动效
					this.waterAnim(this.tabIndex, index)
				}
			},
			
			// #ifdef APP-NVUE
			/** 
			 * 上下滚动特效
			 */
			scrollAnimNvue(_old, _new) {
				let anim = (i, show) => {
					if (this.tabs[i].btn) {
						// 独立按钮不设置
						return
					}
				
					let iconEl = this.$refs[`${this.myId}-icon-${i}`][0]
					let nameEl = this.$refs[`${this.myId}-name-${i}`][0]
					let pointEl = this.$refs[`${this.myId}-point-${i}`][0]
					
					if (show) {
						if(iconEl != null) {
							animation.transition(iconEl, {
								styles: {
									opacity: 0,
									transform: `translateY(-50%)`
								},
								duration: 300,
								delay: 0
							})
						}
						
						if(nameEl != null) {
							animation.transition(nameEl, {
								styles: {
									opacity: 1,
								},
								duration: 100,
								delay: 200
							})
							
							animation.transition(nameEl, {
								styles: {
									transform: `translateY(-80)`
								},
								duration: 300,
								delay: 0
							})
						}
						
						if(pointEl != null) {
							animation.transition(pointEl, {
								styles: {
									opacity: 1,
									transform: `scale(1)`
								},
								duration: 300,
								delay: 0
							})
						}
					} else {
						if(iconEl != null) {
							animation.transition(iconEl, {
								styles: {
									opacity: 1,
									transform: `translateY(0%)`
								},
								duration: 300,
								delay: 0
							})
						}
						
						if(nameEl != null) {
							animation.transition(nameEl, {
								styles: {
									opacity: 0,
								},
								duration: 80,
								delay: 0
							})
							
							animation.transition(nameEl, {
								styles: {
									transform: `translateY(100%)`
								},
								duration: 300,
								delay: 0
							})
						}
						
						if(pointEl != null) {
							animation.transition(pointEl, {
								styles: {
									opacity: 0,
									transform: `scale(0)`
								},
								duration: 300,
								delay: 0
							})
						}
					}
				}
				
				if (_old == _new) {
					// 重置
					anim(_new, true)
				
					for (let i = 0; i < this.tabs.length; i++) {
						if (i == _new) {
							continue
						}
				
						anim(i, false)
					}
				} else {
					// 上一个隐藏
					anim(_old, false)
				
					// 当前显示
					anim(_new, true)
				}
			},
			// #endif
			
			// #ifdef APP-NVUE
			/**
			 * 左右推压动效
			 */
			pushAnimNvue(_old, _new) {
				let screenWidth = this.width
			
				let anim = (i, show) => {
					// 平分宽度
					let per = 100 / this.tabs.length / 100 * screenWidth - 15
			
					// 展开宽度
					let width = per + (5 / 100 * screenWidth)
			
					// 塌缩宽度
					let cwidth = (screenWidth - 30 * 2 - width) / (this.tabs.length - 1)
					
					let tabEl = this.$refs[this.tabs[i].id][0]
					let nameEl = this.$refs[`${this.myId}-name-${i}`][0]
					
					if (show) {
						// 展开
						if(tabEl != null) {
							let color = $ux.Color.getRgba(this.tabs[i].selectedColor, 0.2)
							animation.transition(tabEl, {
								styles: {
									width: `${width}px`,
									backgroundColor: color,
									transform: i == (this.tabs.length - 1) ? `translateX(-30)` : `translateX(0%)`
								},
								duration: 200,
								delay: 0
							})
						}
						
						if(nameEl != null) {
							animation.transition(nameEl, {
								styles: {
									opacity: 1,
								},
								duration: 200,
								delay: 0
							})
						}
					} else {
						// 塌缩
						if(tabEl != null) {
							animation.transition(tabEl, {
								styles: {
									width: `${cwidth}px`,
									backgroundColor: 'transparent',
									transform: i == (this.tabs.length - 1) ? `translateX(0)` : `translateX(0%)`
								},
								duration: 200,
								delay: 0
							})
						}
						
						if(nameEl != null) {
							animation.transition(nameEl, {
								styles: {
									opacity: 0,
								},
								duration: 200,
								delay: 0
							})
						}
					}
				}
			
				anim(_new, true)
			
				for (let i = 0; i < this.tabs.length; i++) {
					if (i == _new) {
						continue
					}
			
					anim(i, false)
				}
			},
			// #endif
			
			/**
			 * 点击态
			 */
			touchstart(i) {
				if (!this.hover) {
					return
				}

				if (this.isPress) {
					return
				}

				this.timer = new Date().getTime()
				this.isPress = true

				// #ifdef APP-VUE
				this.render = {
					event: 'hoverAnim',
					_old: `${this.myId}-hover-${i}`,
					_new: true,
					myId: this.myId,
					tabs: this.tabs
				}
				// #endif
				
				// #ifdef APP-NVUE
				this.hoverAnimNvue(`${this.myId}-hover-${i}`, true)
				// #endif
				
				// #ifdef H5
				this.tabbarjs.hoverAnim(`${this.myId}-hover-${i}`, true)
				// #endif
			},

			touchend(i) {
				let offset = new Date().getTime() - this.timer

				if (offset < 110) {
					setTimeout(() => {
						this.isPress = false
						
						// #ifdef APP-VUE
						this.render = {
							event: 'hoverAnim',
							_old: `${this.myId}-hover-${i}`,
							_new: false,
							myId: this.myId,
							tabs: this.tabs
						}
						// #endif
						
						// #ifdef APP-NVUE
						this.hoverAnimNvue(`${this.myId}-hover-${i}`, false)
						// #endif
						
						// #ifdef H5
						this.tabbarjs.hoverAnim(`${this.myId}-hover-${i}`, false)
						// #endif
					}, 110 - offset)
				} else {
					this.isPress = false
					
					// #ifdef APP-VUE
					this.render = {
						event: 'hoverAnim',
						_old: `${this.myId}-hover-${i}`,
						_new: false,
						myId: this.myId,
						tabs: this.tabs
					}
					// #endif
					
					// #ifdef APP-NVUE
					this.hoverAnimNvue(`${this.myId}-hover-${i}`, false)
					// #endif
					
					// #ifdef H5
					this.tabbarjs.hoverAnim(`${this.myId}-hover-${i}`, false)
					// #endif
				}
			},
			
			// #ifdef APP-NVUE
			/**
			 * hover
			 */
			hoverAnimNvue(id, show) {
				let el = this.$refs[id][0]
				
				if(el != null) {
					animation.transition(el, {
						styles: {
							opacity: show ? 0.1 : 0,
							transform:  show ? 'scale(1)' : 'scale(0)'
						},
						duration: 100,
						delay: 0
					})
				}
			},
			// #endif
			
			/**
			 * 重新渲染
			 */
			reRender() {
				// 清除绘制
				this.resetDraw()

				this.renderOk = false
				this.$nextTick(() => {
					this.renderOk = true

					let f = async () => {
						await this.draw()
						this.animTo(this.tabIndex)
					}

					setTimeout(() => {
						f()
					}, 20)
				})
			},

			/**
			 * 中间tab
			 */
			onCenter() {
				this.$emit('center')
			},

			/**
			 * 点击tab
			 */
			onTab(i) {
				if (this.tabs[i].btn) {
					// 独立按钮
					this.$emit('click', this.tabs[i].index)
					return
				}

				if (this.type == 'special' && i == this.data.length / 2) {
					return
				}

				// 动效
				this.animTo(i)

				// 选择tab
				this.tabIndex = i
				this.$emit('change', this.tabs[i].index)
			},
		}
	}
</script>

<script module="tabbarjs" lang="renderjs">
	export default {
		data() {
			return {
				
			}
		},
		methods: {
			receiveMsg(newValue, oldValue, ownerVm, vm) {
				if(newValue.event == 'hoverAnim') {
					this.hoverAnim(newValue._old, newValue._new)
				} else if(newValue.event == 'scrollAnim') {
					this.scrollAnim(newValue._old, newValue._new, newValue.myId, newValue.tabs)
				} else if(newValue.event == 'pushAnim') {
					this.pushAnim(newValue._old, newValue._new, newValue.myId, newValue.tabs, newValue.colors)
				}
			},
			
			/**
			 * hover
			 */
			hoverAnim(id, show) {
				document.getElementById(id)?.style?.setProperty('opacity', show ? 0.1 : 0)
				document.getElementById(id)?.style?.setProperty('transform', show ? 'scale(1)' : 'scale(0)')
			},

			/**
			 * 上下滚动动效
			 */
			scrollAnim(_old, _new, _myId, _tabs) {
				let myId = _myId || this.myId
				let tabs = _tabs || this.tabs
				let anim = (i, show) => {
					if (tabs[i].btn) {
						// 独立按钮不设置
						return
					}

					if (show) {
						// 上推
						document.getElementById(`${myId}-icon-${i}`)?.style?.setProperty('transform', 'translate(0, -50%)')
						document.getElementById(`${myId}-name-${i}`)?.style?.setProperty('transform', 'translate(0, -230%)')

						// 透明度
						document.getElementById(`${myId}-icon-${i}`)?.style?.setProperty('opacity', 0)
						document.getElementById(`${myId}-name-${i}`)?.style?.setProperty('opacity', 1)

						// 点
						document.getElementById(`${myId}-point-${i}`)?.style?.setProperty('opacity', 1)
						document.getElementById(`${myId}-point-${i}`)?.style?.setProperty('transform', 'scale(1)')
					} else {
						// 下压
						document.getElementById(`${myId}-icon-${i}`)?.style?.setProperty('transform', 'translate(0, 0%)')
						document.getElementById(`${myId}-name-${i}`)?.style?.setProperty('transform', 'translate(0, 100%)')

						// 透明度
						document.getElementById(`${myId}-icon-${i}`)?.style?.setProperty('opacity', 1)
						document.getElementById(`${myId}-name-${i}`)?.style?.setProperty('opacity', 0)

						// 点
						document.getElementById(`${myId}-point-${i}`)?.style?.setProperty('opacity', 0)
						document.getElementById(`${myId}-point-${i}`)?.style?.setProperty('transform', 'scale(0)')
					}
				}

				if (_old == _new) {
					// 重置
					anim(_new, true)

					for (let i = 0; i < tabs.length; i++) {
						if (i == _new) {
							continue
						}

						anim(i, false)
					}
				} else {
					// 上一个隐藏
					anim(_old, false)

					// 当前显示
					anim(_new, true)
				}
			},

			/**
			 * 左右推压动效
			 */
			pushAnim(_old, _new, _myId, _tabs, _colors) {
				let myId = _myId || this.myId
				let tabs = _tabs || this.tabs
				let colors = _colors || this.colors
				let screenWidth = document.documentElement.clientWidth

				let anim = (i, show) => {
					// 平分宽度
					let per = 100 / tabs.length / 100 * screenWidth

					// 展开宽度
					let width = per + (5 / 100 * screenWidth)
					
					// 塌缩宽度
					let cwidth = (screenWidth - 20 * 2 - width) / (tabs.length - 1)

					if (show) {
						// 展开
						document.getElementById(tabs[i].id)?.style?.setProperty('width', `${width}px`)

						// 背景色
						document.getElementById(tabs[i].id)?.style?.setProperty('background-color', colors[i])

						// 透明度
						document.getElementById(`${myId}-name-${i}`)?.style?.setProperty('opacity', 1)
					} else {
						// 塌缩
						document.getElementById(tabs[i].id)?.style?.setProperty('width', `${cwidth}px`)

						// 背景色
						document.getElementById(tabs[i].id)?.style?.setProperty('background-color', 'transparent')

						// 透明度
						document.getElementById(`${myId}-name-${i}`)?.style?.setProperty('opacity', 0)
					}
				}

				anim(_new, true)

				for (let i = 0; i < tabs.length; i++) {
					if (i == _new) {
						continue
					}

					anim(i, false)
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	/* #ifdef H5 */
	uni-view {
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: stretch;
		align-content: stretch;
		justify-content: flex-start;
		box-sizing: border-box;
		min-height: 0px;
		min-width: 0px;
		overflow: hidden;
	}
	/* #endif */
	
	.ux-tabbar {
		position: relative;
		flex: 1;
		height: 54px;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: flex-end;
		z-index: 10000;

		&__blur {
			position: absolute;
			flex: 1;
			/* #ifndef APP-NVUE */
			height: 100%;
			/* #endif */
			/* #ifdef APP-NVUE */
			height: 54px;
			/* #endif */
		}

		.ux-tabbar__center {
			position: absolute;
			width: 60px;
			height: 60px;
			border-radius: 60px;
			background-color: #fff;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			box-shadow: 0 0 10px 2px #ededed;
		}

		.ux-tabbar__content {
			position: relative;
			flex: 1;
			height: 54px;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: space-around;
			/* #ifdef WEB */
			cursor: pointer;
			/* #endif */

			&__canvas {
				position: absolute;
				bottom: 0;
				z-index: 0;
				flex: 1;
				/* #ifndef APP-NVUE */
				height: 100%;
				/* #endif */
				/* #ifdef APP-NVUE */
				height: 54px;
				/* #endif */
			}

			.ux-tabbar__item__none {
				flex: 1;
				/* #ifndef APP-NVUE */
				height: 100%;
				/* #endif */
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				z-index: 1;
				position: relative;

				.ux-tabbar__icon {
					width: 25px;
					height: 25px;
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: center;
				}

				.ux-tabbar__name--b {
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: center;
				}

				.ux-tabbar__name {
					color: black;
					font-size: 12px;
				}
			}

			.ux-tabbar__item__scroll {
				flex: 1;
				/* #ifndef APP-NVUE */
				height: 100%;
				/* #endif */
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				z-index: 1 !important;
				position: relative;

				.ux-tabbar__icon {
					/* #ifndef APP-NVUE */
					position: absolute;
					/* #endif */
					
					width: 36px;
					height: 36px;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
				}

				.ux-tabbar__name--b {
					position: absolute;
					/* #ifdef APP-NVUE */
					top: 100px;
					/* #endif */
					/* #ifndef APP-NVUE */
					top: 100%;
					/* #endif */
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: center;
					opacity: 0;
				}

				.ux-tabbar__name {
					color: black;
					font-size: 18px;
				}
			}

			.ux-tabbar__item__push {
				width: 150rpx;
				height: 38px;
				border-radius: 54px;
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: flex-start;
				background-color: transparent;
				z-index: 1;
				position: relative;

				.ux-tabbar__icon {
					height: 36px;
					margin-left: 20px;
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: flex-start;
				}

				.ux-tabbar__name--b {
					margin-left: 10px;
					opacity: 0;
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: flex-start;
				}

				.ux-tabbar__name {
					color: black;
					font-size: 18px;
					white-space: nowrap;
				}
			}

			.ux-tabbar__item__water {
				flex: 1;
				/* #ifndef APP-NVUE */
				height: 100%;
				/* #endif */
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				z-index: 1;
				position: relative;

				.ux-tabbar__icon {
					position: absolute;
					
					/* #ifdef APP-NVUE */
					top: 10px;
					/* #endif */
					/* #ifndef APP-NVUE */
					top: 25%;
					/* #endif */
					
					width: 36px;
					height: 36px;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
				}

				.ux-tabbar__name--b {
					position: absolute;
					
					/* #ifdef APP-NVUE */
					top: 100px;
					/* #endif */
					/* #ifndef APP-NVUE */
					top: 100%;
					/* #endif */
					
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: flex-start;
					opacity: 0;
				}

				.ux-tabbar__name {
					color: black;
					font-size: 18px;
				}
			}
			
			.ux-tabbar__point--b {
				position: absolute;
				bottom: 5px;
				flex: 1;
				height: 5px;
				opacity: 0;
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: center;
			}

			.ux-tabbar__point {
				width: 5px;
				height: 5px;
				border-radius: 5px;
			}
		}
	}

	.ux-tabbar__hover--b {
		position: absolute;
		top: 0;
		width: 50px;
		height: 50px;
		opacity: 0;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		transform: scale(0);
		transition-property: transform, opacity;
		transition-duration: 0.1s;
		transition-timing-function: linear;
	}

	.ux-tabbar__hover {
		width: 50px;
		height: 50px;
		border-radius: 50px;
	}

	.ux-tabbar__hover--center {
		opacity: 0.8;
	}

	.transform__none {}

	.transform__scroll {
		transition-property: transform, opacity;
		transition-duration: 0.3s;
		transition-timing-function: ease-in-out;
	}

	.transform__push {
		transition-property: transform, opacity, width, background-color;
		transition-duration: 0.2s;
		transition-timing-function: linear;
	}

	.transform__water {
		transition-property: transform, opacity;
		transition-duration: 0.3s;
		transition-timing-function: ease-in-out;
	}
</style>