<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/placeholder?title=Placeholder"></Mobile>

# Placeholder
> 组件类型：UxPlaceholderComponentPublicInstance

支持状态栏、标题栏、底部导航栏、底部安全区域、任意高度

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| height | Any |  | 占位高度优先级更高 |
| statusbar | Boolean | false | 状态栏高度占位 |
| navbar | Boolean | false | 标题栏高度占位 |
| tabbar | Boolean | false | 底部导航栏高度占位 |
| safearea | Boolean | false | 底部安全区高度占位 |
| background | String | transparent | 背景色 |
| backgroundDark | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |

