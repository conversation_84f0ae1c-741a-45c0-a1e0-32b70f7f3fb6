<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/button?title=Button"></Mobile>

# Button
> 组件类型：UxButtonComponentPublicInstance

支持多种主题按钮、镂空按钮，可配置加载中状态，可开启节流等功能

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| [theme](#theme) | String | info | 按钮类型 |
| text | String |  | 按钮文字 |
| color | String |  | 按钮文字颜色 |
| [darkColor](#darkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| width | Any |  | 按钮宽度 |
| height | Any |  | 按钮高度 |
| size | Any | `$ux.Conf.fontSize` | 按钮文字大小 |
| [sizeType](#sizeType) | String | small | 字体大小类型 |
| bold | Boolean | false | 按钮文字加粗 |
| background | String |  | 按钮背景颜色 |
| [backgroundDark](#backgroundDark) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| colors | Array |  | 背景色多个渐变 |
| [gradientDirection](#gradientDirection) | String | bottom | 渐变色方向 |
| border | Boolean | true | 显示边框 |
| corner | Any | 5 | 圆角 |
| plain | Boolean | false | 是否镂空 |
| loading | Boolean | false | 显示加载中 |
| [loadingType](#loadingType) | String | spinner | loading类型 |
| loadingText | String | 加载中... | 加载中文字 |
| loadingSize | Any | `$ux.Conf.fontSize` | 加载中文字大小 |
| loadingColor | String | `$ux.Conf.placeholderColor` | 加载中颜色 |
| icon | String |  | 按钮图标 |
| iconSize | Any | `$ux.Conf.fontSize` | 图标大小 |
| iconColor | String |  | 图标文字颜色 |
| [iconColorDark](#iconColorDark) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| customFamily | String |  | 自定义字体family |
| [direction](#direction) | String | row | 布局方向 |
| path | String |  | 点击跳转的页面路径 |
| throttleTime | Number | 0 | 节流，一定时间内只能触发一次，单位毫秒 |
| hoverStartTime | Number | 0 | 点击态出现时间，单位毫秒 |
| hoverStayTime | Number | 150 | 点击态保留时间，单位毫秒 |
| hover | Boolean | true | 是否显示点击态 |
| margin | Array |  | 边距[上右下左][上下左右][上下左右] |
| mt | Any |  | 距上单位px |
| mr | Any |  | 距右单位px |
| mb | Any |  | 距下单位px |
| ml | Any |  | 距左单位px |
| padding | Array |  | 填充[上右下左][上下左右][上下左右] |
| pt | Any |  | 上内边距单位px |
| pr | Any |  | 右内边距单位px |
| pb | Any |  | 下内边距单位px |
| pl | Any |  | 左内边距单位px |
| xstyle | Array |  | 自定义样式 |
| stopPropagation | Boolean | false | 阻止向上冒泡 |
| disabled | Boolean | false | 是否禁用 |

### [theme](#theme)

| 值   | 说明 |
|:------:|:----:|
| text | 文字
 |
| info | 默认
 |
| primary | 主要
 |
| success | 成功
 |
| warning | 警告
 |
| error | 错误
 |

### [darkColor](#darkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [sizeType](#sizeType)

| 值   | 说明 |
|:------:|:----:|
| normal正常
 |  |
| small较小
 |  |
| big较大
 |  |

### [backgroundDark](#backgroundDark)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [gradientDirection](#gradientDirection)

| 值   | 说明 |
|:------:|:----:|
| top向上
 |  |
| bottom向下
 |  |
| left向左
 |  |
| right向右
 |  |

### [loadingType](#loadingType)

| 值   | 说明 |
|:------:|:----:|
| circular圆环
 |  |
| spinner菊花
 |  |

### [iconColorDark](#iconColorDark)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [direction](#direction)

| 值   | 说明 |
|:------:|:----:|
| row水平
 |  |
| column垂直
 |  |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| click | 非禁止并且非加载中，点击触发 |  |
| longpress | 非禁止并且非加载中，长按触发 |  |
| touchstart | 非禁止手指触摸动作开始时触发 |  |
| touchend | 非禁止手指触摸动作结束时触发 |  |
| touchmove | 非禁止手指触摸动作移动时触发 |  |
| touchcancel | 手指触摸动作被打断时触发 |  |
  
  