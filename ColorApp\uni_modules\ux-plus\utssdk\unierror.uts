
import { UxPlusCallback } from "./interface.uts"

// #ifndef UNI-APP-X
class UniError {
	
}
// #endif

export class UxPlusSuccessCallbackImpl extends UniError implements UxPlusCallback {

	constructor(errMsg : string) {
		super();

		this.errSubject = 'ux-plus'
		this.errCode = 0
		this.errMsg = errMsg
	}
}

export class UxPlusErrorCallbackImpl extends UniError implements UxPlusCallback {

	constructor(errMsg : string) {
		super();

		this.errSubject = 'ux-plus'
		this.errCode = 1
		this.errMsg = errMsg
	}
}