
<p align="center">
    <img alt="logo" src="https://www.uxframe.cn/logo/logo.png" width="120" height="120" style="margin-bottom: 10px;">
</p>
<h3 align="center" style="margin: 30px 0 30px;font-weight: bold;font-size:40px;">UxFrame 微信SDK 1.1.2</h3>

## 特别说明

如果您已经购买了[UxFrame 低代码高性能UI框架](https://ext.dcloud.net.cn/plugin?id=16148), 则无需再次购买本插件，请点击上方`进入交流群`联系我免费获取离线版插件！

#### 无支付，不与官方微信支付冲突版本 ⬇️

[无支付版本 ➡ 点我️（https://ext.dcloud.net.cn/plugin?id=22241）](https://ext.dcloud.net.cn/plugin?id=22241)

## 官方文档

[https://www.uxframe.cn](https://www.uxframe.cn)

## Android注意事项

需在本插件`utssdk` -> `app-android` -> `AndroidManifest.xml` 文件中将所有 `${applicationId}`字段替换成您的`真实包名`

插件注册`universalLink`必填， android可为空

``` ts
wxsdk.register({
	appid: '',
	universalLink: '',
	success: (res) => {
		console.log(res)
	}
} as UxRegisterOptions)
```

## iOS注意事项

需在本插件`utssdk` -> `app-ios` -> `Info.plist` 文件中修改 `CFBundleURLSchemes`字段对应的Value为您的`微信AppId`

插件注册`universalLink`必填

``` ts
wxsdk.register({
	appid: '',
	universalLink: '', // ios 必填
	success: (res) => {
		console.log(res)
	}
} as UxRegisterOptions)
```

## 使用方法

```ts

// #ifdef APP
import * as wxsdk from "@/uni_modules/ux-weixin"
import { UxLoginOptions, UxRegisterOptions, UxShareOptions, UxPayOptions, UxMiniProgram } from "@/uni_modules/ux-weixin"
// #endif

// 注册
wxsdk.register({
	appid: '您的微信appid',
	success: (res) => {
		console.log(res)
	}
} as UxRegisterOptions)

// 登录
wxsdk.login({
	success: (res) => {
		console.log(res);
	},
	fail: (err) => {
		console.log(err);
	}
} as UxLoginOptions)

// 分享图文
wxsdk.share({
	type: 0,
	title: 'UxFrame低代码高性能UI框架',
	summary: 'UxFrame是基于UNI-APP-X开发的低代码高性能原生UI框架',
	href: 'https://www.uxframe.cn',
	imageUrl: 'https://www.uxframe.cn/logo/logo.png',
	success: (res) => {
		console.log(res);
	},
	fail: (err) => {
		console.log(err);
	}
} as UxShareOptions)

// 分享文本
wxsdk.share({
	type: 1,
	title: 'UxFrame低代码高性能UI框架',
	success: (res) => {
		console.log(res);
	},
	fail: (err) => {
		console.log(err);
	}
} as UxShareOptions)

// 分享图片
wxsdk.share({
	type: 2,
	mageUrl: '/static/logo.png', // 仅支持本地路径
	success: (res) => {
		console.log(res);
	},
	fail: (err) => {
		console.log(err);
	}
} as UxShareOptions)

// 分享视频
wxsdk.share({
	type: 4,
	title: 'UxFrame低代码高性能UI框架',
	summary: 'UxFrame是基于UNI-APP-X开发的低代码高性能原生UI框架',
	// imageUrl: '/static/logo.png'
	imageUrl: 'https://www.uxframe.cn/logo/logo.png',
	mediaUrl: 'https://www.uxframe.cn/source/intro/demo.mp4',
	success: (res) => {
		console.log(res);
	},
	fail: (err) => {
		console.log(err);
	}
} as UxShareOptions)

// 分享小程序
wxsdk.share({
	type: 5,
	title: 'UxFrame低代码高性能UI框架',
	summary: 'UxFrame是基于UNI-APP-X开发的低代码高性能原生UI框架',
	// imageUrl: '/static/logo.png'
	imageUrl: 'https://www.uxframe.cn/logo/logo.png',
	miniProgram: {
		id: '小程序id',
		type: 0,
		path: '',
		webUrl: 'https://www.uxframe.cn'
	} as UxMiniProgram,
	success: (res) => {
		console.log(res);
	},
	fail: (err) => {
		console.log(err);
	}
} as UxShareOptions)

// 打开小程序
wxsdk.openMiniProgram({
	miniProgram: {
		id: 'gh_b545368see',
		type: 0,
		path: '',
	} as UxMiniProgram,
	success: (res) => {
		console.log(res);
	},
	fail: (err) => {
		console.log(err);
	}
} as UxShareOptions)

// 微信支付
wxsdk.requestPayment({
	mchid: '商户号',
	prepayId: '预付单',
	sign: '签名',
	signType: '签名类型',
	nonceStr: '随机字符串',
	timeStamp: '时间戳',
	success: (res) => {
		console.log(res);
	},
	fail: (err) => {
		console.log(err);
	}
} as UxPayOptions)

// 联系微信企业客服
wxsdk.share({
	openCustomerServiceChat: true,
	corpid: '企业id',
	customerUrl: 'https://work.weixin.qq.com/kfid/kfc********',
	success: (res) => {
		console.log(res);
	},
	fail: (err) => {
		console.log(err);
	}
} as UxShareOptions)

// 打开微信
wxsdk.openApp()

// 检测是否安装微信
wxsdk.isInstalled()

```
