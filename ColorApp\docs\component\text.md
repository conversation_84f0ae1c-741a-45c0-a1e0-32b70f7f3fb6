<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/text?title=Text"></Mobile>

# Text
> 组件类型：UxTextComponentPublicInstance

扩展支持拨打电话，格式化日期，脱敏，超链接...等功能

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| name | String |  | 标识符/网页名称 |
| [theme](#theme) | String |  | 主题颜色 |
| text | String |  | 文本内容 |
| color | String |  | 文本颜色优先级高于主题 |
| [darkColor](#darkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| size | Any | `$ux.Conf.fontSize` | 字体大小 |
| [sizeType](#sizeType) | String | small | 字体大小类型 |
| lines | Number | 0 | 文本显示的行数，若大于0，超出此行数，将会显示省略号 |
| [bold](#bold) | Boolean | false | 字体加粗 |
| [align](#align) | String | left | 文本对齐方式 |
| [decoration](#decoration) | String | none | 文字装饰 |
| lineHeight | Number |  | 文本行高 |
| prefixIcon | String |  | 前置图标 |
| suffixIcon | String |  | 后置图标 |
| iconSize | Any | `$ux.Conf.fontSize` | 图标字体大小 |
| customFamily | String |  | 字体family |
| selectable | Boolean | false | 文本是否可选 |
| [space](#space) | String |  | 显示连续空格 |
| decode | Boolean | false | 是否解码 |
| [mode](#mode) | String | text | 文本处理的匹配模式 |
| [format](#format) | String |  | 格式化规则 |
| path | String |  | 页面跳转地址 |
| call | Boolean | false | 是否拨打电话 |
| margin | Array |  | 边距[上右下左][上下左右][上下左右] |
| mt | Number |  | 距上单位px |
| mr | Number |  | 距右单位px |
| mb | Number |  | 距下单位px |
| ml | Number |  | 距左单位px |
| padding | Array |  | 填充[上右下左][上下左右][上下左右] |
| pt | Number |  | 上内边距单位px |
| pr | Number |  | 右内边距单位px |
| pb | Number |  | 下内边距单位px |
| pl | Number |  | 左内边距单位px |
| xstyle | Array |  | 自定义样式 |

### [theme](#theme)

| 值   | 说明 |
|:------:|:----:|
| primary | 主色
 |
| warning | 警告
 |
| success | 成功
 |
| error | 错误
 |
| info | 文本
 |

### [darkColor](#darkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [sizeType](#sizeType)

| 值   | 说明 |
|:------:|:----:|
| normal正常
 |  |
| small较小
 |  |
| big较大
 |  |

### [bold](#bold)

| 值   | 说明 |
|:------:|:----:|
| true加粗
 |  |
| false正常
 |  |

### [align](#align)

| 值   | 说明 |
|:------:|:----:|
| left左对齐
 |  |
| center居中对齐
 |  |
| right右对齐
 |  |

### [decoration](#decoration)

| 值   | 说明 |
|:------:|:----:|
| none无
 |  |
| underline下划线
 |  |
| line-through中划线
 |  |

### [space](#space)

| 值   | 说明 |
|:------:|:----:|
| ensp中文字符空格一半大小
 |  |
| emsp中文字符空格大小
 |  |
| nbsp根据字体设置的空格大小
 |  |

### [mode](#mode)

| 值   | 说明 |
|:------:|:----:|
| text普通文本
 |  |
| phone手机号
 |  |
| name姓名
 |  |
| date日期
 |  |
| link超链接
 |  |
| money金额
 |  |

### [format](#format)

| 值   | 说明 |
|:------:|:----:|
| encrypt脱敏加密
 |  |
| verify合法性校验
 |  |
| cmoney大写金额
 |  |
| qmoney金额千分制
 |  |
| wmoney金额万分制
 |  |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| click | 被点击时触发 |  |
  
  