import { UIView, UIColor, UIApplication, UIPasteboard } from 'UIKit';
import { URL } from 'Foundation';

import { UxOpenURLOptions, UxOpenWebOptions } from '../interface.uts'
import { UxMakePhoneCallOptions } from '../interface.uts'
import { UxVibrateOptions } from '../interface.uts'
import { UxPlusSuccessCallbackImpl, UxPlusErrorCallbackImpl } from '../unierror.uts'

var grayView : UIView | null = null;

export default class Common {
	
	hideKeyboard() {
		UTSiOS.getCurrentViewController().view.endEditing(true)
	}
	
	setGray(gray: number) : void {
		if (UTSiOS.available("iOS 13.0, *")) {
			let currentViewController = UTSiOS.getCurrentViewController()
			
			if(gray <= 0) {
				DispatchQueue.main.async(execute = () : void => {
					if (grayView != null) {
						grayView!.removeFromSuperview()
					}
				})
			} else {
				grayView = new UIView()
				grayView!.backgroundColor = UIColor.lightGray
				grayView!.frame = currentViewController.view.bounds
				grayView!.isUserInteractionEnabled = false
				grayView!.layer.compositingFilter = 'saturationBlendMode'
				
				DispatchQueue.main.async(execute = () : void => {
					currentViewController.view.addSubview(grayView!)
				})
			}
		} else {
			console.warn('[ux-plus] 暂不支持ios 13.0以下系统');
		}
	}
	
	openURL(options : UxOpenURLOptions) : void {
		if (options.url.length > 0) {
			let uri = new URL(string = options.url)
			if (uri != null && UIApplication.shared.canOpenURL(uri!)) {
				UIApplication.shared.open(uri!, options = new Map<UIApplication.OpenExternalURLOptionsKey, any>(), completionHandler = null)
				
				const res = new UxPlusSuccessCallbackImpl('openURL:success')
				options.success?.(res)
				options.complete?.(res)
				return
			}
		}
		
		const err = new UxPlusErrorCallbackImpl('openURL:failed')
		options.fail?.(err)
		options.complete?.(err)
	}
	
	openWeb(options : UxOpenWebOptions) : void {
		let darkMode = false
		let data = uni.getStorageSync('UxFrameConf')
		if(data instanceof UTSJSONObject) {
			darkMode = (data! as UTSJSONObject).getBoolean('darkMode') ?? false
		}
		
		let title = options.title ?? ''
		let ok = UxOpenWeb.show(title = title, url = options.url, darkMode = darkMode, close = () => {
			const res = new UxPlusSuccessCallbackImpl('openWeb:complete')
			options.complete?.(res)
		})
		
		if(ok) {
			const res = new UxPlusSuccessCallbackImpl('openWeb:success')
			options.success?.(res)
		} else {
			const res = new UxPlusErrorCallbackImpl('openWeb:failed')
			options.fail?.(res)
		}
	}
	
	makePhoneCall(options : UxMakePhoneCallOptions) {
		let uri = new URL(string = `tel://${options.phoneNumber!}`);
		if(uri == null) {
			const err = new UxPlusErrorCallbackImpl('makePhoneCall:failed')
			options.fail?.(err)
			options.complete?.(err)
			return
		}
		
		if (UTSiOS.available("iOS 10.0, *")) {
			if (UIApplication.shared.canOpenURL(uri!)) {
				let op = new Map<UIApplication.OpenExternalURLOptionsKey, any>()
				UIApplication.shared.open(uri!, options = op, completionHandler = null)
				
				const res = new UxPlusSuccessCallbackImpl('makePhoneCall:ok')
				options.success?.(res)
				options.complete?.(res)
				return
			}
		} else {
			if (UIApplication.shared.canOpenURL(uri!)) {
				UIApplication.shared.open(uri!)
				
				const res = new UxPlusSuccessCallbackImpl('makePhoneCall:ok')
				options.success?.(res)
				options.complete?.(res)
				return
			}
		}
		
		const err = new UxPlusErrorCallbackImpl('makePhoneCall:failed')
		options.fail?.(err)
		options.complete?.(err)
	}
	
	vibrate(options : UxVibrateOptions) {
		let generator = new UIImpactFeedbackGenerator(style = UIImpactFeedbackGenerator.FeedbackStyle.light);
		if (options.type == 'heavy') {
			generator = new UIImpactFeedbackGenerator(style = UIImpactFeedbackGenerator.FeedbackStyle.heavy);
		} else if (options.type == 'medium') {
			generator = new UIImpactFeedbackGenerator(style = UIImpactFeedbackGenerator.FeedbackStyle.medium);
		} else if (options.type == 'light' || options.type == 'lighter') {
			generator = new UIImpactFeedbackGenerator(style = UIImpactFeedbackGenerator.FeedbackStyle.light);
		}
		if (generator == null) {
			const err = new UxPlusErrorCallbackImpl('vibrate:failed')
			options.fail?.(err)
			options.complete?.(err)
		} else {
			generator.prepare();
			generator.impactOccurred();
			generator.prepare();
	
			const res = new UxPlusSuccessCallbackImpl('vibrate:success')
			options.success?.(res);
			options.complete?.(res);
		}
	}
	
	setClipboardData(data : string) {
		let pasteboard = UIPasteboard.general
		pasteboard.string = data
	}
	
	getClipboardData(): string {
		let pasteboard = UIPasteboard.general
		if(pasteboard.string != null){
			return `${pasteboard.string!}`
		}
		
		return ''
	}
}
