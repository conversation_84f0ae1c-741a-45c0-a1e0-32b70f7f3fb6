<template>
	<movable-view class="ux-movable-view"
		:direction="direction"
		:inertia="inertia"
		:outOfBounds="outOfBounds"
		:x="x"
		:y="y"
		:damping="damping"
		:friction="friction"
		:scale="scale"
		:scaleMin="scaleMin"
		:scaleMax="scaleMax"
		:animation="animation"
		:disabled="disabled"
		@scale="onScale"
		@change="change"
		@htouchmove="htouchmove"
		@vtouchmove="vtouchmove">
		<slot></slot>
	</movable-view>
</template>

<script setup lang="ts">
	
	/**
	 * MovableView 拖动视图
	 * @description 可移动的视图容器，在页面中可以拖拽滑动
	 * @demo pages/component/movable-area.uvue
	 * @tutorial https://www.uxframe.cn/component/movable-area.html
	 * @property {String}			direction=[all|vertical|horizontal|none]	String | movable-view的移动方向 (默认 all)
	 * @value all
	 * @value vertical
	 * @value horizontal
	 * @value none
	 * @property {Boolean}			inertia=[true|false]			Boolean | movable-view 是否带有惯性 (默认 false)
	 * @value true
	 * @value false
	 * @property {Boolean}			outOfBounds=[true|false]		Boolean | 超过可移动区域后，movable-view 是否还可以移动 (默认 false)
	 * @value true
	 * @value false
	 * @property {Number}			x								Number | 定义x轴方向的偏移，如果x的值不在可移动范围内，会自动移动到可移动范围；改变x的值会触发动画
	 * @property {Number}			y								Number | 定义y轴方向的偏移，如果y的值不在可移动范围内，会自动移动到可移动范围；改变y的值会触发动画
	 * @property {Number}			damping							Number | 阻尼系数，用于控制x或y改变时的动画和过界回弹的动画，值越大移动越快 (默认 20)
	 * @property {Number}			friction						Number | 摩擦系数，用于控制惯性滑动的动画，值越大摩擦力越大，滑动越快停止；必须大于0，否则会被设置成默认值 (默认 2)
	 * @property {Boolean}			scale=[true|false]				Boolean | 是否支持双指缩放，默认缩放手势生效区域是在movable-view内 (默认 false)
	 * @value true
	 * @value false
	 * @property {Number}			scaleMin						Number | 定义缩放倍数最小值 (默认 0.5)
	 * @property {Number}			scaleMax						Number | 定义缩放倍数最大值 (默认 10)
	 * @property {Number}			scaleValue						Number | 定义缩放倍数
	 * @property {Boolean}			animation=[true|false]			Boolean | 是否使用动画 (默认 true)
	 * @value true
	 * @value false
	 * @property {Boolean}			disabled=[true|false]			Boolean | 是否禁用 (默认 false)
	 * @value true
	 * @value false
	 * @event {Function}			change							Function | 拖动过程中触发的事件，event.detail = {x: x, y: y, source: source}，其中source表示产生移动的原因，值可为touch（拖动）、touch-out-of-bounds（超出移动范围）、out-of-bounds（超出移动范围后的回弹）、friction（惯性）和空字符串（setData）
	 * @event {Function}			scale							Function | 缩放过程中触发的事件，event.detail = {x: x, y: y, scale: scale}
	 * @event {Function}			htouchmove						Function | 初次手指触摸后移动为横向的移动，如果catch此事件，则意味着touchmove事件也被catch
	 * @event {Function}			vtouchmove						Function | 初次手指触摸后移动为纵向的移动，如果catch此事件，则意味着touchmove事件也被catch
	 * <AUTHOR>
	 * @date 2024-12-20 17:27:15
	 */
	
	import { $ux } from '../../index'
	
	defineOptions({
		name: 'ux-movable-view'
	})

	const emit = defineEmits(['change', 'scale', 'htouchmove', 'vtouchmove'])
	
	const props = defineProps({
		direction: {
			type: String,
			default: 'all',
		},
		inertia: {
			type: Boolean,
			default: false
		},
		outOfBounds: {
			type: Boolean,
			default: false
		},
		x: {
			type: Number,
			default: 0
		},
		y: {
			type: Number,
			default: 0
		},
		damping: {
			type: Number,
			default: 20
		},
		friction: {
			type: Number,
			default: 2
		},
		scale: {
			type: Boolean,
			default: false
		},
		scaleMin: {
			type: Number,
			default: 0.5
		},
		scaleMax: {
			type: Number,
			default: 10
		},
		scaleValue: {
			type: Number,
			default: 1
		},
		animation: {
			type: Boolean,
			default: true
		},
		disabled: {
			type: Boolean,
			default: false
		},
	})
	
	function onScale(e: UniEvent) {
		emit('scale', e)
	}
	
	function change(e: UniEvent) {
		emit('change', e)
	}
	
	function htouchmove(e: TouchEvent) {
		emit('htouchmove', e)
	}
	
	function vtouchmove(e: TouchEvent) {
		emit('vtouchmove', e)
	}
</script>

<style lang="scss">
	.ux-movable-view {
		position: absolute;
		top: 0;
		left: 0;
	}
</style>