
<p align="center">
    <img alt="logo" src="https://www.uxframe.cn/logo/logo.png" width="120" height="120" style="margin-bottom: 10px;">
</p>
<h3 align="center" style="margin: 30px 0 30px;font-weight: bold;font-size:40px;">UxFrame Sprite 精灵图动画组件 1.0.1</h3>

## 特别说明

如果您已经购买了[UxFrame 低代码高性能UI框架](https://ext.dcloud.net.cn/plugin?id=16148), 则无需再次购买本插件，请点击上方`进入交流群`联系我免费获取离线版插件！

### 使用方式

``` html

<template>
	<view>
		<ux-sprite style="width: 200px;height: 200px;" :autoplay="true" :loop="true" :frames="anims"></ux-sprite>
	</view>
</template>

<script setup>
	const anims = ref<string[]>([
		'/static/x1.png',
		'/static/x2.png', 
		'/static/x3.png', 
		'/static/x4.png', 
		'/static/x5.png', 
		'/static/x6.png', 
		'/static/x7.png', 
		'/static/x8.png', 
		'/static/x9.png',
		'/static/x10.png',
		'/static/x11.png',
		'/static/x12.png'
	] as string[])
</script>

```

## 文档教程 ⬇️

[https://www.uxframe.cn](https://www.uxframe.cn)
