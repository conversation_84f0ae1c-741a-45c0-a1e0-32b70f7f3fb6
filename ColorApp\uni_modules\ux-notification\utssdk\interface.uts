
export type CreateNotificationProgress = (options : UxCreateNotificationProgressOptions) => void;

export type CancelNotificationProgress = () => void;

export type FinishNotificationProgress = (options : UxFinishNotificationProgressOptions) => void

export type UxCreateNotificationProgressOptions = {
	title ?: string | null
	content : string,
	progress : number,
	onClick ?: (() => void) | null
}

export type UxFinishNotificationProgressOptions = {
	title ?: string | null
	content : string,
	onClick : () => void
}

export interface UxNotificationCallback extends IUniError {
	
}