<template>
	<view :id="myId" ref="uxToastItemRef" class="ux-toast-item" :class="[position, anim]" :style="style"
		@touchstart="touchstart"
		@touchmove="touchmove"
		@touchend="touchend"
		@mousedown="touchstartPc"
		@mousemove="touchmovePc"
		@mouseup="touchendPc"
		@mouseleave="touchendPc">
		<slot>
			<image v-if="logo != ''" class="ux-toast-item__logo" :style="logoStyle" :src="logo" mode="aspectFill"></image>
			<text v-if="content != ''" class="ux-toast-item__content" :style="contentStyle">{{ content }}</text>
		</slot>
	</view>
</template>

<script setup lang="ts">
	
	/**
	 * ToastItem 消息提示项
	 * @demo pages/component/toast.uvue
	 * @tutorial https://www.uxframe.cn/component/toast.html
	 * @property {String}			itemId										String | 唯一id
	 * @property {Number}			index										Number | 下标
	 * @property {String}			logo										String | Logo
	 * @property {Any}				logoWidth									Any | Logo宽 (默认 30)
	 * @property {Any}				logoHeight									Any | Logo高 (默认 30)
	 * @property {Any}				logoRadius									Any | Logo圆角 (默认 10)
	 * @property {String}			content										String | 内容
	 * @property {String} 			color										String | 文本颜色 优先级高于主题
	 * @property {String} 			darkColor=[none|auto|color]					String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Any} 				size										Any | 字体大小（默认 14 ）
	 * @property {Boolean} 			bold=[true|false]							Boolean | 字体加粗（默认 false ）
	 * @value true 加粗
	 * @value false 正常
	 * @property {String}			background									String | 背景色 (默认 $ux.Conf.backgroundColor)
	 * @property {String} 			backgroundDark=[none|auto|color]			String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色 (默认 auto)
	 * @value none 不显示		
	 * @value auto 自动适配深色模式		
	 * @value color 其他颜色
	 * @property {String}			borderColor									String | 边框颜色  (默认 $ux.Conf.borderColor )
	 * @property {String} 			borderDarkColor=[none|auto|color]			String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色 (默认 auto)
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Any}				radius										Any | 圆角 (默认 30)
	 * @property {Boolean}			shadow = [true|false]						Boolean | 显示阴影 (默认 true )
	 * @property {Boolean}			border = [true|false]						Boolean | 显示边框 (默认 false )
	 * @property {Boolean}			scale = [true|false]						Boolean | 缩放动画 (默认 true )
	 * <AUTHOR>
	 * @date 2024-12-04 16:36:15
	 */
	
	import { computed, getCurrentInstance, inject, onMounted, ref } from 'vue'
	import { $ux } from '../../index'
	import { UxToastData } from '../../libs/types/types.uts'
	import { useFontColor, useFontSize, useForegroundColor, useRadius, useBorderColor } from '../../libs/use/style.uts'
	
	defineOptions({
		name: 'ux-toast-item'
	})
	
	const props = defineProps({
		itemId: {
			type: String,
			default: ''
		},
		index: {
			type: Number,
			default: 0
		},
		logo: {
			type: String,
			default: ''
		},
		logoWidth: {
			default: 30
		},
		logoHeight: {
			default: 30
		},
		logoRadius: {
			default: 5
		},
		content: {
			type: String,
			default: ''
		},
		size: {
			default: 14
		},
		color: {
			type: String,
			default: '#fff'
		},
		darkColor: {
			type: String,
			default: '#fff'
		},
		bold: {
			type: Boolean,
			default: false
		},
		background: {
			type: String,
			default: '#333'
		},
		backgroundDark: {
			type: String,
			default: '#222'
		},
		border: {
			type: Boolean,
			default: false
		},
		borderColor: {
			type: String,
			default: ''
		},
		borderDarkColor: {
			type: String,
			default: 'auto'
		},
		radius: {
			default: 30
		},
		shadow: {
			type: Boolean,
			default: true
		},
		scale: {
			type: Boolean,
			default: true
		}
	})
	
	const position = inject('position', 'top') as string
	const anim = inject('anim', 'fade') as string
	const duration = inject('duration', 0) as number
	const threshold = inject('threshold', 0) as number
	const autoclose = inject('autoclose', true) as boolean
	const touchclose = inject('touchclose', true) as boolean
	
	const instance = getCurrentInstance()?.proxy
	const myId = `ux-toast-item-${$ux.Random.uuid()}`
	
	const isDarg = ref(false)
	const left = ref(0)
	const time = ref(0)
	const timer = ref(0)
	
	const fontColor = computed((): string => {
		return useFontColor(props.color, props.darkColor)
	})
	
	const fontSize = computed(() : number => {
		return useFontSize($ux.Util.getPx(props.size), 0)
	})
	
	const backgroundColor = computed((): string => {
		return useForegroundColor(props.background, props.backgroundDark)
	})
	
	const borderColor = computed((): string => {
		return useBorderColor(props.borderColor, props.borderDarkColor)
	})
	
	const radius = computed((): number => {
		return useRadius($ux.Util.getPx(props.radius), 0)
	})
	
	const style = computed(() => {
		let css = {}
		
		css['background-color'] = backgroundColor.value
		
		if(props.shadow && !$ux.Conf.darkMode.value) {
			css['box-shadow'] = `0 5px 24px rgba(0, 0, 0, 0.5)`
		}
		
		if(props.border) {
			css['border'] = `1rpx solid ${borderColor.value}`
		}
		
		css['border-radius'] = $ux.Util.addUnit(radius.value)
		
		return css
	})
	
	const logoStyle = computed(() => {
		let css = {}
		
		css['width'] = $ux.Util.addUnit(props.logoWidth)
		css['height'] = $ux.Util.addUnit(props.logoWidth)
		css['border-radius'] = $ux.Util.addUnit(props.logoRadius)
		
		return css
	})
	
	const contentStyle = computed(() => {
		let css = {}
		
		css['color'] = fontColor.value
		css['font-size'] = $ux.Util.addUnit(fontSize.value)
		css['font-weight'] = props.bold ? 'bold' : 'normal'
		
		return css
	})
	
	function click() {
		$ux.Util.$dispatch(instance!, 'ux-toast', 'click', props.itemId)
	}
	
	function _touchstart(x : number) {
		time.value = new Date().getTime()
		
		timer.value = setTimeout(() => {
			isDarg.value = true
			left.value = x
			
			uni.getElementById(myId)?.style?.setProperty('transition-duration', '0ms')
		}, 150);
	}
	
	function _touchmove(x : number) {
		if(Math.abs(x) > 10) {
			clearTimeout(timer.value)
		}
		
		if(!isDarg.value) {
			return
		}
		
		x = x - left.value
		let absx = Math.abs(x);
		let opacity = absx < 10 ? 1 : Math.max(0, 1 - Math.abs(x) / uni.getWindowInfo().windowWidth)
		
		uni.getElementById(myId)?.style?.setProperty('transform', `translateX(${x}px)`)
		uni.getElementById(myId)?.style?.setProperty('opacity', opacity)
	}
	
	function _touchend(x : number) {
		if(new Date().getTime() - time.value < 120) {
			click()
			return
		}
		
		if(!isDarg.value) {
			return
		}
		
		isDarg.value = false
		x = x - left.value
		
		uni.getElementById(myId)?.style?.setProperty('transition-duration', `${300}ms`)
		
		if (Math.abs(x) > threshold) {
			uni.getElementById(myId)?.style?.setProperty('opacity', 0);
			
			if (x > 0) {
				uni.getElementById(myId)?.style?.setProperty('transform', `translateX(150%)`)
			} else {
				uni.getElementById(myId)?.style?.setProperty('transform', `translateX(-150%)`)
			}
			
			setTimeout(() => {
				$ux.Util.$dispatch(instance!, 'ux-toast', 'unregister', props.itemId)
			}, 350);
		} else {
			uni.getElementById(myId)?.style?.setProperty('transform', `scale(1) translateX(0%)`)
			uni.getElementById(myId)?.style?.setProperty('opacity', 1)
		}
	}
	
	function touchstart(e: TouchEvent) {
		e.preventDefault()
		
		if(autoclose || !touchclose) {
			return
		}
		
		_touchstart(e.changedTouches[0].clientX)
	}
	
	function touchstartPc(e: MouseEvent) {
		e.preventDefault()
		
		if(autoclose || !touchclose) {
			return
		}
		
		_touchstart(e.clientX)
	}
	
	function touchmove(e: TouchEvent) {
		e.preventDefault()
		
		if(autoclose || !touchclose) {
			return
		}
		
		_touchmove(e.changedTouches[0].clientX)
	}
	
	function touchmovePc(e: MouseEvent) {
		e.preventDefault()
		
		if(autoclose || !touchclose) {
			return
		}
		
		_touchmove(e.clientX)
	}
	
	function touchend(e: TouchEvent) {
		e.preventDefault()
		
		if(autoclose || !touchclose) {
			return
		}
		
		_touchend(e.changedTouches[0].clientX)
	}
	
	function touchendPc(e: MouseEvent) {
		e.preventDefault()
		
		if(autoclose || !touchclose) {
			return
		}
		
		_touchend(e.clientX)
	}
	
	function close() {
		clearTimeout(timer.value)
		
		if(anim == 'none') {
			uni.getElementById(myId)?.style?.setProperty('opacity', 0)
			return
		}
		
		if(position == 'bottom') {
			uni.getElementById(myId)?.style?.setProperty('transform', 'scale(1)')
			uni.getElementById(myId)?.style?.setProperty('opacity', 0.3)
			
			setTimeout(() => {
				if(props.scale) {
					uni.getElementById(myId)?.style?.setProperty('transform', 'scale(0.5)')
				}
				uni.getElementById(myId)?.style?.setProperty('opacity', 0)
			}, 200)
		} else {
			uni.getElementById(myId)?.style?.setProperty('transform', 'scale(1)')
			uni.getElementById(myId)?.style?.setProperty('opacity', 0.3)
			
			setTimeout(() => {
				if(props.scale) {
					uni.getElementById(myId)?.style?.setProperty('transform', 'scale(0.5)')
				}
				uni.getElementById(myId)?.style?.setProperty('opacity', 0)
			}, 200)
		}
	}
	
	function register() {
		uni.createSelectorQuery()
			.in(instance)
			.select(`#${myId}`)
			.boundingClientRect().exec((ret) => {
				let node = ret[0] as NodeInfo
				
				$ux.Util.$dispatch(instance!, 'ux-toast', 'register', {
					id: props.itemId,
					height: (node.height ?? 0) + 15,
					node: instance as UxToastItemComponentPublicInstance,
					content: '',
				} as UxToastData)
				
				if(autoclose) {
					timer.value = setTimeout(() => {
						close()
					}, duration);
				}
			})
	}
	
	onMounted(() => {
		if(!props.scale) {
			uni.getElementById(myId)?.style?.setProperty('transition-property', 'left, top, bottom, opacity')
		} else {
			uni.getElementById(myId)?.style?.setProperty('transition-property', 'left, top, bottom, opacity, transform')
		}
		
		setTimeout(() => {
			uni.getElementById(myId)?.style?.setProperty('transform', 'scale(1)')
			uni.getElementById(myId)?.style?.setProperty('opacity', 1)
			
			setTimeout(() => {
				register()
			}, 300);
		}, 20);
	})
	
	defineExpose({
		close
	})
</script>

<style lang="scss" scoped>
	/* #ifdef H5 */
	uni-view {
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: stretch;
		align-content: stretch;
		justify-content: flex-start;
		box-sizing: border-box;
		min-height: 0px;
		min-width: 0px;
		overflow: hidden;
	}
	/* #endif */
	
	.ux-toast-item {
		/* #ifdef APP */
		pointer-events: auto;
		/* #endif */
		/* #ifdef WEB */
		pointer-events: all;
		/* #endif */
		position: absolute;
		padding: 10px 15px;
		min-height: 50px;
		border-radius: 30px;
		background-color: white;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: flex-start;
		opacity: 0;
		
		&__logo {
			margin-right: 10px;
			width: 30px;
			height: 30px;
			border-radius: 10px;
		}
		
		&__content {
			flex: 1;
			font-size: 14px;
			color: black;
		}
	}
	
	.fade {
		transform: scale(0);
		transition-duration: 400ms;
		transition-property: left, top, bottom, opacity, transform;
	}
	
	.none {
		transition-duration: 400ms;
		transition-property: left, top, opacity;
	}
	
	.top {
		margin-top: var(--status-bar-height);
		top: 15px;
		transform-origin: center;
	}
	
	.right {
		top: var(--status-bar-height);
		right: 15px;
		transform-origin: right;
	}
	
	.bottom {
		margin-bottom: var(--window-bottom);
		bottom: 15px;
		transform-origin: center;
	}
	
	.left {
		top: var(--status-bar-height);
		left: 15px;
		transform-origin: left;
	}
	
	.center {
		top: 40%;
	}
</style>