<template>
	<list-view
		:id="myId" 
		class="ux-list" 
		:style="style"
		:direction="direction"
		:associative-container="associativeContainer" 
		:bounces="bounces"
		:upper-threshold="upperThreshold"
		:lower-threshold="lowerThreshold"
		:scroll-top="_scrollTop"
		:scroll-left="_scrollLeft"
		:scroll-into-view="scrollIntoView"
		:scroll-with-animation="scrollWithAnimation" 
		:refresher-enabled="refresherEnabled"
		:refresher-threshold="refresherThreshold"
		:refresher-max-drag-distance="refresherMaxDragDistance"
		refresher-default-style="none"
		:refresher-background="refresherBackground"
		:refresher-triggered="refresherTriggered"
		:show-scrollbar="showScrollbar"
		:custom-nested-scroll="customNestedScroll"
		@refresherpulling="refresherpulling"
		@refresherrefresh="refresherrefresh"
		@refresherrestore="refresherrestore"
		@refresherabort="refresherabort"
		@scrolltoupper="scrolltoupper"
		@scrolltolower="scrolltolower"
		@scroll="scroll"
		@scrollend="scrollend">
		
		<ux-list-item ref="cell-header" id="cell-header">
			<slot name="header"></slot>
		</ux-list-item>
		
		<ux-list-item :ref="`cell-${index}`" :id="`cell-${index}`" v-for="(item, index) in list" :key="index">
			<slot name="cell" :item="item" :index="index"></slot>
		</ux-list-item>
		
		<slot></slot>
		
		<ux-list-item ref="cell-footer" id="cell-footer">
			<slot name="footer"></slot>
		</ux-list-item>
		
		<ux-list-item :type="9998">
			<ux-loadmore v-if="loadmoreEnabled" :states="loadmoreStates" :state="loadmoreState"></ux-loadmore>
		</ux-list-item>
		
		<ux-list-item :type="9999">
			<ux-placeholder v-if="placeholder" :tabbar="true"></ux-placeholder>
		</ux-list-item>
		
		<ux-list-item v-if="refresherEnabled" slot="refresher" :type="10000" style="width: 100%;">
			<ux-refresher :states="refresherStates" :state="refresherState"></ux-refresher>
		</ux-list-item>
	</list-view>
	
	<view style="position: fixed;">
		<ux-backtop v-if="backtop && $slots['backtop'] == null" ref="uxBacktopRef" @click="toTop"></ux-backtop>
		<ux-backtop v-if="backtop && $slots['backtop'] != null" ref="uxBacktopRef" @click="toTop">
			<slot name="backtop"></slot>
		</ux-backtop>
	</view>
</template>

<script setup>
	
	/**
	 * list-view 高性能滚动列表
	 * @description 扩展支持自定义下拉刷新、上拉加载自定义样式，可显示返回顶部按钮
	 * @demo pages/component/list.uvue
	 * @tutorial https://www.uxframe.cn/component/list.html
	 * @property {Slot}				default  									Slot | 默认slot
	 * @property {Slot}				refresher  									Slot | refresher slot
	 * @property {Slot}				loadmore  									Slot | loadmore slot
	 * @property {Slot}				backtop  									Slot | backtop slot
	 * @property {UTSJSONObject}	list										UTSJSONObject[] 虚拟节点数据列表
	 * @property {Number}			pageSize									Number | 单页item数量 (默认 10)
	 * @property {Number}			cachePages									Number | 缓存上下页面数 (默认 1）
	 * @property {String}			direction=[none|all|horizontal|vertical]  	String | 滚动方向，可取值 none、all、horizontal、vertical (默认 vertical)
	 * @value none 无
	 * @value all 全部
	 * @value horizontal 水平
	 * @value vertical 垂直
	 * @property {String}		associativeContainer=[nested-scroll-view]		String | 关联的滚动容器
	 * @value nested-scroll-view	嵌套滚动
	 * @property {Boolean}  	bounces = [true|false]  				Boolean | 控制是否回弹效果 (默认 true)
	 * @property {Number}		upperThreshold  						Number | 距顶部/左边多远时（单位px），触发 scrolltoupper 事件（默认 50 ）
	 * @property {Number} 		lowerThreshold 							Number | 距底部/右边多远时（单位px），触发 scrolltolower 事件 （默认 50 ）
	 * @property {Number} 		scrollTop 								Number | 设置竖向滚动条位置 （默认 0 ）
	 * @property {Number}  		scrollLeft								Number | 设置横向滚动条位置（默认 0）
	 * @property {String}  		scrollIntoView							String | 值应为某子元素id（id不能以数字开头）。设置哪个方向可滚动，则在哪个方向滚动到该元素
	 * @property {Boolean}  	scrollWithAnimation = [true|false]  	Boolean | 是否在设置滚动条位置时使用滚动动画，设置false没有滚动动画 (默认 true)
	 * @property {Boolean}  	refresherEnabled = [true|false]  		Boolean | 开启下拉刷新 (默认 false)
	 * @property {Number}  		refresherThreshold  					Number | 设置下拉刷新阈值（默认 45 ）
	 * @property {Number}		refresherMaxDragDistance				Number | 设置下拉最大拖拽距离（单位px）（默认 80 ）
	 * @property {String}		refresherBackground						String | 设置下拉刷新区域背景颜色 (默认 transparent)
	 * @property {Boolean}		refresherTriggered = [true|false]		Boolean | 设置当前下拉刷新状态，true 表示下拉刷新已经被触发，false 表示下拉刷新未被触发 (默认 false)
	 * @property {Array}		refresherStates							Array | 下拉刷新状态文案
	 * @property {Boolean}  	loadmoreEnabled = [true|false]  		Boolean | 开启上拉加载 (默认 false)
	 * @property {Array}		loadmoreStates							Array | 上拉加载状态文案
	 * @property {Boolean}		showScrollbar = [true|false]			Boolean | 控制是否出现滚动条 (默认 false)
	 * @property {Boolean}		customNestedScroll = [true|false]		Boolean | 子元素是否开启嵌套滚动 将滚动事件与父元素协商处理 (默认 false)
	 * @property {Boolean}		placeholder = [true|false]				Boolean | 底部导航栏高度占位
	 * @property {Boolean}		disabled = [true|false]					Boolean | 禁止滚动 (默认 false)
	 * @property {String}		background								String | 背景颜色 (默认 transparent)
	 * @property {Array}		xstyle									Array<any> | 自定义样式
	 * @event {Function}		refresherpulling						Function | 下拉刷新控件被下拉
	 * @event {Function}		refresherrefresh						Function | 下拉刷新被触发
	 * @event {Function}		refresherrestore						Function | 下拉刷新被复位
	 * @event {Function}		refresherabort							Function | 下拉刷新被中止
	 * @event {Function}		loadmore								Function | 上拉加载触发
	 * @event {Function}		scrolltoupper							Function | 滚动到顶部/左边，会触发 scrolltoupper 事件
	 * @event {Function}		scrolltolower							Function | 滚动到底部/右边，会触发 scrolltolower 事件
	 * @event {Function}		scroll									Function | 滚动时触发
	 * @event {Function}		scrollend								Function | 滚动结束时触发
	 * <AUTHOR>
	 * @date 2023-11-04 01:12:28
	 */
	
	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	
	defineOptions({
		name: 'ux-list',
		mixins: [xstyleMixin]
	})
	
	defineSlots<{
		cell(props : { item : any, index: number }) : any
	}>()
	
	const emit = defineEmits([
		'refresherpulling',
		'refresherrefresh', 
		'refresherrestore', 
		'refresherabort',
		'loadmore',
		'scrolltoupper', 
		'scrolltolower', 
		'scroll',
		'scrollend'])
	
	const props = defineProps({
		list: {
			type: Array as PropType<Array<any>>,
			default: (): any[] => [] as any[]
		},
		pageSize: {
			type: Number,
			default: 10
		},
		cachePages: {
			type: Number,
			default: 1
		},
		direction: {
			type: String,
			default: 'vertical'
		},
		associativeContainer: {
			type: String,
			default: ''
		},
		bounces: {
			type: Boolean,
			default: true
		},
		upperThreshold: {
			type: Number,
			default: 50
		},
		lowerThreshold: {
			type: Number,
			default: 50
		},
		scrollTop: {
			type: Number,
			default: 0
		},
		scrollLeft: {
			type: Number,
			default: 0
		},
		scrollIntoView: {
			type: String,
			default: '',
		},
		scrollWithAnimation: {
			type: Boolean,
			default: true,
		},
		refresherEnabled: {
			type: Boolean,
			default: false,
		},
		refresherThreshold: {
			type: Number,
			default: 45,
		},
		refresherMaxDragDistance: {
			type: Number,
			default: 80,
		},
		refresherBackground: {
			type: String,
			default: 'transparent',
		},
		refresherTriggered: {
			type: Boolean,
			default: false,
		},
		refresherStates: {
			type: Array as PropType<Array<string>>,
			default: (): string[] => {
				return ['下拉刷新', '释放刷新', '刷新中...', '刷新成功']
			},
		},
		loadmoreEnabled: {
			type: Boolean,
			default: false,
		},
		loadmoreStates: {
			type: Array as PropType<Array<string>>,
			default: (): string[] => {
				return ['加载中...', '-- 我也是有底线的 --']
			},
		},
		showScrollbar: {
			type: Boolean,
			default: false,
		},
		customNestedScroll: {
			type: Boolean,
			default: false,
		},
		backtop: {
			type: Boolean,
			default: false
		},
		placeholder: {
			type: Boolean,
			default: false
		},
		background: {
			type: String,
			default: 'transparent',
		}
	})
	
	const uxBacktopRef = ref<UxBacktopComponentPublicInstance | null>(null)
	const myId = `ux-list-${$ux.Random.uuid()}`
	const scrollY = ref(0)
	const refresherState = ref(0)
	const loadmoreState = ref(0)
	const _scrollTop = ref(0)
	const _scrollLeft = ref(0)
	
	provide('scrollY', scrollY)
	
	const direction = computed((): string => {
		return props.disabled ? 'none' : props.direction
	})
	
	const style = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		if(props.direction == 'horizontal') {
			css.set('flex-direction', 'row')
		}
		
		css.set('background-color', props.background)
		
		css = $ux.Util.xStyle(css, props.margin, props.mt, props.mr, props.mb, props.ml, props.padding, props.pt, props.pr, props.pb, props.pl)
		
		return css
	})
	
	const __scrollTop = computed(() => {
		return props.scrollTop
	})
	
	watch(__scrollTop, () => {
		_scrollTop.value = __scrollTop.value
	})
	
	const __scrollLeft = computed(() => {
		return props.scrollLeft
	})
	
	watch(__scrollLeft, () => {
		_scrollLeft.value = __scrollLeft.value
	})
	
	const refresherTriggered = computed((): boolean => {
		return props.refresherTriggered
	})
	
	watch(refresherTriggered, (a: boolean, b: boolean) => {
		if(a == false && b == true) {
			// 刷新完成
			refresherState.value = 3
		}
	})
	
	function scroll(e: ScrollEvent) {
		
		scrollY.value = e.detail.scrollTop
		
		// 显示置顶不卡顿
		if(props.backtop) {
			uxBacktopRef.value!.$callMethod('setShow', scrollY.value > 300)
		}
		
		emit('scroll', e)
	}
	
	function scrollend(e: ScrollEvent) {
		emit('scrollend', e)
	}
	
	function refresherpulling(e: RefresherEvent) {
		// 释放刷新
		if(refresherState.value == 0 || refresherState.value == 1) {
			if(e.detail.dy > props.refresherThreshold) {
				refresherState.value = 1
			} else {
				refresherState.value = 0
			}
		}
		
		emit('refresherpulling', e)
	}
	
	function refresherrefresh(e: RefresherEvent) {
		// 刷新中
		refresherState.value = 2
		
		emit('refresherrefresh', e)
	}
	
	function refresherrestore(e: RefresherEvent) {
		emit('refresherrestore', e)
		
		// 刷新结束
		setTimeout(() => {
			refresherState.value = 0
		}, 50);
	}
	
	function refresherabort(e: RefresherEvent) {
		emit('refresherabort', e)
		
		// 刷新结束
		setTimeout(() => {
			refresherState.value = 0
		}, 50);
	}
	
	function scrolltoupper(e: ScrollToUpperEvent) {
		emit('scrolltoupper', e)
	}
	
	function scrolltolower(e: ScrollToLowerEvent) {
		// 上拉触底加载
		if(props.loadmoreEnabled) {
			loadmoreState.value = 1
			emit('loadmore', e)
		}
		
		emit('scrolltolower', e)
	}
	
	function loadSuccess(lastPage: boolean) {
		loadmoreState.value = lastPage ? 2 : 0
	}
	
	function toTop() {
		if(direction.value == 'vertical') {
			_scrollTop.value = -1
			nextTick(() => {
				_scrollTop.value = 0
			})
		} else {
			_scrollLeft.value = -1
			nextTick(() => {
				_scrollLeft.value = 0
			})
		}
	}
	
	defineExpose({
		loadSuccess
	})
</script>

<style lang="scss">
	.ux-list {
		flex: 1;
	}
</style>