<template>
	<!-- #ifdef APP-NVUE -->
	<cell class="ux-chatlist-item" :type="type">
		<slot></slot>
	</cell>
	<!-- #endif -->
	<!-- #ifndef APP-NVUE -->
	<view class="ux-chatlist-item">
		<slot></slot>
	</view>
	<!-- #endif -->
</template>

<script setup>
	
	/**
	 * UxChatList 子项
	 * @demo pages/component/chatlist.uvue
	 * @tutorial https://www.uxframe.cn/component/chatlist.html
	 * <AUTHOR>
	 * @date 2024-12-05 14:12:28
	 */
	
	defineOptions({
		name: 'ux-chatlist-item'
	})
	
	defineProps({
		type: {
			type: String,
			default: ''
		}
	})
</script>


<style lang="scss" scoped>
	.ux-chatlist-item {
		transform: rotate(-180deg);
	}
</style>