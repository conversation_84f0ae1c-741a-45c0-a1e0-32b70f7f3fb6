/**
 * 将一个数字限制在指定的包含边界内
 * @description 该函数接受一个数字和两个边界，并返回将数字限制在指定边界内的结果。如果数字小于第一个边界，则返回第一个边界；如果数字大于第二个边界，则返回第二个边界；否则返回数字本身。
 * @param {number} value 要限制的数字
 * @param {number} min 最小值
 * @param {number} max 最大值
 * @returns {number} 限制后的数字
 */
function clamp(value: number, min: number): number
function clamp(value: number, min: number, max: number): number
function clamp(value: number, min: number, max: any | null): number {
    if (max == null) {
		return value >= min ? min : value
	}
	
	return Math.max(min, Math.min(value, max as number));
}

/**
 * 检查值是否在指定范围内
 * @description 该函数接受一个数字和两个边界，并返回一个布尔值，指示数字是否在指定范围内。
 * @param {number} value 要检查的数字
 * @param {number} min 范围的下限 (包含在内)
 * @param {number} max 范围的上限 (不包含在内)
 * @returns {boolean} 布尔值，指示数字是否在指定范围内
 */
function inRange(value: number, min: number): boolean
function inRange(value: number, min: number, max: number): boolean
function inRange(value: number, min: number, max: any | null): boolean {
	if (max == null) {
		return value <= min
	}
	const _max = max as number
	// 如果最小值大于等于最大值，则抛出异常
	if (min >= _max) {
		throw new Error('【kux-toolkit:inRange】Invalid range');
		return false
	}
    return value >= Math.min(min, _max) && value <= Math.max(min, _max);
}

/**
 * 计算数字数组的平均值
 * @description 该函数接受一个数字数组，并返回数组中所有数字的平均值。如果数组为空，则返回 NaN。
 * @param {number[]} nums 要计算平均值的数字数组
 * @returns {number} 数组的平均值
 */
function mean(nums: number[]): number {
	const len = nums.length;
	if (len == 0) {
		return NaN;
	}
	let sum = 0;
	for (let i = 0; i < len; i++) {
		sum += nums[i];
	}
	return sum / len;
}

/**
 * 计算数字数组的平均值，通过对每个元素应用 `getValue` 函数来选择值
 * @description 该函数接受一个数字数组和一个 `getValue` 函数，并返回数组中所有元素的平均值。如果数组为空，则返回 NaN。
 * @param {number[]} items 要计算平均值的数字数组
 * @param {function} getValue 一个函数，用于从数组元素中选择值
 * @returns {number} 数组的平均值
 */
function meanBy<T>(items: T[], getValue: (item: T) => number): number {
	const len = items.length;
	if (len == 0) {
		return NaN;
	}
	let sum = 0;
	for (let i = 0; i < len; i++) {
		sum += getValue(items[i]);
	}
	return sum / len;
}

/**
 * 生成指定范围内的随机数。该数可以是整数或浮点数。
 * @description 如果只提供一个参数，则返回一个介于 0 到该参数 (不包含该参数) 之间的随机数。如果提供两个参数，则返回一个介于第一个参数 (包含) 到第二个参数 (不包含) 之间的随机数。
 * @param {number} min 随机数的下限 (包含在内)
 * @param {number} max 随机数的上限 (不包含在内)
 * @returns {number} 随机数
 */
function random(min: number): number
function random(min: number, max: number): number
function random(min: number, max: any | null): number {
	if (max == null) {
		return Math.random() * min;
	}
	const _max = max as number
	// 如果最小值大于等于最大值，则抛出异常
	if (min >= _max) {
		throw new Error('【kux-toolkit:random】Invalid range');
		return 0
	}
	return Math.random() * (_max - min) + min;
}

/**
 * 生成指定范围内的随机整数。
 * @description 如果只提供一个参数，则返回一个介于 `0` 到该参数 (不包含该参数) 之间的随机整数。如果提供两个参数，则返回一个介于第一个参数 (包含) 到第二个参数 (不包含) 之间的随机整数。
 * @param {number} min 随机整数的下限 (包含在内)
 * @param {number} max 随机整数的上限 (不包含在内)
 * @returns {number} 随机数
 */
function randomInt(min: number): number
function randomInt(min: number, max: number): number
function randomInt(min: number, max: any | null): number {
	if (max == null) {
		return Math.floor(Math.random() * min);
	}
	const _max = max as number
	// 如果最小值大于等于最大值，则抛出异常
	if (min >= _max) {
		throw new Error('【kux-toolkit:randomInt】Invalid range');
		return 0
	}
	return Math.floor(Math.random() * (_max - min)) + min;
}

/**
 * 返回一个从 `start` 到 `end` 的数字数组，步长为 `step`。
 * @description 如果未提供 `step`，则默认为 `1`。注意，`step` 必须是一个非零整数。
 * @param {number} start 起始值 (包含在内)
 * @param {number} end 结束值 (不包含在内)
 * @param {number} step 步长 (默认为 `1`)
 * @returns {number[]} 数字数组
 */
function range(end: number): number[]
function range(start: number, end: number): number[]
function range(start: number, end: number, step: number): number[]
function range(start: any | null, end: number, step: any | null = 1): number[] {
	let _start = 0;
	let _end = end;
	let _step = step;
	
	if (start == null) {
		_start = 0;
	} else {
		_start = start as number;
	}
	
	if (_step == null) {
		_step = 1;
	} else {
		_step = _step as number;
	}

	const result: number[] = [];

	// 这里需要处理 step 为 0 的情况
	if (_step == 0) {
		throw new Error('【kux-toolkit:range】Step cannot be zero');
	}
	
	// 这里需要处理 step 为负数的情况
	if ((_step as number) < 0) {
		for (let i = _start; i > _end; i += (_step as number)) {
			result.push(i);
		}
	} else {
		for (let i = _start; i < _end; i += (_step as number)) {
			result.push(i);
		}
	}
	
	return result;
}

/**
 * 将一个数字四舍五入到指定的精度。
 * @description 该函数接受一个数字和一个可选的精度值，并返回该数字四舍五入到指定的小数位数的结果。
 * @param {number} value 要四舍五入的数字
 * @param {number} precision 精度值 (默认为 `0`)
 * @returns {number} 四舍五入后的数字
 */
// function round(value: number): number
// function round(value: number, precision: number): number
function round(value: number, precision: number = 0): number {
	// 如果精度值不是整数，则抛出异常
	if (Math.floor(precision) != precision) {
		throw new Error('【kux-toolkit:round】Precision must be an integer');
		return 0
	}
	const factor = Math.pow(10, precision as number);
	return Math.round(value * factor) / factor;
}

/**
 * 计算一个数字数组的总和。
 * @description 该函数接受一个数字数组，并返回数组中所有数字的总和。
 * @param {number[]} nums 要计算总和的数字数组
 * @returns {number} 数组的总和
 */
function sum(nums: number[]): number {
	let sum = 0;
	for (let i = 0; i < nums.length; i++) {
		sum += nums[i];
	}
	return sum;
}

/**
 * 计算数字数组的总和，通过对每个元素应用 `getValue` 函数来选择值。
 * @description 该函数接受一个数字数组和一个 `getValue` 函数，并返回数组中所有元素的总和。如果数组为空，则返回 `0`。
 * @param {T[]} items 要计算总和的数字数组
 * @param {function} getValue 从每个元素选择数值的函数。
 * @returns {number} 数组的总和
 */
function sumBy<T>(items: T[], getValue: (item: T) => number): number {
	let sum = 0;
	for (let i = 0; i < items.length; i++) {
		sum += getValue(items[i]);
	}
	return sum;
}

export { clamp, inRange, mean, meanBy, random, randomInt, range, round, sum, sumBy }