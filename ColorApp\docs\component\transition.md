<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/transition?title=Transition"></Mobile>

# Transition
> 组件类型：UxTransitionComponentPublicInstance

支持淡入、缩放、缩放淡入、上滑淡入、下滑淡入、左滑淡入、右滑淡入、上滑进入、下滑进入、左滑进入、右滑进入等动画方式

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| show | Boolean | false | 显示状态 |
| [mode](#mode) | String | fade | 动画模式 |
| duration | Number | 300 | 动画的执行时间，单位ms |
| [timingFunction](#timingFunction) | String | ease-out | 使用的动画过渡函数 |
| xstyle | Array |  | 自定义样式 |

### [mode](#mode)

| 值   | 说明 |
|:------:|:----:|
| fade淡入
 |  |
| zoom缩放
 |  |
| fade-zoom缩放淡入
 |  |
| fade-up上滑淡入
 |  |
| fade-left左滑淡入
 |  |
| fade-right右滑淡入
 |  |
| fade-down下滑淡入
 |  |
| slide-up上滑进入
 |  |
| slide-left左滑进入
 |  |
| slide-down下滑进入
 |  |
| slide-right右滑进入
 |  |

### [timingFunction](#timingFunction)

| 值   | 说明 |
|:------:|:----:|
| ease开始缓慢，然后逐渐加速，最后减速结束
 |  |
| ease-in过渡开始时较慢，然后逐渐加速
 |  |
| ease-out过渡开始时较快，然后逐渐减速
 |  |
| ease-in-out过渡开始时较慢，然后加速，最后减速
 |  |
| linear恒定速度，没有加速或减速
 |  |
| cubic-bezier用于自定义CSS过渡（transition）的时间函数的函数，它允许你精确地定义过渡效果的速度变化
 |  |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| beforeEnter | 进入前触发 |  |
| enter | 进入中触发 |  |
| afterEnter | 进入后触发 |  |
| beforeLeave | 离开前触发 |  |
| leave | 离开中触发 |  |
| afterLeave | 离开后触发 |  |
| transitionend | 动画结束后触发 |  |
  
  