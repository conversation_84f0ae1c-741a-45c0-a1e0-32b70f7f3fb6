<template>
	<view :id="myId" class="ux-sidebar" :style="style">
		<scroll-view ref="scrollRef" class="ux-sidebar__scroll" :style="scrollStyle"
			direction="vertical"
			:scroll-into-view="scrollviewId"
			:scroll-with-animation="true"
			:show-scrollbar="false">
			<view ref="sidebarRef" :data-id="tab.id" :id="tab.id" class="ux-sidebar__item" :style="tabStyle(tab)"
				v-for="(tab, index) in tabs" :key="index" @click="click(tab, index)">
				<text class="ux-sidebar__text" :style="textStyle(tab)">{{ tab.name }}</text>
				<ux-badge :theme="theme" :value="tab.badge" :dot="tab.reddot" :size="10" :right="4" :top="4"></ux-badge>
			</view>
		</scroll-view>
		<view ref="rightRef" :style="rightStyle">
			<slot></slot>
		</view>
		
		<view v-if="type == 'line'" class="ux-sidebar__warp" :style="warpStyle">
			<view :class="`ux-sidebar__line--${anim}`" :style="lineStyle"></view>
		</view>
		<view v-if="type == 'point'" class="ux-sidebar__warp" :style="warpStyle">
			<view ref="pointRef" class="ux-sidebar__point" :style="pointStyle"></view>
		</view>
	</view>
</template>

<script setup>
	
	/**
	 * Tabs 标签导航栏
	 * @description 支持滑动距离自动切换到锚点
	 * @demo pages/component/sidebar.uvue
	 * @tutorial https://www.uxframe.cn/component/sidebar.html
	 * @property {Slot}				default								Slot | 默认插槽
	 * @property {String}			theme=[text|info|primary|success|warning|error]	String | 按钮类型 (默认 info)
	 * @value text			文字
	 * @value info   		默认
	 * @value primary   	主要
	 * @value success  		成功
	 * @value warning   	警告
	 * @value error  		错误
	 * @property {Number}			modelValue							Number | 双向绑定值 (默认 0)
	 * @property {UxTab[]}			datas								UxTab[] | sidebar列表
	 * @property {String}			type = [none|line|point]			String | 类型 (默认 line)
	 * @value none 无
	 * @value line 下划线
	 * @value point 点
	 * @property {String}			anim = [none|scroll|push]			String | 动效类型 (默认 scroll)
	 * @value none 无
	 * @value scroll 左右滚动
	 * @value push 左右推压
	 * @property {String}			anchorId							String | 锚点ScrollId
	 * @property {Any}				width								Any | 宽度 (默认 auto)
	 * @property {Any}				height								Any | 高度 (默认 auto)
	 * @property {Any}				tabWidth							Any | Tab宽度 (默认 100)
	 * @property {Any}				tabHeight							Any | Tab高度 (默认 44)
	 * @property {Any}				corner								Any | 圆角 (默认 0)
	 * @property {String}			selectedColor						String | 选中颜色 (默认 $ux.Conf.fontColor)
	 * @property {String}			unselectedColor						String | 未选中颜色 (默认 $ux.Conf.fontColor)
	 * @property {String}			background							String | 背景色 (默认 $ux.Conf.backgroundColor)
	 * @property {String} 			backgroundDark=[none|auto|color]	String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {String}			lineColor							String | 线颜色 selectedColor 优先级更高
	 * @property {Any}				lineWidth							Any | 线宽度 0则自动适配 (默认 2)
	 * @property {Any}				lineHeight							Any | 线高度、点直径 (默认 30)
	 * @property {Any}				fontSize							Any | 字体大小 (默认 $ux.Conf.fontSize)
	 * @property {Any}				selectSize							Any | 选中字体大小 (默认 $ux.Conf.fontSize)
	 * @property {Boolean}			bold = [true|false]					Boolean | 字体加粗 (默认 false)
	 * @property {Boolean}			selectBold = [true|false]			Boolean | 选中字体加粗 (默认 false)
	 * @property {Number}			duration							Number | 过渡时间 (默认 300)
	 * @property {Any}				padding								Any | 内部padding (默认 12)
	 * @property {Any}				offset								Any | 锚点偏移距离 (默认 0)
	 * @event {Function}			change								Function | 当前选择下标改变时触发
	 * @event {Function}			anchor								Function | 当要滚动目标时触发
	 * <AUTHOR>
	 * @date 2024-05-21 17:35:22
	 */
	
	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	import { UxTab, UxNodeInfo } from '../../libs/types/types.uts'
	import { useThemeColor, useForegroundColor, useFontColor, useFontSize, UxThemeType } from '../../libs/use/style.uts'
	import { useResize } from '../../libs/use/resize'
	
	defineOptions({
		name: 'ux-sidebar',
		mixins: [xstyleMixin]
	})
	
	const emit = defineEmits(['update:modelValue', 'change', 'anchor'])
	
	const props = defineProps({
		modelValue: {
			type: Number,
			default: 0
		},
		datas: {
			type: Array as PropType<UxTab[]>,
			default: () : UxTab[] => [] as UxTab[]
		},
		theme: {
			type: String,
			default: 'primary'
		},
		type: {
			type: String,
			default: 'line'
		},
		anim: {
			type: String,
			default: 'scroll'
		},
		anchorId: {
			type: String,
			default: ''
		},
		width: {
			default: 0
		},
		height: {
			default: 0
		},
		tabWidth: {
			default: 100
		},
		tabHeight: {
			default: 44
		},
		corner: {
			default: 0
		},
		background: {
			type: String,
			default: ''
		},
		backgroundDark: {
			type: String,
			default: ''
		},
		selectedColor: {
			type: String,
			default: ''
		},
		unselectedColor: {
			type: String,
			default: ''
		},
		lineColor: {
			type: String,
			default: ''
		},
		lineWidth: {
			default: 0
		},
		lineHeight: {
			default: 30
		},
		fontSize: {
			default: 0
		},
		selectSize: {
			default: 0
		},
		bold: {
			type: Boolean,
			default: false
		},
		selectBold: {
			type: Boolean,
			default: false
		},
		padding:{
			default: 12
		},
		duration: {
			type: Number,
			default: 300
		},
		offset: {
			default: 0
		}
	})
	
	const myId = `ux-sidebar-${ $ux.Random.uuid() }`
	const scrollRef = ref<Element | null>(null)
	const rightRef = ref<Element | null>(null)
	const pointRef = ref<Element | null>(null)
	const sidebarRef = ref<Element[] | null>(null)
	const tabs = ref<UxTab[]>([] as UxTab[])
	const tabIndex = ref(0)
	const lastIndex = ref(0)
	const lineTop = ref(0)
	const lineHeight = ref(30)
	const scrollviewId = ref('')
	const scrollWidth = ref(0)
	const scrollHeight = ref(0)
	const scrollTop = ref(0)
	const scrollBottom = ref(0)
	
	const totalHeight = computed(() => {
		let w = 0
		for (let i = 0; i < tabs.value.length; i++) {
			w += (tabs.value[i].node?.height ?? 0)
		}
		
		return w
	})
	
	const backgroundColor = computed(() : string => {
		return useForegroundColor(props.background, props.backgroundDark)
	})
	
	const selectedColor = computed(() : string => {
		if(props.selectedColor == '') {
			return useThemeColor(props.theme as UxThemeType)
		} else {
			return props.selectedColor
		}
	})
	
	const unselectedColor = computed(() : string => {
		return useFontColor(props.unselectedColor, '')
	})
	
	const lineColor = computed(() : string => {
		if(props.lineColor == '') {
			return selectedColor.value
		} else {
			return props.lineColor
		}
	})
	
	const fontSize = computed(() : number => {
		if($ux.Util.getPx(props.fontSize) > 0) {
			return $ux.Util.getPx(props.fontSize)
		} else {
			return useFontSize($ux.Util.getPx(props.fontSize), 0)
		}
	})
	
	const selectSize = computed(() : number => {
		if($ux.Util.getPx(props.selectSize) > 0) {
			return $ux.Util.getPx(props.selectSize)
		} else {
			return useFontSize($ux.Util.getPx(props.selectSize), 0)
		}
	})
	
	const lineWidth = computed(() : number => {
		if(props.type == 'point') {
			return $ux.Util.getPx(props.lineWidth) > 0? $ux.Util.getPx(props.lineWidth) : 5
		} else {
			return $ux.Util.getPx(props.lineWidth) > 0? $ux.Util.getPx(props.lineWidth) : 2
		}
	})
	
	const rightHeight = computed(async ()=> {
		let rect = await scrollRef.value?.getBoundingClientRectAsync()!
		
		if($ux.Util.getPx(props.height) > 0) {
			return $ux.Util.getPx(props.height)
		} else {
			return rect?.height ?? 0
		}
	})
	
	const style = computed(():Map<string, any> => {
		let css = new Map<string, any>()
		
		if($ux.Util.getPx(props.width) > 0) {
			css.set('width', `${$ux.Util.addUnit(props.width)}`)
		}
		
		if($ux.Util.getPx(props.height) > 0) {
			css.set('height', `${$ux.Util.addUnit(props.height)}`)
		}
		
		css.set('background-color', backgroundColor.value)
		css.set('border-radius', `${$ux.Util.addUnit(props.corner)}`)
		
		return css
	})
	
	const scrollStyle = computed(():Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('width', `${$ux.Util.addUnit(props.tabWidth)}`)
		
		if($ux.Util.getPx(props.height) > 0) {
			css.set('height', `${$ux.Util.addUnit(props.height)}`)
		}
		
		return css
	})
	
	const rightStyle = computed(():Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('flex', 1)
		css.set('height', `${$ux.Util.addUnit(rightHeight.value)}`)
		
		return css
	})
	
	function tabStyle(tab: UxTab):Map<string, any> {
		let css = new Map<string, any>()
		
		if(tab.disabled ?? false) {
			css.set('opacity', 0.6)
		}
		
		css.set('width', `${$ux.Util.addUnit(props.tabWidth)}`)
		css.set('height', `${$ux.Util.addUnit(props.tabHeight)}`)
		
		return css
	}
	
	function textStyle(tab: UxTab):Map<string, any> {
		let css = new Map<string, any>()
		
		if(tabIndex.value == tab.index) {
			css.set('font-size', `${$ux.Util.addUnit(selectSize.value)}`)
			css.set('color', selectedColor.value)
			css.set('font-weight', props.selectBold ? 'bold' : 'normal')
		} else {
			css.set('font-size', `${$ux.Util.addUnit(fontSize.value)}`)
			css.set('color', unselectedColor.value)
			css.set('font-weight', props.bold ? 'bold' : 'normal')
		}
		
		css.set('padding-left', `${$ux.Util.addUnit(props.padding)}`)
		css.set('padding-right', `${$ux.Util.addUnit(props.padding)}`)
		
		return css
	}
	
	const warpStyle = computed(():Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('width', `${$ux.Util.addUnit(lineWidth.value)}`)
		
		if(totalHeight.value > 0) {
			css.set('height', `${$ux.Util.addUnit(totalHeight.value)}`)
		}
		
		css.set('left', $ux.Util.addUnit(scrollWidth.value))
		
		return css
	})
	
	const lineStyle = computed(():Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('top', `${$ux.Util.addUnit(lineTop.value)}`)
		css.set('width', `${$ux.Util.addUnit(lineWidth.value)}`)
		css.set('height', `${$ux.Util.addUnit(lineHeight.value)}`)
		css.set('background-color', lineColor.value)
		css.set('transition-duration', `${props.duration}ms`)
		
		return css
	})

	const pointStyle = computed(():Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('top', `${$ux.Util.addUnit(lineTop.value)}`)
		css.set('width', `${$ux.Util.addUnit(lineWidth.value)}`)
		css.set('height', `${$ux.Util.addUnit(lineWidth.value)}`)
		css.set('border-radius', `${$ux.Util.addUnit(lineWidth.value)}`)
		css.set('background-color', lineColor.value)
		css.set('transition-duration', `${props.duration}ms`)
		
		return css
	})
	
	const modelValue = computed(():number => {
		return props.modelValue
	})
	
	function scrollTo() {
		let node = tabs.value[tabIndex.value].node
		let top = scrollTop.value
		let bottom = scrollBottom.value
		
		let i: number
		
		if (top <= (node?.top ?? 0)) {
			i = Math.max(0, tabIndex.value - 1)
		} else if (bottom <= (node?.bottom ?? 0)) {
			i = Math.min(tabs.value.length - 1, tabIndex.value + 1)
		} else {
			i = Math.max(0, tabIndex.value - 1)
		}
		
		scrollviewId.value = tabs.value[i].id ?? ''
		
		return i
	}
	
	/**
	 * 微信小程序异步 查询慢
	 * 固手动计算滚动偏移
	 */
	function getScrollTop(index: number) {
		let scrollTop = 0
		for (let i = 0; i < tabs.value.length; i++) {
			let height = tabs.value[i].node?.height ?? 0
			
			if(i < index) {
				scrollTop += height
			}
		}
		
		if(scrollTop + scrollHeight.value >= totalHeight.value) {
			scrollTop = totalHeight.value - scrollHeight.value
		}
		
		scrollTop = Math.max(scrollTop, 0)
		
		return scrollTop
	}
	
	function getLineTop(index: number, id: string) {
		const scrollTop = getScrollTop(index)
		
		let lineTop = 0 - scrollTop
		let cur = false
		
		for (let i = 0; i < tabs.value.length; i++) {
			let height = tabs.value[i].node?.height ?? 0
			
			if (id == tabs.value[i].id) {
				cur = true
			} else {
				if(!cur) {
					lineTop += height
				}
			}
		}
		
		return lineTop
	}
	
	function animTo(id : string) {
		if (tabs.value.length == 0 || props.type == 'none') return
	
		const index = scrollTo()
		
		let _lineTop = getLineTop(index, id)
		
		let nodeHeight = tabs.value[tabIndex.value].node?.height ?? 0
		let _lineHeight = $ux.Util.getPx(props.lineHeight) == 0 ? nodeHeight * 0.6 : $ux.Util.getPx(props.lineHeight)
		_lineTop += (nodeHeight - _lineHeight) / 2
		
		if(props.type == 'line') {
			if(props.anim == 'scroll') {
				lineHeight.value = _lineHeight
				lineTop.value = _lineTop
			} else if(props.anim == 'push') {
				let _targeHeight = 0
				
				for (let i = 0; i < tabs.value.length; i++) {
					let height = tabs.value[i].node?.height ?? 0
					if(lastIndex.value < tabIndex.value) {
						if(i >= lastIndex.value && i <= tabIndex.value) {
							_targeHeight += height * 0.7
						}
					} else {
						if(i <= lastIndex.value && i >= tabIndex.value) {
							_targeHeight += height * 0.7
						}
					}
				}
				
				lineHeight.value = _targeHeight
				
				if(lastIndex.value > tabIndex.value) {
					lineTop.value = _lineTop
					setTimeout(() => {
						lineHeight.value = _lineHeight
					}, props.duration - 30);
				} else {
					setTimeout(() => {
						lineHeight.value = _lineHeight
						lineTop.value = _lineTop
					}, props.duration - 30);
				}
			}
		} else if(props.type == 'point') {
			lineTop.value = _lineTop + 8
			
			// pointRef.value?.style?.setProperty('transition-duration', '100ms')
			// setTimeout(function() {
			// 	pointRef.value?.style?.setProperty('transform', 'scale(0)')
			// 	pointRef.value?.style?.setProperty('opacity', 0)
			// }, 10);
			
			// setTimeout(() => {
			// 	pointRef.value?.style?.setProperty('transition-duration', `${props.duration}ms`)
			// 	setTimeout(function() {
			// 		pointRef.value?.style?.setProperty('transform', 'scale(1)')
			// 		pointRef.value?.style?.setProperty('opacity', 1)
			// 	}, 10);
			// }, props.duration);
		}
	}
	
	async function initTabsHeight() {
		let index = 0
		
		let f = async () => {}
		f = async () => {
			if(index == tabs.value.length) {
				return
			}
			
			let tab = tabs.value[index]
			if(tab.height == null && tab.anchorId != null) {
				let rect = await uni.getElementById(tab.anchorId!)?.getBoundingClientRectAsync()!
				tab.height = rect?.height
			}
			
			index++
			await f()
		}
		
		await f()
	}
	
	async function toAnchor(e: ScrollEvent) {
		await initTabsHeight()
		
		let scrollTop = e.detail.scrollTop
		
		let f = () => {
			let top = 0
			for (let i = 0; i < tabs.value.length; i++) {
				let h = tabs.value[i].height ?? 0
				if(top <= scrollTop && scrollTop <= top + h && i != tabIndex.value) {
					tabIndex.value = i
					animTo(tabs.value[tabIndex.value].id ?? '')
					break
				}
				
				top += h
			}
		}
		
		$ux.Util.debouncek(f, 20, 'ux-sidebar')
	}
	
	async function scrollAnchor() {
		await initTabsHeight()
		
		let el = uni.getElementById(props.anchorId)
		if(el != null) {
			let scrollTop = 0
			for (let i = 0; i < tabs.value.length; i++) {
				let h = tabs.value[i].height ?? 0
				if(i < tabIndex.value) {
					scrollTop += h
				}
			}
			
			scrollTop -= $ux.Util.getPx(props.offset)
			
			// #ifdef APP
			el.setAttribute('scroll-top', `${scrollTop}`)
			// #endif
			// #ifndef APP
			el.scrollTop = scrollTop
			// #endif
			
			emit('anchor', scrollTop)
		}
	}

	function click(tab : UxTab, index : number) {
		if (tab.disabled ?? false) {
			return;
		}
		
		lastIndex.value = tabIndex.value
		tabIndex.value = index
		
		scrollAnchor()
		
		emit('update:modelValue', index);
		emit('change', index);
	
		animTo(tab.id ?? '')
	}
	
	const sidebarData = computed((): UxTab[] => {
		return props.datas
	})
	
	function initTabs() {
		let _sidebar = [] as UxTab[]
			
		sidebarData.value.forEach((e : UxTab, i : number) => {
			_sidebar.push({
				id: e.id ?? `${myId}-${i}`,
				anchorId: e.anchorId ?? '',
				name: e.name,
				badge: e.badge ?? 0,
				reddot: e.reddot ?? false,
				index: i,
				disabled: e.disabled,
			} as UxTab)
		})
		
		tabs.value = _sidebar
		
		let f2 = async (e: UniElement, i: number) => {
			const rect = await e.getBoundingClientRectAsync()!
			tabs.value[i].node = {
				left: rect?.left ?? 0,
				right: rect?.right ?? 0,
				top: rect?.top ?? 0,
				bottom: rect?.bottom ?? 0,
				width: rect?.width ?? 0,
				height: rect?.height ?? 0,
			} as UxNodeInfo
		}
		
		let f = async () => {
			sidebarRef.value?.forEach((e, i) => {
				f2(e, i)
			})
			
			const rect = await scrollRef.value!.getBoundingClientRectAsync()!
			scrollWidth.value = rect?.width ?? 0
			scrollHeight.value = rect?.height ?? 0
			scrollTop.value = rect?.top ?? 0
			scrollBottom.value = rect?.bottom ?? 0
		}
		
		setTimeout(() => {
			f()
		}, 50)
	}
	
	watch(sidebarData, () => {
		initTabs()
	}, {immediate: true})
	
	watch(modelValue, () => {
		if (modelValue.value == tabIndex.value) {
			return
		}
		
		lastIndex.value = tabIndex.value
		tabIndex.value = modelValue.value
		
		let index = tabs.value.findIndex((el : UxTab) : boolean => el.index == tabIndex.value)
		if (index != -1) {
			emit('change', index);
			animTo(tabs.value[index].id ?? '')
		}
	})
	
	onMounted(() => {
		lastIndex.value = tabIndex.value
		tabIndex.value = modelValue.value
		
		nextTick(() => {
			let index = tabs.value.findIndex((el : UxTab) : boolean => el.index == tabIndex.value)
			if (index != -1) {
				setTimeout(function() {
					animTo(tabs.value[index].id ?? '')
				}, 100);
			}
		})
		
		useResize(uni.getElementById(myId), () => {
			animTo(tabs.value[tabIndex.value].id ?? '')
		})
	})
	
	defineExpose({
		toAnchor
	})
</script>


<style lang="scss">
	.ux-sidebar {
		flex: 1;
		height: 200px;
		display: flex;
		flex-direction: row;
		position: relative;
		
		&__scroll {
			width: 80px;
			height: 100%;
			display: flex;
			flex-direction: column;
			justify-content: flex-start;
			align-items: center;
		}
		
		&__item {
			width: 100%;
			display: flex;
			flex-direction: row;
			flex-wrap: nowrap;
			justify-content: center;
			align-items: center;
		}
		
		&__text {
			transition-duration: 300ms;
			transition-property: font-size, color;
			transition-timing-function: cubic-bezier(0, 0.55, 0.45, 1);
		}
		
		&__warp {
			position: absolute;
			left: 0;
			top: 0px;
		}
		
		&__line--scroll {
			position: absolute;
			right: 0px;
			width: 20px;
			border-radius: 6px;
			transition-duration: 300ms;
			transition-property: top, height;
			transition-timing-function: cubic-bezier(0, 0.55, 0.45, 1);
		}
		
		&__line--push {
			position: absolute;
			right: 0px;
			width: 20px;
			border-radius: 6px;
			transition-duration: 300ms;
			transition-property: top, height;
		}
		
		&__point {
			position: absolute;
			right: 0px;
			width: 5px;
			height: 5px;
			border-radius: 5px;
			transition-duration: 300ms;
			transition-property: transform, opacity;
		}
	}
	
</style>