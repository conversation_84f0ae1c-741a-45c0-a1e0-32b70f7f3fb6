<template>
	<view>
		<slot></slot>
	</view>
</template>

<script lang="uts">
	/**
	* 辉光组件
	* @description 可用于骨架屏辉光效果等场景
	* @demo pages/component/shimmer.uvue
	* @tutorial https://www.uxframe.cn/component/shimmer.html
	* @property {Number} 			duration				Number | 周期 (默认 2000)
	* @property {Number} 			alpha					Number | 透明度 (默认 0.5)
	* <AUTHOR>
	* @date 2025-04-16 12:10:21
	*/
   
	import {
		UIView,
		CAGradientLayer,
		CABasicAnimation,
		CGPoint,
		CGSize,
		CGRect
	} from "UIKit"

	export default {
		name: "ux-shimmer",
		data() {
			return {
				container: null as UIView | null,
				shimmer: null as CAGradientLayer | null,
			}
		},
		props: {
			duration: {
				type: Number,
				default: 2000
			},
			alpha: {
				type: Number,
				default: 0.5
			},
		},
		expose: ['show', 'hide'],
		methods: {
			show() {
				if(this.shimmer == null) {
					this.initShimmer()
				}
				
				this.container!.layer.mask = this.shimmer
			},
			hide() {
				this.container!.layer.mask = null
			},
			initShimmer() {
				this.shimmer = CAGradientLayer()
				let baseColor = UIColor(red = CGFloat(196/255.0), green = CGFloat(196/255.0), blue = CGFloat(196/255.0), alpha = CGFloat(this.alpha)).cgColor
				this.shimmer!.colors = [baseColor, UIColor.white.cgColor, baseColor]
				this.shimmer!.locations = [0.0, 0.5, 1.0]
				this.shimmer!.startPoint = CGPointMake(0.0, 0.5)
				this.shimmer!.endPoint = CGPointMake(1.0, 0.5)
				this.shimmer!.frame = CGRectMake(0, 0, this.container!.frame.size.width, this.container!.frame.size.height)
				
				let animation = CABasicAnimation(keyPath = "locations")
				animation.duration = this.duration.toDouble() / 1000.0
				animation.fromValue = [-1.0, -0.5, 0.0]
				animation.toValue = [1.0, 1.5, 2.0]
				animation.repeatCount = Float.infinity
				this.shimmer!.add(animation, forKey = "shimmer")
			}
		},
		NVLoad() : UIView {
			this.container = new UIView()
			return this.container!
		},
		NVLayouted() {
			this.show()
		}
	}
</script>

<style>

</style>