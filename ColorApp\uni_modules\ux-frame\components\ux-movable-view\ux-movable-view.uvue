<template>
	<!-- #ifdef APP -->
	<view ref="itemRef" class="ux-movable-view transform"
		:style="{'top': _y + 'px', 'left': _x + 'px', 'transform': `scale(${_scaleValue})`, 'transition-duration': !animation || isMoving? '0ms' : '150ms'}"
		@touchstart="touchstart" @touchmove="touchmove" @touchend="touchend" @touchcancel="touchend"
		@mousedown="touchstartPc" @mousemove="touchmovePc" @mouseup="touchendPc" @mouseleave="touchendPc">
		<slot></slot>
	</view>
	<!-- #endif -->
	<!-- #ifndef APP -->
	<movable-view class="ux-movable-view" :direction="direction" :inertia="inertia" :outOfBounds="outOfBounds" :x="x"
		:y="y" :damping="damping" :friction="friction" :scale="scale" :scaleMin="scaleMin" :scaleMax="scaleMax"
		:animation="animation" :disabled="disabled" @scale="onScale" @change="change" @htouchmove="htouchmove"
		@vtouchmove="vtouchmove">
		<slot></slot>
	</movable-view>
	<!-- #endif -->
</template>

<script setup>
	/**
	 * MovableView 拖动视图
	 * @description 可移动的视图容器，在页面中可以拖拽滑动
	 * @demo pages/component/movable-area.uvue
	 * @tutorial https://www.uxframe.cn/component/movable-area.html
	 * @property {String}			direction=[all|vertical|horizontal|none]	String | movable-view的移动方向 (默认 all)
	 * @value all
	 * @value vertical
	 * @value horizontal
	 * @value none
	 * @property {Boolean}			inertia=[true|false]			Boolean | movable-view 是否带有惯性 (默认 false)
	 * @value true
	 * @value false
	 * @property {Boolean}			outOfBounds=[true|false]		Boolean | 超过可移动区域后，movable-view 是否还可以移动 (默认 false)
	 * @value true
	 * @value false
	 * @property {Number}			x								Number | 定义x轴方向的偏移，如果x的值不在可移动范围内，会自动移动到可移动范围；改变x的值会触发动画
	 * @property {Number}			y								Number | 定义y轴方向的偏移，如果y的值不在可移动范围内，会自动移动到可移动范围；改变y的值会触发动画
	 * @property {Number}			damping							Number | 阻尼系数，用于控制x或y改变时的动画和过界回弹的动画，值越大移动越快 (默认 20)
	 * @property {Number}			friction						Number | 摩擦系数，用于控制惯性滑动的动画，值越大摩擦力越大，滑动越快停止；必须大于0，否则会被设置成默认值 (默认 2)
	 * @property {Boolean}			scale=[true|false]				Boolean | 是否支持双指缩放，默认缩放手势生效区域是在movable-view内 (默认 false)
	 * @value true
	 * @value false
	 * @property {Number}			scaleMin						Number | 定义缩放倍数最小值 (默认 0.5)
	 * @property {Number}			scaleMax						Number | 定义缩放倍数最大值 (默认 10)
	 * @property {Number}			scaleValue						Number | 定义缩放倍数
	 * @property {Boolean}			animation=[true|false]			Boolean | 是否使用动画 (默认 true)
	 * @value true
	 * @value false
	 * @property {Boolean}			disabled=[true|false]			Boolean | 是否禁用 (默认 false)
	 * @value true
	 * @value false
	 * @event {Function}			change							Function | 拖动过程中触发的事件，event.detail = {x: x, y: y, source: source}，其中source表示产生移动的原因，值可为touch（拖动）、touch-out-of-bounds（超出移动范围）、out-of-bounds（超出移动范围后的回弹）、friction（惯性）和空字符串（setData）
	 * @event {Function}			scale							Function | 缩放过程中触发的事件，event.detail = {x: x, y: y, scale: scale}
	 * @event {Function}			htouchmove						Function | 初次手指触摸后移动为横向的移动，如果catch此事件，则意味着touchmove事件也被catch
	 * @event {Function}			vtouchmove						Function | 初次手指触摸后移动为纵向的移动，如果catch此事件，则意味着touchmove事件也被catch
	 * <AUTHOR>
	 * @date 2024-12-20 17:27:15
	 */

	import { $ux } from '../../index'

	type Pos = {
		x : number,
		y : number
	}

	type Item = {
		x : number,
		y : number,
		width : number,
		height : number
	}

	defineOptions({
		name: 'ux-movable-view'
	})

	const emit = defineEmits(['change', 'scale', 'htouchmove', 'vtouchmove'])

	const props = defineProps({
		direction: {
			type: String,
			default: 'all',
		},
		inertia: {
			type: Boolean,
			default: false
		},
		outOfBounds: {
			type: Boolean,
			default: false
		},
		x: {
			type: Number,
			default: 0
		},
		y: {
			type: Number,
			default: 0
		},
		damping: {
			type: Number,
			default: 20
		},
		friction: {
			type: Number,
			default: 2
		},
		scale: {
			type: Boolean,
			default: false
		},
		scaleMin: {
			type: Number,
			default: 0.5
		},
		scaleMax: {
			type: Number,
			default: 10
		},
		scaleValue: {
			type: Number,
			default: 1
		},
		animation: {
			type: Boolean,
			default: true
		},
		disabled: {
			type: Boolean,
			default: false
		},
	})

	const instance = getCurrentInstance()?.proxy
	const itemRef = ref<Element | null>(null)
	const scaleArea = inject('scaleArea', false) as boolean
	const pWidth = inject('width', ref(0)) as Ref<number>
	const pHeight = inject('height', ref(0)) as Ref<number>
	const pLeft = inject('left', ref(0)) as Ref<number>
	const pTop = inject('top', ref(0)) as Ref<number>
	const pScale = inject('scale', ref(1)) as Ref<number>

	const isMoving = ref(false)
	let start = { x: 0, y: 0 } as Pos
	let last = { x: 0, y: 0 } as Pos
	let current = { x: 0, y: 0 } as Pos
	let velocity = { x: 0, y: 0 } as Pos
	let lastMoveTime = 0

	let lastDistance = 0
	let lastScale = 0

	const _x = ref(0)
	const _y = ref(0)
	const _scaleValue = ref(1)

	const x = computed(() => {
		return props.x
	})

	const y = computed(() => {
		return props.y
	})

	const scaleValue = computed(() => {
		return props.scaleValue
	})

	function onScale(e : UniEvent) {
		emit('scale', e)
	}
	
	function app_scale(e : UniEvent) {
		emit('scale', {
			'detail': {
				'x': _x.value,
				'y': _y.value,
				'scale': _scaleValue.value
			}
		})
	}

	function change(e : UniEvent) {
		emit('change', e)
	}
	
	function app_change() {
		emit('change', {
			'type': 'change',
			'timeStamp': 0,
			'target': null,
			'currentTarget': null,
			'detail': {
				'x': _x.value,
				'y': _y.value,
				'source': ''
			}
		})
	}

	function htouchmove(e : TouchEvent) {
		emit('htouchmove', e)
	}

	function vtouchmove(e : TouchEvent) {
		emit('vtouchmove', e)
	}

	function setBounds() {
		const rect = itemRef.value?.getBoundingClientRect()
		let w = rect?.width ?? 0
		let h = rect?.height ?? 0

		// 水平边界限制
		if (props.direction == 'horizontal' || props.direction == 'all') {
			let min = 0
			let max = pWidth.value - w

			// 可超出边界
			if (props.outOfBounds && isMoving.value) {
				min = min - w / 2
				max = max + w / 2
			}

			_x.value = Math.max(_x.value, min)
			_x.value = Math.min(_x.value, max)
		}

		// 垂直边界限制
		if (props.direction == 'vertical' || props.direction == 'all') {
			let min = 0
			let max = pHeight.value - h

			// 可超出边界
			if (props.outOfBounds && isMoving.value) {
				min = min - h / 2
				max = max + h / 2
			}

			_y.value = Math.max(_y.value, min)
			_y.value = Math.min(_y.value, max)
		}
		
		app_change()
	}

	function setScale() {
		if (!props.scale) {
			_scaleValue.value = 1
			return
		}

		_scaleValue.value = Math.max(_scaleValue.value, props.scaleMin)
		_scaleValue.value = Math.min(_scaleValue.value, props.scaleMax)
	}

	function _touchstart(e : Pos) {
		if (props.disabled) {
			return
		}

		start = {
			x: e.x,
			y: e.y
		}

		current = {
			x: _x.value,
			y: _y.value
		}

		isMoving.value = true
	}

	function _touchmove(e : Pos) {
		if (props.disabled) {
			return
		}

		let offsetX = e.x - start.x
		let offsetY = e.y - start.y

		if (props.direction == 'horizontal' || props.direction == 'all') {
			_x.value = current.x + offsetX
		}

		if (props.direction == 'vertical' || props.direction == 'all') {
			_y.value = current.y + offsetY
		}

		setBounds()

		// 惯性
		let now = new Date().getTime()
		if (lastMoveTime != 0) {
			let distanceX = current.x - last.x
			let distanceY = current.y - last.y
			let timeDelta = now - lastMoveTime

			velocity = {
				x: distanceX / timeDelta,
				y: distanceY / timeDelta
			}
		}

		last = {
			x: _x.value,
			y: _y.value
		}

		lastMoveTime = now
	}

	function _touchend(e : Pos) {
		isMoving.value = false

		// 模拟惯性
		if (props.inertia) {
			if (velocity.x != 0 && velocity.y != 0) {
				_x.value -= 4 / props.friction * velocity.x
				_y.value -= 4 / props.friction * velocity.y
				setBounds()
			}
		} else {
			// 超出边界的回弹
			setBounds()
		}
	}

	function _scalestart(touches : UniTouch[]) {
		if (!props.scale || scaleArea) {
			return
		}

		let xMove = touches[1].clientX - touches[0].clientX
		let yMove = touches[1].clientY - touches[0].clientY
		lastDistance = Math.sqrt(xMove * xMove + yMove * yMove)
		lastScale = _scaleValue.value
	}

	function _scalemove(touches : UniTouch[]) {
		if (!props.scale || scaleArea) {
			return
		}

		let xMove = touches[1].clientX - touches[0].clientX
		let yMove = touches[1].clientY - touches[0].clientY
		let distance = Math.sqrt(xMove * xMove + yMove * yMove)
		_scaleValue.value = lastScale * (distance / lastDistance)
		setScale()
	}

	function touchstart(e : TouchEvent) {
		if(!scaleArea) {
			e.preventDefault()
			e.stopPropagation()
		}
		
		if(e.touches.length == 2) {
			_scalestart(e.touches)
		}
		
		if(e.touches.length == 1) {
			_touchstart({ x: e.touches[0].clientX, y: e.touches[0].clientY } as Pos)
		}
	}

	function touchstartPc(e : MouseEvent) {
		e.preventDefault()
		_touchstart({ x: e.clientX, y: e.clientY } as Pos)
	}

	function touchmove(e : TouchEvent) {
		if(!scaleArea) {
			e.preventDefault()
			e.stopPropagation()
		}
		
		if(e.touches.length == 2) {
			_scalemove(e.changedTouches)
		}
		
		if(e.touches.length == 1) {
			_touchmove({ x: e.changedTouches[0].clientX, y: e.changedTouches[0].clientY } as Pos)
		}
	}

	function touchmovePc(e : MouseEvent) {
		e.stopPropagation()
		e.preventDefault()

		_touchmove({ x: e.clientX, y: e.clientY } as Pos)
	}

	function touchend(e : TouchEvent) {
		_touchend({ x: e.changedTouches[0].clientX, y: e.changedTouches[0].clientY } as Pos)
	}

	function touchendPc(e : MouseEvent) {
		_touchend({ x: e.clientX, y: e.clientY } as Pos)
	}

	watch(x, () => {
		_x.value = x.value
	}, { immediate: true })

	watch(y, () => {
		_y.value = y.value
	}, { immediate: true })

	watch(scaleValue, () => {
		_scaleValue.value = scaleValue.value
		setScale()
	}, { immediate: true })
	
	watch(pScale, () => {
		isMoving.value = true
		_scaleValue.value = pScale.value
		setScale()
	}, { immediate: true })
</script>

<style lang="scss">
	.ux-movable-view {
		position: absolute;
		top: 0;
		left: 0;
	}

	.transform {
		transition-property: top, left, transform;
		transition-duration: 150ms;
		transform-origin: top left;
	}
</style>