
import { Table, TableColumn, Column } from '@/uni_modules/ux-orm'

export class CardColumn extends TableColumn {
	
	readonly id = Column.int('id').primaryKey()
	readonly uuid = Column.string('uuid')
	readonly bank = Column.string('bank')
	readonly account = Column.string('account')
	
	public constructor() {
	    super()
	}
}

export class CardTable extends Table {
	// 覆写表名称
	override name = 'tb_card'
	// 覆写表结构
	override columns: TableColumn = new CardColumn()
	
	// 字段映射
	id ?: number = null
	uuid ?: string = null
	bank ?: string = null
	account ?: string = null

	public constructor() {
		super()
	}
}