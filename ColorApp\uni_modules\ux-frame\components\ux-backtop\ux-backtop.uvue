<template>
	<ux-transition mode="fade" :show="show" :xstyle="[transitionStyle]">
		<view class="ux-backtop" :style="style" @click="click">
			<view v-if="$slots['default'] == null" class="ux-backtop__content">
				<view class="ux-backtop__icon">
					<ux-icon :type="icon" :size="iconSize" :color="color" :custom-family="customFamily"></ux-icon>
				</view>
				<text class="ux-backtop__text" :style="[textStyle]">{{ text }}</text>
			</view>
			<slot v-else></slot>
		</view>
	</ux-transition>
</template>

<script setup>
	
	/**
	 * backTop 返回顶部
	 * @description 支持形状、图标、大小、颜色、背景色、插槽等自定义样式
	 * @demo pages/component/backtop.uvue
	 * @tutorial https://www.uxframe.cn/component/backtop.html
	 * @property {Slot}			default  					Slot | 默认插槽
	 * @property {String}		parent  					String | 父级[scroll-view list-view] id，如果未指定系统会自动查找父级或平级下的[scroll-view list-view]
	 * @property {String}		shape = [circle|square]  	String | 形状（默认 circle ）
	 * @value circle 圆形
	 * @value square 矩形
	 * @property {String} 		icon 						String | 图标 （默认 arrowup ）
	 * @property {String} 		text 						String | 文字 （默认 TOP ）
	 * @property {Number}  		duration					Number | 返回顶部滚动时间 （默认 100）
	 * @property {Any}  		right  						Any | 距右距离 （默认 30 ）
	 * @property {Any}  		bottom  					Any | 距底距离 （默认 100 ）
	 * @property {Any}			iconSize					Any | 图标大小  (默认 18)
	 * @property {Any}			fontSize					Any | 字体大小  (默认 14)
	 * @property {Boolean}		fontBold = [true|false]		Boolean | 字体加粗 (默认 false)
	 * @property {String}		customFamily				String | 字体family
	 * @property {String}		background					String | 背景色 (默认 #FFFFFF)
	 * @property {Number}		zIndex						Number | 层级z-index (默认 10000)
	 * @property {Boolean}		fixed = [true|false]		Boolean | 固定定位 (默认 true)
	 * @value true 固定定位
	 * @value false 相对定位
	 * @event {Function}		click						Function | 点击按钮时触发
	 * @date 2023-11-02 09:59:21
	 */
	
	import { $ux } from '../../index'
	
	defineOptions({
		name: 'ux-backtop'
	})
	
	const emit = defineEmits(['click'])
	
	const props = defineProps({
		parent: {
			type: String,
			default: ''
		},
		shape: {
			type: String,
			default: 'circle'
		},
		icon: {
			type: String,
			default: 'arrowup',
		},
		text: {
			type: String,
			default: 'TOP',
		},
		right: {
			default: 30
		},
		bottom: {
			default: 100
		},
		iconSize: {
			default: 18
		},
		fontSize: {
			default: 12
		},
		fontBold: {
			type: Boolean,
			default: true
		},
		customFamily: {
			type: String,
			default: ''
		},
		color: {
			type: String,
			default: '#000000'
		},
		background: {
			type: String,
			default: 'rgba(196, 196, 196, 0.7)'
		},
		zIndex: {
			type: Number,
			default: 100
		},
		fixed: {
			type: Boolean,
			default: true
		},
	})
	
	let width = 42
	const show = ref(false)
	
	const transitionStyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('right', $ux.Util.addUnit(props.right))
		css.set('bottom', $ux.Util.addUnit(props.bottom))
		css.set('width', $ux.Util.addUnit(width))
		css.set('height', $ux.Util.addUnit(width))
		css.set('position', props.fixed?'fixed':'absolute')
		css.set('z-index', props.zIndex)
		
		return css
	})
	
	const style = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('background-color', props.background)
		css.set('border-radius', props.shape == 'square' ? $ux.Util.addUnit(6) : $ux.Util.addUnit(25))
		
		return css
	})
	
	const textStyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('color', props.color)
		css.set('font-size', $ux.Util.addUnit(props.fontSize))
		css.set('font-weight', props.fontBold?'bold':'normal')
		
		return css
	})
	
	function getEl(): UniElement | null {
		if(props.parent == '') {
			return null
		}
		
		let el = uni.getElementById(props.parent)
		
		if(el == null) {
			// TODO 查找父级或平级
			el = $ux.Util.$findEl(getCurrentInstance()?.proxy as ComponentPublicInstance, ['ux-list', 'ux-scroll'])
		}
		
		if(el == null) {
			// console.error('[ux-backtop]配置错误：需配置父级[scroll-view list-view]Id');
		}
		
		return el
	}
	
	function click(e: MouseEvent) {
		let el = getEl()
		
		if(el != null) {
			// #ifdef APP
			el!.setAttribute('scroll-top', `${0}`)
			// #endif
			// #ifndef APP
			el!.scrollTop = 0
			// #endif
		}
		
		emit('click')
		
		e.stopPropagation()
	}
	
	function setShow(_show: boolean) {
		show.value = _show
	}
	
	onMounted(() => {
		if(getEl() == null) {
			// console.error('[ux-backtop]配置错误：需配置父级[scroll-view list-view]Id');
		}
	})
	
	defineExpose({
		setShow
	})
</script>

<style lang="scss">
	.ux-backtop {
		width: 40px;
		height: 40px;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		/* #ifdef WEB */
		cursor: pointer;
		/* #endif */
		
		&__content {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
		}
		
		&__icon {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			height: 14px;
		}
		
		&__text {
			font-size: 14px;
			font-weight: bold;
		}
	}
</style>