
<p align="center">
    <img alt="logo" src="https://www.uxframe.cn/logo/logo.png" width="120" height="120" style="margin-bottom: 10px;">
</p>
<h3 align="center" style="margin: 30px 0 30px;font-weight: bold;font-size:40px;">UxFrame 生物指纹认证SDK 1.0.1</h3>

## 特别说明

如果您已经购买了[UxFrame 低代码高性能UI框架](https://ext.dcloud.net.cn/plugin?id=16148), 则无需再次购买本插件，请点击上方`进入交流群`联系我免费获取离线版插件！

## 使用方法

```ts

// #ifdef APP
import * as soter from '@/uni_modules/ux-soter'
// #endif

// #ifdef APP
// 设备是否支持生物认证
soter.isSupport()

// 设备是否录入生物特征
soter.isEnrolled()

// 设备是否录入密码锁
soter.isKeyguardSecure()

// 开始认证
soter.startSoterAuth({
	challenge: '1234567812345678', // 挑战因子
	title: '安全支付', // 标题
	content: '请验证支付环境', // 副标题
	success: (res) => {
		console.log(res);
	},
	fail: (err) => {
		console.log(err);
	}
})
// #endif

## 官方文档

[https://www.uxframe.cn](https://www.uxframe.cn)