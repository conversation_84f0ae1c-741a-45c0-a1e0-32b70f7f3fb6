import { UxLoginOptions, UxRegisterOptions, UxShareOptions, UxPayOptions, UxWeixinFail, UxWeixinSuccess } from '../../interface.uts'
import { MyFailImpl } from '../../unierror';

import Util from './Util.uts';

import IWXAPI from "com.tencent.mm.opensdk.openapi.IWXAPI";
import WXTextObject from "com.tencent.mm.opensdk.modelmsg.WXTextObject";
import WXMediaMessage from "com.tencent.mm.opensdk.modelmsg.WXMediaMessage";
import SendMessageToWX from "com.tencent.mm.opensdk.modelmsg.SendMessageToWX";
import WXAPIFactory from "com.tencent.mm.opensdk.openapi.WXAPIFactory";
import WXWebpageObject from "com.tencent.mm.opensdk.modelmsg.WXWebpageObject";
import Bitmap from "android.graphics.Bitmap";
import WXImageObject from "com.tencent.mm.opensdk.modelmsg.WXImageObject";
import WXVideoObject from "com.tencent.mm.opensdk.modelmsg.WXVideoObject";
import WXMiniProgramObject from "com.tencent.mm.opensdk.modelmsg.WXMiniProgramObject";
import WXLaunchMiniProgram from "com.tencent.mm.opensdk.modelbiz.WXLaunchMiniProgram";
import WXMusicObject from "com.tencent.mm.opensdk.modelmsg.WXMusicObject";
import Build from "com.tencent.mm.opensdk.constants.Build";
import WXOpenCustomerServiceChat from "com.tencent.mm.opensdk.modelbiz.WXOpenCustomerServiceChat";
import SendAuth from "com.tencent.mm.opensdk.modelmsg.SendAuth";
import Intent from "android.content.Intent";
import Activity from 'android.app.Activity';
import IWXAPIEventHandler from "com.tencent.mm.opensdk.openapi.IWXAPIEventHandler";
import BaseReq from "com.tencent.mm.opensdk.modelbase.BaseReq";
import BaseResp from "com.tencent.mm.opensdk.modelbase.BaseResp";
import Bundle from "android.os.Bundle";
import PayReq from "com.tencent.mm.opensdk.modelpay.PayReq";

let appid = ''

let isAuth = false
let loginOptions: UxLoginOptions | null = null
let payOptions: UxPayOptions | null = null
let shareOptions: UxShareOptions | null = null

export class Weixin {

	api : IWXAPI | null = null

	/**
	 * 注册
	 */
	register(options : UxRegisterOptions) {
		appid = options.appid

		if (appid == '') {
			options.fail?.(new MyFailImpl(9010001))
			return
		}

		this.api = WXAPIFactory.createWXAPI(UTSAndroid.getAppContext()!, appid, false);
		let b = this.api?.registerApp(appid) ?? false
		
		if(b) {
			options.success?.({
				code: '0',
				msg: 'register success'
			} as UxWeixinSuccess)
		} else {
			options.fail?.(new MyFailImpl(9010001))
		}
	}

	/**
	 * 是否注册
	 */
	isRegister(fail ?: (res : UxWeixinFail) => void): boolean {
		
		if(this.api == null) {
			fail?.(new MyFailImpl(9010000))
			return false
		} else {
			
			if(!this.isInstalled()) {
				fail?.(new MyFailImpl(9010007))
				return false
			}
			
			return true
		}
	}

	/**
	 * 登录
	 */
	login(options : UxLoginOptions) {
		if(!this.isRegister(options.fail)) {
			return
		}
		
		isAuth = true
		loginOptions = options
		
		let req = new SendAuth.Req();
		req.scope = "snsapi_userinfo";
		req.state = `ux-weixin-${new Date().getTime()}`;

		let b = this.api?.sendReq(req) ?? false
		
		if(!b) {
			options.fail?.(new MyFailImpl(9010003))
		}
	}

	/**
	 * 分享
	 */
	share(options : UxShareOptions) {
		if(!this.isRegister(options.fail)) {
			return
		}
		
		isAuth = false
		shareOptions = options
		
		switch (options.type) {
			case 0:
				this.shareWebpage(options)
				break;
			case 1:
				this.shareText(options)
				break;
			case 2:
				this.shareImage(options)
				break;
			case 3:
				this.shareMusic(options)
				break;
			case 4:
				this.shareVideo(options)
				break;
			case 5:
				this.shareMp(options)
				break;
			default:
				if (options.openCustomerServiceChat ?? false) {
					this.openCustomerService(options)
				}

				break;
		}
	}

	/**
	 * 图文
	 */
	private shareWebpage(options : UxShareOptions) {
		if(!this.isRegister(options.fail)) {
			return
		}
		
		let webpage = new WXWebpageObject()
		webpage.webpageUrl = options.href ?? ''

		let msg = new WXMediaMessage(webpage)
		msg.title = options.title ?? ''
		msg.description = options.summary ?? ''

		let req = new SendMessageToWX.Req();
		req.transaction = this.buildTransaction("webpage");
		req.scene = this.getScene(options.scene ?? '');

		Util.getBitmap(options.imageUrl, 100, 100, (bitmap : Bitmap | null, bytes : ByteArray | null) => {
			bitmap?.recycle()

			if (bytes != null) {
				msg.thumbData = bytes
			}

			req.message = msg;
			
			let b = this.api?.sendReq(req) ?? false
			if(!b) {
				options.fail?.(new MyFailImpl(9010002))
			}
		})
	}

	/**
	 * 纯文本
	 */
	private shareText(options : UxShareOptions) {
		if(!this.isRegister(options.fail)) {
			return
		}

		let textObj = new WXTextObject();
		textObj.text = options.title ?? '';

		let msg = new WXMediaMessage();
		msg.mediaObject = textObj;
		msg.description = options.title ?? '';

		let req = new SendMessageToWX.Req();
		req.transaction = this.buildTransaction("text");
		req.message = msg;
		req.scene = this.getScene(options.scene ?? '');

		let b = this.api?.sendReq(req) ?? false
		if(!b) {
			options.fail?.(new MyFailImpl(9010002))
		}
	}

	/**
	 * 纯图片
	 */
	private shareImage(options : UxShareOptions) {
		if(!this.isRegister(options.fail) || options.imageUrl == null || options.imageUrl == '') {
			return
		}
		
		let req = new SendMessageToWX.Req();
		req.transaction = this.buildTransaction("img");
		req.scene = this.getScene(options.scene ?? '');
		
		if(Util.isNetworkImage(options.imageUrl!)) {
			Util.getBitmap(options.imageUrl, 100, 100, (bitmap : Bitmap | null, bytes : ByteArray | null) => {
				bitmap?.recycle()
			
				let imgObj = new WXImageObject();
				if (bytes != null) {
					imgObj.imageData = bytes
				}
				
				let msg = new WXMediaMessage();
				msg.mediaObject = imgObj;
				
				req.message = msg;
				
				let b = this.api?.sendReq(req) ?? false
				if(!b) {
					options.fail?.(new MyFailImpl(9010002))
				}
			})
		} else {
			let imgObj = new WXImageObject();
			imgObj.imagePath = Util.getPath(options.imageUrl!)
			
			let msg = new WXMediaMessage();
			msg.mediaObject = imgObj;

			req.message = msg;
			
			let b = this.api?.sendReq(req) ?? false
			if(!b) {
				options.fail?.(new MyFailImpl(9010002))
			}
		}
	}

	/**
	 * 视频
	 */
	private shareVideo(options : UxShareOptions) {
		if(!this.isRegister(options.fail)) {
			return
		}
		
		let video = new WXVideoObject();
		video.videoUrl = options.mediaUrl ?? '';
		video.videoLowBandUrl = options.mediaUrl ?? '';

		let msg = new WXMediaMessage(video);
		msg.title = options.title ?? ''
		msg.description = options.summary ?? ''

		let req = new SendMessageToWX.Req();
		req.transaction = this.buildTransaction("video");
		req.scene = this.getScene(options.scene ?? '');

		Util.getBitmap(options.imageUrl, 100, 100, (bitmap : Bitmap | null, bytes : ByteArray | null) => {
			bitmap?.recycle()

			if (bytes != null) {
				msg.thumbData = bytes
			}

			req.message = msg;
			
			let b = this.api?.sendReq(req) ?? false
			if(!b) {
				options.fail?.(new MyFailImpl(9010002))
			}
		})
	}

	/**
	 * 音乐
	 */
	private shareMusic(options : UxShareOptions) {
		if(!this.isRegister(options.fail)) {
			return
		}
		
		let music = new WXMusicObject();
		music.musicUrl = options.mediaUrl ?? '';
		music.musicLowBandUrl = options.mediaUrl ?? '';

		let msg = new WXMediaMessage(music);
		msg.title = options.title ?? ''
		msg.description = options.summary ?? ''

		let req = new SendMessageToWX.Req();
		req.transaction = this.buildTransaction("music");
		req.scene = this.getScene(options.scene ?? '');

		Util.getBitmap(options.imageUrl, 100, 100, (bitmap : Bitmap | null, bytes : ByteArray | null) => {
			bitmap?.recycle()

			if (bytes != null) {
				msg.thumbData = bytes
			}

			req.message = msg;
			
			let b = this.api?.sendReq(req) ?? false
			if(!b) {
				options.fail?.(new MyFailImpl(9010002))
			}
		})
	}

	/**
	 * 小程序
	 */
	private shareMp(options : UxShareOptions) {
		if(!this.isRegister(options.fail)) {
			return
		}
		
		if (options.miniProgram == null) {
			return
		}

		let miniProgramObj = new WXMiniProgramObject();
		miniProgramObj.webpageUrl = options.miniProgram!.webUrl ?? ''

		// 正式版:0，测试版:1，体验版:2
		if (options.miniProgram!.type == 0) {
			miniProgramObj.miniprogramType = WXMiniProgramObject.MINIPTOGRAM_TYPE_RELEASE;
		} else if (options.miniProgram!.type == 1) {
			miniProgramObj.miniprogramType = WXMiniProgramObject.MINIPROGRAM_TYPE_TEST;
		} else if (options.miniProgram!.type == 2) {
			miniProgramObj.miniprogramType = WXMiniProgramObject.MINIPROGRAM_TYPE_PREVIEW;
		}

		miniProgramObj.userName = options.miniProgram!.id
		miniProgramObj.path = options.miniProgram!.path

		let msg = new WXMediaMessage(miniProgramObj);
		msg.title = options.title ?? ''
		msg.description = options.summary ?? ''

		let req = new SendMessageToWX.Req();
		req.transaction = this.buildTransaction("miniProgram");
		req.scene = SendMessageToWX.Req.WXSceneSession;

		Util.getBitmap(options.imageUrl, 250, 200, (bitmap : Bitmap | null, bytes : ByteArray | null) => {
			bitmap?.recycle()
			
			if (bytes != null) {
				msg.thumbData = bytes
			}

			req.message = msg;
			
			let b = this.api?.sendReq(req) ?? false
			if(!b) {
				options.fail?.(new MyFailImpl(9010002))
			}
		})
	}

	/**
	 * 打开微信小程序
	 */
	openMiniProgram(options : UxShareOptions) {
		if(!this.isRegister(options.fail)) {
			return
		}
		
		let req = new WXLaunchMiniProgram.Req();
		req.userName = options.miniProgram!.id
		req.path = options.miniProgram!.path
		
		// 正式版:0，测试版:1，体验版:2
		if (options.miniProgram!.type == 0) {
			req.miniprogramType = WXLaunchMiniProgram.Req.MINIPTOGRAM_TYPE_RELEASE;
		} else if (options.miniProgram!.type == 1) {
			req.miniprogramType = WXLaunchMiniProgram.Req.MINIPROGRAM_TYPE_TEST;
		} else if (options.miniProgram!.type == 2) {
			req.miniprogramType = WXLaunchMiniProgram.Req.MINIPROGRAM_TYPE_PREVIEW;
		}
		
		this.api!.sendReq(req)
	}
	
	/**
	 * 请求支付
	 */
	requestPayment(options : UxPayOptions) {
		if(!this.isRegister(options.fail)) {
			return
		}
		
		payOptions = options
		
		let req = new PayReq();
		
		req.appId = appid
		req.partnerId = options.mchid
		req.prepayId = options.prepayId
		req.nonceStr = options.nonceStr
		req.packageValue = 'Sign=WXPay'
		req.sign = options.sign
		req.signType = options.signType
		req.timeStamp = options.timeStamp
		
		let b = this.api?.sendReq(req) ?? false
		if(!b) {
			options.fail?.(new MyFailImpl(9010009))
		}
	}

	/**
	 * 微信客服
	 */
	private openCustomerService(options : UxShareOptions) {
		if(!this.isRegister(options.fail)) {
			return
		}
		
		if (this.api!.getWXAppSupportAPI() >= Build.SUPPORT_OPEN_CUSTOMER_SERVICE_CHAT) {

			let req = new WXOpenCustomerServiceChat.Req();
			req.corpId = options.corpid ?? ''
			req.url = options.customerUrl ?? ''

			let b = this.api?.sendReq(req) ?? false
			if(!b) {
				options.fail?.(new MyFailImpl(9010002))
			}
		}
	}

	/**
	 * 微信app是否安装
	 */
	isInstalled() : boolean {
		if(this.api == null) {
			return false
		}
		
		return this.api!.isWXAppInstalled()
	}
	
	/**
	 * 打开微信app
	 */
	openApp() {
		if(this.api == null) {
			return
		}
		
		this.api?.openWXApp()
	}

	/**
	 * 场景
	 */
	private getScene(scene : string) : Int {
		if (scene == 'WXSceneSession') {
			return SendMessageToWX.Req.WXSceneSession
		} else if (scene == 'WXSceneTimeline') {
			return SendMessageToWX.Req.WXSceneTimeline
		} else if (scene == 'WXSceneFavorite') {
			return SendMessageToWX.Req.WXSceneFavorite
		} else {
			return SendMessageToWX.Req.WXSceneSession
		}
	}

	/**
	 * 构建状态
	 */
	private buildTransaction(type : string) : string {
		return `${type}${new Date().getTime()}`
	}
}

/**
 * 接收微信回调Activity
 */
export class WXEntryActivity extends Activity implements IWXAPIEventHandler {

	api : IWXAPI | null = null
	
	constructor() {
		super();
	}
	
	override onCreate(savedInstanceState ?: Bundle) : void {
		super.onCreate(savedInstanceState)
		
		this.api = WXAPIFactory.createWXAPI(this, appid, false)
		this.api?.handleIntent(this.getIntent(), this);
	}
	
	override onNewIntent(intent : Intent) : void {
		super.onNewIntent(intent);
	
		this.setIntent(intent);
	
		this.api?.handleIntent(intent, this);
	}
	
	override onReq(req : BaseReq) {
		this.finish();
	}
	
	override onResp(resp : BaseResp) {
		console.log('[ux-weixin] 微信回调：', resp.errCode);
		switch (resp.errCode) {
			case BaseResp.ErrCode.ERR_OK:
				if(isAuth) {
					let authResp = resp as SendAuth.Resp
					loginOptions?.success?.({
						code: authResp.code,
						msg: 'auth success'
					} as UxWeixinSuccess)
				} else {
					shareOptions?.success?.({
						code: '0',
						msg: 'share success'
					} as UxWeixinSuccess)
				}
				
				break;
			case BaseResp.ErrCode.ERR_AUTH_DENIED:
				// 拒绝
				if(isAuth) {
					loginOptions?.fail?.(new MyFailImpl(9010004))
				} else {
					shareOptions?.fail?.(new MyFailImpl(9010004))
				}
				break;
			case BaseResp.ErrCode.ERR_USER_CANCEL:
				// 取消
				if(isAuth) {
					loginOptions?.fail?.(new MyFailImpl(9010005))
				} else {
					shareOptions?.fail?.(new MyFailImpl(9010005))
				}
				break;
			case BaseResp.ErrCode.ERR_UNSUPPORT:
				// 不支持
				if(isAuth) {
					loginOptions?.fail?.(new MyFailImpl(9010006))
				} else {
					shareOptions?.fail?.(new MyFailImpl(9010006))
				}
			default:
				if(isAuth) {
					loginOptions?.fail?.(new MyFailImpl(9010008))
				} else {
					shareOptions?.fail?.(new MyFailImpl(9010008))
				}
				break;
		}
		
		this.finish();
	}
}

/**
 * 接收微信支付回调Activity
 */
export class WXPayEntryActivity extends Activity implements IWXAPIEventHandler {

	api : IWXAPI | null = null
	
	constructor() {
		super();
	}
	
	override onCreate(savedInstanceState ?: Bundle) : void {
		super.onCreate(savedInstanceState)
		
		this.api = WXAPIFactory.createWXAPI(this, appid, false)
		this.api?.handleIntent(this.getIntent(), this);
	}
	
	override onNewIntent(intent : Intent) : void {
		super.onNewIntent(intent);
	
		this.setIntent(intent);
	
		this.api?.handleIntent(intent, this);
	}
	
	override onReq(req : BaseReq) {
		this.finish();
	}
	
	override onResp(resp : BaseResp) {
		console.log('[ux-weixin] 微信回调：', resp.errCode);
		switch (resp.errCode) {
			case BaseResp.ErrCode.ERR_OK:
				payOptions?.success?.({
					code: '0',
					msg: 'pay success'
				} as UxWeixinSuccess)
				break;
			case BaseResp.ErrCode.ERR_AUTH_DENIED:
				// 拒绝
				payOptions?.fail?.(new MyFailImpl(9010004))
				break;
			case BaseResp.ErrCode.ERR_USER_CANCEL:
				// 取消
				payOptions?.fail?.(new MyFailImpl(9010005))
				break;
			case BaseResp.ErrCode.ERR_UNSUPPORT:
				// 不支持
				payOptions?.fail?.(new MyFailImpl(9010006))
			default:
				payOptions?.fail?.(new MyFailImpl(9010008))
				break;
		}
		
		this.finish();
	}
}