# mp-html 组件

mp-html 是一个用于解析HTML并通过uni-app x原生组件渲染的组件。它可以将HTML字符串转换为原生的view、text、image等组件，实现高性能的富文本显示。

## 特性

- ✅ 支持常用HTML标签解析（p, div, span, h1-h6, strong, em, u, del, ul, ol, li, img, a, br, hr等）
- ✅ 支持CSS样式解析（color, font-size, font-weight, text-align, background-color, margin, padding等）
- ✅ 支持图片显示和点击事件
- ✅ 支持链接点击事件
- ✅ 支持列表渲染（有序列表和无序列表）
- ✅ 跨平台兼容（App、小程序、H5）
- ✅ 高性能原生渲染
- ✅ 递归解析嵌套结构
- ✅ 自动处理HTML实体编码

## 安装

将 `mp-html` 文件夹复制到项目的 `uni_modules` 目录下即可。

## 基础用法

```vue
<template>
  <mp-html :content="htmlContent" />
</template>

<script setup lang="uts">
const htmlContent = '<p>Hello <strong>World</strong>!</p>'
</script>
```

## 高级用法

```vue
<template>
  <mp-html 
    :content="htmlContent"
    :selectable="true"
    :show-img-menu="true"
    @link-tap="onLinkTap"
    @img-tap="onImgTap"
    @error="onError"
  />
</template>

<script setup lang="uts">
import { LinkTapEventType, ImgTapEventType, ErrorEventType } from '@/uni_modules/mp-html/libs/types.uts'

const htmlContent = `
  <div style="padding: 15px;">
    <h2 style="color: #333;">标题</h2>
    <p>这是一个包含<a href="https://example.com">链接</a>的段落。</p>
    <img src="https://example.com/image.jpg" style="width: 200px;" />
    <ul>
      <li>列表项1</li>
      <li>列表项2</li>
    </ul>
  </div>
`

const onLinkTap = (event: LinkTapEventType) => {
  console.log('链接点击:', event.href)
  // 处理链接点击逻辑
}

const onImgTap = (event: ImgTapEventType) => {
  console.log('图片点击:', event.src)
  // 处理图片点击逻辑，如预览图片
}

const onError = (event: ErrorEventType) => {
  console.error('解析错误:', event.error)
}
</script>
```

## API

### Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| content | string | '' | HTML内容字符串 |
| selectable | boolean | false | 是否可选择文本 |
| show-img-menu | boolean | true | 是否显示图片长按菜单 |

### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| link-tap | 链接点击事件 | {href: string} |
| img-tap | 图片点击事件 | {src: string} |
| error | 解析错误事件 | {error: string} |

## 支持的HTML标签

### 文本标签
- `p` - 段落
- `span` - 内联文本
- `div` - 块级容器
- `h1` ~ `h6` - 标题
- `strong`, `b` - 粗体
- `em`, `i` - 斜体
- `u` - 下划线
- `del`, `s` - 删除线

### 列表标签
- `ul` - 无序列表
- `ol` - 有序列表
- `li` - 列表项

### 媒体标签
- `img` - 图片
- `a` - 链接

### 其他标签
- `br` - 换行
- `hr` - 水平线

## 支持的CSS样式

### 文字样式
- `color` - 文字颜色
- `font-size` - 字体大小
- `font-weight` - 字体粗细
- `text-align` - 文本对齐

### 布局样式
- `margin` - 外边距
- `padding` - 内边距
- `width` - 宽度
- `height` - 高度

### 背景样式
- `background-color` - 背景颜色

## 样式说明

1. **单位支持**：支持 `px`、`rpx`、`%`、`em` 等单位
2. **颜色支持**：支持十六进制颜色、RGB颜色、颜色关键字
3. **默认样式**：组件为各种HTML标签提供了合理的默认样式
4. **样式继承**：子元素会继承父元素的部分样式
5. **样式优先级**：内联样式 > 默认样式

## 注意事项

1. **性能考虑**：对于大量HTML内容，建议分页或懒加载
2. **图片处理**：图片默认使用 `widthFix` 模式，会自动适应容器宽度
3. **链接处理**：需要在 `link-tap` 事件中自行处理链接跳转逻辑
4. **样式限制**：受uni-app x样式系统限制，部分CSS属性可能不支持
5. **标签支持**：目前支持常用HTML标签，复杂标签可能需要扩展

## 扩展开发

如需支持更多HTML标签或CSS样式，可以修改以下文件：

- `libs/parser.uts` - HTML解析逻辑
- `libs/style-utils.uts` - 样式处理逻辑
- `components/mp-html/mp-html-node.uvue` - 节点渲染逻辑

## 示例

项目中包含了完整的示例页面 `pages/mp-html-demo.uvue`，展示了各种HTML标签和样式的渲染效果。

## 兼容性

- ✅ App (Android/iOS)
- ✅ 小程序 (微信/支付宝/百度/字节跳动等)
- ✅ H5

## 更新日志

### 1.0.0 (2024-01-01)
- 初始版本发布
- 支持基础HTML标签解析
- 支持CSS样式解析
- 支持图片、链接等媒体元素
- 跨平台兼容性支持