import App from './App.uvue'
import { createSSRApp } from 'vue'
import { getForegroundColor, getBackgroundColor, getTabbarTextColor, getTabbarBackgroundColor, usePrimaryColor } from '@/common/theme.uts'
import { uxframe, UxFrameInstallOptions } from '@/uni_modules/ux-frame'

import zhCN from './locales/zh_CN'
import enUS from './locales/en_US'

export function createApp() {
	const app = createSSRApp(App)

	// 安装UxFrame
	app.use(uxframe({
		conf: {
			// false 可关闭插件内部日志打印
			debugLog: true,
			// 全局字体大小
			fontSize: 16,
			// 不跟随系统主题
			followSys: false,
			// 主题色
			primaryColor: usePrimaryColor(),
			// 背景色
			backgroundColor: getBackgroundColor(false),
			backgroundColorDark: getBackgroundColor(true),
			// 前景色
			foregroundColor: getForegroundColor(false),
			foregroundColorDark: getForegroundColor(true),
			// tabbar背景色
			tabbarBackgroundColor: getTabbarBackgroundColor(false),
			tabbarBackgroundColorDark: getTabbarBackgroundColor(true),
			// tabbar文本色
			tabbarTextColor: getTabbarTextColor(false),
			tabbarTextColorDark: getTabbarTextColor(true),
			// 其他配置...
		},
		// 可选 i18n安装
		i18n: {
			locale: 'zh-CN',
			messages: {
				'zh-CN': zhCN,
				'en-US': enUS,
			}
		}
	} as UxFrameInstallOptions))

	return {
		app,
	}
}