
<p align="center">
    <img alt="logo" src="https://www.uxframe.cn/logo/logo.png" width="120" height="120" style="margin-bottom: 10px;">
</p>
<h3 align="center" style="margin: 30px 0 30px;font-weight: bold;font-size:40px;">UxFrame 抖音SDK 1.0.1</h3>

## 特别说明

如果您已经购买了[UxFrame 低代码高性能UI框架](https://ext.dcloud.net.cn/plugin?id=16148), 则无需再次购买本插件，请点击上方`进入交流群`联系我免费获取离线版插件！

本插件暂时只支持Android，后续版本会支持iOS

## Android注意事项

需在本插件`utssdk` -> `app-android` -> `AndroidManifest.xml` 文件中将所有 `${applicationId}`字段替换成您的`真实包名`

## 使用方法

```ts

// #ifdef APP
import * as douyin from '@/uni_modules/ux-douyin'
// #endif

// 注册
// #ifdef APP
douyin.register({
	appid: '',
	success: (res) => {
		uni.showToast({
			title: `${res.msg}`,
			icon: 'none'
		})
	},
	fail: (err) => {
		console.log(err);
	}
})
// #endif

// 登录
// #ifdef APP
douyin.login({
	state: 'test',
	success: (res) => {
		uni.showToast({
			title: `login ${res.code}`,
			icon: 'none'
		})
	},
	fail: (err) => {
		console.log(err);
	}
})
// #endif

// 分享图片
// #ifdef APP
douyin.share({
	type: 0,
	imageUrl: '/static/logo.png' // 只支持本地图片
})
// #endif

// 分享链接
douyin.share({
	type: 1,
	title: 'UxFrame低代码高性能UI框架',
	summary: 'UxFrame是基于UNI-APP-X开发的低代码高性能原生UI框架',
	href: 'https://www.uxframe.cn',
	// imageUrl: '/static/logo.png',
	success: (res) => {
		console.log(res);
	},
	fail: (err) => {
		console.log(err);
	}
})

// 分享小程序
douyin.share({
	type: 2,
	miniProgram: {
		id: 'gh_b5453753cee',
		title: 'UxFrame低代码高性能UI框架',
		path: '/pages/index',
		query: 'a=1',
		imageId: '1'
	},
	success: (res) => {
		console.log(res);
	},
	fail: (err) => {
		console.log(err);
	}
})

```

## 官方文档

[https://www.uxframe.cn](https://www.uxframe.cn)