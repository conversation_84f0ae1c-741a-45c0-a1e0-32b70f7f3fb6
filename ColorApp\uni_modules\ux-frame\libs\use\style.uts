// #ifndef UNI-APP-X
import { ref, computed } from 'vue'
// #endif
import { $ux } from '../../index'

/**
 * 水平外边距
 * @param margin 边距 
 * @param type 边距类型 0 - 正常 1 - 较小边距 2 - 较大边距 
 */
export const useHmargin = (margin : number, type: number) : number => {
	if (margin >= 0) {
		return margin
	} else {
		if(type == 1) {
			return $ux.Conf.hmargin.small.value
		} else if(type == 2) {
			return $ux.Conf.hmargin.large.value
		}  else {
			return $ux.Conf.hmargin.normal.value
		}
	}
}

/**
 * 垂直外边距
 * @param margin 边距 
 * @param type 边距类型 0 - 正常 1 - 较小边距 2 - 较大边距 
 */
export const useVmargin = (margin : number, type: number) : number => {
	if (margin >= 0) {
		return margin
	} else {
		if(type == 1) {
			return $ux.Conf.vmargin.small.value
		} else if(type == 2) {
			return $ux.Conf.vmargin.large.value
		}  else {
			return $ux.Conf.vmargin.normal.value
		}
	}
}

/**
 * 垂直内边距
 * @param padding 边距 
 * @param type 边距类型 0 - 正常 1 - 较小边距 2 - 较大边距 
 */
export const useHpadding = (padding : number, type: number) : number => {
	if (padding >= 0) {
		return padding
	} else {
		if(type == 1) {
			return $ux.Conf.hpadding .small.value
		} else if(type == 2) {
			return $ux.Conf.hpadding.large.value
		}  else {
			return $ux.Conf.hpadding.normal.value
		}
	}
}

/**
 *  水平内边距
 * @param padding 边距 
 * @param type 边距类型 0 - 正常 1 - 较小边距 2 - 较大边距 
 */
export const useVpadding = (padding : number, type: number) : number => {
	if (padding >= 0) {
		return padding
	} else {
		if(type == 1) {
			return $ux.Conf.vpadding.small.value
		} else if(type == 2) {
			return $ux.Conf.vpadding.large.value
		}  else {
			return $ux.Conf.vpadding.normal.value
		}
	}
}

/**
 * 圆角
 * @param radius 圆角 
 * @param type 圆角类型 0 - 正常 1 - 较小圆角 2 - 较大圆角 
 */
export const useRadius = (radius : number, type: number) : number => {
	if (radius >= 0) {
		return radius
	} else {
		if(type == 1) {
			return $ux.Conf.radius.small.value
		} else if(type == 2) {
			return $ux.Conf.radius.large.value
		}  else {
			return $ux.Conf.radius.normal.value
		}
	}
}

/**
 * 字体大小
 * @param size 尺寸 
 * @param type 尺寸类型 0 - 正常 1 - 较小尺寸 2 - 较大尺寸 3 - 超大尺寸  4 - 迷你尺寸 
 */
export const useFontSize = (size : number, type: number) : number => {
	if (size > 0) {
		return size
	} else {
		if(type == 1) {
			return $ux.Conf.fontSize.small.value
		} else if(type == 2) {
			return $ux.Conf.fontSize.large.value
		} else if(type == 3) {
			return $ux.Conf.fontSize.huge.value
		} else if(type == 4) {
			return $ux.Conf.fontSize.mini.value
		}  else {
			return $ux.Conf.fontSize.normal.value
		}
	}
}

/**
 * 文本色
 * @param color 浅色
 * @param darkColor 深色
 */
export const useFontColor = (color : string, darkColor : string) : string => {
	if (color != '') {
		return useColor(color, darkColor)
	} else {
		return $ux.Conf.darkMode.value  && darkColor != 'none' ? $ux.Conf.fontColor.dark.value : $ux.Conf.fontColor.color.value
	}
}

/**
 * 背景色
 * @param color 浅色
 * @param darkColor 深色
 */
export const useBackgroundColor = (color : string, darkColor : string) : string => {
	if (color != '') {
		return useColor(color, darkColor)
	} else {
		return $ux.Conf.darkMode.value  && darkColor != 'none' ? $ux.Conf.backgroundColor.dark.value : $ux.Conf.backgroundColor.color.value
	}
}

/**
 * 前景色
 * @param color 浅色
 * @param darkColor 深色
 */
export const useForegroundColor = (color : string, darkColor : string) : string => {
	if (color != '') {
		return useColor(color, darkColor)
	} else {
		return $ux.Conf.darkMode.value  && darkColor != 'none' ? $ux.Conf.foregroundColor.dark.value : $ux.Conf.foregroundColor.color.value
	}
}

export type UxThemeType = 'info' | 'primary' | 'success' | 'warning' | 'error'

/**
 * 主题色
 * @param theme 主题
 */
export const useThemeColor = (theme : UxThemeType) : string => {
	
	const infoColor = computed(():string => {
		return $ux.Conf.darkMode.value ? $ux.Conf.infoColor.dark.value : $ux.Conf.infoColor.color.value
	})
	
	if (theme == 'info') {
		return infoColor.value
	} else if (theme == 'primary') {
		return $ux.Conf.primaryColor.color.value
	} else if (theme == 'success') {
		return $ux.Conf.successColor.color.value
	} else if (theme == 'warning') {
		return $ux.Conf.warningColor.color.value
	} else if (theme == 'error') {
		return $ux.Conf.errorColor.color.value
	}
	
	return infoColor.value
}

/**
 * 标题色
 * @param color 浅色
 * @param darkColor 深色
 */
export const useTitleColor = (color : string, darkColor : string) : string => {
	if (color != '') {
		return useColor(color, darkColor)
	} else {
		return $ux.Conf.darkMode.value && darkColor != 'none' ? $ux.Conf.titleColor.dark.value : $ux.Conf.titleColor.color.value
	}
}

/**
 * 二级标题色
 * @param color 浅色
 * @param darkColor 深色
 */
export const useSubTitleColor = (color : string, darkColor : string) : string => {
	if (color != '') {
		return useColor(color, darkColor)
	} else {
		return $ux.Conf.darkMode.value && darkColor != 'none' ? $ux.Conf.subtitleColor.dark.value : $ux.Conf.subtitleColor.color.value
	}
}

/**
 * 次要色
 * @param color 浅色
 * @param darkColor 深色
 */
export const useSecondaryColor = (color : string, darkColor : string) : string => {
	if (color != '') {
		return useColor(color, darkColor)
	} else {
		return $ux.Conf.darkMode.value && darkColor != 'none' ? $ux.Conf.secondaryColor.dark.value : $ux.Conf.secondaryColor.color.value
	}
}

/**
 * 占位色
 * @param color 浅色
 * @param darkColor 深色
 */
export const usePlaceholderColor = (color : string, darkColor : string) : string => {
	if (color != '') {
		return useColor(color, darkColor)
	} else {
		return $ux.Conf.darkMode.value && darkColor != 'none' ? $ux.Conf.placeholderColor.light.value : $ux.Conf.placeholderColor.color.value
	}
}

/**
 * 取消色
 * @param color 浅色
 * @param darkColor 深色
 */
export const useCancelColor = (color : string, darkColor : string) : string => {
	if (color != '') {
		return useColor(color, darkColor)
	} else {
		return $ux.Conf.darkMode.value && darkColor != 'none' ? $ux.Conf.cancelColor.light.value : $ux.Conf.cancelColor.color.value
	}
}

/**
 * 边框色
 * @param color 浅色
 * @param darkColor 深色
 */
export const useBorderColor = (color : string, darkColor : string) : string => {
	if (color != '') {
		return useColor(color, darkColor)
	} else {
		return $ux.Conf.darkMode.value && darkColor != 'none' ? $ux.Conf.borderColor.dark.value : $ux.Conf.borderColor.color.value
	}
}

/**
 * 背景文本颜色 适配深色模式
 * @param color 浅色
 * @param darkColor 深色
 */
export const useColor = (color : string, darkColor : string) : string => {
	if (color != '') {
		let dark = color

		if (darkColor == '' || darkColor == 'none') {
			dark = color
		} else if (darkColor == 'auto') {
			dark = useInverseColor(color)
		} else if ($ux.Color.isValid(darkColor)) {
			dark = darkColor
		}

		return $ux.Conf.darkMode.value ? dark : color
	} else {
		return ''
	}
}

/**
 * 反色
 * 深色 -> 浅色
 * 浅色 -> 深色
 * @param color 颜色值
 */
export const useInverseColor = (color : string) : string => {
	if ($ux.Color.isDarkColor(color)) {
		return $ux.Color.getLightColor(color, 80)
	} else {
		return $ux.Color.getDarkColor(color, 80)
	}
}

/**
 * 浅色
 * @param color 颜色值
 */
export const useLightColor = (color : string) : string => {
	return $ux.Color.getLightColor(color, 20)
}

/**
 * 禁止色
 * @param color 颜色值
 */
export const useDisabledColor = (color : string) : string => {
	return $ux.Color.getRgba(color, 0.6)
}