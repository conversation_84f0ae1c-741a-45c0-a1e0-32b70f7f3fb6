<template>
	<!-- #ifdef APP -->
	<view :id="myId" class="ux-movable-area" @touchstart="touchstart" @touchmove="touchmove" @touchend="touchend" @touchcancel="touchend">
		<slot></slot>
	</view>
	<!-- #endif -->
	<!-- #ifndef APP -->
	<movable-area :scale-area="scaleArea" class="ux-movable-area">
		<slot></slot>
	</movable-area>
	<!-- #endif -->
</template>

<script setup>
	/**
	 * MovableArea 拖动区域
	 * @description 拖动或双指缩放区域
	 * @demo pages/component/movable-area.uvue
	 * @tutorial https://www.uxframe.cn/component/movable-area.html
	 * @property {Boolean}			scaleArea=[true|false]	Boolean | 当里面的 movable-view 设置为支持双指缩放时，设置此值可将缩放手势生效区域修改为整个 movable-area (默认 false)
	 * @value true
	 * @value false
	 * <AUTHOR>
	 * @date 2024-12-20 17:27:15
	 */
	
	import { $ux } from '../../index'
	
	defineOptions({
		name: 'ux-movable-area'
	})
	
	const props = defineProps({
		scaleArea: {
			type: Boolean,
			default: false
		},
	})
	
	const myId = `ux-movable-area-${$ux.Random.uuid()}`
	const instance = getCurrentInstance()?.proxy
	const width = ref(0)
	const height = ref(0)
	const top = ref(0)
	const left = ref(0)
	const scale = ref(1)
	
	let lastDistance = 0
	let lastScale = 0
	
	provide('scaleArea', props.scaleArea)
	provide('width', width)
	provide('height', height)
	provide('top', top)
	provide('left', left)
	provide('scale', scale)
	
	function touchstart(e : TouchEvent) {
		if(!props.scaleArea || e.touches.length < 2) {
			return
		}
		
		e.preventDefault()
		
		let xMove = e.touches[1].clientX - e.touches[0].clientX
		let yMove = e.touches[1].clientY - e.touches[0].clientY
		lastDistance = Math.sqrt(xMove * xMove + yMove * yMove)
		lastScale = scale.value
	}
	
	function touchmove(e : TouchEvent) {
		if(!props.scaleArea || e.changedTouches.length < 2) {
			return
		}
		
		e.preventDefault()
		e.stopPropagation()
	
		let xMove = e.changedTouches[1].clientX - e.changedTouches[0].clientX
		let yMove = e.changedTouches[1].clientY - e.changedTouches[0].clientY
		let distance = Math.sqrt(xMove * xMove + yMove * yMove)
		scale.value = lastScale * (distance / lastDistance)
	}
	
	function touchend(e : TouchEvent) {
		if(!props.scaleArea) {
			return
		}
	}
	
	onMounted(() => {
		// #ifdef APP
		setTimeout(function() {
			uni.createSelectorQuery()
				.in(instance)
				.select(`#${myId}`)
				.boundingClientRect().exec((ret) => {
					let node = ret[0] as NodeInfo;
					width.value = node.width ?? 0
					height.value = node.height ?? 0
					left.value = node.left ?? 0
					top.value = node.top ?? 0
				})
		}, 100);
		// #endif
	})
</script>

<style lang="scss">
	.ux-movable-area {
		position: relative;
		width: 100%;
		height: 100%;
		overflow: hidden;
	}
</style>