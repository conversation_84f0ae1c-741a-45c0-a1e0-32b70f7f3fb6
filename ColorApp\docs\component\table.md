<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/table?title=Table"></Mobile>

# Table
> 组件类型：UxTableComponentPublicInstance

支持多种主题自定义、字段插槽、格式化、高亮、边框、斑马纹、序号、全选、单选、编辑、合计、加载状态等多种功能

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| columnkey | Slot |  | 列字段插槽 |
| edit | Slot |  | 编辑插槽 |
| datas | Array |  | 数据源 |
| columns | Array |  | 列数据 |
| cellHeight | Any | 40 | 单元格高度 |
| height | Any |  | 表格高度 |
| maxHeight | Any |  | 表格最大高度 |
| headerColor | String |  | 头部内容颜色 |
| headerSize | Any | `$ux.Conf.fontSize.small` | 头部字体大小 |
| headerBold | Boolean | true | 头部字体加粗 |
| headerBackground | String | #f5f5f5 | 头部背景色 |
| color | String | #333333 | 内容颜色 |
| fontSize | Any | `$ux.Conf.fontSize.small` | 内容字体大小 |
| background | String | #fafafa | 背景色 |
| highlight | String | #ecf5ff | 高亮色 |
| border | Boolean | true | 显示边框 |
| ripple | Boolean | true | 显示斑马纹 |
| rippleColor | String | #f5f5f5 | 斑马纹背景色 |
| nums | Boolean | false | 显示序号 |
| numSort | Boolean | false | 开启序号排序 |
| selection | Boolean | false | 开启选择 |
| edit | Boolean | false | 开启编辑 |
| total | Boolean | false | 合计 |
| loading | Boolean | false | 加载中... |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| click | 点击时触发 |  |
| input | 输入时触发 |  |
| scrolltolower | 滚动触底时触发 |  |
  
  