<template>
	<view v-if="value != 0" class="ux-badge" :style="[style, xstyle]">
		<text v-if="!dot" class="ux-badge__value" :style="[textStyle]">{{ _value }}</text>
	</view>
</template>

<script setup lang="ts">
	
	/**
	 * Badge 角标
	 * @description 支持多种形状、支持圆点、反转显示，可配置超出显示样式
	 * @demo pages/component/badge.uvue
	 * @tutorial https://www.uxframe.cn/component/badge.html
	 * @property {String}			theme=[primary|success|warning|error]	String | 主题 (默认 primary)
	 * @value primary   	主要
	 * @value success  		成功
	 * @value warning   	警告
	 * @value error  		错误
	 * @property {Number}			value									Number | 角标数量
	 * @property {Number}			max										Number | 最大值 (默认 99)
	 * @property {String}			overflow								String | 超出显示样式 (默认 normal)
	 * @value normal 正常
	 * @value format 格式化
	 * @property {String}			shape = [normal|lt|rt|rb|lb|ltrb|rtlb]	String | 形状 (默认 normal)
	 * @value normal 圆角
	 * @value lt 左上角
	 * @value rt 右上角
	 * @value rb 右下角
	 * @value lb 左下角
	 * @value ltrb 左上右下角
	 * @value rtlb 右上左下角
	 * @property {Boolean}			dot = [true|false]						Boolean | 显示圆点 (默认 false)
	 * @value true
	 * @value false
	 * @property {Boolean}			inverted = [true|false]					Boolean | 反转色 (默认 false)
	 * @value true
	 * @value false
	 * @property {Boolean}			fixed = [true|false]					Boolean | 固定定位 (默认 true)
	 * @value true
	 * @value false
	 * @property {Any} 				size									Any | 字体大小（默认 $ux.Conf.fontSize ）
	 * @property {String} 			color									String | 字体色（默认 $ux.Conf.fontColor ）
	 * @property {String} 			darkColor=[none|auto|color]				String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {String} 			backgroundColor							String | 背景色（默认 $ux.Conf.backgroundColor ）
	 * @property {String} 			backgroundDark=[none|auto|color]		String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Any}				top										Any | 距上 (默认 0)
	 * @property {Any}				right									Any | 距右 (默认 0)
	 * @property {Any}				left									Any | 距左 (默认 0)
	 * @property {Any}				bottom									Any | 距下 (默认 0)
	 * @property {Array}			padding									Any[] | 自定义角标padding
	 * @property {Array}			xstyle									Array<any> | 自定义样式
	 * <AUTHOR>
	 * @date 2024-12-03 21:30:37
	 */
	
	import { ref, computed, onMounted, PropType } from 'vue'
	import { $ux } from '../../index'
	import { useFontColor, useFontSize,  useForegroundColor, useThemeColor, UxThemeType } from '../../libs/use/style.uts'

	defineOptions({
		name: 'ux-badge'
	})
	
	const props = defineProps({
		theme: {
			type: String,
			default: 'primary'
		},
		value: {
			type: Number,
			default: 0
		},
		max: {
			type: Number,
			default: 99
		},
		overflow: {
			type: String,
			default: 'normal'
		},
		shape: {
			type: String,
			default: 'normal'
		},
		dot: {
			type: Boolean,
			default: false
		},
		inverted: {
			type: Boolean,
			default: false
		},
		fixed: {
			type: Boolean,
			default: true
		},
		top: {
			default: 0
		},
		right: {
			default: 0
		},
		left: {
			default: 0
		},
		bottom: {
			default: 0
		},
		color: {
			type: String,
			default: '#ffffff'
		},
		darkColor: {
			type: String,
			default: ''
		},
		background: {
			type: String,
			default: ''
		},
		backgroundDark: {
			type: String,
			default: ''
		},
		size: {
			default: 0
		},
		padding: {
			type: Array as PropType<number[]>,
			default: () : Array<number> => [2, 6] as Array<number>
		},
		xstyle: {
			type: Array as PropType<any[]>,
			default: () : Array<any> => [] as Array<any>
		}
	})

	const fontSize = computed(() : number => {
		return useFontSize($ux.Util.getPx(props.size), 1)
	})
	
	const fontColor = computed((): string => {
		if(props.inverted) {
			if(props.background != '' || props.theme == '') {
				return useForegroundColor(props.background, props.backgroundDark)
			} else {
				return useThemeColor(props.theme as UxThemeType) 
			}
		} else {
			return useFontColor(props.color, props.darkColor)
		}
	})
	
	const backgroundColor = computed(() : string => {
		if(props.inverted) {
			return 'transparent'
		} else {
			if(props.background != '' || props.theme == '') {
				return useForegroundColor(props.background, props.backgroundDark)
			} else {
				return useThemeColor(props.theme as UxThemeType) 
			}
		}
	})
	
	const style = computed(() => {
		let css = {}
		
		css['background-color'] = backgroundColor.value
		
		if (props.fixed) {
			css['position'] = 'absolute'
			
			if ($ux.Util.getPx(props.top) != 0) {
				css['top'] = $ux.Util.addUnit(props.top)
			}
			if ($ux.Util.getPx(props.right) != 0) {
				css['right'] = $ux.Util.addUnit(props.right)
			}
			if ($ux.Util.getPx(props.bottom) != 0) {
				css['bottom'] = $ux.Util.addUnit(props.bottom)
			}
			if ($ux.Util.getPx(props.left) != 0) {
				css['left'] = $ux.Util.addUnit(props.left)
			}
		} else {
			if ($ux.Util.getPx(props.top) != 0) {
				css['margin-top'] = $ux.Util.addUnit(props.top)
			}
			if ($ux.Util.getPx(props.right) != 0) {
				css['margin-right'] = $ux.Util.addUnit(props.right)
			}
			if ($ux.Util.getPx(props.bottom) != 0) {
				css['margin-bottom'] = $ux.Util.addUnit(props.bottom)
			}
			if ($ux.Util.getPx(props.left) != 0) {
				css['margin-left'] = $ux.Util.addUnit(props.left)
			}
		}
		
		if(props.dot) {
			css['width'] = $ux.Util.addUnit(7)
			css['height'] = $ux.Util.addUnit(7)
			css['border-radius'] = $ux.Util.addUnit(14)
		} else {
			let _padding: any[] = []
			if(props.padding != null) {
				let arr = props.padding as any[]
				for (let i = 0; i < arr.length; i++) {
					_padding.push($ux.Util.addUnit(arr[i]))
				}
			}
			css['padding'] = _padding.join(' ')
			
			if(props.shape == 'lt') {
				css['border-radius'] = '2px 10px 10px 10px'
			} else if(props.shape == 'rt') {
				css['border-radius'] = '10px 2px 10px 10px'
			} else if(props.shape == 'rb') {
				css['border-radius'] = '10px 10px 2px 10px'
			} else if(props.shape == 'lb') {
				css['border-radius'] = '10px 10px 10px 2px'
			} else if(props.shape == 'ltrb') {
				css['border-radius'] = '2px 10px 2px 10px'
			} else if(props.shape == 'rtlb') {
				css['border-radius'] = '10px 2px 10px 2px'
			} else {
				css['border-radius'] = '15px'
			}
		}
		
		return css
	})
	
	const textStyle = computed(() => {
		let css = {}
		
		css['font-size'] = $ux.Util.addUnit(fontSize.value)
		css['color'] = fontColor.value
		
		return css
	})
	
	const _value = computed((): string => {
		if(props.value == 0) {
			return ''
		}
		
		if(props.overflow == 'format') {
			if(props.value > 9999) {
				return `${Math.floor(props.value / 1e4 * 100) / 100}w`
			} else if(props.value > 999) {
				return `${Math.floor(props.value / 1e3 * 100) / 100}k`
			} else if(props.value > props.max) {
				return `${props.max}+`
			}
		} else {
			if(props.value > props.max) {
				return `${props.max}+`
			}
		}
		
		return `${props.value}`
	})
	
</script>

<style lang="scss" scoped>
	.ux-badge {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		
		&__value {
			font-size: 10px;
			color: white;
			text-align: center;
		}
	}
</style>