/**
 * canvas 绘制库
 */
export class UxCanvas {
	
	// #ifdef UNI-APP-X && APP
	ctx?: DrawableContext
	// #endif
	
	// #ifndef UNI-APP-X && APP
	ctx?: CanvasRenderingContext2D
	// #endif
	
	context?: CanvasContext
	
	constructor(id: string, instance ?: VueComponent) {
		// #ifdef UNI-APP-X && APP
		this.ctx = uni.getElementById(id)?.getDrawableContext()
		// #endif
		
		// #ifndef UNI-APP-X && APP
		this.initCanvas(id, instance)
		// #endif
	}
	
	private initCanvas(id: string, instance ?: VueComponent) {
		const dpr = uni.getWindowInfo().pixelRatio
		
		uni.createCanvasContextAsync({
			id: id,
			component: instance,
			success: (context : CanvasContext) => {
				this.context = context
				this.ctx = this.context?.getContext('2d')
				
				if(this.ctx != null) {
					this.ctx.canvas.width = this.ctx.canvas.offsetWidth * dpr
					this.ctx.canvas.height = this.ctx.canvas.offsetHeight * dpr
					this.ctx.scale(dpr, dpr)
				}
			},
			fail: (err) => {
				console.error('[ux-canvas]', err);
			}
		})
	}
	
	/**
	 * 重置画布
	 */
	reset() {
		if(this.ctx == null) return
		
		// #ifdef UNI-APP-X && APP
		this.ctx.reset()
		// #endif
		
		// #ifndef UNI-APP-X && APP
		this.ctx.clearRect(0, 0, this.ctx!.canvas.width, this.ctx!.canvas.height)
		// #endif
	}
	
	/**
	 * 绘制到画布
	 */
	draw() {
		if(this.ctx == null) return
		
		// #ifdef UNI-APP-X && APP
		this.ctx.update()
		// #endif
	}
	
	
}