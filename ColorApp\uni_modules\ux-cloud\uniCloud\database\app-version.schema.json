{"bsonType": "object", "required": ["uni_platform", "version", "type"], "permission": {"read": false, "create": false, "update": false, "delete": false}, "properties": {"_id": {"description": "记录id,自动生成"}, "title": {"bsonType": "string", "description": "更新标题", "label": "更新标题"}, "contents": {"bsonType": "string", "description": "更新内容", "label": "更新内容", "componentForEdit": {"name": "textarea"}, "componentForShow": {"name": "textarea", "props": {"disabled": true}}}, "platform": {"bsonType": "string", "description": "更新平台 android ios web", "label": "平台", "enum": [{"value": "android", "text": "Android"}, {"value": "ios", "text": "iOS"}, {"value": "web", "text": "Web"}]}, "type": {"bsonType": "string", "enum": [{"value": "native_app", "text": "原生App安装包"}, {"value": "wgt", "text": "Wgt资源包"}], "description": "安装包类型，native_app || wgt", "label": "安装包类型"}, "version": {"bsonType": "string", "description": "当前包版本号，必须大于当前线上发行版本号", "label": "版本号"}, "url": {"bsonType": "string", "description": "可下载或跳转的链接", "label": "链接"}, "silently": {"bsonType": "bool", "description": "是否静默更新", "label": "静默更新", "defaultValue": false}, "mandatory": {"bsonType": "bool", "description": "是否强制更新", "label": "强制更新", "defaultValue": false}, "create_date": {"bsonType": "timestamp", "label": "上传时间", "forceDefaultValue": {"$env": "now"}, "componentForEdit": {"name": "uni-dateformat"}}}, "version": "0.0.1"}