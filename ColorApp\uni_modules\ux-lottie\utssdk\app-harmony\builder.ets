import lottie, { AnimationItem } from '@ohos/lottie';

@Component
struct LottieView {
	private src: string = '';
	private renderingSettings: RenderingContextSettings = new RenderingContextSettings(true)
	private canvasRenderingContext: CanvasRenderingContext2D = new CanvasRenderingContext2D(this.renderingSettings)
	private animateItem: AnimationItem | null = null;
	private animateName: string = "animation"
	
	build() {
		Canvas(this.canvasRenderingContext)
		    .width(200)
		    .height(200)
		    .onReady(() => {
		      // 加载动画
		      if (this.animateItem != null) {
		        // 可在此生命回调周期中加载动画，可以保证动画尺寸正确
		        this.animateItem?.resize();
		      } else {
		        // 抗锯齿的设置
		        this.canvasRenderingContext.imageSmoothingEnabled = true;
		        this.canvasRenderingContext.imageSmoothingQuality = 'medium'
		        this.loadAnimation();
		      }
		    })
	}
	
	loadAnimation() {
	    this.animateItem = lottie.loadAnimation({
	      container: this.canvasRenderingContext,
	      renderer: 'canvas', // canvas 渲染模式
	      loop: true,
	      autoplay: false,
	      name: this.animateName,
	      path: this.src,
	    })
	    // 因为动画是异步加载，所以对animateItem的操作需要放在动画加载完成回调里操作
	    this.animateItem?.addEventListener('DOMLoaded', (args: Object): void => {
	      this.animateItem?.play()
	    });
	  }
	
	destroy() {
	  this.animateItem?.removeEventListener("DOMLoaded");
	  this.animateItem = null;
	  
	  lottie.destroy(this.animateName);
	}
	  
	aboutToDisappear() {
	  this.destroy();
	}
}

@Builder
export function buildLottieView(params: ESObject) {
	if (params.src != '') {
		LottieView({src: params.src})
	} else {
		Column()
	}
}