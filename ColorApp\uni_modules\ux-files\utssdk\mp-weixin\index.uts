
import { OpenFileManager, UxOpenFileManagerOptions, UxFile, UxMimeType } from '../interface.uts'
import { SearchFiles, UxSearchFilesOptions } from '../interface.uts'
import { StopSearch } from '../interface.uts'

export * from '../interface.uts'

/**
 * 打开文件管理器
 * @param {UxOpenFileManagerOptions} options 参数
 */
export const openFileManager : OpenFileManager = (options : UxOpenFileManagerOptions) => {
	
}

/**
 * 搜索文件
 * @param {UxSearchFilesOptions} options 参数
 */
export const searchFiles : SearchFiles = (options : UxSearchFilesOptions) => {
	console.log('[ux-files] mp不支持 searchFiles');
}

/**
 * 停止搜索文件
 */
export const stopSearch : StopSearch = () => {
	console.log('[ux-files] mp不支持 stopSearch');
}