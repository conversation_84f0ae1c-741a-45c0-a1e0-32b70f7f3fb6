<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina6_5" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22685"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="dc_launchscreen_pad_background.png" translatesAutoresizingMaskIntoConstraints="NO" id="Oly-Jg-H5o">
                                <rect key="frame" x="0.0" y="0.0" width="1032" height="1376"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="dc_launchscreen_landscape_background.png" translatesAutoresizingMaskIntoConstraints="NO" id="jN2-Td-r8h">
                                <rect key="frame" x="0.0" y="0.0" width="812" height="375"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="dc_launchscreen_portrait_background.png" translatesAutoresizingMaskIntoConstraints="NO" id="Tt8-jS-2H5">
                                <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="dc_launchscreen_icon.png" translatesAutoresizingMaskIntoConstraints="NO" id="vp6-uo-KS2">
                                <rect key="frame" x="150.66666666666666" y="391.66666666666669" width="112.66666666666666" height="112.66666666666669"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="QBH-Ne-rcx">
                                <rect key="frame" x="168" y="835" width="78.333333333333314" height="17"/>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <color key="textColor" white="0.66666666666666663" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="IW3-oA-Ytg"/>
                        <color key="backgroundColor" red="1.0" green="1.0" blue="1.0" alpha="1.0" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="vp6-uo-KS2" secondAttribute="trailing" id="1Cb-y2-pRQ"/>
                            <constraint firstItem="QBH-Ne-rcx" firstAttribute="bottom" secondItem="Ze5-6b-2t3" secondAttribute="bottom" constant="-44" id="5MD-Bb-oGe"/>
                            <constraint firstAttribute="trailing" secondItem="jN2-Td-r8h" secondAttribute="trailing" id="Bbg-68-h1T"/>
                            <constraint firstItem="Tt8-jS-2H5" firstAttribute="top" secondItem="Ze5-6b-2t3" secondAttribute="top" id="FH7-NM-QY8"/>
                            <constraint firstItem="vp6-uo-KS2" firstAttribute="centerX" secondItem="Ze5-6b-2t3" secondAttribute="centerX" id="GM5-vc-qeO"/>
                            <constraint firstItem="jN2-Td-r8h" firstAttribute="leading" secondItem="Ze5-6b-2t3" secondAttribute="leading" id="J9z-eJ-GkK"/>
                            <constraint firstItem="vp6-uo-KS2" firstAttribute="top" relation="greaterThanOrEqual" secondItem="Ze5-6b-2t3" secondAttribute="top" constant="10" id="M4i-XX-uO2"/>
                            <constraint firstItem="jN2-Td-r8h" firstAttribute="top" secondItem="Ze5-6b-2t3" secondAttribute="top" id="QFR-Dp-WZN"/>
                            <constraint firstAttribute="trailing" secondItem="Tt8-jS-2H5" secondAttribute="trailing" id="QGW-bk-xBw"/>
                            <constraint firstItem="vp6-uo-KS2" firstAttribute="centerY" secondItem="Ze5-6b-2t3" secondAttribute="centerY" id="Qji-wc-LLD"/>
                            <constraint firstAttribute="bottom" secondItem="Oly-Jg-H5o" secondAttribute="bottom" id="RrE-yv-HsN"/>
                            <constraint firstItem="Oly-Jg-H5o" firstAttribute="top" secondItem="Ze5-6b-2t3" secondAttribute="top" id="VcW-dk-n8h"/>
                            <constraint firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="vp6-uo-KS2" secondAttribute="bottom" constant="10" id="dGX-JM-hiU"/>
                            <constraint firstItem="Oly-Jg-H5o" firstAttribute="leading" secondItem="Ze5-6b-2t3" secondAttribute="leading" id="fa7-4C-n5e"/>
                            <constraint firstItem="Tt8-jS-2H5" firstAttribute="leading" secondItem="Ze5-6b-2t3" secondAttribute="leading" id="fnt-hE-VMi"/>
                            <constraint firstAttribute="bottom" secondItem="jN2-Td-r8h" secondAttribute="bottom" id="iIX-Hl-IOF"/>
                            <constraint firstAttribute="trailing" secondItem="Oly-Jg-H5o" secondAttribute="trailing" id="mm9-f4-toK"/>
                            <constraint firstItem="vp6-uo-KS2" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="Ze5-6b-2t3" secondAttribute="leading" id="oA2-4f-IGA"/>
                            <constraint firstItem="QBH-Ne-rcx" firstAttribute="centerX" secondItem="Ze5-6b-2t3" secondAttribute="centerX" id="rO7-2t-bpH"/>
                            <constraint firstAttribute="bottom" secondItem="Tt8-jS-2H5" secondAttribute="bottom" id="seh-wj-zPF"/>
                        </constraints>
                        <variation key="default">
                            <mask key="subviews">
                                <exclude reference="Oly-Jg-H5o"/>
                                <exclude reference="jN2-Td-r8h"/>
                                <exclude reference="Tt8-jS-2H5"/>
                            </mask>
                        </variation>
                        <variation key="heightClass=compact">
                            <mask key="subviews">
                                <include reference="jN2-Td-r8h"/>
                            </mask>
                        </variation>
                        <variation key="heightClass=regular-widthClass=compact">
                            <mask key="subviews">
                                <include reference="Tt8-jS-2H5"/>
                            </mask>
                        </variation>
                        <variation key="heightClass=regular-widthClass=regular">
                            <mask key="subviews">
                                <include reference="Oly-Jg-H5o"/>
                            </mask>
                        </variation>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="52.173913043478265" y="374.33035714285711"/>
        </scene>
    </scenes>
    <resources>
        <image name="dc_launchscreen_icon.png" width="112.66666412353516" height="112.66666412353516"/>
        <image name="dc_launchscreen_landscape_background.png" width="812" height="375"/>
        <image name="dc_launchscreen_pad_background.png" width="768" height="1024"/>
        <image name="dc_launchscreen_portrait_background.png" width="375" height="812"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
