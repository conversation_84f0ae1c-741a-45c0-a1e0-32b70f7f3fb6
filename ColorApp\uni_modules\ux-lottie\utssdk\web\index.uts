import lottie from 'lottie-web'
import { AnimationItem, AnimationDirection } from 'lottie-web'

export class UxLottie {
	
	anim ?: AnimationItem = null
	id: string
	
	constructor(id: string) {
		this.id = id
	}
	
	init(src: string) {
		this.destroy()
		this.anim = lottie.loadAnimation({
			container: uni.getElementById(this.id)!,
			renderer: 'svg',
			loop: false,
			autoplay: false,
			path: src,
		})
	}
	
	play() {
		this.anim?.play()
	}
	
	stop() {
		this.anim?.stop()
	}
	
	pause() {
		this.anim?.pause()
	}
	
	resume() {
		this.anim?.play()
	}
	
	destroy() {
		this.anim?.destroy()
	}
	
	setDirection(direction: number) {
		this.anim?.setDirection(direction as AnimationDirection)
	}
	
	setRepeatCount(count: number) {
		this.anim?.setLoop(false)
	}
	
	loop(loop: boolean) {
		this.anim?.setLoop(loop)
	}
}