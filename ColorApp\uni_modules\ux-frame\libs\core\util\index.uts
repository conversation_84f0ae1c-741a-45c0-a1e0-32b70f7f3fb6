// #ifndef UNI-APP-X
import { ComponentPublicInstance } from 'vue'
// #endif
import { Core } from '../core'

/**
   @Name    :	工具
   <AUTHOR>   UxFrame
   @Date    :   2023-10-09 11:20:22
*/

export class Util {

	core : Core

	constructor(core : Core) {
		this.core = core
	}

	/**
	 * 睡眠
	 * @param {number} time 时间 单位：毫秒
	 */
	async sleep(time : number) : Promise<boolean> {
		return new Promise<boolean>(resolve => {
			setTimeout(function () {
				resolve(true)
			}, time);
		})
	}

	/**
	 * 获取 px 值
	 * @param {string|number} value 值
	 */
	getPx(value : any | null) : number {
		if(value == null) {
			return 0
		}
		
		let _value = value.toString().toLowerCase()

		// % 按屏幕宽取值
		if (_value.indexOf('%') != -1) {
			try {
				return uni.getWindowInfo().screenWidth * (parseFloat(_value.replace('%', '')) / 100)
			} catch (e) { }
		}

		if (this.core.Verify.isNumber(_value)) {
			return parseFloat(_value)
		}

		// 如果带有rpx，先取出其数值部分，再转为px值
		if (/(rpx|upx)$/.test(_value)) {
			try {
				let n = parseInt(_value.replace(/rpx/g, '').replace(/upx/g, ''))
				return uni.rpx2px(n)
			} catch (e) { }
		}

		// px 取值
		return parseFloat(_value.replace('px', ''))
	}

	/**
	 * 格式化单位 px
	 * @param {string|number} value 需要添加单位的值
	 * @param {string} unit 添加的单位名 比如px
	 */
	addUnit(value : any | null, unit : string = 'px') : string {
		if(value == null) {
			return ''
		}
		
		let _value = value.toString().toLowerCase()

		// % 不处理
		if (_value.indexOf('%') != -1) {
			return _value
		}

		// 如果带有rpx，先取出其数值部分，再转为px值
		if (/(rpx|upx)$/.test(_value)) {
			try {
				let n = parseInt(_value.replace(/rpx/g, '').replace(/upx/g, ''))
				_value = `${uni.rpx2px(n)}`
			} catch (e) { }
		}

		if (this.core.Verify.isNumber(_value)) {
			if (unit == 'px') {
				// 对px进行缩放 保证在不同设备上表现一致
				let width = uni.getWindowInfo().screenWidth
				let scale = width > 750 ? 1 : (width / 375)
				return `${parseFloat(_value) * scale}px`
			}

			return `${_value}${unit}`
		}

		return _value
	}

	/**
	 * 查询节点
	 * @param {String} id 节点 id / className
	 * @param {ComponentPublicInstance} context 实例
	 */
	async getBoundingClientRect(id : string, context ?: any): Promise<NodeInfo | null> {
		return new Promise<NodeInfo | null>((resolve, reject) => {
			let query = uni.createSelectorQuery()
			if (context != null) {
				query = query.in(context)
			}

			query.select(id).boundingClientRect((rect) => {
				resolve(rect as NodeInfo)
			}).exec()
		})
	}

	/**
	 * 查询节点数组
	 * @param {String} id 节点 id / className
	 * @param {ComponentPublicInstance} context 实例
	 */
	async getBoundingClientRectList(id : string, context ?: any): Promise<NodeInfo[]> {
		return new Promise<NodeInfo[]>((resolve, reject) => {
			let query = uni.createSelectorQuery()
			if (context != null) {
				query = query.in(context)
			}

			query.selectAll(id).boundingClientRect((rect) => {
				resolve(rect as NodeInfo[])
			}).exec()
		})
	}

	/**
	 * 查询节点滚动位置
	 * @param {String} id 节点 id / className
	 * @param {ComponentPublicInstance} context 实例
	 */
	async getScrollOffset(id : string, context ?: any): Promise<NodeInfo> {
		return new Promise<NodeInfo>((resolve, reject) => {
			let query = uni.createSelectorQuery()
			if (context != null) {
				query = query.in(context)
			}

			query.select(id).scrollOffset((rect) => {
				resolve(rect as NodeInfo)
			}).exec()
		})
	}

	/**
	 * 获取CanvasContext对象实例
	 * @param {String} id canvasId
	 * @param {ComponentPublicInstance} context 实例
	 */
	async createCanvasContext(id : string, context ?: VueComponent): Promise<CanvasContext> {
		return new Promise<CanvasContext>((resolve, reject) => {
			uni.createCanvasContextAsync({
				id: id,
				component: context,
				success: (context : CanvasContext) => {
					resolve(context)
				},
				fail: (err) => {
					reject(err)
				}
			})
		})
	}

	/**
	 * 查找父级或平级指定元素
	 * @param {ComponentPublicInstance} context 实例
	 * @param {Array} refs 查询对象ref名称
	 */
	$findEl(context : ComponentPublicInstance | null, refs : string[]) : UniElement | null {
		if (context == null) {
			return null
		}
		
		let parent = context.$parent
		if (parent != null) {
			for (let index = 0; index < refs.length; index++) {
				let name = refs[index]

				// 查找父级
				if (parent.$refs[name] != null) {
					return parent.$refs[name] as UniElement
				} else {
					// 查找平级
					let childrem: VueComponent[] | null  = parent.$children
					if (childrem != null) {
						for (let i = 0; i < childrem.length; i++) {
							let child = childrem[i]
						
							if (child.$refs[name] != null) {
								return child.$refs[name] as UniElement
							}
						}
					}
				}
			}
		}

		return null
	}

	/**
	 * 查找父组件实例 执行操作
	 * @param {ComponentPublicInstance} context 实例
	 * @param {String} componentName 组件名
	 * @param {String} eventName 事件名
	 * @param {Array} params 参数
	 */
	$dispatch(
		context : ComponentPublicInstance,
		componentName : string,
		eventName : string,
		...params : any[]
	) {

		let parent = context.$parent
		let name = parent?.$options?.name
		while (parent != null && (name == null || componentName != name)) {
			parent = parent.$parent
			if (parent != null) {
				name = parent.$options.name
			}
		}

		if (parent != null) {
			// #ifdef APP-ANDROID
			parent.$callMethod(eventName, ...params)
			// #endif
			// #ifndef APP-ANDROID
			parent[eventName](...params)
			// #endif
		}
	}


	timer : Map<any, number> = new Map<any, number>()
	flag : boolean = false

	/**
	 * 节流：在一定时间内，只能触发一次
	 *
	 * @param {Function} func 要执行的回调函数
	 * @param {Number} wait 延时的时间
	 */
	throttle(func : () => void, wait : number) {
		if (!this.flag) {
			this.flag = true

			func()

			if (wait <= 0) {
				this.flag = false
			} else {
				setTimeout(() => {
					this.flag = false
				}, wait)
			}
		}
	}

	/**
	 * 防抖：一定时间内，只有最后一次操作，再过wait毫秒后才执行函数
	 *
	 * @param {Function} func 要执行的回调函数
	 * @param {Number} wait 延时的时间
	 */
	debounce(func : () => void, wait : number) {
		this.debouncek(func, wait, '')
	}

	/**
	 * 防抖：一定时间内，只有最后一次操作，再过wait毫秒后才执行函数
	 *
	 * @param {Function} func 要执行的回调函数
	 * @param {Number} wait 延时的时间
	 * @param {Number} key 唯一key
	 */
	debouncek(func : () => void, wait : number, key : string) {
		// 清除定时器
		if (this.timer[key] != null) {
			clearTimeout(this.timer[key]! as number)
		}

		// 立即执行
		if (wait <= 0) {
			func()
		} else {
			// 延时wait毫秒后执行func回调方法
			this.timer[key] = setTimeout(() => {
				func()
			}, wait)
		}
	}

	/**
	 * 去除空格
	 * 
	 * @param {String} str 需要去除空格的字符串
	 * @param {String} pos both(左右)|left|right|all
	 */
	trim = (str : string, pos : string) : string => {
		switch (pos) {
			case 'both':
				return str.replace(/^\s+|\s+$/g, '')
			case 'left':
				return str.replace(/^\s*/, '')
			case 'right':
				return str.replace(/(\s*$)/g, '')
			case 'all':
				return str.replace(/\s+/g, '')
		}

		return str.replace(/\s+/g, '')
	}

	// #ifdef UNI-APP-X
	/**
	 * 公共样式
	 * 
	 * @returns {Map<string, any>}
	 */
	xStyle(css : Map<string, any>, margin : any[], mt : number, mr : number, mb : number, ml : number, padding : any[], pt : number, pr : number, pb : number, pl : number) : Map<string, any> {
		if (margin.length > 0) {
			css.set('margin', margin.map((n : any) : string => this.addUnit(n)).join(' '))
		} else {
			if (mt != 0) {
				css.set('margin-top', this.addUnit(mt))
			}
			if (mr != 0) {
				css.set('margin-right', this.addUnit(mr))
			}
			if (mb != 0) {
				css.set('margin-bottom', this.addUnit(mb))
			}
			if (ml != 0) {
				css.set('margin-left', this.addUnit(ml))
			}
		}

		if (padding.length > 0) {
			css.set('padding', padding.map((n : any) : string => this.addUnit(n)).join(' '))
		} else {
			if (pt != 0) {
				css.set('padding-top', this.addUnit(pt))
			}
			if (pr != 0) {
				css.set('padding-right', this.addUnit(pr))
			}
			if (pb != 0) {
				css.set('padding-bottom', this.addUnit(pb))
			}
			if (pl != 0) {
				css.set('padding-left', this.addUnit(pl))
			}
		}

		return css
	}
	// #endif

	// #ifndef UNI-APP-X
	/**
	 * 公共样式
	 * 
	 * @returns {Map<string, any>}
	 */
	xStyle(css : UTSJSONObject, margin : any[], mt : number, mr : number, mb : number, ml : number, padding : any[], pt : number, pr : number, pb : number, pl : number) : Map<string, any> {
		if (margin.length > 0) {
			css['margin'] = margin.map((n : any) : string => this.addUnit(n)).join(' ')
		} else {
			if (mt != 0) {
				css['margin-top'] = this.addUnit(mt)
			}
			if (mr != 0) {
				css['margin-right'] = this.addUnit(mr)
			}
			if (mb != 0) {
				css['margin-bottom'] = this.addUnit(mb)
			}
			if (ml != 0) {
				css['margin-left'] = this.addUnit(ml)
			}
		}

		if (padding.length > 0) {
			css['padding'] = padding.map((n : any) : string => this.addUnit(n)).join(' ')
		} else {
			if (pt != 0) {
				css['padding-top'] = this.addUnit(pt)
			}
			if (pr != 0) {
				css['padding-right'] = this.addUnit(pr)
			}
			if (pb != 0) {
				css['padding-bottom'] = this.addUnit(pb)
			}
			if (pl != 0) {
				css['padding-left'] = this.addUnit(pl)
			}
		}

		return css
	}
	// #endif
}