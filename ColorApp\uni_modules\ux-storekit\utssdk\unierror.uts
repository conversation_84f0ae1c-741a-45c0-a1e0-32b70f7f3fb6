import { UxStoreKitErrorCode, UxStoreKitFail } from "./interface.uts"

export const UniErrorSubject = 'ux-storekit';

export const UxStoreKitErrors : Map<UxStoreKitErrorCode, string> = new Map([
  [-1, 'open error'],
  [-2, 'not support']
])

export class UxStoreKitFailImpl extends UniError implements UxStoreKitFail {
  constructor(errCode : UxStoreKitErrorCode) {
    super();
    this.errSubject = UniErrorSubject;
    this.errCode = errCode;
    this.errMsg = UxStoreKitErrors.get(errCode) ?? "";
  }
}
