import { UxOpenURLOptions } from '../interface.uts'
import { UxMakePhoneCallOptions } from '../interface.uts'
import { UxVibrateOptions } from '../interface.uts'
import { UxOpenWebOptions } from '../interface.uts'
import { UxPlusSuccessCallbackImpl, UxPlusErrorCallbackImpl } from '../unierror.uts'
import * as OpenWeb from "./OpenWeb.uts"

import Want from '@ohos.app.ability.Want';
import common from '@ohos.app.ability.common';
import vibrator from '@ohos.vibrator';
import { BusinessError } from '@ohos.base';

export default class Common {
	
	hideKeyboard() {
		
	}
	
	setGray(gray: number) {
		// document.body.style.filter = `grayscale(${gray * 100}%)`
	}
	
	openURL(options : UxOpenURLOptions) {
		// try {
		// 	// 没有传入url
		// 	if (options.url == '' || options.url.length == 0) {
		// 		const err = errorCallback('openURL:failed - 请提供有效的URL地址');
		// 		options.fail?.(err)
		// 		options.complete?.(err)
		// 		return
		// 	}
		
		// 	// 获取鸿蒙应用上下文 - 使用 UTSHarmony 提供的方法
		// 	const context = UTSHarmony.getUIAbilityContext();
		
		// 	// 构造 Want 对象
		// 	const want : Want = {
		// 		action: 'ohos.want.action.viewData',
		// 		uri: options.url,
		// 		type: 'text/plain'
		// 	};
		
		// 	// 启动外部应用
		// 	context.startAbility(want)
		// 		.then(() => {
		// 			const res = successCallback('openURL:success', { url: options.url })
		// 			options.complete?.(res)
		// 			options.success?.(res)
		// 		})
		// 		.catch((error : BusinessError) => {
		// 			const err = errorCallback(`openURL:failed -${error.code}`);
		// 			options.fail?.(err)
		// 			options.complete?.(err)
		// 		})
		
		// } catch (error) {
		// 	const err = errorCallback(`openURL:failed - ${error}`);
		// 	options.fail?.(err)
		// 	options.complete?.(err)
		// }
	}
	
	openWeb(options : UxOpenWebOptions) {
		OpenWeb.show(options)
	}
	
	makePhoneCall(options : UxMakePhoneCallOptions) {
		
	}
	
	vibrate(options : UxVibrateOptions) {
		// try {
		// 	const simpleVibrateOptions : vibrator.VibrateEffect = {
		// 		type: 'time',
		// 		duration: options.duration
		// 	}
		// 	const simpleVibrateAttribute : vibrator.VibrateAttribute = {
		// 		id: 0,
		// 		usage: 'touch'
		// 	}
		// 	vibrator.startVibration(simpleVibrateOptions, simpleVibrateAttribute)
		// 		.then(() => {
		// 			const res = successCallback('vibrate:success');
		// 			options.success?.(res);
		// 			options.complete?.(res);
		// 		})
		// 		.catch((fallbackError : BusinessError) => {
		// 			const err = errorCallback(`震动错误 ${fallbackError}`);
		// 			options.fail?.(err)
		// 			options.complete?.(err)
		// 		});
		// } catch (error) {
		// 	const err = errorCallback(`vibrate:failed - ${error}`);
		// 	options.fail?.(err)
		// 	options.complete?.(err)
		// }
	}
}
