
<p align="center">
    <img alt="logo" src="https://www.uxframe.cn/logo/logo.png" width="120" height="120" style="margin-bottom: 10px;">
</p>
<h3 align="center" style="margin: 30px 0 30px;font-weight: bold;font-size:40px;">UxFrame 通知栏下载进度SDK 1.0.1</h3>

## 特别说明

如果您已经购买了[UxFrame 低代码高性能UI框架](https://ext.dcloud.net.cn/plugin?id=16148), 则无需再次购买本插件，请点击上方`进入交流群`联系我免费获取离线版插件！

### 使用说明

``` ts

import * as notification from "@/uni_modules/ux-notification"

const progress = ref(0)
let t = 0

function cancel() {
	uni.showModal({
		title: '是否取消下载？',
		cancelText: '否',
		confirmText: '是',
		success: res => {
			if (res.confirm) {
				clearInterval(t)
				notification.cancelNotificationProgress()
			}
		}
	});
}

function start() {
	t = setInterval(() => {
		if(progress.value >= 100) {
			clearInterval(t)
			notification.finishNotificationProgress({
				title: "安装升级包",
				content: "下载完成",
				onClick() { }
			} as notification.UxFinishNotificationProgressOptions)
			return
		}
		
		progress.value += 1
		notification.createNotificationProgress({
			title: "升级中心正在下载安装包……",
			content: `${progress.value}%`,
			progress: progress.value,
			onClick: () => {
				cancel()
			}
		} as notification.UxCreateNotificationProgressOptions)
		
	}, 1000)
}

```

## 文档教程 ⬇️

[https://www.uxframe.cn](https://www.uxframe.cn)
