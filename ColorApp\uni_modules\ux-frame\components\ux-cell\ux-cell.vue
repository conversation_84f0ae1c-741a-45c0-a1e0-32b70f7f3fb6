<template>
	<view v-if="show" class="ux-cell" :style="[style, xstyle]" :hover-class="hoverClass" :hover-start-time="20" :hover-stay-time="200" @click="click">
		<view class="ux-cell__body" :style="bodyStyle">
			<slot name="left">
				<view class="ux-col-left">
					<view :class="`ux-row-${center?'center':'left'}`">
						<view v-if="icon != ''" class="ux-cell__body__icon">
							<ux-icon :type="icon" :size="iconSize" :color="iconColor" :custom-family="customFamily" :xstyle="[iconStyle]"></ux-icon>
						</view>
						<text class="ux-cell__body__title" :style="[_titleStyle, titleStyle]">{{ title }}</text>
						<text v-if="required" class="ux-cell__body__star">*</text>
					</view>
					<slot name="summary">
						<text v-if="summary != ''" class="ux-cell__body__summary" :style="[_summaryStyle, summaryStyle]">{{ summary }}</text>
					</slot>
				</view>
			</slot>
			
			<slot name="right">
				<view class="ux-row-right">
					<text v-if="label != '' || placeholder != ''" class="ux-cell__body__label" :style="[_labelStyle, _xlabelStyle]">{{ label == '' ? placeholder : label }}</text>
					<view v-if="_rightIcon != ''" class="ux-cell__body__icon--right">
						<ux-icon :type="_rightIcon" :size="18" color="#a9a9a9" :custom-family="customFamily" :xstyle="[rightIconStyle]"></ux-icon>
					</view>
				</view>
			</slot>
		</view>
	</view>
</template>

<script setup lang="ts">
	
	/**
	 * Cell 单元格
	 * @description 支持配置标题、描述文本、内容及样式，左右图标、插槽，最小高度、固定高度，对齐方式
	 * @demo pages/component/cell.uvue
	 * @tutorial https://www.uxframe.cn/component/cell.html
	 * @property {Slot}				left								Slot | 左边插槽
	 * @property {Slot}				right								Slot | 右边插槽
	 * @property {String}			title								String | 标题
	 * @property {Any}				size								Any | 标题大小 (默认 14)
	 * @property {String}			color								String | 标题颜色 (默认 $ux.Conf.fontColor)
	 * @property {String} 			darkColor=[none|auto|color]			String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Boolean}			bold = [true|false]					Boolean | 标题加粗 (默认 false)
	 * @property {String}			titleStyle							String | 标题样式
	 * @property {String}			summary								String | 描述文本
	 * @property {String}			summaryColor						String | 描述文本颜色 (默认 $ux.Conf.subtitleColor)
	 * @property {Any}				summarySize							Any | 描述文本大小 (默认 $ux.Conf.fontSize.small)
	 * @property {String}			summaryStyle						String | 描述文本样式
	 * @property {String}			label								String | 右侧内容
	 * @property {Any}				labelSize							Any | 内容大小 (默认 $ux.Conf.fontSize)
	 * @property {String}			labelColor							String | 内容颜色 (默认 $ux.Conf.subtitleColor)
	 * @property {Boolean}			labelBold = [true|false]			Boolean | 内容加粗 (默认 false)
	 * @property {String}			labelStyle							String | 内容样式
	 * @property {String}			placeholder							String | 右侧占位内容
	 * @property {Any}				placeholderSize						Any | 右侧占位内容大小 (默认 $ux.Conf.fontSize.small)
	 * @property {String}			placeholderColor					String | 右侧占位内容颜色 (默认 $ux.Conf.placeholderColor)
	 * @property {String} 			placeholderDark=[none|auto|color]	String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {String}			placeholderStyle					String | 内容样式
	 * @property {String}			icon								String | 标题图标
	 * @property {String}			iconColor							String | 标题图标颜色 (默认 $ux.Conf.fontColor)
	 * @property {Any}				iconSize							Any | 标题图标大小 (默认 $ux.Conf.fontSize)
	 * @property {String}			iconStyle							String | 标题图标样式
	 * @property {String}			rightIcon							String | 右侧图标
	 * @property {Boolean}			rightArrow = [true|false]			Boolean | 右侧箭头图标（默认 true）
	 * @property {String}			rightIconStyle						String | 右侧图标样式
	 * @property {String}			customFamily						String | 字体family
	 * @property {String}			background							String | 背景色
	 * @property {String} 			backgroundDark=[none|auto|color]	String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色 (默认 auto)
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Boolean}			border = [true|false]				Boolean | 显示下边框 (默认 true )
	 * @property {String}			borderColor							String | 下边框颜色  (默认 $ux.Conf.borderColor )
	 * @property {String} 			borderDarkColor=[none|auto|color]	String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色 (默认 auto)
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Any}				minHeight							Any | 最小高度（默认 0）
	 * @property {Any}				height								Any | 固定高度 优先级高于最小高度（默认 40）
	 * @property {Any}				padding								Any | 内部水平边距 (默认 $ux.Conf.rowPadding)
	 * @property {Boolean}			center = [true|false]				Boolean | 内容是否垂直居中 (默认 false )
	 * @property {Boolean}			required = [true|false]				Boolean | 是否显示必填星号（默认 false ）
	 * @property {String}			path								String | 页面跳转路径
	 * @property {String}			show								Boolean | 是否显示cell（默认 true ）
	 * @property {Boolean}			disabled = [true|false]				Boolean | 是否禁用cell（默认 false ）
	 * @property {Boolean}			hover = [true|false]				Boolean | 显示点击态 (默认 true)
	 * @property {Array}			margin								Array | 边距 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}				mt									Any | 距上 单位px
	 * @property {Any}				mr									Any | 距右 单位px
	 * @property {Any}				mb									Any | 距下 单位px
	 * @property {Any}				ml									Any | 距左 单位px
	 * @property {Array}			xstyle								Array<any> | 自定义样式
	 * @event {Function}			click								Function | 单元格点击时触发
	 * <AUTHOR>
	 * @date 2024-12-01 01:39:55
	 */
	
	import { ref, computed, PropType } from 'vue'
	import { $ux } from '../../index'
	import { useTitleColor, useSubTitleColor, useFontSize, useForegroundColor, useBorderColor, useHpadding, usePlaceholderColor, useDisabledColor } from '../../libs/use/style.uts'
	
	defineOptions({
		name: 'ux-cell'
	})
	
	const emit = defineEmits(['click'])
	
	const props = defineProps({
		title: {
			type: String,
			default: ''
		},
		size: {
			default: 0
		},
		color: {
			type: String,
			default: ''
		},
		darkColor: {
			type: String,
			default: ''
		},
		bold: {
			type: Boolean,
			default: false
		},
		titleStyle: {
			type: String,
			default: ''
		},
		summary: {
			type: String,
			default: ''
		},
		summaryColor: {
			type: String,
			default: ''
		},
		summarySize: {
			default: 0
		},
		summaryStyle: {
			type: String,
			default: ''
		},
		label: {
			type: String,
			default: ''
		},
		labelSize: {
			default: 0
		},
		labelColor: {
			type: String,
			default: ''
		},
		labelBold: {
			type: Boolean,
			default: false
		},
		labelStyle: {
			type: String,
			default: ''
		},
		placeholder: {
			type: String,
			default: ''
		},
		placeholderSize: {
			default: 0
		},
		placeholderColor: {
			type: String,
			default: ''
		},
		placeholderDark: {
			type: String,
			default: ''
		},
		placeholderStyle: {
			type: String,
			default: ''
		},
		icon: {
			type: String,
			default: ''
		},
		iconColor: {
			type: String,
			default: ''
		},
		iconSize: {
			default: 0
		},
		iconStyle: {
			type: String,
			default: ''
		},
		rightIcon: {
			type: String,
			default: ''
		},
		rightArrow: {
			type: Boolean,
			default: true
		},
		rightIconStyle: {
			type: String,
			default: ''
		},
		customFamily: {
			type: String,
			default: ''
		},
		background: {
			type: String,
			default: ''
		},
		backgroundDark: {
			type: String,
			default: ''
		},
		border: {
			type: Boolean,
			default: true
		},
		borderColor: {
			type: String,
			default: ''
		},
		borderDarkColor: {
			type: String,
			default: ''
		},
		minHeight: {
			default: 0
		},
		height: {
			default: 40
		},
		padding: {
			default: -1
		},
		center: {
			type: Boolean,
			default: false
		},
		required: {
			type: Boolean,
			default: false
		},
		path: {
			type: String,
			default: ''
		},
		hover: {
			type: Boolean,
			default: true
		},
		disabled: {
			type: Boolean,
			default: false
		},
		show: {
			type: Boolean,
			default: true
		},
		margin: {
			type: Array as PropType<Array<any>>,
			default: (): any[] => [] as any[]
		},
		mt: {
			default: 0
		},
		mr: {
			default: 0
		},
		mb: {
			default: 0
		},
		ml: {
			default: 0
		},
		xstyle: {
			type: Array as PropType<Array<any>>,
			default: (): any[] => [] as any[]
		}
	})
	
	const padding = computed((): number => {
		if($ux.Util.getPx(props.padding) == 0) {
			return 0
		} else {
			return useHpadding($ux.Util.getPx(props.padding), 0)
		}
	})
	
	const backgroundColor = computed((): string => {
		if(props.background == '') {
			return 'transparent'
		}
		
		return useForegroundColor(props.background, props.backgroundDark)
	})
	
	const borderColor = computed((): string => {
		return useBorderColor(props.borderColor, props.borderDarkColor)
	})
	
	const fontSize = computed((): number => {
		return useFontSize($ux.Util.getPx(props.size), 0)
	})
	
	const fontColor = computed((): string => {
		if(props.disabled) {
			return useDisabledColor(useTitleColor(props.color, props.darkColor))
		}  else {
			return useTitleColor(props.color, props.darkColor)
		}
	})
	
	const summarySize = computed((): number => {
		return useFontSize($ux.Util.getPx(props.summarySize), 1)
	})
	
	const summaryColor = computed((): string => {
		return useSubTitleColor(props.summaryColor, '')
	}) 
	
	const labelSize = computed((): number => {
		return useFontSize($ux.Util.getPx(props.labelSize), 1)
	})
	
	const labelColor = computed((): string => {
		return useSubTitleColor(props.labelColor, '')
	})
	
	const placeholderSize = computed((): number => {
		return useFontSize($ux.Util.getPx(props.placeholderSize), 1)
	})
	
	const placeholderColor = computed((): string => {
		return usePlaceholderColor(props.placeholderColor, props.placeholderDark)
	})
	
	const style = computed(() => {
		let css = {}
		
		if(props.margin.length > 0) {
			css['margin'] = props.margin.join(' ')
		} else {
			if($ux.Util.getPx(props.mt) != 0) {
				css['margin-top'] = $ux.Util.addUnit(props.mt)
			}
			if($ux.Util.getPx(props.mr) != 0) {
				css['margin-right'] = $ux.Util.addUnit(props.mr)
			}
			if($ux.Util.getPx(props.mb) != 0) {
				css['margin-bottom'] = $ux.Util.addUnit(props.mb)
			}
			if($ux.Util.getPx(props.ml) != 0) {
				css['margin-left'] = $ux.Util.addUnit(props.ml)
			}
		}
		
		css['background-color'] = backgroundColor.value
		
		// #ifdef WEB
		css['cursor'] = props.disabled? 'not-allowed' : 'pointer'
		// #endif
		
		return css
	})
	
	const bodyStyle = computed(() => {
		let css = {}
		
		if($ux.Util.getPx(props.minHeight) > 0) {
			if(props.label == '') {
				css['height'] = $ux.Util.addUnit(props.minHeight)
			} else {
				css['min-height'] = $ux.Util.addUnit(props.minHeight)
			}
		} else {
			if($ux.Util.getPx(props.height) > 0) {
				css['height'] = $ux.Util.addUnit(props.height)
			}
		}
		
		if(props.border) {
			css['border-bottom'] = `1px solid ${borderColor.value}`
		}
		
		css['margin'] = `0 ${$ux.Util.addUnit(padding.value)}`
		
		if(props.center) {
			css['justify-content'] = 'center'
		}
		
		return css
	})
	
	const _titleStyle = computed(() => {
		let css = {}
		
		css['font-size'] = $ux.Util.addUnit(fontSize.value)
		css['color'] = fontColor.value
		css['font-weight'] = props.bold?'bold':'normal'
		
		return css
	})
	
	const _summaryStyle = computed(() => {
		let css = {}
		
		css['font-size'] = $ux.Util.addUnit(summarySize.value)
		css['color'] = summaryColor.value
		
		return css
	})
	
	const _labelStyle = computed(() => {
		let css = {}
		
		css['max-width'] = `${uni.getWindowInfo().windowWidth * 0.6}px`
		
		if(props.label == '') {
			css['font-size'] = $ux.Util.addUnit(placeholderSize.value)
			css['color'] = placeholderColor.value
			css['font-weight'] = 'normal'
		} else {
			css['font-size'] = $ux.Util.addUnit(labelSize.value)
			css['color'] = labelColor.value
			css['font-weight'] = props.labelBold?'bold':'normal'
		}
		
		return css
	})
	
	const _xlabelStyle = computed((): string => {
		return props.label == '' ? props.placeholderStyle : props.labelStyle
	})
	
	const _rightIcon = computed((): string => {
		if(props.rightIcon != '') {
			return props.rightIcon
		}
		
		if(props.rightArrow) {
			return 'arrowright'
		}
		
		return ''
	})
	
	const hoverClass = computed((): string => {
		let className = $ux.Conf.darkMode.value  ? 'ux-cell__hover--dark' : 'ux-cell__hover'
		return props.hover && !props.disabled ? className : 'none'
	})
	
	function click(e: MouseEvent) {
		if(props.disabled) {
			return
		}
		
		if(props.path != '') {
			uni.navigateTo({
				url: props.path
			})
		}
		
		emit('click', e)
	}
</script>

<style lang="scss" scoped>

	.ux-cell {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		
		&__body {
			flex: 1; 
			margin: 0 15px;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;
			
			&__icon {
				margin-right: 10px;
				
				&--right {
					margin-left: 8px;
				}
			}
			
			&__title {
				color: #333333;
				font-size: 14px;
			}
			
			&__summary {
				color: #333333;
				font-size: 9px;
				margin-top: 2px;
			}
			
			&__label {
				color: #333333;
				font-size: 14px;
				text-align: right;
			}
			
			&__star {
				font-size: 12px;
				color: red;
				margin-left: 5px;
			}
		}
		
		&__hover {
			background-color: #F0F0F0 !important;
		}
		
		&__hover--dark {
			background-color: #666666 !important;
		}
	}
	
	.ux-col-left {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
	}
	
	.ux-row-left {
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
	}
	
	.ux-row-right {
		display: flex;
		flex-direction: row;
		justify-content: flex-end;
		align-items: center;
	}
	
	.ux-row-center {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
	}
</style>