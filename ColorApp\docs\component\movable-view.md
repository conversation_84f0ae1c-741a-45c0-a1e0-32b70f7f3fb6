<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/movable-view?title=Movable-view"></Mobile>

# Movable-view
> 组件类型：UxMovableViewComponentPublicInstance

可移动的视图容器，在页面中可以拖拽滑动

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| [direction](#direction) | String | all | movable-view的移动方向 |
| [inertia](#inertia) | Boolean | false | movable-view是否带有惯性 |
| [outOfBounds](#outOfBounds) | Boolean | false | 超过可移动区域后，movable-view是否还可以移动 |
| x | Number |  | 定义x轴方向的偏移，如果x的值不在可移动范围内，会自动移动到可移动范围；改变x的值会触发动画 |
| y | Number |  | 定义y轴方向的偏移，如果y的值不在可移动范围内，会自动移动到可移动范围；改变y的值会触发动画 |
| damping | Number | 20 | 阻尼系数，用于控制x或y改变时的动画和过界回弹的动画，值越大移动越快 |
| friction | Number | 2 | 摩擦系数，用于控制惯性滑动的动画，值越大摩擦力越大，滑动越快停止；必须大于0，否则会被设置成默认值 |
| [scale](#scale) | Boolean | false | 是否支持双指缩放，默认缩放手势生效区域是在movable-view内 |
| scaleMin | Number | 0.5 | 定义缩放倍数最小值 |
| scaleMax | Number | 10 | 定义缩放倍数最大值 |
| scaleValue | Number |  | 定义缩放倍数 |
| [animation](#animation) | Boolean | true | 是否使用动画 |
| disabled | Boolean | false | 是否禁用 |

### [direction](#direction)

| 值   | 说明 |
|:------:|:----:|
| all
 |  |
| vertical
 |  |
| horizontal
 |  |
| none
 |  |

### [inertia](#inertia)

| 值   | 说明 |
|:------:|:----:|
| true
 |  |
| false
 |  |

### [outOfBounds](#outOfBounds)

| 值   | 说明 |
|:------:|:----:|
| true
 |  |
| false
 |  |

### [scale](#scale)

| 值   | 说明 |
|:------:|:----:|
| true
 |  |
| false
 |  |

### [animation](#animation)

| 值   | 说明 |
|:------:|:----:|
| true
 |  |
| false
 |  |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| change | 拖动过程中触发的事件，event.detail={x:x,y:y,source:source}，其中source表示产生移动的原因，值可为touch(拖动)、touch-out-of-bounds(超出移动范围)、out-of-bounds(超出移动范围后的回弹)、friction(惯性)和空字符串(setData) |  |
| scale | 缩放过程中触发的事件，event.detail={x:x,y:y,scale:scale} |  |
| htouchmove | 初次手指触摸后移动为横向的移动，如果catch此事件，则意味着touchmove事件也被catch |  |
| vtouchmove | 初次手指触摸后移动为纵向的移动，如果catch此事件，则意味着touchmove事件也被catch |  |
  
  