import { BuilderNode } from "@kit.ArkUI"
import { buildLottieView } from "./builder.ets"

interface UxLottieOptions {
    src : string,
}

export class UxLottie {
	
	private $element : UniNativeViewElement;
	private builder : BuilderNode<[UxLottieOptions]> | null = null
	
	private params : UxLottieOptions = {
	    src: '',
	}
	
	constructor(element : UniNativeViewElement) {
		this.builder = element.bindHarmonyWrappedBuilder(wrapBuilder<[UxLottieOptions]>(buildLottieView), this.params)
		this.$element = element
		this.$element.bindHarmonyController(this)
	}
	
	play(src: string) {
		if(src == '') {
			return
		}
		
		this.params.src = UTSHarmony.getResourcePath(src)
		this.builder?.update(this.params)
	}
	
	stop() {
		
	}
	
	pause() {
		
	}
	
	resume() {
		
	}
	
	destroy() {
		
	}
	
	setDirection(speed: number) {
		
	}
	
	setRepeatCount(count: number) {
		
	}
	
	loop(loop: boolean) {
		
	}
}