<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/unimoduleUxMqtt-Swift.h</key>
		<data>
		ug+5IRAImiCcdAT5JVYyxpXgAjE=
		</data>
		<key>Headers/unimoduleUxMqtt.h</key>
		<data>
		FH2SRnQAHIO8AbDZPa5/Bz/CcdA=
		</data>
		<key>Info.plist</key>
		<data>
		Ug7lktG4lklb9TVqDVdnRzMlJwI=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		+URfL/UMU2bzb3BwZmVsZDYoDZc=
		</data>
		<key>Modules/unimoduleUxMqtt.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		JplX+10HwT1x3BAO9T/seeY1yq4=
		</data>
		<key>Modules/unimoduleUxMqtt.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		Rziv1R5PsOiTaEgfVrABcHBLB4o=
		</data>
		<key>Modules/unimoduleUxMqtt.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		qkk28etdxpOFehOkC/gWOe38nIg=
		</data>
		<key>Modules/unimoduleUxMqtt.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		N4DuLUQzNEgWo2vAbjgF8o3H3wc=
		</data>
		<key>Modules/unimoduleUxMqtt.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		qkk28etdxpOFehOkC/gWOe38nIg=
		</data>
		<key>Modules/unimoduleUxMqtt.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		m6OfU1t7ce6+VPiKWun45fHTvC8=
		</data>
		<key>config.json</key>
		<data>
		jaOtxu0sD30wsRab+BFU4b8qkAU=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/unimoduleUxMqtt-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			NjllWDblNow5hPlrKssDRUWsQX2/1WANe0hrzXWXTuM=
			</data>
		</dict>
		<key>Headers/unimoduleUxMqtt.h</key>
		<dict>
			<key>hash2</key>
			<data>
			XAsze1m7Z4PhyI2gj+RafZVS4xyqWjgvqraTawdVPMY=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			+UDv4PM6i+ZqnkabB5N67QPduXtIRRZyW9JRkQTSFhE=
			</data>
		</dict>
		<key>Modules/unimoduleUxMqtt.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			fdG2If1M0nMjbaHIrS4pyc28QtrRg3un3BvjuewsXoY=
			</data>
		</dict>
		<key>Modules/unimoduleUxMqtt.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			broWIGIREm2FtIbpXhiwYatJGkzCxbrdg0k/hPeiK6Q=
			</data>
		</dict>
		<key>Modules/unimoduleUxMqtt.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			A+7kFGr4/DwpQkYDB9hn8jY6OuXw9m9KGVRk6r3pDl8=
			</data>
		</dict>
		<key>Modules/unimoduleUxMqtt.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			nqTsIpAuBd1GnnM9G6A0l8cFegzMv7ipvYWzW3QcNWA=
			</data>
		</dict>
		<key>Modules/unimoduleUxMqtt.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			A+7kFGr4/DwpQkYDB9hn8jY6OuXw9m9KGVRk6r3pDl8=
			</data>
		</dict>
		<key>Modules/unimoduleUxMqtt.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			/5IRyRDXhdhXo6C8U9yxGIGSTnEqqKmnfqqCrSfzfDE=
			</data>
		</dict>
		<key>config.json</key>
		<dict>
			<key>hash2</key>
			<data>
			SVNcylxHfDF8jOBSDZeOuk2sOhB9gRzwW+xaXMf0DCc=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
