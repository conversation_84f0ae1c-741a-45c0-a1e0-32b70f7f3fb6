<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" xmlns:tools="http://schemas.android.com/tools" package="uts.sdk.modules.uxWeixin">
	
	<uses-permission android:name="android.permission.INTERNET" />
	<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
	<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
	
	<queries>
		<package android:name="com.tencent.mm" />
	</queries>
	
	<!-- ${applicationId} -->
	
	<application>

		<activity
		    android:name="uts.sdk.modules.uxWeixin.WXEntryActivity"
		    android:theme="@android:style/Theme.Translucent.NoTitleBar"
		    android:exported="true"
		    android:taskAffinity="com.chunge.suanmi"
		    android:launchMode="singleTask">
		</activity>
		
		<activity-alias
		    android:name="com.chunge.suanmi.wxapi.WXEntryActivity"
		    android:exported="true"
		    android:targetActivity="uts.sdk.modules.uxWeixin.WXEntryActivity"
			tools:replace="android:targetActivity">
			<intent-filter>
			    <action android:name="android.intent.action.VIEW"/>
			    <category android:name="android.intent.category.DEFAULT"/>
			    <data android:scheme="wx"/>
			</intent-filter>
		</activity-alias>
		
		<activity
		    android:name="uts.sdk.modules.uxWeixin.WXPayEntryActivity"
		    android:theme="@android:style/Theme.Translucent.NoTitleBar"
		    android:exported="true"
		    android:taskAffinity="com.chunge.suanmi"
		    android:launchMode="singleTask">
		</activity>
		
		<activity-alias
		    android:name="com.chunge.suanmi.wxapi.WXPayEntryActivity"
		    android:exported="true"
		    android:targetActivity="uts.sdk.modules.uxWeixin.WXPayEntryActivity"
			tools:replace="android:targetActivity">
			<intent-filter>
			    <action android:name="android.intent.action.VIEW"/>
			    <category android:name="android.intent.category.DEFAULT"/>
			    <data android:scheme="wx"/>
			</intent-filter>
		</activity-alias>
		
		<provider
		    android:name="uts.sdk.modules.uxWeixin.UxWeixinFileProvider"
		    android:authorities="com.chunge.suanmi.uxWeixin.fileprovider"
		    android:exported="false"
		    android:grantUriPermissions="true"
			tools:replace="android:authorities">
		    <meta-data
		        android:name="android.support.FILE_PROVIDER_PATHS"
		        android:resource="@xml/file_provider"
		    	tools:replace="android:resource"/>
		</provider>
		
	</application>
</manifest>