import { UxRegisterOptions, UxLoginOptions, UxShareOptions, UxPayOptions } from '../interface.uts'
import { Weixin, WXEntryActivity, WXPayEntryActivity } from './src/Weixin.uts'

/**
 * 导出微信登录Activity
 */
export const wxEntryActivity: WXEntryActivity | null = null;

/**
 * 导出微信支付Activity
 */
export const wXPayEntryActivity: WXPayEntryActivity | null = null;

/**
 * 微信
 */
let weixin = new Weixin()

/**
* 注册
* @param {UxRegisterOptions} options
* @tutorial https://www.uxframe.cn/api/weixin.html#register
*/
export const register = function (options : UxRegisterOptions) {
	weixin.register(options)
}

/**
* 登录
* @param {UxLoginOptions} options
* @tutorial https://www.uxframe.cn/api/weixin.html#login
*/
export const login = function (options : UxLoginOptions) {
	weixin.login(options)
}

/**
* 分享
* @param {UxShareOptions} options
* @tutorial https://www.uxframe.cn/api/weixin.html#share
*/
export const share = function (options : UxShareOptions) {
	weixin.share(options)
}

/**
* 请求支付
* @param {UxPayOptions} options
* @tutorial https://www.uxframe.cn/api/weixin.html#requestPayment
*/
export const requestPayment = function (options : UxPayOptions) {
	weixin.requestPayment(options)
}

/**
* 打开小程序
* @param {UxShareOptions} options
* @tutorial https://www.uxframe.cn/api/weixin.html#openMiniProgram
*/
export const openMiniProgram = function (options : UxShareOptions) {
	weixin.openMiniProgram(options)
}

/**
* 打开微信app
* @tutorial https://www.uxframe.cn/api/weixin.html#openApp
*/
export const openApp = function () {
	weixin.openApp()
}

/**
* 微信app是否安装
* @tutorial https://www.uxframe.cn/api/weixin.html#isInstalled
*/
export const isInstalled = function () : boolean {
	return weixin.isInstalled()
}