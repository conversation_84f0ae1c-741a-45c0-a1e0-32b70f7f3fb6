<template>
	<ux-drawer ref="uxDrawerRef" direction="top" :height="`${pickerHeight}px`" :touchable="touchable" :show-blur="showBlur" :blur-radius="blurRadius" :blur-color="blurColor" :opacity="opacity" :maskClose="maskClose" :border="false" @close="onMaskClose">
		<view @click="open()">
			<slot></slot>
		</view>

		<template v-slot:drawer>
			<view :id="myId" class="ux-actionsheet">
				<view class="ux-actionsheet__body" :style="style">
					<view v-if="title != ''" class="ux-actionsheet__title" :style="titleStyle">
						<text class="ux-actionsheet__title--text" :style="titleTextStyle">{{ title }}</text>
					</view>
					<scroll-view class="ux-actionsheet__menus" direction="vertical">
						<view class="ux-actionsheet__menu" :style="menuStyle(menu, index)" v-for="(menu, index) in menus" :key="index" hover-class="hover" :hover-start-time="100" @click="click(menu, index)">
							<ux-icon v-if="(menu.icon ?? '') != ''" :mr="5" :type="menu.icon!" :size="menu.iconSize ?? 16" :color="menu.iconColor ?? ''"></ux-icon>
							<text class="ux-actionsheet__menu--text" :style="menuTextStyle(menu)">{{ menu.text }}</text>
						</view>
					</scroll-view>
					<view v-if="showCancel" class="ux-actionsheet__cancel" :style="cancelStyle" hover-class="hover" :hover-start-time="100" @click="cancel()">
						<text class="ux-actionsheet__cancel--text" :style="cancelTextStyle">{{ cancelText }}</text>
					</view>
					<view v-if="showClose" class="ux-actionsheet__close" @click="close()">
						<ux-icon type="closecircle-outline" :color="titleColor" :size="24"></ux-icon>
					</view>
				</view>
			</view>
		</template>
	</ux-drawer>
</template>

<script setup lang="ts">
	
	/**
	 * Actionsheet 底部操作栏
	 * @description 支持菜单自定义样式
	 * @demo pages/component/actionsheet.uvue
	 * @tutorial https://www.uxframe.cn/component/actionsheet.html
	 * @property {Array}			menus												UxActionsheetItem[] | 菜单
	 * @property {String}			theme=[text|info|primary|success|warning|error]		String | 主题 (默认 primary)
	 * @value text			文字
	 * @value info   		默认
	 * @value primary   	主要
	 * @value success  		成功
	 * @value warning   	警告
	 * @value error  		错误
	 * @property {String}			title									String | 标题
	 * @property {Any}				titleSize								Any | 标题大小 (默认 15)
	 * @property {String}			titleColor								String | 标题颜色 (默认 #999 )
	 * @property {String} 			titleDarkColor=[none|auto|color]		String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Boolean}			titleBold								Boolean | 标题加粗 (默认 false )
	 * @property {Any}				menuSize								Any | 按钮文字大小 (默认 15)
	 * @property {String}			menuColor								String | 按钮文字颜色 (默认 #333 )
	 * @property {String} 			menuDarkColor=[none|auto|color]			String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {String}			cancelText								String | 取消按钮文字 (默认 取消 )
	 * @property {Any}				cancelSize								Any | 取消按钮文字大小 (默认 15)
	 * @property {String}			cancelColor								String | 取消按钮文字颜色 (默认 #999 )
	 * @property {String} 			cancelDarkColor=[none|auto|color]		String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {String}			background								String | 背景颜色
	 * @property {String} 			backgroundDark=[none|auto|color]		String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Boolean}			showClose = [true|false]				Boolean | 自动关闭 (默认 false )
	 * @property {Boolean}			showCancel = [true|false]				Boolean | 点击关闭 (默认 true )
	 * @property {Boolean}			showBlur = [true|false]					Boolean | 开启模糊背景 (默认 false )
	 * @property {Number}			blurRadius								Number | 模糊半径 (默认 10 )
	 * @property {String}			blurColor								String | 模糊颜色  (默认 rgba(10, 10, 10, 0.3) )
	 * @property {Any}				maxHeight								Any | 最大高度 (默认 500)
	 * @property {Any}				radius									Any | 圆角 (默认 20)
	 * @property {Any}				space									Any | 间隙 (默认 15)
	 * @property {Number}			opacity									Number | 遮罩透明度 0-1 (默认 $ux.Conf.maskAlpha)
	 * @property {Boolean}			touchable=[true|false]					Boolean | 允许滑动关闭 (默认 false)
	 * @value true	
	 * @value false	
	 * @property {Boolean}			maskClose=[true|false]					Boolean | 遮罩层关闭 (默认 true)
	 * @value true	
	 * @value false	
	 * @property {Boolean}			disabled=[true|false]					Boolean | 是否禁用 (默认 false)
	 * @value true
	 * @value false
	 * @event {Function}			click									Function | 按钮点击时触发
	 * @event {Function}			cancel									Function | 取消按钮点击时触发
	 * @event {Function}			close									Function | 关闭按钮点击时触发发
	 * <AUTHOR>
	 * @date 2024-12-04 12:36:15
	 */
	
	import { PropType, computed, getCurrentInstance, ref } from 'vue'
	import { $ux } from '../../index'
	import { UxActionsheetItem } from '../../libs/types/types.uts'
	import { useForegroundColor, useFontColor, useBorderColor } from '../../libs/use/style.uts'
	
	defineOptions({
		name: 'ux-actionsheet'
	})
	
	const emit = defineEmits(['cancel', 'close', 'click'])
	
	const props = defineProps({
		menus: {
			type: Array as PropType<UxActionsheetItem[]>,
			default: () : UxActionsheetItem[] => [] as UxActionsheetItem[]
		},
		theme: {
			type: String,
			default: 'primary'
		},
		title: {
			type: String,
			default: ''
		},
		titleSize: {
			default: 15
		},
		titleColor: {
			type: String,
			default: '#999'
		},
		titleDarkColor: {
			type: String,
			default: '#fff'
		},
		titleBold: {
			type: Boolean,
			default: false
		},
		menuSize: {
			default: 15
		},
		menuColor: {
			type: String,
			default: '#333'
		},
		menuDarkColor: {
			type: String,
			default: 'auto'
		},
		cancelText: {
			type: String,
			default: '取消'
		},
		cancelSize: {
			default: 15
		},
		cancelColor: {
			type: String,
			default: '#999'
		},
		cancelDarkColor: {
			type: String,
			default: '#f0f0f0'
		},
		background: {
			type: String,
			default: ''
		},
		backgroundDark: {
			type: String,
			default: ''
		},
		showClose: {
			type: Boolean,
			default: false
		},
		showCancel: {
			type: Boolean,
			default: true
		},
		maxHeight: {
			default: 500
		},
		radius: {
			default: 20
		},
		space: {
			default: 15
		},
		opacity: {
			type: Number,
			default: -1
		},
		touchable: {
			type: Boolean,
			default: false
		},
		showBlur: {
			type: Boolean,
			default: false
		},
		blurRadius: {
			type: Number,
			default: 10
		},
		blurColor: {
			type: String,
			default: 'rgba(10, 10, 10, 0.3)'
		},
		maskClose: {
			type: Boolean,
			default: true
		},
		disabled: {
			type: Boolean,
			default: false
		}
	})
	
	const uxDrawerRef = ref<UxDrawerComponentPublicInstance | null>(null)
	const myId = `ux-actionsheet-${$ux.Random.uuid()}`
	
	const pickerHeight = ref(0)
	
	const backgroundColor = computed((): string => {
		return useForegroundColor(props.background, props.backgroundDark)
	})
	
	const titleColor = computed((): string => {
		return useForegroundColor(props.titleColor, props.titleDarkColor)
	})
	
	const borderColor = computed((): string => {
		return useBorderColor('#f0f0f0', '#444')
	})
	
	const menuColor = computed((): string => {
		return useFontColor(props.menuColor, props.menuDarkColor)
	})
	
	const cancelColor = computed((): string => {
		return useFontColor(props.cancelColor, props.cancelDarkColor)
	})
	
	const style = computed(() => {
		let css = {}
		
		css['background-color'] = backgroundColor.value
		css['border-radius'] = $ux.Util.addUnit(props.radius)
		
		css['margin'] = $ux.Util.addUnit(props.space)
		
		if($ux.Util.getPx(props.maxHeight) > 0) {
			css['max-height'] = $ux.Util.addUnit(props.maxHeight)
		}
		
		return css
	})
	
	const titleStyle = computed(() => {
		let css = {}
		
		css['border-bottom'] = `5px solid ${borderColor.value}`
		
		return css
	})
	
	const titleTextStyle = computed(() => {
		let css = {}
		
		css['font-size'] = $ux.Util.addUnit(props.titleSize)
		css['color'] = titleColor.value
		css['font-weight'] = props.titleBold ? 'bold' : 'normal'
		
		return css
	})
	
	const cancelStyle = computed(() => {
		let css = {}
		
		css['border-top'] = `5px solid ${borderColor.value}`
		
		return css
	})
	
	const cancelTextStyle = computed(() => {
		let css = {}
		
		css['font-size'] = $ux.Util.addUnit(props.cancelSize)
		css['color'] = cancelColor.value
		
		return css
	})
	
	function menuStyle(menu: UxActionsheetItem, index: number) {
		let css = {}
		
		if(index < props.menus.length - 1) {
			css['border-bottom'] = `1px solid ${borderColor.value}`
		}
		
		// #ifdef WEB
		css['cursor'] = (menu.disabled ?? false) ? 'not-allowed' : 'pointer'
		// #endif
		
		return css
	}
	
	function menuTextStyle(menu: UxActionsheetItem) {
		let css = {}
		
		css['font-size'] = $ux.Util.addUnit((menu.fontSize ?? 0) > 0 ? menu.fontSize! : props.menuSize)
		css['font-weight'] = (menu.bold ?? false) ? 'bold' : 'normal'
		css['color'] = (menu.fontColor ?? '') != '' ? menu.fontColor! : menuColor.value
		
		if((menu.disabled ?? false)) {
			css['opacity'] = 0.5
		}
		
		return css
	}
	
	function cancel() {
		uxDrawerRef.value!.close()
		emit('cancel')
	}
	
	function close() {
		uxDrawerRef.value!.close()
		emit('close')
	}
	
	function onMaskClose() {
		emit('close')
	}
	
	function open() {
		if (props.disabled) {
			return
		}
	
		let f = async () => {
			let rect = await $ux.Util?.getBoundingClientRect(`#${myId}`, getCurrentInstance()?.proxy)
			pickerHeight.value = rect?.height ?? 0
		}
	
		setTimeout(() => {
			uxDrawerRef.value!.open()
			setTimeout(() => {
				f()
			}, 50);
		}, 100)
	}
	
	function click(menu: UxActionsheetItem, index: number) {
		if(menu.disabled ?? false) {
			return
		}
		
		uxDrawerRef.value!.close()
		emit('click', index)
	}
	
	defineExpose({
		open,
		close
	})
</script>

<style lang="scss" scoped>
	/* #ifdef H5 */
	uni-view {
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: stretch;
		align-content: stretch;
		justify-content: flex-start;
		box-sizing: border-box;
		min-height: 0px;
		min-width: 0px;
		overflow: hidden;
	}
	/* #endif */
	
	.ux-actionsheet {
		position: relative;
		width: 100%;
		display: flex;
		flex-direction: column;
		
		&__body {
			flex: 1;
			min-height: 240px;
			border-radius: 20px;
			background-color: white;
			position: relative;
		}
		
		&__title {
			width: 100%;
			height: 50px;
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
			
			&--text {
				font-size: 15px;
			}
		}
		
		&__menus {
			flex: 1;
			width: 100%;
			height: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;
		}
		
		&__menu {
			margin-left: 5%;
			width: 90%;
			min-height: 30px;
			padding: 8px 0;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
			
			&--text {
				font-size: 14px;
			}
		}
		
		&__cancel {
			/* #ifdef WEB */
			cursor: pointer;
			/* #endif */
			width: 100%;
			height: 50px;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
			
			&--text {
				font-size: 14px;
			}
		}
		
		&__close {
			/* #ifdef WEB */
			cursor: pointer;
			/* #endif */
			position: absolute;
			top: 0;
			right: 0;
			padding: 10px;
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
		}
	}
	
	.hover {
		opacity: 0.5;
	}
</style>