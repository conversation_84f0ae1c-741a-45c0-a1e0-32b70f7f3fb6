import * as db from '@/uni_modules/ux-sqlite'
import { ColumnType } from "./column";
import { WhereWrap } from "./where";
import { toDate } from './utils'

/**
 * 字段映射
 */
export class Column {
	
	static string(name: string): ColumnType {
		return new ColumnType(name, 'Text', 'string')
	}
	
	static boolean(name: string): ColumnType {
		return new ColumnType(name, 'Boolean', 'boolean')
	}
	
	static int(name: string): ColumnType {
		return new ColumnType(name, 'Integer', 'int')
	}
	
	static double(name: string): ColumnType {
		return new ColumnType(name, 'Double', 'double')
	}
	
	static float(name: string): ColumnType {
		return new ColumnType(name, 'Float', 'float')
	}
	
	static long(name: string): ColumnType {
		return new ColumnType(name, 'Long', 'long')
	}
	
	static short(name: string): ColumnType {
		return new ColumnType(name, 'Short', 'short')
	}
	
	static blob(name: string): ColumnType {
		return new ColumnType(name, 'ByteArray', 'blob')
	}
	
	static timestamp(name: string): ColumnType {
		return new ColumnType(name, 'Long', 'timestamp')
	}
	
	static datetime(name: string): ColumnType {
		return new ColumnType(name, 'Text', 'datetime')
	}
	
	static json(name: string): ColumnType {
		return new ColumnType(name, 'Text', 'json')
	}
}

/**
 * 表列
 */
export class TableColumn {

	public constructor() {

	}

	/**
	 * 获取派生类字段
	 */
	keys() : string[] {
		let keys : string[] = []

		// #ifdef APP-ANDROID
		let fields = UTSAndroid.getJavaClass(this).getDeclaredFields()
		
		for (let i = 0; i < fields.size; i++) {
			let field = fields[i as Int]
			field.setAccessible(true)
			keys.push(field.getName())
		}
		// #endif
		
		// #ifndef APP-ANDROID
		keys = UTSJSONObject.keys(this)
		// #endif

		return keys
	}

	/**
	 * 获取派生类ColumnType
	 */
	column(key : string) : ColumnType {
		// #ifdef APP-ANDROID
		let field = UTSAndroid.getJavaClass(this).getDeclaredField(key)
		field.setAccessible(true)
		
		return field.get(this) as ColumnType
		// #endif
		
		// #ifndef APP-ANDROID
		return this[key] as ColumnType
		// #endif
	}
	
	/**
	 * 列
	 */
	warpColumns(): UTSJSONObject {
		let columns: UTSJSONObject = {}
		
		let keys = this.keys()
		keys.forEach(key => {
			let col = this.column(key)
			columns[key] = {
				'type': col.getUtsType(),
				'name': col.getName()
			} as UTSJSONObject
		})
		
		return columns
	}
}

export class Table {
	
	/**
	 * 是否初始化
	 */
	private isInit : boolean = false

	/**
	 * 表名称
	 */
	name : string = ''
	/**
	 * 表结构
	 */
	columns : TableColumn = new TableColumn()
	/**
	 * And 条件
	 */
	private wheres : WhereWrap[] = []
	/**
	 * Or 条件
	 */
	private ors : WhereWrap[] = []
	/**
	 * 查询字段
	 */
	private selects : string[] = []
	/**
	 * 查询字段去重
	 */
	private isDistinct : boolean = false
	/**
	 * 分组
	 */
	private groupByColumn : string = ''
	/**
	 * 分组条件
	 */
	private havings : WhereWrap[] = []
	/**
	 * 排序
	 */
	private orders : string[] = []
	/**
	 * 分页
	 */
	private offset : number = 0
	private limit : number = 0
	private pageNo: number = 0
	/**
	 * 分页信回调
	 */
	private pageInfoCallback: ((pageInfo: PageInfo) => void) | null = null
	/**
	 * 转json
	 */
	private toJson: boolean = false

	public constructor() {
		if(!db.isOpen()) {
			console.error('[ux-orm] db未初始化')
		}
	}
	
	/**
	 * 构建 table
	 */
	private buildTable() : boolean {
		if (!this.isInit) {
			let columns = this.buildColumns()
			if (columns == null) {
				console.error('[ux-orm]', 'table ' + this.name + ' columns is empty');
				return false
			}
			
			db.execSQL({
				sql: 'CREATE TABLE IF NOT EXISTS ' + this.name + ' (' + columns + ')'
			} as db.ExecSQLOptions)
			
			this.upgrade()
			this.isInit = true
		}

		return true
	}
	
	/**
	 * 升级表
	 * 只支持新增列，系统不支持删除列，如要删除请删表重建
	 */
	upgrade() {
		let dbColumns = db.getColumns({
			table: this.name
		} as db.GetColumnsOptions)
		
		let columns = this.columns.keys()
		for (let i = 0; i < columns.length; i++) {
			let k = columns[i]
			
			let column = this.columns.column(k)
			if(dbColumns.indexOf(column.getName()) == -1) {
				this.addColumn(column)
			}
		}
	}

	/**
	 * 删除表
	 */
	drop() : boolean {
		db.execSQL({
			sql: 'DROP TABLE IF EXISTS ' + this.name
		} as db.ExecSQLOptions)
		
		return true
	}
	
	/**
	 * 新增列
	 * @param column 列
	 */
	addColumn(column: ColumnType) : boolean {
		db.execSQL({
			sql: 'ALTER TABLE ' + this.name + ' ADD COLUMN ' + column.getColumn()
		} as db.ExecSQLOptions)
		
		return true
	}

	/**
	 * 列
	 */
	warpColumns(): UTSJSONObject {
		return this.columns.warpColumns()
	}

	/**
	 * 构建表结构
	 */
	private buildColumns() : string | null {
		let columns : string[] = []

		let keys = this.columns.keys()

		for (let i = 0; i < keys.length; i++) {
			let column = this.columns.column(keys[i])
			columns.push(column.getColumn())
		}

		if (columns.length == 0) {
			return null
		}

		columns.sort((a, b) : number => {
			let n1 = a.indexOf('PRIMARY KEY') != -1 ? 1 : 0
			let n2 = b.indexOf('PRIMARY KEY') != -1 ? 1 : 0
			return n2 - n1
		})

		return columns.join(', ')
	}

	/**
	 * 构建键值
	 */
	private buildContentValues(table : Table) : UTSJSONObject | null {
		let values : UTSJSONObject = {}

		let keys = this.columns.keys()
		for (let i = 0; i < keys.length; i++) {
			let column = this.columns.column(keys[i])

			// #ifdef APP-ANDROID
			let field = UTSAndroid.getJavaClass(table).getDeclaredField(keys[i])
			field.setAccessible(true)
			
			let value = field.get(table)
			if (value != null) {
				values.set(column.getName(), value)
			}
			// #endif
			
			// #ifndef APP-ANDROID
			let value = table[keys[i]]
			if (value != null) {
				if(value instanceof ArrayBuffer) {
					values.set(column.getName(), value)
				} else {
					values.set(column.getName(), value)
				}
			}
			// #endif
		}

		return values
	}

	/**
	 * 构建查询条件
	 */
	private buildWhereSql() : string | null {
		let wheres : string[] = []
		for (var i = 0; i < this.wheres.length; i++) {
			wheres.push(this.wheres[i].getWhereSql())
		}

		let ors : string[] = []
		for (var i = 0; i < this.ors.length; i++) {
			ors.push(this.ors[i].getWhereSql())
		}

		if (wheres.length > 0 && ors.length > 0) {
			return `(${wheres.join(' AND ')}) or (${ors.join(' AND ')})`
		} else if (wheres.length > 0 && ors.length == 0) {
			return `${wheres.join(' AND ')}`
		} else if (wheres.length == 0 && ors.length > 0) {
			return `${ors.join(' AND ')}`
		}

		return null
	}
	
	/**
	 * 构建and条件
	 */
	private buildWhere() : db.UxSqlWhere[] | null {
		if(this.wheres.length > 0) {
			let wheres : db.UxSqlWhere[] = []
			for (var i = 0; i < this.wheres.length; i++) {
				wheres.push(this.wheres[i].getWhere())
			}
			
			return wheres
		}
	
		return null
	}

	/**
	 * 构建or条件
	 */
	private buildOr() : db.UxSqlWhere[] | null {
		if(this.ors.length > 0) {
			let ors : db.UxSqlWhere[] = []
			for (var i = 0; i < this.ors.length; i++) {
				ors.push(this.ors[i].getWhere())
			}
			
			return ors
		}
	
		return null
	}
	
	/**
	 * 构建查询字段
	 */
	private buildSelects() : string[] | null {
		if (this.selects.length > 0) {
			return this.selects
		}

		return null
	}
	
	/**
	 * 构建分组
	 */
	private buildGroupBy() : string | null {
		if (this.groupByColumn != '') {
			return this.groupByColumn
		}

		return null
	}

	/**
	 * 构建分组条件
	 */
	private buildHaving() : string | null {
		if (this.havings.length > 0) {
			let wheres : string[] = []
			for (var i = 0; i < this.havings.length; i++) {
				wheres.push(this.havings[i].getWhereSql())
			}

			return `${wheres.join(' AND ')}`
		}

		return null
	}

	/**
	 * 构建排序
	 */
	private buildOrderBy() : string | null {
		if (this.orders.length > 0) {
			return this.orders.join(', ')
		}

		return null
	}

	/**
	 * 构建limit
	 */
	private buildLimit() : string | null {
		if (this.limit > 0) {
			return this.limit.toString() + ' OFFSET ' + this.offset.toString()
		}

		return null
	}

	/**
	 * 构建PageInfo
	 */
	private buildPageInfo() {
		if(this.pageInfoCallback != null) {
			let totalCount = this.count()
			let totalPages = Math.ceil(totalCount / this.limit)
			
			let pageInfo: PageInfo = {
				pageNo: this.pageNo,
				pageSize: this.limit,
				totalCount: totalCount,
				totalPages: totalPages,
				hasPreviousPage: totalCount > 0 && this.pageNo > 1,
				hasNextPage: this.pageNo < totalPages - 1
			}
			
			this.pageInfoCallback?.(pageInfo)
		}
	}

	/**
	 * 数据库存储位置
	 * @returns 数据库本地路径
	 */
	path() : string {
		return db.getPath()
	}

	/**
	 * 是否打开数据库
	 * @returns 数据库打开状态
	 */
	isOpen() : boolean | null {
		return db.isOpen()
	}

	/**
	 * 查询字段
	 * @param columns 字段列表
	 */
	select(columns : string[]) : Table {
		this.selects = columns
		this.isDistinct = false
		return this
	}

	/**
	 * 查询字段 去重
	 * @param columns 字段列表
	 */
	selectDistinct(columns : string[]) : Table {
		this.selects = columns
		this.isDistinct = true
		return this
	}

	/**
	 * And 条件
	 * @param condition 条件
	 */
	where(condition : WhereWrap) : Table {
		this.wheres.push(condition)
		return this
	}

	/**
	 * OR 条件
	 * @param condition 条件
	 */
	or(condition : WhereWrap) : Table {
		this.ors.push(condition)
		return this
	}

	/**
	 * 分组
	 * @param column 分组字段
	 */
	groupBy(column : string) : Table {
		this.groupByColumn = column
		return this
	}

	/**
	 * 分组后条件
	 * @param condition 条件
	 */
	having(condition : WhereWrap) : Table {
		this.havings.push(condition)
		return this
	}

	/**
	 * 排序
	 * @param order 排序方式
	 */
	orderBy(order : string) : Table {
		this.orders.push(order)
		return this
	}

	/**
	 * 分页
	 * @param {Number} pageNo 页码
	 * @param {Number} pageSize 页面大小
	 */
	paging(pageNo : number, pageSize : number) : Table {
		this.limit = Math.max(pageSize, 0)
		this.pageNo = Math.max(pageNo, 0)
		this.offset = this.limit * this.pageNo
		return this
	}
	
	/**
	 * 分页信息回调
	 */
	pagingInfo(callback: (pageInfo: PageInfo) => void) : Table {
		this.pageInfoCallback = callback
		return this
	}
	
	/**
	 * 转json
	 */
	fromJson() : Table  {
		this.toJson = true
		return this
	}
	
	/**
	 * 转json数据
	 */
	toJsonDatas(datas: Array<Map<string, any | null>>) : UTSJSONObject[]  {
		let rows = [] as UTSJSONObject[]
		
		datas.forEach((data: Map<string, any | null>) => {
			let row: UTSJSONObject = {}
			
			// #ifdef APP-ANDROID
			data.forEach((d, k) => {
				row[k] = d
			})
			// #endif
			
			// #ifndef APP-ANDROID
			let keys = Object.keys(data)
			keys.forEach((k) => {
				row[k] = data[k]
			})
			// #endif
			
			rows.push(row)
		})
		
		return rows
	}
	
	/**
	 * 事务
	 * @param {Function} operations 事务函数，返回值 false 表示事务发生错误，将回滚
	 * @returns boolean false 表示事务发生错误，会自动回滚
	 */
	transaction(operations : () => boolean) : boolean {
		if (!this.buildTable()) return false
		
		return db.transaction({
			operations: operations
		} as db.ExecTransactionOptions)
	}

	/**
	 * 插入
	 * @returns 插入成功主键id，0表示插入失败
	 */
	insert() : number {
		if (!this.buildTable()) return 0

		return db.insert({
			table: this.name,
			content: this.buildContentValues(this),
			columns: this.warpColumns()
		} as db.ExecInsertOptions) as number
	}

	/**
	 * 批量插入
	 * @returns 批量插入成功的数量，0表示插入失败
	 */
	inserts(rows : Table[]) : number {
		if (!this.buildTable()) return 0

		let n = 0
		this.transaction((): boolean => {
			try {
				for (let i = 0; i < rows.length; i++) {
					db.insert({
						table: this.name,
						content: this.buildContentValues(rows.get(i)),
						columns: this.warpColumns()
					} as db.ExecInsertOptions)
					n++
				}
				return true
			} catch(err) {
				console.error('ux-orm', err);
				return false
			}
		})

		return n
	}

	/**
	 * 更新
	 * @returns 更新成功的数量，0表示更新失败
	 */
	update() : number {
		if (!this.buildTable()) return 0

		return db.update({
			table: this.name,
			content: this.buildContentValues(this),
			whereSql: this.buildWhereSql(),
			wheres: this.buildWhere(),
			ors: this.buildOr(),
			columns: this.warpColumns()
		} as db.ExecUpdateOptions) as number
	}

	/**
	 * 删除
	 * @returns 删除成功的数量，0表示删除失败
	 */
	delete() : number {
		if (!this.buildTable()) return 0
		
		return db.del({
			table: this.name,
			whereSql: this.buildWhereSql(),
			wheres: this.buildWhere(),
			ors: this.buildOr(),
		} as db.ExecDeleteOptions) as number
	}
	
	/**
	 * 行数
	 * @returns 满足条件的总行数
	 */
	count(): number {
		if (!this.buildTable()) return 0
		
		return db.count({
			table: this.name,
			whereSql: this.buildWhereSql(),
			wheres: this.buildWhere(),
			ors: this.buildOr(),
		} as db.ExecCountOptions) as number
	}

	/**
	 * 查询
	 * @returns 数据对象列表
	 */
	query<T>() : T[] {
		if (!this.buildTable()) return [] as T[]
		
		this.buildPageInfo()
		
		let results = db.query({
			table: this.name,
			distinct: this.isDistinct,
			selects: this.buildSelects(),
			whereSql: this.buildWhereSql(),
			wheres: this.buildWhere(),
			ors: this.buildOr(),
			groupBy: this.buildGroupBy(),
			having: this.buildHaving(),
			orderBy: this.buildOrderBy(),
			limit: this.buildLimit(),
			columns: this.warpColumns()
		} as db.ExecQueryOptions)
		return this.wrapDatas<T>(results)
	}

	/**
	 * 原生sql语句查询
	 * @param {String} sql 原生sql语句
	 * @returns 数据对象列表
	 */
	rawQuery<T>(sql : string) : T[] {
		if (!this.buildTable()) return [] as T[]
		
		let results = db.rawQuery({
			sql: sql,
			columns: this.warpColumns()
		} as db.ExecRawQueryOptions)
		
		return this.wrapDatas<T>(results)
	}
	
	/**
	 * 执行sql
	 * @param {String} sql 原生sql语句
	 */
	execSQL(sql: string) {
		db.execSQL({
			sql: sql
		} as db.ExecSQLOptions)
	}

	/**
	 * 包装数据集
	 */
	private wrapDatas<T>(datas : Array<Map<string, any | null>>) : T[] {
		let rows : T[] = []
		
		datas.forEach((data, k) => {
			if(this.toJson) {
				let row: UTSJSONObject = {}
				
				// #ifdef APP-ANDROID
				data.forEach((d, k) => {
					row[k] = d
				})
				// #endif
				
				// #ifndef APP-ANDROID
				let keys = Object.keys(data)
				keys.forEach((k) => {
					row[k] = data[k]
				})
				// #endif
				
				rows.push(row as T)
			} else {
				// #ifdef APP-ANDROID
				let clazz = UTSAndroid.getJavaClass(this) as Class<T>
				let instance = clazz.getConstructor().newInstance()
				
				data.forEach((d, k) => {
					let field = clazz.getDeclaredField(k)
					field.setAccessible(true)
					field.set(instance, d)
				})
				
				rows.push(instance as T)
				// #endif
				
				// #ifndef APP-ANDROID
				rows.push(data as T)
				// #endif
			}
		})
		
		return rows
	}
	
	/**
	 * toString
	 */
	override toString(): string {
		let values : UTSJSONObject = {}
		
		let keys = this.columns.keys()
		for (let i = 0; i < keys.length; i++) {
			// #ifdef APP-ANDROID
			let field = UTSAndroid.getJavaClass(this).getDeclaredField(keys[i])
			field.setAccessible(true)
			
			let value = field.get(this)
			// #endif
			
			// #ifndef APP-ANDROID
			let value = this[keys[i]]
			// #endif
			
			if (value instanceof Date) {
				values[keys[i]] = toDate(value)
			} else if (value instanceof ArrayBuffer) {
				values[keys[i]] = value.toByteBuffer()
			} else {
				values[keys[i]] = value
			}
		}
		
		return JSON.stringify(values)
	}
}

export type PageInfo = {
	/**
	 * 当前页码
	 */
	pageNo: number
	/**
	 * 每页大小
	 */
	pageSize: number
	/**
	 * 总记录数
	 */
	totalCount: number
	/**
	 * 总页数
	 */
	totalPages: number
	/**
	 * 是否有上一页
	 */
	hasPreviousPage: boolean
	/**
	 * 是否有下一页
	 */
	hasNextPage: boolean
}
