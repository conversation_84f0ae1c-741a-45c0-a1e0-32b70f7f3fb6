<template>
	<view class="ux-picker__item">
		<text class="ux-picker__text" :style="style">{{ text }}</text>
	</view>
</template>

<script setup>
	
	/**
	 * PickerItem 选择器子项
	 * @description 内部使用
	 * @demo pages/component/picker.uvue
	 * @tutorial https://www.uxframe.cn/component/picker.html
	 * @property {String}		text							String | 文本
	 * @property {Any}			size							Any | 字体大小
	 * @property {String}		color							String | 字体颜色
	 * @property {String}		selectColor						String | 选择颜色
	 * @property {Number}		index							Number | 下标
	 * @property {Number}		selectedIndex					Number | 已选择下标
	 * <AUTHOR>
	 * @date 2024-04-14 00:42:46
	 */
	
	import { $ux } from '../../index'
	import { useFontSize } from '../../libs/use/style.uts'
	
	defineOptions({
		name: 'ux-picker-item'
	})
	
	const props = defineProps({
		text: {
			type: String,
			default: ''
		},
		size: {
			default: 0
		},
		color: {
			type: String,
			default: ''
		},
		selectColor: {
			type: String,
			default: ''
		},
		index:{
			type: Number,
			default: 0
		},
		selectedIndex:{
			type: Number,
			default: 0
		}
	})
	
	const selected = computed(():boolean => {
		return props.selectedIndex == props.index 
	})
	
	const fontSize = computed(():number => {
		return useFontSize($ux.Util.getPx(props.size), 0)
	})
	
	const color = computed(():string => {
		return selected.value ? props.selectColor : props.color
	})
	
	const style = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('font-size', $ux.Util.addUnit(fontSize.value))
		css.set('font-weight', selected.value ? 'bold' : 'normal')
		css.set('color', color.value)
		
		return css
	})
</script>

<style lang="scss">
	.ux-picker__item {
		height: 45px;
	}
	
	.ux-picker__text {
		line-height: 45px;
		text-align: center;
		color: black;
	}
</style>