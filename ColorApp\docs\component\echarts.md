<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/echarts?title=Echarts"></Mobile>

# Echarts
> 组件类型：UxEchartsComponentPublicInstance

基于ECharts开发的图表组件库

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| option | UTSJSONObject |  | 图表配置数据 |
| showLoading | Boolean | true | 显示加载中 |
| loadingText | String | 加载中... | 加载中文案 |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| message | 回调消息 |  |
| loading | 加载中 |  |
| load | 加载完成 |  |
| error | 加载失败 |  |
  
  