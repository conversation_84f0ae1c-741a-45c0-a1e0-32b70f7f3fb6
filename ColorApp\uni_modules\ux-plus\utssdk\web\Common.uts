import { UxOpenURLOptions } from '../interface.uts'
import { UxMakePhoneCallOptions } from '../interface.uts'
import { UxVibrateOptions } from '../interface.uts'
import { UxOpenWebOptions } from '../interface.uts'
import { UxPlusSuccessCallbackImpl, UxPlusErrorCallbackImpl } from '../unierror.uts'
import * as OpenWeb from "./OpenWeb.uts"

export default class Common {
	
	hideKeyboard() {
		var inputs = document.querySelectorAll('input, textarea');
		
		for (var i = 0; i < inputs.length; i++) {
		  inputs[i].blur();
		}
	}
	
	setGray(gray: number) {
		document.body.style.filter = `grayscale(${gray * 100}%)`
	}
	
	openURL(options : UxOpenURLOptions) {
		window.location.href = options.url
		
		const res = new UxPlusSuccessCallbackImpl('openURL:success')
		options.success?.(res)
		options.complete?.(res)
	}
	
	openWeb(options : UxOpenWebOptions) {
		OpenWeb.show(options)
	}
	
	makePhoneCall(options : UxMakePhoneCallOptions) {
		window.location.href = 'tel:' + options.phoneNumber
	
		const res = new UxPlusSuccessCallbackImpl('makePhoneCall:ok')
		options.success?.(res)
		options.complete?.(res)
	}
	
	vibrate(options : UxVibrateOptions) {
		if ("vibrate" in navigator) {
			let duration = 100;
			if (options.type == 'heavy') {
				duration = 300;
			} else if (options.type == 'medium') {
				duration = 200;
			} else if (options.type == 'light') {
				duration = 100;
			}
			
			navigator.vibrate(duration);
			
			const res = new UxPlusSuccessCallbackImpl('vibrate:success')
			options.success?.(res)
			options.complete?.(res)
		} else {
			const err = new UxPlusErrorCallbackImpl('vibrate:failed')
			options.fail?.(err)
			options.complete?.(err)
		}
	}
}
