# 更新日志

## [1.0.3] - 2024-12-19

### 🔧 修复
- **自闭合标签检测逻辑优化**
  - 修复自闭合标签修复导致复杂内容无法正确渲染的问题
  - 严格检查 `/>` 组合，避免误判普通标签中的 `/` 字符
  - 确保只有真正的自闭合标签语法才被识别为自闭合标签

### 📝 技术细节
- 在检测 `/` 字符时，同时验证下一个字符是否为 `>`
- 避免将普通标签中的 `/` 字符误判为自闭合标签标识
- 保持对标准自闭合标签语法的完整支持

## [1.0.2] - 2024-12-19

### 🔧 修复
- **自闭合标签识别优化**
  - 修复 HtmlParser 未能正确识别以 `/>` 结尾的自闭合标签问题
  - 优化解析逻辑，支持标准的自闭合标签语法
  - 检测标签是否以 `/>` 结尾，正确处理自闭合标签
  - 支持 `<img />`, `<br/>`, `<hr/>`, `<input />`, `<meta />`, `<link />` 等标准写法
  - 兼容预定义的自闭合标签列表

### 📝 技术细节
- 在 `parseElement` 方法中添加了对 `/>` 结尾的检测
- 引入 `isSelfClosing` 变量来标记通过语法识别的自闭合标签
- 保持与预定义自闭合标签列表的兼容性

## [1.0.1] - 2024-12-19

### 🐛 Bug 修复
- **修复 StyleUtils.mergeStyles 错误**：解决了 `defaultStyle.keys is not a function` 的运行时错误
- **UTS 语法优化**：将 `UTSJSONObject.keys()` 方法替换为标准的 `for...in` 循环遍历对象属性
- **兼容性提升**：确保所有代码符合 UTS 语言规范，避免使用不支持的 JavaScript 方法

### 📝 技术细节
- 在 `StyleUtils.mergeStyles` 方法中，`UTSJSONObject` 类型不支持 `.keys()` 方法
- 使用 `for (const key in object)` 语法替代，这是 UTS 中推荐的对象遍历方式
- 此修复确保样式合并功能正常工作，解决组件初始化时的关键错误

---

## [1.0.0] - 2024-01-01

### ✨ 新功能
- 初始版本发布
- 支持基础HTML标签解析
- 支持CSS样式解析
- 支持图片、链接等媒体元素
- 跨平台兼容性支持