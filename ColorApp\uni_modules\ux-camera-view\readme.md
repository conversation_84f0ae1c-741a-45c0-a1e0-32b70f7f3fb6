
<p align="center">
    <img alt="logo" src="https://www.uxframe.cn/logo/logo.png" width="120" height="120" style="margin-bottom: 10px;">
</p>
<h3 align="center" style="margin: 30px 0 30px;font-weight: bold;font-size:40px;">UxFrame 自定义相机预览拍照组件 1.0.3</h3>

## 特别说明

如果您已经购买了[UxFrame 低代码高性能UI框架](https://ext.dcloud.net.cn/plugin?id=16148), 则无需再次购买本插件，请点击上方`进入交流群`联系我免费获取离线版插件！

## 功能说明

支持Android、iOS，Web仅支持电脑端浏览器！

- 拍照
- 拍摄
- 前后摄像头
- 闪光灯
- 手电筒

### 使用

``` html

<ux-camera-view ref="uxCameraRef" style="flex: 1;"></ux-camera-view>

```

``` ts

const uxCameraRef = ref<UxCameraViewElement | null>(null)

// 打开摄像头
uxCameraRef.value?.open?.()

// 关闭摄像头
uxCameraRef.value?.close?.()

// 拍照
uxCameraRef.value?.take?.()

// 切换摄像头
uxCameraRef.value?.switch?.(true)

// 闪光灯
uxCameraRef.value?.flash?.(true)

// 手电筒
uxCameraRef.value?.torch?.(true)

```

## 文档教程 ⬇️

[https://www.uxframe.cn](https://www.uxframe.cn)
