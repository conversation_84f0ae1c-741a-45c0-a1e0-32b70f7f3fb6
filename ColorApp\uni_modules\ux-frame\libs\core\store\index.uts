/**
 * 状态管理
 */
// #ifndef UNI-APP-X
import { reactive } from 'vue'
// #endif

import { Random } from '../../core/random/index.uts'
const random = new Random()

const xdata = new Map<string, any | null>();
const xstore = reactive(xdata);

export class Store {
	// #ifdef UNI-APP-X
	private STORE_KEY: string = 'UXFRAME_STORE_KEY'
	// #endif
	// #ifndef UNI-APP-X
	private STORE_KEY: string = 'UXFRAME_STORE_KEY'
	// #endif
	private keys: Map<string, number> = new Map<string, number>()
	
	constructor() {
		let data = uni.getStorageSync(this.STORE_KEY)
		if(data != null && data != '') {
			// #ifdef UNI-APP-X
			let keys = data as UTSJSONObject
			let _keys = UTSJSONObject.keys(keys)
			for (let i = 0; i < _keys.length; i++) {
				let k = _keys[i]
				this.keys.set(k, keys[k] as number)
			}
			// #endif
			// #ifndef UNI-APP-X
			let keys = data
			let _keys = Object.keys(keys)
			for (let i = 0; i < _keys.length; i++) {
				let k = _keys[i]
				this.keys.set(k, keys[k] as number)
			}
			// #endif
			
			let delKeys: Array<string> = []
			this.keys.forEach((_: number, k: string) => {
				let _key = this.getKey(k)
				
				let _data = uni.getStorageSync(_key)
				if(_data == null || _data == '') {
					delKeys.push(k)
				} else {
					xstore.set(_key, _data)
				}
			})
			
			delKeys.forEach((k: string)=>{
				this.keys.delete(k)
			})
			
			this.saveKeys()
		}
		
		// 3s周期清理过期keys
		setInterval(() => {
			this.keys.forEach((_:number, k:string) => {
				this.removeExpires(k)
			})
		}, 3000)
	}
	
	/**
	 * 获取缓存数据
	 * @param key 键
	 * @returns {any}
	 */
	get(key : string) : any | null {
		this.removeExpires(key)
		return xstore.get(this.getKey(key))
	}
	
	/**
	 * 内存缓存数据，不会持久化
	 * @param key 键
	 * @param value 值
	 */
	set(key : string, value : any) {
		xstore.set(this.getKey(key), value)
		this.setKey(key, 0)
	}
	
	/**
	 * 异步持久化缓存数据，可设置过期时间
	 * 持久化存储只支持原生类型、及能够通过 JSON.stringify 序列化的对象
	 * @param key 键
	 * @param value 值
	 * @param expires <= 0 永不过期; > 0 过期时间 单位ms
	 */
	setExpires(key : string, value : any, expires: number): void {
		let _key = this.getKey(key)
		
		uni.setStorage({
			key: _key,
			data: value,
			success: () => {
				xstore.set(_key, value)
				this.setKey(key, expires)
			},
			fail: (err) => {
				console.error('[UxFrame] setExpires error', key, err);
			}
		})
	}
	
	/**
	 * 同步持久化缓存数据，可设置过期时间
	 * 持久化存储只支持原生类型、及能够通过 JSON.stringify 序列化的对象
	 * @param key 键
	 * @param value 值
	 * @param expires <= 0 永不过期; > 0 过期时间 单位ms
	 */
	setExpiresSync(key : string, value : any, expires: number): void {
		let _key = this.getKey(key)
		
		uni.setStorageSync(_key, value)
		
		xstore.set(_key, value)
		this.setKey(key, expires)
	}
	
	/**
	 * 同步持久化缓存数据，可设置过期时间，返回一个随机key
	 * 持久化存储只支持原生类型、及能够通过 JSON.stringify 序列化的对象
	 * @param value 值
	 * @param expires <= 0 永不过期; > 0 过期时间 单位ms
	 */
	setDataSync(value : any, expires: number): string {
		let key = random.uuid()
		let _key = this.getKey(key)
		
		uni.setStorageSync(_key, value)
		
		xstore.set(_key, value)
		this.setKey(key, expires)
		return key
	}
	
	/**
	 * 删除数据
	 * @param key 键
	 */
	delete(key : string) {
		let _key = this.getKey(key)
		xstore.delete(_key)
		this.keys.delete(key)
		uni.removeStorageSync(_key)
		this.saveKeys()
	}
	
	/**
	 * 清空数据
	 */
	clear() {
		xstore.clear()
		this.keys.forEach((_: number, k: string) => {
			uni.removeStorageSync(this.getKey(k))
		})
		this.keys.clear()
		this.saveKeys()
	}
	
	/**
	 * Key
	 */
	private getKey(key: string): string {
		return this.STORE_KEY + key
	}
	
	/**
	 * 获取keys
	 */
	getKeys(): Map<string, number> {
		return this.keys
	}
	
	/**
	 * 设置key
	 */
	private setKey(key: string, expires: number) {
		this.keys.set(key, expires == 0 ? 0 : (new Date().getTime() + expires))
		this.saveKeys()
	}
	
	/**
	 * 持久化keys
	 */
	private saveKeys() {
		let keys = {} as UTSJSONObject
		this.keys.forEach((v: number, k: string) => {
			keys[k] = v
		})
		
		// uni.setStorage({
		// 	key: this.STORE_KEY,
		// 	data: keys
		// })
		
		uni.setStorageSync(this.STORE_KEY, keys)
	}
	
	/**
	 * 移除过期key
	 * @param key 键
	 */
	private removeExpires(key: string) {
		if(this.keys.has(key)) {
			let expires = this.keys.get(key) ?? 0
			if(expires > 0) {
				if(new Date().getTime() >= expires) {
					this.delete(key)
				}
			}
		}
	}
}