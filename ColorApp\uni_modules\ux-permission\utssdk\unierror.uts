
import { UxPermissionCallback } from "./interface.uts"

// #ifndef UNI-APP-X
class UniError {
	
}
// #endif

export class UxPermissionSuccessCallbackImpl extends UniError implements UxPermissionCallback {

	constructor(errMsg : string) {
		super();

		this.errSubject = 'ux-permission'
		this.errCode = 0
		this.errMsg = errMsg
	}
}

export class UxPermissionErrorCallbackImpl extends UniError implements UxPermissionCallback {

	constructor(errMsg : string) {
		super();

		this.errSubject = 'ux-permission'
		this.errCode = 1
		this.errMsg = errMsg
	}
}