import { UxRegisterOptions, UxLoginOptions, UxShareOptions } from '../interface.uts'

/**
* 注册
* @param {UxRegisterOptions} options
* @tutorial https://www.uxframe.cn/api/wxwork.html#register
*/
export const register = function (options : UxRegisterOptions) {
	
}

/**
* 登录
* @param {UxLoginOptions} options
* @tutorial https://www.uxframe.cn/api/wxwork.html#login
*/
export const login = function (options : UxLoginOptions) {
	
}

/**
* 分享
* @param {UxShareOptions} options
* @tutorial https://www.uxframe.cn/api/wxwork.html#share
*/
export const share = function (options : UxShareOptions) {
	
}

/**
* 微信app是否安装
* @tutorial https://www.uxframe.cn/api/wxwork.html#isInstalled
*/
export const isInstalled = function () : boolean {
	return false
}

/**
* 打开微信app
* @tutorial https://www.uxframe.cn/api/wxwork.html#openApp
*/
export const openApp = function () {
	
}