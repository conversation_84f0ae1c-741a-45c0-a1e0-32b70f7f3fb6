<template>
	<view class="ux-tree">
		<ux-tree-item
			:values="values"
			:id-key="idKey"
			:label-key="labelKey" 
			:node="node" 
			:open-ids="openIds" 
			:offset="offset"
			:color="color"
			:active-color="activeColor"
			:size="size"
			:size-type="sizeType"
			:bold="bold"
			:open-icon="openIcon"
			:close-icon="closeIcon"
			:icon-size="iconSize"
			:custom-family="customFamily"
			:include-child="includeChild"
			:only-child="onlyChild"
			@click="click"
			@change="change"
			v-for="(node, index) in nodes" :key="index">
		</ux-tree-item>
	</view>
</template>

<script setup>
	
	/**
	 * tree 树结构
	 * @description 支持无限嵌套，支持异步加载
	 * @demo pages/component/tree.uvue
	 * @tutorial https://www.uxframe.cn/component/tree.html
	 * @property {Array}			value												string[] | 默认值
	 * @property {Array}			modelValue											string[] | 双向绑定
	 * @property {String}			idKey												String | id key（默认 id ）
	 * @property {String}			labelKey											String | label key（默认 label ）
	 * @property {Array}			nodes												UTSJSONObject[] | 节点数据
	 * @property {Boolean}			includeChild = [true|false]							Boolean | 允许选择父级同时选择其所有子级（默认 false ）
	 * @property {Boolean}			onlyChild = [true|false]							Boolean | 只允许选择子级（默认 false ）
	 * @property {Number}			offset												Number | 子级距父级水平偏移量（默认 15 ）
	 * @property {String} 			theme=[primary|warning|success|error|info]			String | 主题颜色
	 * @value primary 	主色
	 * @value warning 	警告
	 * @value success 	成功
	 * @value error 	错误
	 * @value info 		文本
	 * @property {String} 			color												String | 文本颜色
	 * @property {String} 			darkColor=[none|auto|color]							String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {String} 			activeColor											String | 选中时颜色 优先级高于主题
	 * @property {String} 			activeDarkColor=[none|auto|color]					String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Any} 				size												Any | 字体大小（默认 $ux.Conf.fontSize ）
	 * @property {String}			sizeType=[normal|small|big]							String | 字体大小类型（默认 small ）
	 * @value normal 正常
	 * @value small 较小
	 * @value big 较大
	 * @property {Boolean} 			bold=[true|false]									Boolean | 字体加粗（默认 false ）
	 * @value true 加粗
	 * @value false 正常
	 * @property {String} 			openIcon											String | 打开图标
	 * @property {String} 			closeIcon											String | 关闭图标
	 * @property {Any}				iconSize											Any | 图标字体大小 (默认 $ux.Conf.fontSize)
	 * @property {String}			customFamily										String | 字体family
	 * @property {Boolean}			disabled = [true|false]			Boolean | 是否禁用（默认 false ）
	 * @event {Function} 			openedChange					Function | 打开时触发
	 * @event {Function} 			change							Function | 选择时触发
	 * @event {Function} 			click							Function | 点击时触发
	 * <AUTHOR>
	 * @date 2025-02-18 11:35:16
	 */
	
	import { useFontColor, useThemeColor, UxThemeType } from '../../libs/use/style.uts'
	
	defineOptions({
		name: 'ux-tree'
	})
	
	const emit = defineEmits(['click', 'opened-change', 'change', 'update:modelValue'])
	
	const props = defineProps({
		modelValue: {
			type: Array as PropType<string[]>,
			default: () : string[] => [] as string[]
		},
		openedValue: {
			type: Array as PropType<string[]>,
			default: () : string[] => [] as string[]
		},
		idKey: {
			type: String,
			default: 'id'
		},
		labelKey: {
			type: String,
			default: 'label'
		},
		nodes: {
			type: Array as PropType<Array<UTSJSONObject>>,
			default: (): UTSJSONObject[] => [] as UTSJSONObject[]
		},
		includeChild: {
			type: Boolean,
			default: false
		},
		onlyChild: {
			type: Boolean,
			default: false
		},
		theme: {
			type: String,
			default: ''
		},
		color: {
			type: String,
			default: '',
		},
		darkColor: {
			type: String,
			default: '',
		},
		activeColor: {
			type: String,
			default: '',
		},
		activeDarkColor: {
			type: String,
			default: '',
		},
		size: {
			default: 0
		},
		sizeType: {
			type: String,
			default: 'small'
		},
		bold: {
			type: Boolean,
			default: false
		},
		openIcon: {
			type: String,
			default: 'folderopen-filled'
		},
		closeIcon: {
			type: String,
			default: 'folderclose-outline'
		},
		iconSize: {
			default: 0
		},
		customFamily: {
			type: String,
			default: ''
		},
		offset: {
			type: Number,
			default: 15
		},
		disabled: {
			type: Boolean,
			default: false
		},
	})
	
	const nodes = ref<UTSJSONObject[]>([])
	
	const openIds = ref<string[]>([])
	
	const values = ref<string[]>([])
	
	watch((): UTSJSONObject[] => props.nodes, () => {
		nodes.value = props.nodes
	}, {immediate: true})
	
	watch(():string[] => props.openedValue, () => {
		openIds.value = props.openedValue
	}, {immediate: true})
	
	watch(():string[] => props.modelValue, () => {
		values.value = props.modelValue
	}, {immediate: true})
	
	function setNodeChildren(id: string, datas: UTSJSONObject[], children: UTSJSONObject[]) {
		for (let i = 0; i < children.length; i++) {
			let _id = children[i].getString(props.idKey) ?? ''
			if(id == _id) {
				children[i].set('children', datas)
				break
			} else {
				let _children = children[i].getArray<UTSJSONObject>('children') ?? []
				setNodeChildren(id, datas, _children)
			}
		}
	}
	
	function getChildren(node: UTSJSONObject) {
		let childs = [] as string[]
		
		let id = node.getString(props.idKey) ?? ''
		if(id != '') {
			childs.push(id)
		}
		
		if(props.includeChild) {
			let children = node.getArray<UTSJSONObject>('children') ?? []
			if(children.length > 0) {
				children.forEach((n) => {
					const ids = getChildren(n)
					childs.push(...ids)
				})
			}
		}
		
		return childs
	}
	
	function change(node: UTSJSONObject) {
		let vals = values.value
		let children = getChildren(node)
		
		let id = node.getString(props.idKey) ?? ''
		let index = vals.findIndex(e => e == id)
		if(index == -1) {
			vals.push(...children)
		} else {
			for (let i = children.length - 1; i >= 0; i--) {
				index = vals.findIndex(e => e == children[i])
				if(index != -1) {
					vals.splice(index, 1)
				}
			}
		}
		
		values.value = vals
		emit('update:modelValue', values.value)
		emit('change', values.value)
	}
	
	function openClick(id: string) {
		let index = openIds.value.findIndex(e => e == id)
		if(index == -1) {
			openIds.value.push(id)
		} else {
			openIds.value.splice(index, 1)
		}
		
		emit('opened-change', openIds.value)
	}
	
	async function click(node: UTSJSONObject) {
		let id = node.getString(props.idKey) ?? ''
		
		let children = node.getArray<UTSJSONObject>('children') ?? []
		if(children.length > 0) {
			openClick(id)
		} else {
			// 异步加载
			if(node.getAny('callback') != null) {
				let callback = node.getAny('callback') as (id: string) => Promise<UTSJSONObject[]>
				const datas = await callback(id)
				node.set('children', datas)
			}
			
			children = node.getArray<UTSJSONObject>('children') ?? []
			if(children.length > 0) {
				openClick(id)
			}
		}
		
		emit('click', id)
	}
	
	const color = computed(() => {
		return useFontColor(props.color, props.darkColor)
	})
	
	const activeColor = computed(() => {
		if(props.activeColor != '' || props.theme == '') {
			return useFontColor(props.activeColor, props.activeDarkColor)
		} else {
			return useThemeColor(props.theme as UxThemeType) 
		}
	})
</script>

<style lang="scss">
	
	.ux-tree {
		
	}
	
</style>