{"android": {"abiFilters": ["arm64-v8a", "armeabi-v7a", "x86"], "appName": "UxFrame", "applicationAttrs": [], "buildFeatures": [], "composeCompilerVersion": "1.5.1", "manifestPlaceholders": {}, "mavenRepos": [], "metaData": [], "minSdkVersion": 21, "packageName": "com.chunge.suanmi", "signingConfig": {"keyAlias": "__uni__341c706", "keyPassword": "4ppsZEgh", "storeFile": "assets/com.chunge.suanmi.4ppsZEgh.keystore", "storePassword": "4ppsZEgh"}, "targetSdkVersion": 31}, "ios": {"appIcon": "assets/AppIcon.appiconset", "appName": "UxFrame", "bundleId": "cn.uxframe.demo", "capabilities": {"entitlements": {}}, "deploymentTarget": "13.0", "deviceType": [1, 2], "plist": {}, "podSources": [], "signingConfig": {"development": {"certificateFile": "assets/dev.p12", "certificateFilePassword": "111111", "certificateProfileFile": "assets/dev.mobileprovision"}, "release": {"certificateFile": "assets/dist.p12", "certificateFilePassword": "111111", "certificateProfileFile": "assets/dist.mobileprovision"}}, "staticPods": []}, "uniSdk": {"android": "/Users/<USER>/Documents/sdk/unix/Android-uni-app-x-SDK@13795-4.75", "ios": "/Users/<USER>/Documents/sdk/unix/UniAppX-iOS@4.75"}}