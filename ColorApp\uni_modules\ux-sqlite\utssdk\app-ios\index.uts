import { OpenDatabase, OpenDatabaseOptions } from '../interface.uts'
import { CloseDatabase } from '../interface.uts'
import { DeleteDatabase, DeleteDatabaseOptions } from '../interface.uts'
import { ExecSQL, ExecSQLOptions } from '../interface.uts'
import { ExecQuery, ExecQueryOptions, UxSqlWhere } from '../interface.uts'
import { ExecRawQuery, ExecRawQueryOptions } from '../interface.uts'
import { ExecInsert, ExecInsertOptions } from '../interface.uts'
import { ExecUpdate, ExecUpdateOptions } from '../interface.uts'
import { ExecDelete, ExecDeleteOptions } from '../interface.uts'
import { ExecTransaction, ExecTransactionOptions } from '../interface.uts'
import { ExecCount, ExecCountOptions } from '../interface.uts'
import { GetColumns, GetColumnsOptions } from '../interface.uts'
import { GetPath } from '../interface.uts'
import { IsOpen } from '../interface.uts'
import { UxSqlSuccess } from '../interface.uts'
import { UxSqlFailImpl } from '../unierror.uts'

import { Double , Int16 , Bool } from 'Swift';
import { Data } from 'Foundation';

/**
 * 打开数据库
 * @param { OpenDatabaseOptions } options 参数
 */
export const openDatabase : OpenDatabase = function (options : OpenDatabaseOptions) {
	try {
		closeDatabase()
		
		let database = `${options.database ?? 'app'}.sqlite`
		let log = options.log ?? true
		
		let ok = UxDB.openDatabase(name = database)
		
		if(ok) {
			options.success?.({
				code: 0,
				count: 1,
				datas: []
			} as UxSqlSuccess)
		} else {
			options.fail?.(new UxSqlFailImpl(-1))
		}
	} catch (error) {
		options.fail?.(new UxSqlFailImpl(-1))
	}

	options.complete?.()
}

/**
 * 关闭数据库
 */
export const closeDatabase : CloseDatabase = function () : boolean {
	return UxDB.closeDatabase()
}

/**
 * 删除数据库
 */
export const deleteDatabase : DeleteDatabase = function (options : DeleteDatabaseOptions) : boolean {
	closeDatabase()
	
	return UxDB.deleteDatabase(name = `${options.database ?? 'app'}.sqlite`)
}

/**
 * 执行SQL
 */
export const execSQL : ExecSQL = function (options: ExecSQLOptions): boolean {
	return UxDB.execSQL(sql = options.sql)
}

/**
 * 查询
 */
export const query : ExecQuery = function (options: ExecQueryOptions): Array<Map<string, any>> {
	let sql = 'SELECT '
	sql += (options.distinct ?? false) ? 'DISTINCT ' : ''
	sql += options.selects?.join(', ') ?? '*'
	sql += ` FROM ${options.table!}`
	
	let values: any[] = []
	let res = buildWhereSql(options.wheres, options.ors)
	if(res != null) {
		sql += res!.sql
		values = values.concat(res!.values)
	}
	
	if((options.groupBy ?? '') != '') {
		sql += ` GROUP BY ${options.groupBy!}`
	}
	
	if((options.having ?? '') != '') {
		sql += ` HAVING BY ${options.having!}`
	}
	
	if((options.orderBy ?? '') != '') {
		sql += ` ORDER BY ${options.orderBy!}`
	}
	
	if((options.limit ?? '') != '') {
		sql += ` LIMIT ${options.limit!}`
	}
	
	let results = UxDB.query(sql = sql, values = values)
	return wrapDatas(results, options.columns)
}

/**
 * SQL查询
 */
export const rawQuery : ExecRawQuery = function (options: ExecRawQueryOptions): Array<Map<string, any>> {
	let values: any[] = []
	let results = UxDB.query(sql = options.sql, values = values)
	return wrapDatas(results, options.columns)
}

/**
 * 插入SQL
 */
export const insert : ExecInsert = function (options: ExecInsertOptions): number {
	if(options.content == null) {
		return 0
	}
	
	let keys = UTSJSONObject.keys(options.content!)
	let sql = `INSERT INTO ${options.table!} ${buildInsertSql(keys)}`
	let values = buildContentValues(keys, options.content!, options.columns)
	
	return UxDB.insert(sql = sql, values = values) as number
}

/**
 * 更新SQL
 */
export const update : ExecUpdate = function (options: ExecUpdateOptions): number {
	if(options.content == null) {
		return 0
	}
	
	let keys = UTSJSONObject.keys(options.content!)
	let sql = `UPDATE ${options.table!} SET ${buildUpdateSql(keys)}`
	let values = buildContentValues(keys, options.content!, options.columns)
	
	let res = buildWhereSql(options.wheres, options.ors)
	if(res != null) {
		sql += res!.sql
		values = values.concat(res!.values)
	}

	return UxDB.update(sql = sql, values = values) as number
}

/**
 * 删除SQL
 */
export const del : ExecDelete = function (options: ExecDeleteOptions): number {
	
	let sql = `DELETE FROM ${options.table!}`
	let values: any[] = []
	
	let res = buildWhereSql(options.wheres, options.ors)
	if(res != null) {
		sql += res!.sql
		values = values.concat(res!.values)
	}
	
	return UxDB.delete(sql = sql, values = values) as number
}

/**
 * 执行事务
 */
export const transaction : ExecTransaction = function (options: ExecTransactionOptions): boolean {
	let callback = (): boolean => {
		return options.operations()
	}
	return UxDB.transaction(operations = callback)
}

/**
 * 行数
 */
export const count : ExecCount = function (options: ExecCountOptions): number {
	let sql = `SELECT COUNT(*) FROM ${options.table!}`
	let values: any[] = []
	
	let res = buildWhereSql(options.wheres, options.ors)
	if(res != null) {
		sql += res!.sql
		values = values.concat(res!.values)
	}
	
	return UxDB.count(sql = sql, values = values) as number
}

/**
 * 获取表字段
 */
export const getColumns : GetColumns = function (options: GetColumnsOptions): string[] {
	return UxDB.columns(tableName = options.table) as string[]
}

/**
 * 获取数据库路径
 */
export const getPath : GetPath = function (): string {
	return UxDB.getPath() ?? ''
}

/**
 * 是否打开数据库
 */
export const isOpen : IsOpen = function (): boolean {
	return UxDB.isOpen() ?? false
}

/**
 * 包装数据集
 */
function wrapDatas(results : Array<Map<string, any | null>>, columns ?: UTSJSONObject) : Array<Map<string, any | null>> {
	let datas: Map<string, any | null>[] = []
	
	if(columns == null) {
		for (let i = 0; i < results.length; i++) {
			let result = results[i]
			
			let row = new Map<string, any | null>()
			result.forEach((val, key) => {
				row.set(key, val)
			})
			
			datas.push(row)
		}
	} else {
		let _keys = UTSJSONObject.keys(columns!)
		for (let i = 0; i < results.length; i++) {
			let result = results[i]
			
			let row = new Map<string, any | null>()
			result.forEach((val, key) => {
				for (let y = 0; y < _keys.length; y++) {
					let k = _keys[y]
					let col = columns!.getJSON(k)!
					if (key == col.getString('name')) {
						row.set(k, wrapColumnData(val, col))
						break
					}
				}
			})
			
			datas.push(row)
		}
	}
	
	return datas
}

/**
 * 构建 Insert Sql
 */
function buildInsertSql(keys: string[]): string {
	let values: string[] = []
	keys.forEach(k => {
		values.push('?')
	})

	return `(${keys.join(', ')}) VALUES (${values.join(', ')})`
}

/**
 * 构建 Update Sql
 */
function buildUpdateSql(keys: string[]): string {
	let sqls: string[] = []
	keys.forEach((k) => {
		sqls.push(k + ' = ?')
	})
	
	return `${sqls.join(', ')}`
}

type BuildSqlRes = {
	sql: string
	values: any[]
}

type BuildRes = {
	keys: string[]
	values: any[]
}

/**
 * 构建 Where Sql
 */
function buildWhereSql(wheres ?: UxSqlWhere[] | null, ors : UxSqlWhere[] | null): BuildSqlRes | null {
	let sql: string = ''
	let values: any[] = []
	
	let _wheres = wheres ?? []
	let _ors = ors ?? []
	
	if (_wheres.length > 0 && _ors.length > 0) {
		let whereSql = ''
		let orSql = ''
		
		_wheres.forEach(a => {
			let res = getWhereRes(a)
			whereSql += res.keys.join(' AND ')
			values = values.concat(res.values)
		})
		
		_ors.forEach(a => {
			let res = getWhereRes(a)
			orSql += res.keys.join(' AND ')
			values = values.concat(res.values)
		})
		
		sql = ` WHERE (${whereSql}) OR (${orSql})`
	} else if (_wheres.length > 0 && _ors.length == 0) {
		let whereSql = ''
		
		_wheres.forEach(a => {
			let res = getWhereRes(a)
			whereSql += res.keys.join(' AND ')
			values = values.concat(res.values)
		})
		
		sql = ' WHERE ' + whereSql
	} else if (_wheres.length == 0 && _ors.length > 0) {
		let orSql = ''
		
		_ors.forEach(a => {
			let res = getWhereRes(a)
			orSql += res.keys.join(' AND ')
			values = values.concat(res.values)
		})
		
		sql = ' WHERE ' + orSql
	}
	
	if(sql != '' || values.length > 0) {
		return {
			sql: sql,
			values: values
		} as BuildSqlRes
	}
	
	return null
}

function getWhereRes(e: UxSqlWhere): BuildRes {
	let keys: string[] = []
	let values: any[] = []
	
	if(e.operator == 'between' || e.operator == 'not between') {
		let vals = e.value as any[]
		keys.push(`(${e.name!} ${e.operator!} ? AND ?)`)
		values.push(vals[0])
		values.push(vals[1])
	} else if(e.operator == 'in' || e.operator == 'not in') {
		let _vals = e.value as any[]
		let vals: string[] = []
		for (let i = 0; i < _vals.length; i++) {
			vals.add(vals[i].toString())
		}
		keys.push(`${e.name!} ${e.operator!} (?)`)
		values.push(vals.join(', '))
	} else if(e.operator == 'is' || e.operator == 'is not') {
		keys.push(`${e.name!} ${e.operator!} ?`)
		values.push(e.value)
	} else {
		keys.push(`${e.name!} ${e.operator!} ?`)
		values.push(e.value)
	}
	
	return {
		keys: keys,
		values: values
	} as BuildRes
}

/**
 * 构建Value
 */
function buildContentValues(keys: string[], content : UTSJSONObject, columns: UTSJSONObject): any[] {
	let _keys = UTSJSONObject.keys(columns)
	let getType = (key: string) => {
		for (let i = 0; i < _keys.length; i++) {
			let col = columns.getJSON(_keys[i])!
			if(col.getString('name')! == key) {
				return col.getString('type')!
			}
		}
		
		return ''
	}
	
	let values : any[] = []
	
	for (let i = 0; i < keys.length; i++) {
		let value = content[keys[i]]
		if(value == null) {
			values.push('nil')
		} else {
			let type = getType(keys[i])
			
			if (type == 'string') {
				values.push(value as String)
			} else if (type == 'boolean') {
				values.push(value as Boolean)
			} else if (type == 'int') {
				values.push(value as Int)
			} else if (type == 'double') {
				values.push(value as Double)
			} else if (type == 'float') {
				values.push(value as Float)
			} else if (type == 'long') {
				values.push(value as Int64)
			} else if (type == 'short') {
				values.push(value as Int16)
			} else if (type == 'json') {
				values.push(JSON.stringify(value) as String)
			} else if (type == 'blob') {
				console.warn('[ux-orm] iOS暂不支持blob存储');
				// values.push(value as Data)
			} else if (type == 'timestamp') {
				values.push(value as Int64)
			} else if (type == 'datetime') {
				values.push(toDate(value! as Date))
			}
		}
	}
	
	return values
}

/**
 * 包装column数据
 */
function wrapColumnData(value : any | null, column : UTSJSONObject) : any | null {
	let type = column.getString('type') ?? ''
	
	if (type == 'boolean') {
		return value == 1
	} else if (type == 'blob') {
		return null
	} else if (type == 'datetime') {
		return value == null ? null : fromDate(`${value!}`)
	} else if (type == 'json') {
		if(value == null) {
			return null
		} else {
			let d = JSON.parse(`${value!}`) as UTSJSONObject
			let keys = UTSJSONObject.keys(d)
			
			let m = new Map<string, any>()
			keys.forEach(k => {
				m.set(k, d[k])
			})
			return m
		}
	} else {
		return value
	}
}

/**
 * date from string
 */
function fromDate (date: string): Date {
	let d = date.split(/[^0-9]/)
	
	let year = d.length < 1 ? 0 : parseInt(d[0])
	let month = d.length < 2 ? 0 : parseInt(d[1]) - 1
	let day = d.length < 3 ? 0 : parseInt(d[2])
	let hour = d.length < 4 ? 0 : parseInt(d[3])
	let minute = d.length < 5 ? 0 : parseInt(d[4])
	let second = d.length < 6 ? 0 : parseInt(d[5])
	
	return new Date(year, month, day, hour, minute, second)
}

/**
 * date to string
 */
function toDate (date: Date): string {
	let padding = (n: number): string => {
		return `${n}`.padStart(2, '0')
	}
	
	return `${date.getFullYear()}-${padding(date.getMonth() + 1)}-${padding(date.getDate())} ${padding(date.getHours())}:${padding(date.getMinutes())}:${padding(date.getSeconds())}`
}