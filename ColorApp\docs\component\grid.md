<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/grid?title=Grid"></Mobile>

# Grid
> 组件类型：UxGridComponentPublicInstance

宫格布局 需搭配 ux-grid-item 使用

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| col | Number | 3 | 宫格的列数 |
| border | Boolean | false | 是否显示宫格的边框 |
| [align](#align) | String | left | 宫格对齐方式，表现为数量少的时候，靠左，居中，还是靠右 |
| background | String | transparent | 背景色 |

### [align](#align)

| 值   | 说明 |
|:------:|:----:|
| left左对齐
 |  |
| center居中
 |  |
| right右对齐
 |  |

