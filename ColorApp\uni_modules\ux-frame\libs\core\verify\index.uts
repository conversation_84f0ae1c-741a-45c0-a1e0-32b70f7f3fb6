/**
   @Name    :	校验
   <AUTHOR>   UxFrame
   @Date    :   2023-11-20 12:21:36
*/

export class Verify {

	/**
	 * 日期验证
	 */
	isDate(date : any) : boolean {

		if (date == '' || date == 0) {
			return false
		}

		return !/Invalid|NaN/.test(new Date(date.toString()).toString())
	}

	/** 链接验证
	 * @param {string} url
	 * @returns {Boolean}
	 */
	isURL(url : string) : boolean {
		const reg = /^((https|http|ftp|rtsp|mms):\/\/)(([0-9a-zA-Z_!~*'().&=+$%-]+: )?[0-9a-zA-Z_!~*'().&=+$%-]+@)?(([0-9]{1,3}.){3}[0-9]{1,3}|([0-9a-zA-Z_!~*'()-]+.)*([0-9a-zA-Z][0-9a-zA-Z-]{0,61})?[0-9a-zA-Z].[a-zA-Z]{2,6})(:[0-9]{1,4})?((\/?)|(\/[0-9a-zA-Z_!~*'().;?:@&=+$,%#-]+)+\/?)$/
		return reg.test(url)
	}

	/** 数字验证
	 * @param {string} num
	 * @returns {Boolean}
	 */
	isNumber(num : string) : boolean {
		const reg = /^-?[0-9]+(\.[0-9]+)?$/
		return reg.test(num)
	}

	/** 字母验证
	 * @param {string} str
	 * @returns {Boolean}
	 */
	isAbc(str : string) : boolean {
		const reg = /^[A-Za-z]+$/
		return reg.test(str)
	}

	/** 字母或数字验证
	 * @param {string} str
	 * @returns {Boolean}
	 */
	isAbcNum(str : string) : boolean {
		const reg = /^[0-9a-zA-Z]*$/g
		return reg.test(str)
	}

	/** 中文验证
	 * @param {string} str
	 * @returns {Boolean}
	 */
	isChinese(str : string) : boolean {
		const reg = /[\u4E00-\u9FA5]/g
		return reg.test(str)
	}

	/** 邮箱验证
	 * @param {string} email
	 * @returns {Boolean}
	 */
	isEmail(email : string) : boolean {
		const reg = /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/
		return reg.test(email)
	}

	/** 电话验证
	 * @param {string} phone
	 * @returns {Boolean}
	 */
	isPhone(phone : string) : boolean {
		const reg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/
		return reg.test(phone)
	}

	/** 固定电话验证
	 * @param {string} phone
	 * @returns {Boolean}
	 */
	isLandline(phone : string) : boolean {
		const reg = /^\d{3,4}-\d{7,8}(-\d{3,4})?$/
		return reg.test(phone)
	}

	/** 身份证验证
	 * @param {string} idcard
	 * @returns {Boolean}
	 */
	isIdcard(idcard : string) : boolean {
		if (idcard == '') {
			return false
		}

		// 内地身份证
		idcard = idcard.toUpperCase()

		// 身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X。
		if (!/(^\d{15}$)|(^\d{17}([0-9]|X)$)/.test(idcard)) {
			return false
		}

		// 校验位按照ISO 7064:1983.MOD 11-2的规定生成，X可以认为是数字10。
		let len : number = idcard.length

		if (len == 15) {
			let arrSplit = idcard.match(/^(\d{6})(\d{2})(\d{2})(\d{2})(\d{3})$/)!

			// 检查生日日期是否正确
			let dtmBirth = new Date('19' + arrSplit[2] + '/' + arrSplit[3] + '/' + arrSplit[4])
			let bCorrectDay =
				dtmBirth.getFullYear() == parseInt(arrSplit[2].toString()) &&
				dtmBirth.getMonth() + 1 == parseInt(arrSplit[3].toString()) &&
				dtmBirth.getDate() == parseInt(arrSplit[4].toString())

			if (!bCorrectDay) {
				return false
			} else {
				// 将15位身份证转成18位
				// 校验位按照ISO 7064:1983.MOD 11-2的规定生成，X可以认为是数字10。
				let arrInt = new Array(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2)
				let arrCh = new Array('1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2')
				let nTemp = 0

				idcard = idcard.substring(0, 6) + '19' + idcard.substring(6, idcard.length - 6)
				for (let i = 0; i < 17; i++) {
					nTemp += idcard.substring(i, 1).length * arrInt[i]
				}

				idcard += arrCh[nTemp % 11]

				return true
			}
		}

		if (len == 18) {
			let arrSplit = idcard.match(/^(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X)$/)!

			// 检查生日日期是否正确
			let dtmBirth = new Date(arrSplit[2] + '/' + arrSplit[3] + '/' + arrSplit[4])

			let bCorrectDay =
				dtmBirth.getFullYear() == parseInt(arrSplit[2].toString()) &&
				dtmBirth.getMonth() + 1 == parseInt(arrSplit[3].toString()) &&
				dtmBirth.getDate() == parseInt(arrSplit[4].toString())

			if (!bCorrectDay) {
				return false
			} else {
				// 检验18位身份证的校验码是否正确。
				// 校验位按照ISO 7064:1983.MOD 11-2的规定生成，X可以认为是数字10。
				let arrInt = new Array(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2)
				let arrCh = new Array('1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2')

				let nTemp = 0
				for (let i = 0; i < 17; i++) {
					nTemp += idcard.substring(i, 1).length * arrInt[i]
				}

				let valnum = arrCh[nTemp % 11]
				if (valnum != idcard.substring(17, 1)) {
					return false
				}

				return true
			}
		}

		return false
	}

	/** 图片验证
	 * @param {string} url
	 * @returns {boolean}
	 */
	isImage(url : string) : boolean {
		const reg = /\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg)/i
		return reg.test(url.split('?')[0])
	}

	/** 视频验证
	 * @param {string} url
	 * @returns {boolean}
	 */
	isVideo(url : string) : boolean {
		const reg = /\.(mp4|mpg|mpeg|dat|asf|avi|rm|rmvb|mov|wmv|flv|mkv|m3u8)/i
		return reg.test(url.split('?')[0])
	}

	/** 车牌号验证
	 * @param {string} no
	 * @returns {boolean}
	 */
	isCarNo(no : string) : boolean {
		// 新能源车牌
		const xreg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/

		// 车牌
		const creg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/

		if (no.length == 7) {
			return creg.test(no)
		} if (no.length == 8) {
			return xreg.test(no)
		}

		return false
	}

	/** 判空验证
	 * @param {string} val
	 * @returns {boolean}
	 */
	isEmpty<T>(val : T) : boolean {
		if (val == null) {
			return false
		}
		
		if (typeof val == 'string') {
			return (val as string).trim().split('').length == 0
		}
		
		if (Array.isArray(val)) {
			return (val as any[]).length == 0
		}
		
		if (isNaN(val as number)) {
			return false
		}

		return false
	}
}