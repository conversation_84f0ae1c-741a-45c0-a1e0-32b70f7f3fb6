import FrameLayout from 'android.widget.FrameLayout'
import Loop<PERSON> from 'android.os.Looper'
import <PERSON><PERSON> from 'android.os.Handler'
import Surface from 'android.view.Surface'
import LinearLayout from 'android.widget.LinearLayout'
import Context from 'android.content.Context'
import File from 'java.io.File'
import Bitmap from "android.graphics.Bitmap"
import ViewGroup from 'android.view.ViewGroup'
import LifecycleOwner from "androidx.lifecycle.LifecycleOwner"
import PreviewView from "androidx.camera.view.PreviewView"
import CameraSelector from "androidx.camera.core.CameraSelector"
import Preview from 'androidx.camera.core.Preview'
import Camera from 'androidx.camera.core.Camera'
import ImageProxy from 'androidx.camera.core.ImageProxy'
import ProcessCameraProvider from "androidx.camera.lifecycle.ProcessCameraProvider"
import CameraManager from 'android.hardware.camera2.CameraManager'
import FileOutputStream from 'java.io.FileOutputStream'
import UUID from 'java.util.UUID'
import ImageCapture from 'androidx.camera.core.ImageCapture'
import Executors from 'java.util.concurrent.Executors'
import ImageCaptureException from 'androidx.camera.core.ImageCaptureException'
import ExifInterface from 'androidx.exifinterface.media.ExifInterface'
import Matrix from 'android.graphics.Matrix'
import OutOfMemoryError from 'java.lang.OutOfMemoryError'
import CameraCharacteristics from 'android.hardware.camera2.CameraCharacteristics'

export class UxCamera {
	context : Context
	previewer : FrameLayout
	cameraManager : CameraManager
	cameraView : PreviewView
	cameraProvider : ProcessCameraProvider | null = null
	camera : Camera | null = null
	cameraPreview : Preview | null = null
	imageCapture: ImageCapture | null = null
	isFront = false

	constructor(isTextureView: boolean, previewer : FrameLayout, context: Context) {
		this.context = context
		this.cameraManager = this.context.getSystemService(Context.CAMERA_SERVICE) as CameraManager
		
		this.previewer = previewer
		
		this.cameraView = new PreviewView(this.context)
		this.cameraView.scaleType = PreviewView.ScaleType.FILL_CENTER
		if(isTextureView) {
			this.cameraView.implementationMode = PreviewView.ImplementationMode.COMPATIBLE
		}
		this.cameraView.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT))
		
		this.previewer.addView(this.cameraView)
	}

	checkPermissions(callback : (istrue : boolean) => void) {
		let permissions = ['android.permission.CAMERA']
		if (UTSAndroid.checkSystemPermissionGranted(UTSAndroid.getUniActivity()!, permissions)) {
			callback(true)
		} else {
			UTSAndroid.requestSystemPermission(UTSAndroid.getUniActivity()!, permissions, function (allRight : boolean, _ : string[]) {
				callback(true)
			}, function (_ : boolean, _ : string[]) {
				callback(false)
			})
		}
	}
	
	open() {
		this.checkPermissions((ok : boolean) => {
			if (ok) {
				this.openCamera()
			}
		})
	}
	
	close() {
		this.cameraProvider?.unbindAll()
		this.cameraPreview?.setSurfaceProvider(null)
		this.imageCapture?.onUnbind()
		this.camera = null
	}
	
	take(correctOrientation: boolean, callback: (path: string) => void) {
		let that = this
		class CapturedCallback implements ImageCapture.OnImageCapturedCallback {
			constructor() {
			    super()
			}
			
			override onCaptureSuccess(imageProxy: ImageProxy) {
				try {
					let filePath = UTSAndroid.getAppContext()!.getExternalCacheDir()?.getPath() ?? ''
					let bitmap = imageProxy.toBitmap()
					let file = new File(filePath, UUID.randomUUID().toString() + '.jpg')
					let fs = new FileOutputStream(file);
					
					if(correctOrientation) {
						let exif = new ExifInterface(file.getAbsolutePath())
						let orientation = exif.getAttributeInt(ExifInterface.TAG_ORIENTATION, ExifInterface.ORIENTATION_UNDEFINED);
						let rotatedBitmap = that.rotateBitmap(bitmap, orientation)
						rotatedBitmap.compress(Bitmap.CompressFormat.JPEG, 100, fs);
					} else {
						bitmap.compress(Bitmap.CompressFormat.JPEG, 100, fs);
					}
					
					fs.close()
					
					callback(file.getPath())
				} catch (err) {
					console.log(err);
				} finally {
					imageProxy.close();
				}
			}
			
			override onError(exception: ImageCaptureException) {
			    exception.printStackTrace();
			}
		}
		
		this.imageCapture?.takePicture(Executors.newSingleThreadExecutor(), new CapturedCallback())
	}
	
	switch(isFront : boolean) {
		this.isFront = isFront
		this.openCamera()
	}
	
	flash(isFlash : boolean) {
		this.imageCapture?.setFlashMode(isFlash ? 1 : 0)
	}
	
	torch(isTorch : boolean) {
		this.close()
		
		let that = this
		class IRunable extends Runnable {
			override run() {
			   that.cameraManager.setTorchMode(that.getCameraId(), isTorch)
			}
		}
		new Handler(Looper.getMainLooper()).postDelayed(new IRunable(), 500)
	}
	
	private openCamera() {
		this.close()
		
		let f = () => {
			// 获取CameraProvider实例
			const cameraProviderInstance = ProcessCameraProvider.getInstance(this.context)
			this.cameraProvider = cameraProviderInstance.get()
			
			// 创建Preview用于显示相机预览
			this.cameraPreview = new Preview.Builder().build()
			this.cameraPreview!.setSurfaceProvider(this.cameraView.getSurfaceProvider())
			
			// 获取当前活动的LifecycleOwner实例
			const lifecycleOwner = UTSAndroid.getUniActivity()! as LifecycleOwner
			
			// 创建CameraSelector选择后置摄像头
			const cameraSelector = new CameraSelector.Builder().requireLensFacing(this.isFront ? CameraSelector.LENS_FACING_FRONT : CameraSelector.LENS_FACING_BACK).build()
			
			// 绑定相机预览到生命周期
			this.camera = this.cameraProvider!.bindToLifecycle(lifecycleOwner, cameraSelector, this.cameraPreview)
			
			// 创建ImageCapture实例用于拍照
			this.imageCapture = ImageCapture.Builder().setTargetRotation(Surface.ROTATION_90).build()
			
			// 绑定ImageCapture到生命周期
			this.cameraProvider!.bindToLifecycle(lifecycleOwner, cameraSelector, this.imageCapture)
		}
		
		class IRunable extends Runnable {
			override run() {
			   f()
			}
		}
		new Handler(Looper.getMainLooper()).postDelayed(new IRunable(), 500)
	}
	
	getCameraId(): string {
	    var ids = this.cameraManager.getCameraIdList()
	    for (let id in ids) {
	        let c = this.cameraManager.getCameraCharacteristics(id)
	        let flashAvailable = c.get<Boolean>(CameraCharacteristics.FLASH_INFO_AVAILABLE)
	        let lensFacing = c.get<Int>(CameraCharacteristics.LENS_FACING)
			let nowLensFacing = this.isFront ? CameraSelector.LENS_FACING_FRONT : CameraSelector.LENS_FACING_BACK
	        if (flashAvailable != null
					&& flashAvailable
	                && lensFacing != null
					&& lensFacing == nowLensFacing) {
	            return id;
	        }
	    }
		
	    return ''
	}
		
	rotateBitmap(bitmap: Bitmap, orientation: Int): Bitmap {
	    let matrix = new Matrix()
	    switch (orientation) {
	        case ExifInterface.ORIENTATION_NORMAL:
	            return bitmap;
	        case ExifInterface.ORIENTATION_FLIP_HORIZONTAL:
	            matrix.setScale(-1 as Float, 1 as Float);
	            break;
	        case ExifInterface.ORIENTATION_ROTATE_180:
	            matrix.setRotate(180 as Float);
	            break;
	        case ExifInterface.ORIENTATION_FLIP_VERTICAL:
	            matrix.setScale(1 as Float, -1 as Float);
	            break;
	        case ExifInterface.ORIENTATION_TRANSPOSE:
	            matrix.setRotate(90 as Float);
	            matrix.postScale(-1 as Float, 1 as Float);
	            break;
	        case ExifInterface.ORIENTATION_ROTATE_90:
	            matrix.setRotate(90 as Float);
	            break;
	        case ExifInterface.ORIENTATION_TRANSVERSE:
	            matrix.setRotate(-90 as Float);
	            matrix.postScale(-1 as Float, 1 as Float);
	            break;
	        case ExifInterface.ORIENTATION_ROTATE_270:
	            matrix.setRotate(-90 as Float);
	            break;
	        default:
	            return bitmap;
	    }
		
	    try {
	        let rotatedBitmap = Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), matrix, true);
	        bitmap.recycle();
	        return rotatedBitmap;
	    } catch (e: OutOfMemoryError) {
	        e.printStackTrace()
	        return bitmap
	    }
	}
}