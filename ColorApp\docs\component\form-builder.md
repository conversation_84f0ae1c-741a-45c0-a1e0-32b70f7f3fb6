# FormBuilder 动态表单构建器

FormBuilder 是一个基于 FieldConfig 配置的动态表单渲染器组件，可以根据 JSON 配置动态生成表单界面，支持多种字段类型、验证规则和分组显示。

## 功能特性

- 🚀 **动态渲染**: 根据 FieldConfig 配置动态生成表单
- 📝 **多种字段类型**: 支持文本、数字、选择、开关等多种字段类型
- ✅ **表单验证**: 内置多种验证规则，支持必填、长度、正则等验证
- 📊 **分组显示**: 支持字段分组，提供更好的用户体验
- 🎨 **样式定制**: 支持自定义样式类和主题
- 📱 **响应式**: 适配不同屏幕尺寸

## 基本用法

```vue
<template>
  <FormBuilder 
    :fields="formFields"
    v-model="formData"
    @submit="handleSubmit"
    @change="handleFieldChange"
  />
</template>

<script setup>
import FormBuilder from '@/components/FormBuilder.uvue'

const formFields = ref([
  {
    Id: 'username',
    Name: '用户名',
    Title: '用户名',
    Type: 'Text',
    DataType: 'string',
    Required: true,
    Placeholder: '请输入用户名',
    Order: 1
  }
])

const formData = ref({})

const handleSubmit = (result) => {
  if (result.valid) {
    console.log('表单数据:', result.data)
  } else {
    console.log('验证失败:', result.error)
  }
}

const handleFieldChange = (event) => {
  console.log('字段变化:', event.field, event.value)
}
</script>
```

## 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| fields | Array | [] | 字段配置数组 |
| modelValue | Object | {} | 表单数据对象 |
| showActions | Boolean | true | 是否显示操作按钮 |
| showReset | Boolean | true | 是否显示重置按钮 |

## 组件事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| submit | result | 表单提交事件 |
| change | event | 字段值变化事件 |
| update:modelValue | data | 表单数据更新事件 |

## 组件方法

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| submit | - | Object | 提交表单并返回验证结果 |
| reset | - | - | 重置表单数据 |
| getFormData | - | Object | 获取当前表单数据 |
| setFormData | data | - | 设置表单数据 |

## FieldConfig 字段配置

### 基本信息

| 字段 | 类型 | 说明 |
|------|------|------|
| Id | string | 字段标识符，用于数据绑定 |
| Name | string | 字段名称 |
| Title | string | 字段标题，显示给用户 |
| Group | string | 字段分组 |
| Order | number | 字段排序 |

### 字段类型

| 字段 | 类型 | 说明 |
|------|------|------|
| Type | string | 字段组件类型，见 FieldEditorType |
| DataType | string | 数据类型：string、bool、int、long、decimal、json |
| IsArray | boolean | 是否为数组类型 |
| InputType | string | 输入框类型：text、mobile、email、number、password |

### 状态控制

| 字段 | 类型 | 说明 |
|------|------|------|
| Required | boolean | 是否必填 |
| Disabled | boolean | 是否禁用 |
| Readonly | boolean | 是否只读 |
| Hide | boolean | 是否隐藏 |

### 输入控制

| 字段 | 类型 | 说明 |
|------|------|------|
| Placeholder | string | 输入框占位文字 |
| MaxLength | number | 最大输入长度 |
| MinLength | number | 最小输入长度 |
| DefaultValue | string | 默认值 |

### 数值控制

| 字段 | 类型 | 说明 |
|------|------|------|
| MinDecimal | number | 数字最小值 |
| MaxDecimal | number | 数字最大值 |
| Precision | number | 小数精度 |

### 验证规则

| 字段 | 类型 | 说明 |
|------|------|------|
| ValidationType | string | 验证类型：pattern、equal、lessThan、greaterThan |
| Validation | string | 验证表达式（如正则表达式） |
| ValidMesasge | string | 验证失败提示信息 |

### 数据源配置

| 字段 | 类型 | 说明 |
|------|------|------|
| DataSourceType | string | 数据源类型：Json、Enum、Menu、API |
| DataSource | any | 数据源内容 |
| DefaultDataSource | string | 默认数据源，逗号分隔 |

### 样式配置

| 字段 | 类型 | 说明 |
|------|------|------|
| Class | string | 字段样式类 |
| GroupClass | string | 分组样式类 |

## 支持的字段类型

### 文本输入类型

- **Text**: 普通文本输入框
- **TextArea**: 多行文本输入框
- **PhoneFormat**: 手机号输入框

### 选择类型

- **Select**: 下拉选择框
- **Picker**: 多级选择器
- **RadioGroup**: 单选组
- **CheckboxGroup**: 多选组
- **Checkbox**: 复选框

### 开关类型

- **Switch**: 开关选择器

### 日期时间类型

- **Date**: 日期选择器
- **DateTime**: 日期时间选择器
- **DateRange**: 日期范围选择器
- **DateTimeRange**: 日期时间范围选择器

## 使用示例

### 用户注册表单

```javascript
const userFormFields = [
  {
    Id: 'username',
    Title: '用户名',
    Type: 'Text',
    DataType: 'string',
    Required: true,
    Placeholder: '请输入用户名',
    MinLength: 3,
    MaxLength: 20,
    ValidationType: 'pattern',
    Validation: '^[a-zA-Z0-9_]{3,20}$',
    ValidMesasge: '用户名只能包含字母、数字和下划线，长度3-20位',
    Order: 1
  },
  {
    Id: 'email',
    Title: '邮箱地址',
    Type: 'Text',
    DataType: 'string',
    InputType: 'email',
    Required: true,
    Placeholder: '请输入邮箱地址',
    ValidationType: 'pattern',
    Validation: '^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$',
    ValidMesasge: '请输入正确的邮箱格式',
    Order: 2
  },
  {
    Id: 'gender',
    Title: '性别',
    Type: 'Select',
    DataType: 'string',
    Required: true,
    DefaultDataSource: '男,女,其他',
    Order: 3
  },
  {
    Id: 'newsletter',
    Title: '订阅邮件通知',
    Type: 'Switch',
    DataType: 'bool',
    DefaultValue: 'false',
    Order: 4
  }
]
```

### 分组表单

```javascript
const productFormFields = [
  {
    Id: 'productName',
    Title: '产品名称',
    Group: '基本信息',
    Type: 'Text',
    DataType: 'string',
    Required: true,
    Order: 1
  },
  {
    Id: 'price',
    Title: '产品价格',
    Group: '基本信息',
    Type: 'Text',
    DataType: 'decimal',
    InputType: 'number',
    Required: true,
    MinDecimal: 0.01,
    Order: 2
  },
  {
    Id: 'description',
    Title: '产品描述',
    Group: '详细信息',
    Type: 'TextArea',
    DataType: 'string',
    MaxLength: 1000,
    Order: 3
  }
]
```

## 注意事项

1. **字段标识符**: `Id` 字段必须唯一，用于数据绑定
2. **数据类型**: `DataType` 必须与实际数据类型匹配
3. **验证规则**: 正则表达式需要正确转义
4. **数据源**: JSON 格式的数据源需要包含 `id` 和 `text` 字段
5. **排序**: `Order` 字段用于控制字段显示顺序
6. **分组**: 相同 `Group` 的字段会显示在同一分组下

## 扩展开发

如需支持更多字段类型，可以在 `getFieldComponent` 方法中添加新的组件映射，并在 `getFieldProps` 方法中配置相应的属性。

```javascript
// 添加新的字段类型支持
const getFieldComponent = (field) => {
  const type = field.Type
  switch (type) {
    case 'CustomType':
      return 'custom-component'
    // ... 其他类型
  }
}
```