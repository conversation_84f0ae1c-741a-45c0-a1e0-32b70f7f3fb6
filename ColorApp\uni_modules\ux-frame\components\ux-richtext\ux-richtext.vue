<template>
	<rich-text class="ux-richtext" :nodes="value" @itemclick="click"></rich-text>
</template>

<script setup lang="ts">
	/**
	* RichText 富文本
	* @description 可渲染文字样式、图片、超链接，支持部分HTML标签
	* @demo pages/component/richtext.uvue
	* @tutorial https://www.uxframe.cn/component/richtext.html
	* @property {String} 			nodes												UTSJSONObject[] | 节点列表
	* @property {String} 			html												String | HTML标签
	* @property {Boolean} 			selectable=[true|false]								Boolean | 文本是否可选，Android设置selectable属性为 true 时，click事件不触发（默认 false ）
	* @value true
	* @value false
	* @event {Function} 			click 												Function |  被点击时触发
	* <AUTHOR>
	* @date 2024-12-01 22:56:28
	*/
	
	import { ref, computed } from 'vue'
	import { $ux } from '../../index'
	
	defineOptions({
		name: 'ux-richtext'
	})
	
	const emit = defineEmits(['click'])
	
	const props = defineProps({
		nodes: {
			type: Array as PropType<UTSJSONObject[]>,
			default: (): UTSJSONObject[] => {
				return [] as UTSJSONObject[]
			},
		},
		html: {
			type: String,
			default: '',
		},
		selectable: {
			type: Boolean,
			default: false
		}
	})
	
	const value = computed((): any => {
		return props.html == '' ? props.nodes : props.html
	})
	
	function click(e: UniRichTextItemClickEvent) {
		emit('click', e)
	}
</script>

<style lang="scss" scoped>
	.ux-richtext {
		
	}
</style>