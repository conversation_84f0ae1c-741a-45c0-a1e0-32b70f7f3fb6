<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/lottie?title=Lottie"></Mobile>

# Lottie
> 组件类型：UxLottieComponentPublicInstance



## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| src | String |  | 路径 |
| loop | Boolean | true | 循环播放 |
| repeat | Number | 1 | 重复次数 |
| autoplay | Boolean | true | 自动播放 |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| loaded | 加载成功时触发 |  |
  
  