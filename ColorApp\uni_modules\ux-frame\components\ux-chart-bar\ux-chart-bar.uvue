<template>
	<view class="ux-chart-bar">
		<canvas id="bar" class="canvas" @touchstart="touchstart" @touchmove="touchmove" @touchend="touchend" @touchcancel="touchend" />
	</view>
</template>

<script setup>
	
	/**
	 * 图表 - 柱状图
	 * @description canvas绘制的柱状图图表
	 * @demo pages/component/chart-bar.uvue
	 * @tutorial https://www.uxframe.cn/component/chart-bar.html
	 * @property {Object}		data							UxChartBarType | 数据
	 * @property {Number}		delay							Number | 延迟初始化 (默认 0)
	 * <AUTHOR>
	 * @date 2025-02-27 17:35:45
	 */
	
	import { $ux } from "@/uni_modules/ux-frame"
	import { useFontSize, useFontColor, useThemeColor, useColor } from '../../libs/use/style.uts'
	import { UxChartBarType } from '../../libs/types/chart.uts'
	
	defineOptions({
		name: 'ux-chart-bar'
	})

	type Pos = {
		x : number,
		y : number
	}

	const props = defineProps({
		data: {
			type: Object as PropType<UxChartBarType>,
			required: true
		},
		delay: {
			type: Number,
			default: 0
		}
	})
	
	let ctx : CanvasRenderingContext2D | null = null
	
	const data = computed((): UxChartBarType => {
		return props.data as UxChartBarType
	})
	
	// x轴
	let xAxis : string[] = []
	
	// 数据
	let datas : number[][] = []
	
	// 上次数据
	let lastData = new Map<number, number>()
	
	// canvas宽高
	let width = 0
	let height = 0
	
	// 绘制区域宽高
	let w = 0
	let h = 0
	
	// x轴位置
	let x0 : Pos = { x: 0, y: 0 }
	let x1 : Pos = { x: 0, y: 0 }
	
	// y轴位置
	let y0 : Pos = { x: 0, y: 0 }
	let y1 : Pos = { x: 0, y: 0 }
	
	// 原点
	let zero: Pos = {x: 0, y: 0}
	
	// x轴左偏移
	const offsetX = 40
	
	// y轴底偏移
	const offsetY = 40
	
	const fontSize = computed((): number => {
		return useFontSize($ux.Util.getPx(data.value.fontSize ?? 12), 0)
	})
	
	const fontColor = computed((): string => {
		return useFontColor(data.value.color ?? '', data.value.darkColor ?? '')
	})
	
	const backgroundColor = computed((): string => {
		let theme = useThemeColor('primary')
		let bc = data.value.backgroundColor ?? ''
		let dc = data.value.backgroundColorDark ?? ''
		let color = bc == '' ? theme : bc
		let darkColor = dc == '' ? 'auto' : dc
		return useColor(color, darkColor)
	})
	
	const dashesColor = computed((): string => {
		return useColor(data.value.dashesColor ?? '#c5cacf', data.value.dashesColorDark ?? '')
	})
	
	const lineColor = computed((): string => {
		return useColor(data.value.lineColor ?? '#000000', data.value.lineColorDark ?? '')
	})
	
	const yStep = computed(() => {
		return data.value.yStep ?? 5
	})
	
	const wp = computed(() => {
		return data.value.width ?? 0.7
	}) 
	
	function maxValue() {
		let a = 0
		datas.forEach(e => {
			let b = Math.max(...e)
			a = Math.ceil(Math.max(a, b))
		})
		return a
	}
	
	function minValue() {
		let a = 0
		datas.forEach(e => {
			let b = Math.min(...e)
			a = Math.ceil(Math.min(a, b))
		})
		return a
	}
	
	function stepValue(value : number) {
		const roughStep = value / (yStep.value - 1)
		const stepPower = Math.pow(10, Math.floor(Math.log10(roughStep)))
		const step = Math.floor(roughStep / stepPower) * stepPower
	
		// 确保步长至少为1
		return Math.max(step, 1)
	}

	function drawAxis() {
		if (ctx == null) return
	
		// x轴最大值 最小值
		let minX = 0
		let maxX = w
	
		// 获取数据范围
		const max = maxValue()
		const min = minValue()
		const range = max - min // 数值总跨度
	
		// 自动计算合适的步长
		let step = stepValue(range)
	
		// 绘制y轴
		for (let value = min; value <= max; value += step) {
			const p = (value - min) / range
			const y = h - Math.ceil(p * h) + offsetY
	
			// x轴虚线网格
			if (value / step != 0) {
				ctx!.strokeStyle = dashesColor.value
				ctx!.setLineDash([5, 5])
				ctx!.beginPath()
				ctx!.moveTo(x0.x, y)
				ctx!.lineTo(x1.x, y)
				ctx!.stroke()
				ctx!.setLineDash([])
			} else {
				// y轴零点位置
				zero = {
					x: x0.x,
					y: y
				}
			}
	
			// y轴刻度值
			ctx!.font = `${fontSize.value}px Arial`
			ctx!.fillStyle = fontColor.value
			ctx!.textAlign = 'right'
			ctx!.fillText(value.toString(), y0.x - 8, y + 4)
		}
	
		// 绘制x轴
		ctx!.beginPath()
		ctx!.moveTo(x0.x, zero.y)
		ctx!.lineTo(x1.x, zero.y)
		ctx!.closePath()
		ctx!.strokeStyle = lineColor.value
		ctx!.stroke()
	
		// x轴每一段宽度
		const xWidth = w / (xAxis.length + 1)
		
		// 文本旋转角度
		const angle = data.value.labelAngle ?? 0
	
		// 绘制x轴数据
		xAxis.forEach((label, index) => {
			// 刻度线位置
			const x = x0.x + xWidth * index
	
			ctx!.beginPath()
			ctx!.moveTo(x, zero.y)
			ctx!.lineTo(x, zero.y + 5)
			ctx!.stroke()
	
			// 文本
			ctx!.font = `${fontSize.value}px Arial`
			ctx!.fillStyle = fontColor.value
			ctx!.textAlign = angle == 0 ? 'center' : 'right'
			ctx!.save()
			ctx!.translate(x + (angle == 0 ? (xWidth / 2) : (xWidth - 6)), x0.y + 15)
			ctx!.rotate(-Math.PI / (180 / angle)) // 倾斜防止重叠
			ctx!.fillText(label, 0, 0)
			ctx!.restore()
		})
	
		// 补充最后一个刻度
		ctx!.beginPath()
		ctx!.moveTo(x1.x, zero.y)
		ctx!.lineTo(x1.x, zero.y + 5)
		ctx!.stroke()
	}

	function drawBar(vals : number[]) {
		if (ctx == null) {
			return
		}
		
		// 获取数据范围
		const max = maxValue()
		const min = minValue()
		const range = max - min // 数值总跨度
		
		// 步长高度
		const stepH = h / yStep.value
		
		// x轴每一段宽度
		const xWidth = (w / (xAxis.length + 1)) * wp.value
		
		// 偏移
		const offset = (w /  (xAxis.length + 1)) * (1 - wp.value)
		
		// 节点相对位置
		let points : Pos[] = vals.map((val : number, index : number) : Pos => {
			const x = x0.x + (xWidth + offset) * index + offset / 2
			
			let p = (val - min) / range
			let y: number
			if(val < zero.y) {
				y = h * p + offsetY - zero.y
			} else {
				y = zero.y - h * p
			}
			
			return {
				x: x,
				y: y
			} as Pos
		})
		
		// 绘制柱状图
		ctx!.fillStyle = backgroundColor.value
		points.forEach((e, index) => {
			// 结束值 目标位置
			const endVal = e.y
			// 记录上一次结束的位置
			const lastVal = lastData.get(index) ?? 0
			// 动画起始值继承上一次结束值，从上次位置继续动画
			const startVal = lastVal
			
			$ux.Anim.run({
				startVal: startVal,
				endVal: endVal,
				lastVal: lastVal,
				speed: 2,
				frame: (value: number) => {
					ctx!.fillRect(e.x, zero.y - value, xWidth, value)
				},
				end: (value: number) => {
					lastData.set(index, value)
				}
			})
		})
	}

	function draw() {
		if (xAxis.length == 0) {
			return
		}
		
		if (ctx != null) {
			ctx!.clearRect(0, 0, width, height)
		}

		drawAxis()
		datas.forEach(e => drawBar(e))
	}
	
	function touchstart(e: TouchEvent) {
		
	}
	
	function touchmove(e: TouchEvent) {
		
	}
	
	function touchend(e: TouchEvent) {
		
	}
	
	function init() {
		xAxis = data.value.xAxis
		datas = data.value.data
		
		uni.createCanvasContextAsync({
			id: 'bar',
			component: getCurrentInstance()?.proxy!,
			success: (context : CanvasContext) => {
				ctx = context.getContext('2d')!;
				const canvas = ctx!.canvas;

				// 处理高清屏逻辑
				const dpr = uni.getDeviceInfo().devicePixelRatio ?? 1;
				canvas.width = canvas.offsetWidth * dpr;
				canvas.height = canvas.offsetHeight * dpr;
				ctx!.scale(dpr, dpr)

				width = canvas.offsetWidth
				height = canvas.offsetHeight
				
				w = width - offsetX
				h = height - offsetY * 2
				x0 = { x: offsetX, y: offsetY + h } as Pos
				x1 = { x: w, y: offsetY + h } as Pos
				y0 = { x: offsetX, y: offsetY + h } as Pos
				y1 = { x: offsetX, y: 0 } as Pos

				setTimeout(function() {
					draw()
				}, props.delay);
			}
		})
	}

	watch(data, () => {
		datas = data.value.data
		xAxis = data.value.xAxis
		draw()
	}, {deep: true})
	
	onMounted(() => {
		init()
	})
</script>

<style lang="scss" scoped>
	.ux-chart-bar {
		flex: 1;
		
		.canvas {
			width: 100%;
			height: 100%;
		}
	}
</style>