<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/tree?title=Tree"></Mobile>

# Tree
> 组件类型：UxTreeComponentPublicInstance

支持无限嵌套，支持异步加载

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| value | Array |  | 默认值 |
| modelValue | Array |  | 双向绑定 |
| idKey | String | id | idkey |
| labelKey | String | label | labelkey |
| nodes | Array |  | 节点数据 |
| includeChild | Boolean | false | 允许选择父级同时选择其所有子级 |
| onlyChild | Boolean | false | 只允许选择子级 |
| offset | Number | 15 | 子级距父级水平偏移量 |
| [theme](#theme) | String |  | 主题颜色 |
| color | String |  | 文本颜色 |
| [darkColor](#darkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| activeColor | String |  | 选中时颜色优先级高于主题 |
| [activeDarkColor](#activeDarkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| size | Any | `$ux.Conf.fontSize` | 字体大小 |
| [sizeType](#sizeType) | String | small | 字体大小类型 |
| [bold](#bold) | Boolean | false | 字体加粗 |
| openIcon | String |  | 打开图标 |
| closeIcon | String |  | 关闭图标 |
| iconSize | Any | `$ux.Conf.fontSize` | 图标字体大小 |
| customFamily | String |  | 字体family |
| disabled | Boolean | false | 是否禁用 |

### [theme](#theme)

| 值   | 说明 |
|:------:|:----:|
| primary | 主色
 |
| warning | 警告
 |
| success | 成功
 |
| error | 错误
 |
| info | 文本
 |

### [darkColor](#darkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [activeDarkColor](#activeDarkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [sizeType](#sizeType)

| 值   | 说明 |
|:------:|:----:|
| normal正常
 |  |
| small较小
 |  |
| big较大
 |  |

### [bold](#bold)

| 值   | 说明 |
|:------:|:----:|
| true加粗
 |  |
| false正常
 |  |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| openedChange | 打开时触发 |  |
| change | 选择时触发 |  |
| click | 点击时触发 |  |
  
  