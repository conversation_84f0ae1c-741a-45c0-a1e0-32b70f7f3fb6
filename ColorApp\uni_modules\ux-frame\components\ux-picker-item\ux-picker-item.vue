<template>
	<view class="ux-picker__item">
		<text class="ux-picker__text" :style="style">{{ text }}</text>
	</view>
</template>

<script setup lang="ts">
	
	/**
	 * PickerItem 选择器子项
	 * @description 内部使用
	 * @demo pages/component/picker.uvue
	 * @tutorial https://www.uxframe.cn/component/picker.html
	 * @property {String}		text							String | 文本
	 * @property {Any}			size							Any | 字体大小
	 * @property {String}		color							String | 字体颜色
	 * @property {String}		selectColor						String | 选择颜色
	 * @property {Number}		index							Number | 下标
	 * @property {Number}		selectedIndex					Number | 已选择下标
	 * <AUTHOR>
	 * @date 2024-12-02 00:42:46
	 */
	
	import { PropType, computed } from 'vue'
	import { $ux } from '../../index'
	import { useFontSize } from '../../libs/use/style.uts'
	
	defineOptions({
		name: 'ux-picker-item'
	})
	
	const props = defineProps({
		text: {
			type: String,
			default: ''
		},
		size: {
			default: 0
		},
		color: {
			type: String,
			default: ''
		},
		selectColor: {
			type: String,
			default: ''
		},
		index:{
			type: Number,
			default: 0
		},
		selectedIndex:{
			type: Number,
			default: 0
		}
	})
	
	const selected = computed(():boolean => {
		return props.selectedIndex == props.index 
	})
	
	const fontSize = computed(():number => {
		return useFontSize($ux.Util.getPx(props.size), 0)
	})
	
	const color = computed(():string => {
		return selected.value ? props.selectColor : props.color
	})
	
	const style = computed(() => {
		let css = {}
		
		css['font-size'] = $ux.Util.addUnit(fontSize.value)
		css['font-weight'] = selected.value ? 'bold' : 'normal'
		css['color'] = color.value
		
		return css
	})
</script>

<style lang="scss" scoped>
	.ux-picker__item {
		height: 45px;
	}
	
	.ux-picker__text {
		line-height: 45px;
		text-align: center;
		color: black;
	}
</style>