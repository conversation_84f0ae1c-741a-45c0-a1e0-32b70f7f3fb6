
import { OpenFileManager, UxOpenFileManagerOptions, UxFile, UxMimeType } from '../interface.uts'
import { SearchFiles, UxSearchFilesOptions } from '../interface.uts'
import { StopSearch } from '../interface.uts'

export * from '../interface.uts'

/**
 * 打开文件管理器
 * @param {UxOpenFileManagerOptions} options 参数
 */
export const openFileManager : OpenFileManager = (options : UxOpenFileManagerOptions) => {
	let mimeTypes = options.mimeTypes ?? []
	let multiple = options.multiple ?? false

	let callback = (files : UxFile[]) => {
		options.success?.(files)
	}

	_openFileManager(getMimeTypes(mimeTypes, []), multiple, callback)
}

/**
 * 搜索文件
 * @param {UxSearchFilesOptions} options 参数
 */
export const searchFiles : SearchFiles = (options : UxSearchFilesOptions) => {
	console.log('[ux-files] web不支持 searchFiles');
}

/**
 * 停止搜索文件
 */
export const stopSearch : StopSearch = () => {
	console.log('[ux-files] web不支持 stopSearch');
}

var fileDom : HTMLInputElement = null
function _openFileManager(mimeTypes : string[], multiple : boolean, callback : (files : UxFile[]) => void) {
	fileDom?.remove()

	fileDom = document.createElement('input')
	fileDom.type = 'file'
	fileDom.accept = mimeTypes.join(', ')
	fileDom.multiple = multiple ? 'multiple' : ''

	fileDom.style.setProperty('position', 'fixed')
	fileDom.style.setProperty('opacity', '0')

	fileDom.onchange = function (e) {
		let files = fileDom.files || [] as File[]
		
		let _files: UxFile[] = []
		for (let i = 0; i < files.length; i++) {
			let file = files[i]
			
			_files.push({
				name: file.name,
				path: '',
				extension: file.type.substring(file.type.lastIndexOf('/') + 1),
				blob: file,
				size: file.size,
				date: toDate(file.lastModifiedDate),
			})
		}

		callback(_files)
	}

	fileDom.click()
}

const ImageTypes = {
	"jpg": "image/jpeg",
	"jpeg": "image/jpeg",
	"png": "image/png",
	"gif": "image/gif",
	"bmp": "image/bmp",
	"webp": "image/webp",
	"heic": "image/heic",
	"tiff": "image/tiff",
	"svg": "image/svg+xml",
	"ico": "image/x-icon",
}

const VideoTypes = {
	"mp4": "video/mp4",
	"webm": "video/webm",
	"ogv": "video/ogg",
	"mov": "video/quicktime",
	"avi": "video/x-msvideo",
	"wmv": "video/x-ms-wmv",
	"mkv": "video/x-matroska",
}

const AudioTypes = {
	"mp3": "audio/mpeg",
	"wav": "audio/wav",
	"ogg": "audio/ogg",
	"flac": "audio/flac",
	"aac": "audio/aac",
	"m4a": "audio/mp4",
	"wma": "audio/x-ms-wma",
	"opus": "audio/opus",
}

const TxtTypes = {
	"txt": "text/plain",
	"html": "text/html",
	"css": "text/css",
	"js": "text/javascript",
	"xml": "text/xml",
	"csv": "text/csv",
	"markdown": "text/markdown",
	"json": "application/json",
}

const PdfTypes = {
	"pdf": "application/pdf"
}

const DocTypes = {
	"doc": "application/msword",
	"docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
}

const XlsTypes = {
	"xls": "application/vnd.ms-excel",
	"xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
}

const PptTypes = {
	"ppt": "application/vnd.ms-powerpoint",
	"pptx": "application/vnd.openxmlformats-officedocument.presentationml.presentation",
}

const ZipTypes = {
	"zip": "application/zip",
	"rar": "application/vnd.rar",
	"7z": "application/x-7z-compressed",
	"epub": "application/epub+zip",
	"exe": "application/octet-stream",
	"bin": "application/octet-stream",
}

function handleTypes(types: UTSJSONObject, ignore: string[]): string[] {
	let mimeTypes: string[] = []
	
	let keys = UTSJSONObject.keys(types)
	keys.filter(e => ignore.indexOf(e) == -1).forEach(k => {
		mimeTypes.push(types.getString(k)!)
	})
	
	return mimeTypes
}
	
function getMimeTypes(types: UxMimeType[], ignore: string[]): string[] {
	let mimeTypes: string[] = []
	
	for (let i = 0; i < types.length; i++) {
		let t = types[i]
		
		if(t == 'image') {
			mimeTypes = mimeTypes.concat(handleTypes(ImageTypes, ignore))
			mimeTypes.push('image/*')
		} else if(t == 'video') {
			mimeTypes = mimeTypes.concat(handleTypes(VideoTypes, ignore))
			mimeTypes.push('video/*')
		} else if(t == 'audio') {
			mimeTypes = mimeTypes.concat(handleTypes(AudioTypes, ignore))
			mimeTypes.push('audio/*')
		} else if(t == 'txt') {
			mimeTypes = mimeTypes.concat(handleTypes(TxtTypes, ignore))
			mimeTypes.push('text/*')
		} else if(t == 'pdf') {
			mimeTypes = mimeTypes.concat(handleTypes(PdfTypes, ignore))
		} else if(t == 'doc') {
			mimeTypes = mimeTypes.concat(handleTypes(DocTypes, ignore))
		} else if(t == 'xls') {
			mimeTypes = mimeTypes.concat(handleTypes(XlsTypes, ignore))
		} else if(t == 'ppt') {
			mimeTypes = mimeTypes.concat(handleTypes(PptTypes, ignore))
		} else if(t == 'zip') {
			mimeTypes = mimeTypes.concat(handleTypes(ZipTypes, ignore))
		}
	}
	
	return mimeTypes
}

function toDate (date: Date): string {
	let padding = (n: number): string => {
		return `${n}`.padStart(2, '0')
	}
	
	return `${date.getFullYear()}-${padding(date.getMonth() + 1)}-${padding(date.getDate())} ${padding(date.getHours())}:${padding(date.getMinutes())}:${padding(date.getSeconds())}`
}