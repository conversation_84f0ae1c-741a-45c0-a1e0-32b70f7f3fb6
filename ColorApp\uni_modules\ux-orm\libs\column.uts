import { toDate } from './utils'
import { WhereWrap } from './where'

export type types = 'string' | 'boolean' | 'int' | 'double' | 'float' | 'long' | 'short' | 'blob' | 'timestamp' | 'datetime' | 'json'

export class ColumnType {
	private primary: boolean = false
	private name: string
	private type: string
	private utstype: types
	private alias: string = ''
	private defValue ?: any = null
	
	constructor(name: string, type: string, utstype: types) {
	    this.name = name
		this.type = type
		this.utstype = utstype
	}
	
	/**
	 * 自增主键
	 */
	primaryKey(): ColumnType {
		this.primary = true
		return this
	}
	
	/**
	 * 默认值
	 */
	default(value: any | null): ColumnType {
		this.defValue = value
		return this
	}
	
	/**
	 * 字段类型
	 */
	getType(): string {
		return this.type
	}
	
	/**
	 * UTS type
	 */
	getUtsType(): types {
		return this.utstype
	}
	
	/**
	 * sql字段
	 */
	getName(): string {
		return this.name
	}
	
	/**
	 * 别名
	 */
	getAlias(): string {
		return this.name + ' AS ' + this.alias
	}
	
	/**
	 * sql 列
	 */
	getColumn(): string {
		let column = this.name + ' ' + this.type
		
		if(this.primary) {
			column += ' PRIMARY KEY AUTOINCREMENT'
		}
		
		if(this.defValue != null) {
			if (this.utstype == 'datetime') {
				column += ` DEFAULT '${toDate(this.defValue as Date)}'`
			} else if (this.utstype == 'json') {
				column += ` DEFAULT '${JSON.stringify(this.defValue)}'`
			} else {
				column += ` DEFAULT ${this.defValue}`
			}
		}
		
		return column
	}
	
	/**
	 * 别名
	 */
	aliased(name: string): ColumnType {
		this.alias = name
		return this
	}
	
	/**
	 * 等于 运算符
	 */
	eq(value: any): WhereWrap {
		return new WhereWrap(this.name, value, '=')
	}
	
	/**
	 * 不等于 运算符
	 */
	neq(value: any): WhereWrap {
		return new WhereWrap(this.name, value, '!=')
	}
	
	/**
	 * 大于 运算符
	 */
	gt(value: any): WhereWrap {
		return new WhereWrap(this.name, value, '>')
	}
	
	/**
	 * 大于或等于 运算符
	 */
	gte(value: any): WhereWrap {
		return new WhereWrap(this.name, value, '>=')
	}
	
	/**
	 * 小于 运算符
	 */
	lt(value: any): WhereWrap {
		return new WhereWrap(this.name, value, '<')
	}
	
	/**
	 * 小于或等于 运算符
	 */
	lte(value: any): WhereWrap {
		return new WhereWrap(this.name, value, '<=')
	}
	
	/**
	 * 模糊匹配 运算符, 例：%张三、张三%、%张三%
	 */
	like(value: any): WhereWrap {
		return new WhereWrap(this.name, value, 'like')
	}
	
	/**
	 * 模糊不匹配 运算符, 例：%张三、张三%、%张三%
	 */
	notLike(value: any): WhereWrap {
		return new WhereWrap(this.name, value, 'not like')
	}
	
	/**
	 * between 运算符
	 */
	between(value1: any, value2: any): WhereWrap {
		return new WhereWrap(this.name, [value1, value2], 'between')
	}
	
	/**
	 * notBetween 运算符
	 */
	notBetween(value1: any, value2: any): WhereWrap {
		return new WhereWrap(this.name, [value1, value2], 'not between')
	}
	
	/**
	 * in 运算符
	 */
	in(value: any[]): WhereWrap {
		return new WhereWrap(this.name, value, 'in')
	}
	
	/**
	 * notIn 运算符
	 */
	notIn(value: any[]): WhereWrap {
		return new WhereWrap(this.name, value, 'not in')
	}
	
	/**
	 * isNull 运算符
	 */
	isNull(): WhereWrap {
		return new WhereWrap(this.name, null, 'is')
	}
	
	/**
	 * isNotNull 运算符
	 */
	isNotNull(): WhereWrap {
		return new WhereWrap(this.name, null, 'is not')
	}
	
	/**
	 * 正序
	 */
	asc(): string {
		return `${this.name} ASC`
	}
	
	/**
	 * 倒叙
	 */
	desc(): string {
		return `${this.name} DESC`
	}
}
