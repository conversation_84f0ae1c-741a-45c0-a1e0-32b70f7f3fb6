<template>
	<view>
	</view>
</template>

<script lang="uts">
	/**
	* 精灵动画组件
	* @demo pages/component/sprite.uvue
	* @tutorial https://www.uxframe.cn/component/sprite.html
	* @property {Array} 			frames					string[] | 精灵图集
	* @property {Number} 			duration				Number | 每帧周期 (默认 100)
	* @property {Boolean} 			play					Boolean | 是否播放 (默认 false)
	* @property {Boolean} 			loop					Boolean | 循环播放 (默认 false)
	* @property {Boolean} 			autoplay				Boolean | 自动播放 (默认 false)
	* @event {Function} 			start 					Function | 开始时触发
	* @event {Function} 			end 					Function | 结束时触发
	* <AUTHOR>
	* @date 2025-04-07 21:10:21
	*/
   
</script>

<style>

</style>