<template>
	<view :id="myId" class="ux-tabs" :style="style">
		<view :id="leftId" ref="leftRef">
			<slot name="left"></slot>
		</view>
		<scroll-view :id="scrollId" ref="scrollRef" class="ux-tabs__scroll"
			:scroll-x="true"
			:scroll-into-view="scrollviewId"
			:scroll-with-animation="true"
			:show-scrollbar="false">
			<view ref="tabsRef" :data-id="tab.id" :id="tab.id" class="ux-tabs__item" :style="tabStyle(tab)"
				v-for="(tab, index) in tabs" :key="index" @click="click(tab, index)">
				<text class="ux-tabs__text" :style="textStyle(tab)">{{ tab.name }}</text>
				<ux-badge :theme="theme" :value="tab.badge" :dot="tab.reddot" :size="10" :right="4" :top="1"></ux-badge>
			</view>
			<view v-if="type == 'line'" class="ux-tabs__warp" :style="warpStyle">
				<view :class="`ux-tabs__line--${anim}`" :style="lineStyle"></view>
			</view>
			<view v-if="type == 'smile'" class="ux-tabs__warp" :style="warpStyle">
				<view ref="smileRef" class="ux-tabs__smile" :style="smileStyle">
					<canvas class="ux-tabs__smile--canvas" :id="canvasId" :canvas-id="canvasId"></canvas>
				</view>
			</view>
			<view v-if="type == 'point'" class="ux-tabs__warp" :style="warpStyle">
				<view ref="pointRef" class="ux-tabs__point" :style="pointStyle"></view>
			</view>
		</scroll-view>
		<view :id="rightId" ref="rightRef">
			<slot name="right"></slot>
		</view>
	</view>
</template>

<script setup lang="ts">
	
	/**
	 * Tabs 标签导航栏
	 * @description 支持点、下划线、微笑类型，下划线支持左右滚动、左右推压效果
	 * @demo pages/component/tabs.uvue
	 * @tutorial https://www.uxframe.cn/component/tabs.html
	 * @property {Slot}				left								Slot | 左插槽
	 * @property {Slot}				right								Slot | 右插槽
	 * @property {String}			theme=[text|info|primary|success|warning|error]	String | 按钮类型 (默认 info)
	 * @value text			文字
	 * @value info   		默认
	 * @value primary   	主要
	 * @value success  		成功
	 * @value warning   	警告
	 * @value error  		错误
	 * @property {Number}			modelValue							Number | 双向绑定值 (默认 0)
	 * @property {UxTab[]}			datas								UxTab[] | tabs列表
	 * @property {String}			type = [none|line|point|smile]		String | 类型 (默认 line)
	 * @value none 无
	 * @value line 下划线
	 * @value point 点
	 * @value smile 微笑
	 * @property {String}			anim = [none|scroll|push]			String | 动效类型 (默认 scroll)
	 * @value none 无
	 * @value scroll 左右滚动
	 * @value push 左右推压
	 * @property {String}			anchorId							String | 锚点ScrollId
	 * @property {Boolean}			anchor = [true|false]				Boolean | 自动滚动到锚点 (默认 false)
	 * @property {Any}				width								Any | 宽度 (默认 auto)
	 * @property {Any}				height								Any | 高度 (默认 44)
	 * @property {Any}				corner								Any | 圆角 (默认 0)
	 * @property {String}			selectedColor						String | 选中颜色 (默认 $ux.Conf.fontColor)
	 * @property {String}			unselectedColor						String | 未选中颜色 (默认 $ux.Conf.fontColor)
	 * @property {String}			background							String | 背景色 (默认 $ux.Conf.backgroundColor)
	 * @property {String} 			backgroundDark=[none|auto|color]	String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {String}			lineColor							String | 线颜色 selectedColor 优先级更高
	 * @property {Any}				lineWidth							Any | 线宽度 0则自动适配 (默认 20)
	 * @property {Any}				lineHeight							Any | 线高度、点直径 (默认 2)
	 * @property {Any}				fontSize							Any | 字体大小 (默认 $ux.Conf.fontSize)
	 * @property {Any}				selectSize							Any | 选中字体大小 (默认 $ux.Conf.fontSize)
	 * @property {Boolean}			bold = [true|false]					Boolean | 字体加粗 (默认 false)
	 * @property {Boolean}			selectBold = [true|false]			Boolean | 选中字体加粗 (默认 false)
	 * @property {Any}				padding								Any | 内部padding (默认 12)
	 * @property {Number}			duration							Number | 过渡时间 (默认 300)
	 * @property {Any}				offset								Any | 锚点偏移距离 (默认 0)
	 * @event {Function}			change								Function | 当前选择下标改变时触发
	 * @event {Function}			anchor								Function | 当要滚动目标时触发
	 * <AUTHOR>
	 * @date 2024-12-05 09:31:35
	 */
	
	import { PropType, computed, getCurrentInstance, nextTick, onMounted, ref, watch } from 'vue'
	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	import { UxTab } from '../../libs/types/types.uts'
	import { useThemeColor, useForegroundColor, useFontColor, useFontSize, UxThemeType } from '../../libs/use/style.uts'
	import { useResize } from '../../libs/use/resize'
	
	defineOptions({
		name: 'ux-tabs',
		mixins: [xstyleMixin]
	})
	
	const emit = defineEmits(['update:modelValue', 'change', 'anchor'])
	
	const props = defineProps({
		modelValue: {
			type: Number,
			default: 0
		},
		datas: {
			type: Array as PropType<UxTab[]>,
			default: () : UxTab[] => [] as UxTab[]
		},
		theme: {
			type: String,
			default: 'primary'
		},
		type: {
			type: String,
			default: 'line'
		},
		anim: {
			type: String,
			default: 'scroll'
		},
		anchorId: {
			type: String,
			default: ''
		},
		anchor: {
			type: Boolean,
			default: false
		},
		width: {
			default: 0
		},
		height: {
			default: 44
		},
		corner: {
			default: 0
		},
		background: {
			type: String,
			default: ''
		},
		backgroundDark: {
			type: String,
			default: ''
		},
		selectedColor: {
			type: String,
			default: ''
		},
		unselectedColor: {
			type: String,
			default: ''
		},
		lineColor: {
			type: String,
			default: ''
		},
		lineWidth: {
			default: 20
		},
		lineHeight: {
			default: 0
		},
		lineBottom: {
			default: 0
		},
		fontSize: {
			default: 0
		},
		selectSize: {
			default: 0
		},
		bold: {
			type: Boolean,
			default: false
		},
		selectBold: {
			type: Boolean,
			default: false
		},
		padding:{
			default: 12
		},
		duration: {
			type: Number,
			default: 300
		},
		offset: {
			default: 5
		}
	})
	
	const myId = `ux-tabs-${ $ux.Random.uuid() }`
	const ctx = ref<any | null>(null)
	const canvasId = `ux-tabs-canvas-${ $ux.Random.uuid() }`
	const dpr = uni.getWindowInfo().pixelRatio
	const scrollId = `ux-tabs-scroll-${ $ux.Random.uuid() }`
	const leftId = `ux-tabs-left-${ $ux.Random.uuid() }`
	const rightId = `ux-tabs-right-${ $ux.Random.uuid() }`
	const smileRef = ref<UniElement | null>(null)
	const pointRef = ref<UniElement | null>(null)
	const tabsRef = ref<UniElement[] | null>(null)
	const tabs = ref<UxTab[]>([] as UxTab[])
	const tabIndex = ref(0)
	const lastIndex = ref(0)
	const lineLeft = ref(0)
	const lineWidth = ref(20)
	const totalWidth = ref(0)
	const scrollviewId = ref('')
	
	const backgroundColor = computed(() : string => {
		return useForegroundColor(props.background, props.backgroundDark)
	})
	
	const selectedColor = computed(() : string => {
		if(props.selectedColor == '') {
			return useThemeColor(props.theme as UxThemeType)
		} else {
			return props.selectedColor
		}
	})
	
	const unselectedColor = computed(() : string => {
		return useFontColor(props.unselectedColor, '')
	})
	
	const lineColor = computed(() : string => {
		if(props.lineColor == '') {
			return selectedColor.value
		} else {
			return props.lineColor
		}
	})
	
	const fontSize = computed(() : number => {
		if($ux.Util.getPx(props.selectSize) > 0) {
			return $ux.Util.getPx(props.selectSize)
		} else {
			return useFontSize($ux.Util.getPx(props.selectSize), 0)
		}
	})
	
	const selectSize = computed(() : number => {
		if($ux.Util.getPx(props.selectSize) > 0) {
			return $ux.Util.getPx(props.selectSize)
		} else {
			return useFontSize($ux.Util.getPx(props.selectSize), 0)
		}
	})
	
	const lineHeight = computed(() : number => {
		if(props.type == 'smile') {
			return 12
		} else if(props.type == 'point') {
			return $ux.Util.getPx(props.lineHeight) > 0? $ux.Util.getPx(props.lineHeight) : 5
		} else {
			return $ux.Util.getPx(props.lineHeight) > 0? $ux.Util.getPx(props.lineHeight) : 2
		}
	})
	
	const style = computed(() => {
		let css = {}
		
		if($ux.Util.getPx(props.width) > 0) {
			css['width'] = `${$ux.Util.addUnit(props.width)}`
		}
		
		css['height'] = `${$ux.Util.addUnit(props.height)}`
		css['min-height'] = `${$ux.Util.addUnit(props.height)}`
		css['background-color'] =  backgroundColor.value
		css['border-radius'] =  `${$ux.Util.addUnit(props.corner)}`
		
		return css
	})
	
	function tabStyle(tab: UxTab) {
		let css = {}
		
		if(tab.disabled ?? false) {
			css['opacity'] = 0.6
		}
		
		return css
	}
	
	function textStyle(tab: UxTab) {
		let css = {}
		
		if(tabIndex.value == tab.index) {
			css['font-size'] = `${$ux.Util.addUnit(selectSize.value)}`
			css['color'] = selectedColor.value
			css['font-weight'] = props.selectBold ? 'bold' : 'normal'
		} else {
			css['font-size'] = `${$ux.Util.addUnit(fontSize.value)}`
			css['color'] = unselectedColor.value
			css['font-weight'] = props.bold ? 'bold' : 'normal'
		}
		
		css['padding-left'] = `${$ux.Util.addUnit(props.padding)}`
		css['padding-right'] = `${$ux.Util.addUnit(props.padding)}`
		
		return css
	}
	
	const warpStyle = computed(() => {
		let css = {}
		
		if(totalWidth.value) {
			css['width'] = `${$ux.Util.addUnit(totalWidth.value)}`
		}
		
		css['height'] = `${$ux.Util.addUnit(lineHeight.value + 2)}`
		css['bottom'] = `${$ux.Util.addUnit(props.lineBottom)}`
		
		return css
	})
	
	const lineStyle = computed(() => {
		let css = {}
		
		css['left'] = `${$ux.Util.addUnit(lineLeft.value)}`
		css['width'] = `${$ux.Util.addUnit(lineWidth.value)}`
		css['height'] = `${$ux.Util.addUnit(lineHeight.value)}`
		css['background-color'] = lineColor.value
		css['transition-duration'] = `${props.duration}ms`
		
		return css
	})
	
	const smileStyle = computed(() => {
		let css = {}
		
		css['left'] = `${$ux.Util.addUnit(lineLeft.value)}`
		css['width'] = `${$ux.Util.addUnit(20)}`
		css['height'] = `${$ux.Util.addUnit(lineHeight.value)}`
		css['transition-duration'] = `${props.duration}ms`
		
		return css
	})
	
	const pointStyle = computed(() => {
		let css = {}
		
		css['bottom'] = `${$ux.Util.addUnit(1)}`
		css['left'] = `${$ux.Util.addUnit(lineLeft.value)}`
		css['width'] = `${$ux.Util.addUnit(lineHeight.value)}`
		css['height'] = `${$ux.Util.addUnit(lineHeight.value)}`
		css['border-radius'] = `${$ux.Util.addUnit(lineHeight.value)}`
		css['background-color'] = lineColor.value
		css['transition-duration'] = `${props.duration}ms`
		
		return css
	})
	
	const modelValue = computed(():number => {
		return props.modelValue
	})
	
	async function scrollTo() {
		let node = tabsRef.value![tabIndex.value]
		let rect = await $ux.Util.getBoundingClientRect(`#${node._.attrs.id}`, getCurrentInstance()?.proxy)
		let arect = await $ux.Util.getBoundingClientRect(`#${scrollId}`, getCurrentInstance()?.proxy)
		let lrect = await $ux.Util.getBoundingClientRect(`#${leftId}`, getCurrentInstance()?.proxy)
		let rrect = await $ux.Util.getBoundingClientRect(`#${rightId}`, getCurrentInstance()?.proxy)
		let left = (arect.left ?? 0) - (lrect.width ?? 0)
		let right = (arect.right ?? 0) - (rrect.width ?? 0)
		
		if (left <= rect.left ?? 0) {
			let i = Math.max(0, tabIndex.value - 1)
			scrollviewId.value = tabsRef.value![i]._.attrs.id as string
		} else if (right <= rect.right ?? 0) {
			let i = Math.min(tabsRef.value!.length - 1, tabIndex.value + 1)
			scrollviewId.value = tabsRef.value![i]._.attrs.id as string
		} else {
			let i = Math.max(0, tabIndex.value - 1)
			scrollviewId.value = tabsRef.value![i]._.attrs.id as string
		}
	}
	
	async function animTo(id : string) {
		if (tabs.value.length == 0 || props.type == 'none') return
	
		let _totalWidth = 0
		let _lineLeft = 0
		let cur = false
		
		let index = 0
		let f = async () => {
			if(index == tabsRef.value.length) {
				return
			}
			
			let el = tabsRef.value[index]
			let rect = await $ux.Util.getBoundingClientRect(`#${el._.attrs.id}`, getCurrentInstance()?.proxy)
			let nodeWidth = rect.width ?? 0
			_totalWidth += nodeWidth
			if (id == el._.attrs.id) {
				cur = true
			} else {
				if(!cur) {
					_lineLeft += nodeWidth
				}
			}
			
			index++
			await f()
		}
		await f()
		
		totalWidth.value = _totalWidth
		
		let rect = await $ux.Util.getBoundingClientRect(`#${tabsRef.value![tabIndex.value]._.attrs.id}`, getCurrentInstance()?.proxy)
		let nodeWidth = rect.width ?? 0
		let _lineWidth = $ux.Util.getPx(props.lineWidth) == 0 ? nodeWidth * 0.6 : $ux.Util.getPx(props.lineWidth)
		_lineLeft += (nodeWidth - _lineWidth) / 2
		
		if(props.type == 'line') {
			if(props.anim == 'scroll') {
				lineWidth.value = _lineWidth
				lineLeft.value = _lineLeft
			} else if(props.anim == 'push') {
				let _targetWidth = 0
				
				let _index = 0
				let f2 = async () => {
					if(_index == tabsRef.value.length) {
						return
					}
					
					let el = tabsRef.value[_index]
					let rect = await $ux.Util.getBoundingClientRect(`#${el._.attrs.id}`, getCurrentInstance()?.proxy)
					
					if(lastIndex.value < tabIndex.value) {
						if(_index >= lastIndex.value && _index <= tabIndex.value) {
							_targetWidth += rect.width * 0.7
						}
					} else {
						if(_index <= lastIndex.value && _index >= tabIndex.value) {
							_targetWidth += rect.width * 0.7
						}
					}
					
					_index++
					await f2()
				}
				await f2()
				
				lineWidth.value = _targetWidth
				
				if(lastIndex.value > tabIndex.value) {
					lineLeft.value = _lineLeft
					setTimeout(() => {
						lineWidth.value = _lineWidth
					}, props.duration - 30);
				} else {
					setTimeout(() => {
						lineWidth.value = _lineWidth
						lineLeft.value = _lineLeft
					}, props.duration - 30);
				}
			}
		} else if(props.type == 'smile') {
			lineLeft.value = _lineLeft + 5
		} else if(props.type == 'point') {
			lineLeft.value = _lineLeft + 8
		}
		
		scrollTo()
	}
	
	async function initTabsHeight() {
		let index = 0
		let f = async () => {
			if(index == tabs.value.length) {
				return
			}
			
			let tab = tabs.value[index]
			if(tab.height == null && tab.anchorId != null) {
				let rect = await $ux.Util.getBoundingClientRect(`#${tab.anchorId}`)
				tab.height = rect.height
			}
			
			index++
			await f()
		}
		
		await f()
	}
	
	async function toAnchor(e: ScrollEvent) {
		await initTabsHeight()
		
		let scrollTop = e.detail.scrollTop
		
		let f = () => {
			let top = 0
			for (let i = 0; i < tabs.value.length; i++) {
				let h = tabs.value[i].height ?? 0
				if(top <= scrollTop && scrollTop <= top + h && i != tabIndex.value) {
					tabIndex.value = i
					animTo(tabs.value[tabIndex.value].id ?? '')
					break
				}
				
				top += h
			}
		}
		
		$ux.Util.debouncek(f, 20, 'ux-tabs')
	}
	
	async function scrollAnchor() {
		await initTabsHeight()
		
		let el = uni.getElementById(props.anchorId)
		if(el != null) {
			let scrollTop = 0
			for (let i = 0; i < tabs.value.length; i++) {
				let h = tabs.value[i].height ?? 0
				if(i < tabIndex.value) {
					scrollTop += h
				}
			}
			
			scrollTop -= $ux.Util.getPx(props.offset)
			
			// #ifdef APP
			el.setAttribute('scroll-top', `${scrollTop}`)
			// #endif
			// #ifndef APP
			el.scrollTop = scrollTop
			// #endif
			
			emit('anchor', scrollTop)
		}
	}
	
	/**
	 * ctx
	 */
	function getCtx(): any {
		return ctx.value!
	}
	
	function draw() {
		ctx.value = uni.createCanvasContext(canvasId, getCurrentInstance()?.proxy)
		
		// #ifdef WEB
		// @ts-ignore
		// ctx.value.width = canvas.offsetWidth * dpr
		// ctx.value.height = canvas.offsetHeight * dpr
		// ctx.value!.scale(dpr, dpr)
		// #endif
		
		const _ctx = getCtx()
		
		_ctx.beginPath()
		_ctx.arc(5, 5, 6, 0, Math.PI)
		_ctx.closePath()
		_ctx.strokeStyle = lineColor.value
		_ctx.stroke()
		
		_ctx.draw()
	}
	
	async function click(tab : UxTab, index : number) {
		if (tab.disabled ?? false) {
			return;
		}
		
		lastIndex.value = tabIndex.value
		tabIndex.value = index
		
		if(props.anchor) {
			await scrollAnchor()
		}
		
		emit('update:modelValue', index);
		emit('change', index);
	
		animTo(tab.id ?? '')
	}
	
	const tabsData = computed((): UxTab[] => {
		return props.datas
	})
	
	function initTabs() {
		let _tabs = [] as UxTab[]
			
		tabsData.value.forEach((e : UxTab, i : number) => {
			_tabs.push({
				id: e.id ?? `${myId}-${i}`,
				anchorId: e.anchorId ?? '',
				name: e.name,
				badge: e.badge ?? 0,
				reddot: e.reddot ?? false,
				index: i,
				disabled: e.disabled,
			} as UxTab)
		})
		
		tabs.value = _tabs
	}
	
	watch(tabsData, () => {
		initTabs()
	}, {immediate: true, deep: true})
	
	watch(modelValue, () => {
		if (modelValue.value == tabIndex.value) {
			return
		}
		
		lastIndex.value = tabIndex.value
		tabIndex.value = modelValue.value
		
		let index = tabs.value.findIndex((el : UxTab) : boolean => el.index == tabIndex.value)
		if (index != -1) {
			emit('change', index);
			animTo(tabs.value[index].id ?? '')
		}
	})
	
	onMounted(() => {
		lastIndex.value = tabIndex.value
		tabIndex.value = modelValue.value
		
		nextTick(() => {
			if(props.type == 'smile') {
				draw()
			}
			
			let index = tabs.value.findIndex((el : UxTab) : boolean => el.index == tabIndex.value)
			if (index != -1) {
				animTo(tabs.value[index].id ?? '')
			}
		})
		
		useResize(uni.getElementById(myId), () => {
			if(tabs.value.length == 0) {
				return
			}
			
			animTo(tabs.value[tabIndex.value].id ?? '')
		})
	})
	
	defineExpose({
		toAnchor
	})
</script>

<style lang="scss" scoped>
	/* #ifdef H5 */
	uni-view {
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: stretch;
		align-content: stretch;
		justify-content: flex-start;
		box-sizing: border-box;
		min-height: 0px;
		min-width: 0px;
		overflow: hidden;
	}
	/* #endif */
	
	.ux-tabs {
		width: 100%;
		display: flex;
		flex-direction: row;
		align-items: center;
		position: relative;
		
		&__scroll {
			flex: 1;
			height: 100%;
			display: flex;
			flex-direction: row;
			flex-wrap: nowrap;
			justify-content: flex-start;
			align-items: center;
			position: relative;
			white-space: nowrap
		}
		
		&__item {
			height: 100%;
			display: inline-block;
			flex-direction: row;
			flex-wrap: nowrap;
			justify-content: center;
			align-items: center;
			position: relative;
		}
		
		&__text {
			transition-duration: 300ms;
			transition-property: font-size, color;
			transition-timing-function: cubic-bezier(0, 0.55, 0.45, 1);
		}
		
		&__warp {
			position: absolute;
		}
		
		&__line--scroll {
			position: absolute;
			left: 0px;
			width: 20px;
			border-radius: 6px;
			transition-duration: 300ms;
			transition-property: left, width;
			transition-timing-function: cubic-bezier(0, 0.55, 0.45, 1);
		}
		
		&__line--push {
			position: absolute;
			left: 0px;
			width: 20px;
			border-radius: 6px;
			transition-duration: 300ms;
			transition-property: left, width;
		}
		
		&__smile {
			position: absolute;
			left: 0px;
			width: 16px;
			transition-duration: 300ms;
			transition-property: left;
			transition-timing-function: cubic-bezier(0, 0.55, 0.45, 1);
			
			&--canvas {
				width: 100%;
				height: 100%;
			}
		}
		
		&__point {
			position: absolute;
			left: 0px;
			width: 5px;
			height: 5px;
			border-radius: 5px;
			transition-duration: 300ms;
			transition-property: transform, opacity;
		}
	}
	
</style>