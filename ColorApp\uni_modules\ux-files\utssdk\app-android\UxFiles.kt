import io.dcloud.uts.UTSAndroid
import io.dcloud.uts.console
import android.app.Activity
import android.content.Intent
import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.os.Environment
import android.provider.MediaStore
import androidx.core.content.ContextCompat
import android.content.ContentUris
import java.io.File
import android.Manifest
import android.content.pm.PackageManager
import androidx.core.app.ActivityCompat
import android.provider.OpenableColumns
import android.os.Build
import android.provider.Settings
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import android.provider.DocumentsContract
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.io.OutputStream
import java.io.ByteArrayOutputStream
import java.util.UUID
import android.webkit.MimeTypeMap

object UxFiles {
	
	var gCursor: Cursor? = null
	
	val OPEN_FILES_RESULT = 10000
	val ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION_RESULT = 10001
	val READ_EXTERNAL_STORAGE_RESULT = 10002
	
	var CALLBACK: (files: MutableList<MutableMap<String, Any?>>) -> Unit = {}
	
	val requestResult: (requestCode: Int, resultCode: Int, intent: Intent?) -> Unit = { requestCode, _, intent ->
		if(requestCode == OPEN_FILES_RESULT) {
			if(intent != null) {
				val files: MutableList<MutableMap<String, Any?>> = mutableListOf()
				
				if(intent.clipData != null) {
					val count = intent.clipData!!.itemCount
					for (i in 0 until count) {
					    val item = intent.clipData!!.getItemAt(i)
					    files.add(handleUri(item.uri))
					}
				} else if(intent.data != null) {
					files.add(handleUri(intent.data!!))
				}
				
				CALLBACK(files)
			}
		} else if(requestCode == ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION_RESULT) {
			// 授权访问所有资源权限
		} else if(requestCode == READ_EXTERNAL_STORAGE_RESULT) {
			// 授权访问存储权限
		}
	}
	
	fun openFileManager(mimeTypes: Array<String>, multiple: Boolean, callback: (files: MutableList<MutableMap<String, Any?>>) -> Unit): Boolean {
		
		CALLBACK = callback
		
		UTSAndroid.onAppActivityResult(requestResult);
		
		val intent = Intent(Intent.ACTION_GET_CONTENT)
		if(mimeTypes.size == 0) {
			intent.type = "*/*"
		} else {
			intent.type = mimeTypes[mimeTypes.size-1]
		}
		intent.addCategory(Intent.CATEGORY_OPENABLE)
		intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, multiple)
		intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
		
		UTSAndroid.getUniActivity()!!.startActivityForResult(intent, OPEN_FILES_RESULT)
			
		return true
	}
	
	fun searchFiles(mimeTypes: Array<String>, callback: (file: MutableMap<String, Any?>) -> Unit): Boolean {
		val context = UTSAndroid.getAppContext()!!
		val activity = UTSAndroid.getUniActivity()!!
		
		if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
		    //判断是否有管理外部存储的权限
		    if (!Environment.isExternalStorageManager()) {
				
		        val intent = Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION);
		        intent.setData(Uri.parse("package:" + context.getPackageName()));
		        activity.startActivityForResult(intent, ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION_RESULT);
				return false
		    }
		}
		
		val p1 = ContextCompat.checkSelfPermission(context, Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED 
		var p2 = ContextCompat.checkSelfPermission(context, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED
		
		if (p1 || p2) {
		    ActivityCompat.requestPermissions(activity,
		        arrayOf(
					Manifest.permission.READ_EXTERNAL_STORAGE,
					Manifest.permission.WRITE_EXTERNAL_STORAGE),
		        READ_EXTERNAL_STORAGE_RESULT)
			return false
		}
		
		val projection = arrayOf(
		    MediaStore.Files.FileColumns._ID,
		    MediaStore.Files.FileColumns.DISPLAY_NAME,
		    MediaStore.Files.FileColumns.SIZE,
		    MediaStore.Files.FileColumns.DATE_MODIFIED
		)
		
		val selection = "${MediaStore.Files.FileColumns.MIME_TYPE} IN (${ mimeTypes.joinToString(",") { "?" } })"
		val selectionArgs = mimeTypes
		val sortOrder = "${MediaStore.Files.FileColumns.DATE_MODIFIED} DESC"
		
		stopSearch()
		gCursor = context.contentResolver.query(
		    MediaStore.Files.getContentUri("external"),
		    projection,
		    selection,
		    selectionArgs,
		    sortOrder
		)
		
		gCursor?.use { it ->
		    val idColumn = it.getColumnIndexOrThrow(MediaStore.Files.FileColumns._ID)
		    val nameColumn = it.getColumnIndex(MediaStore.Files.FileColumns.DISPLAY_NAME)
		    val sizeColumn = it.getColumnIndex(MediaStore.Files.FileColumns.SIZE)
		    val dateColumn = it.getColumnIndex(MediaStore.Files.FileColumns.DATE_MODIFIED)
			
		    while (it.moveToNext()) {
		        val id = it.getLong(idColumn)
		        val fileName = if (nameColumn == -1) "" else it.getString(nameColumn)
		        val fileSize = if (sizeColumn == -1) 0 else it.getLong(sizeColumn)
		        val fileDate = if (dateColumn == -1) 0 else it.getLong(dateColumn)
				
		        val uri: Uri = ContentUris.withAppendedId(
		            MediaStore.Files.getContentUri("external"),
		            id
		        )
				
				var filePath = getCachePath(uri)
				
				val file = mutableMapOf<String, Any?>()
				file["name"] = fileName
				file["path"] = filePath
				file["extension"] = getExtension(filePath)
				file["blob"] = null
				file["size"] = fileSize
				file["date"] = formatDate(fileDate)
				
				callback(file)
		    }
		}
		
		stopSearch()
		
		return true
	}
	
	fun stopSearch() {
		gCursor?.close()
		gCursor = null
	}
	
	fun handleUri(uri: Uri): MutableMap<String, Any?> {
		val contentResolver = UTSAndroid.getAppContext()!!.contentResolver
		
		var filePath = getUriPath(uri) ?: ""
		
		var fileDate = ""
		
		var fileName = filePath.substringBeforeLast("/").substringBeforeLast(".")
		if(fileName.isNullOrBlank()) {
			if (Build.VERSION.SDK_INT > Build.VERSION_CODES.Q) {
			    val cursor = contentResolver.query(uri, null, null, null, null)
			    cursor?.use {
			        if (it.moveToFirst()) {
			            val nameIndex = it.getColumnIndex(OpenableColumns.DISPLAY_NAME)
			            if (nameIndex != -1) {
			                fileName = it.getString(nameIndex)
			            }
						
						// val dateColumn = it.getColumnIndex(OpenableColumns.DATE_MODIFIED)
						// if (dateColumn != -1) {
						//     fileDate = formatDate(it.getLong(dateColumn))
						// }
			        }
			    }
			}
		}
		
		val fileDesc = contentResolver.openFileDescriptor(uri, "r")
		val filesize = fileDesc?.statSize ?: 0
		
		val file = mutableMapOf<String, Any?>()
		file["name"] = fileName
		file["path"] = filePath
		file["extension"] = getExtension(filePath)
		file["blob"] = null
		file["size"] = filesize
		file["date"] = fileDate
		
		return file
	}
	
	fun getUriPath(uri: Uri): String? {
		val context = UTSAndroid.getAppContext()!!
		
		val bytes = readFileToByteArray(uri, 1024)
		
		var originalFileName: String? = ""
		val path = uri.path
		var lastIndex = path?.lastIndexOf('/')?: -1
		if (lastIndex >= 0) {
		   originalFileName = path?.substring(lastIndex + 1)
		}
		
		var fileExtension: String? = ""
		lastIndex = originalFileName?.lastIndexOf('.') ?: 0
		if (lastIndex >= 0) {
		   fileExtension = originalFileName?.substring(lastIndex)
		}
		
		if(fileExtension == null || fileExtension == "") {
			fileExtension = getFileExtension(context, uri)
		}
		
		val name = "${UUID.randomUUID()}$fileExtension"
		return saveBytesAsFileToCache(bytes, context, name)
	}
	
	fun getCachePath(uri: Uri): String? {
		val context = UTSAndroid.getAppContext()!!
		
		val path = getRealPathFromUri(context, uri)
		val cachePath = copyFileToAppDir(context, path, File(path).name)
		
		return cachePath
	}
	
	fun getExtension(path: String?): String {
	    if (!path.isNullOrEmpty()) {
	        val dotIndex = path.lastIndexOf('.')
	        if (dotIndex > 0) {
	            return path.substring(dotIndex + 1)
	        }
	    }
		
		return ""
	}
	
	fun getFileExtension(context: Context, uri: Uri): String? {
	    val mimeTypeMap = MimeTypeMap.getSingleton()
	    val extension = mimeTypeMap.getExtensionFromMimeType(context.contentResolver.getType(uri))
	    return if (extension.isNullOrEmpty()) null else ".$extension"
	}
	
	fun formatDate(dateLong: Long): String {
		if (dateLong > 0) {
	       val instant = Instant.ofEpochMilli(dateLong)
	       val date = LocalDateTime.ofInstant(instant, ZoneId.systemDefault())
	       val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
	       return date.format(formatter)
		}
		
		return ""
	}
	
	fun getRealPathFromUri(context: Context, uri: Uri): String {
	    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT && DocumentsContract.isDocumentUri(context, uri)) {
	        when {
	            isExternalStorageDocument(uri) -> {
	                val docId = DocumentsContract.getDocumentId(uri)
	                val split = docId.split(":")
	                val type = split[0]
	                if ("primary".equals(type, ignoreCase = true)) {
	                    File(Environment.getExternalStorageDirectory(), split[1]).absolutePath
	                } else {
	                    ""
	                }
	            }
	            isDownloadsDocument(uri) -> {
	                val id = DocumentsContract.getDocumentId(uri)
	                val contentUri = ContentUris.withAppendedId(
	                    Uri.parse("content://downloads/public_downloads"),
	                    id.toLong()
	                )
	                getDataColumn(context, contentUri)
	            }
	            isMediaDocument(uri) -> {
	                val docId = DocumentsContract.getDocumentId(uri)
	                val split = docId.split(":")
	                val type = split[0]
	                val contentUri = when (type) {
	                    "image" -> MediaStore.Images.Media.EXTERNAL_CONTENT_URI
	                    "video" -> MediaStore.Video.Media.EXTERNAL_CONTENT_URI
	                    "audio" -> MediaStore.Audio.Media.EXTERNAL_CONTENT_URI
	                    else -> null
	                }
	                if (contentUri!= null) {
	                    val selection = "_id=?"
	                    val selectionArgs = arrayOf(split[1])
	                    getDataColumn(context, contentUri, selection, selectionArgs)
	                } else {
	                    ""
	                }
	            }
	            else -> ""
	        }
	    } else {
	        when (uri.scheme) {
	            "content" -> getDataColumn(context, uri)
	            "file" -> uri.path.orEmpty()
	            else -> ""
	        }
	    }
	}
	
	private fun isExternalStorageDocument(uri: Uri): Boolean {
	    return "com.android.externalstorage.documents".equals(uri.authority)
	}
	
	private fun isDownloadsDocument(uri: Uri): Boolean {
	    return "com.android.downloads.documents".equals(uri.authority)
	}
	
	private fun isMediaDocument(uri: Uri): Boolean {
	    return "com.android.media.documents".equals(uri.authority)
	}
	
	private fun getDataColumn(context: Context, uri: Uri, selection: String? = null, selectionArgs: Array<String>? = null): String {
	    val column = "_data"
	    val projection = arrayOf(column)
	    context.contentResolver.query(uri, projection, selection, selectionArgs, null)?.use { cursor ->
	        if (cursor.moveToFirst()) {
	            val index = cursor.getColumnIndex(column)
	            return cursor.getString(index)
	        }
	    }
	    return ""
	}
	
	fun copyFileToAppDir(context: Context, sourceFilePath: String, targetFileName: String): String? {
	    val appDir = context.getExternalFilesDir(null)
	    appDir?.let {
	        val targetFile = File(it, targetFileName)
	        try {
	            FileInputStream(sourceFilePath).use { inputStream ->
	                FileOutputStream(targetFile).use { outputStream ->
	                    val buffer = ByteArray(1024)
	                    var length: Int
	                    while (inputStream.read(buffer).also { length = it } > 0) {
	                        outputStream.write(buffer, 0, length)
	                    }
	                }
	            }
	        } catch (e: IOException) {
	            e.printStackTrace()
	        }
	    }
		
		return appDir?.absolutePath!! + "/" + targetFileName
	}
	
	fun readFileToByteArray(uri: Uri, sizeBlock: Long): ByteArray? {
	    val contentResolver = UTSAndroid.getAppContext()!!.contentResolver
	    val inputStream = contentResolver.openInputStream(uri)?: return null
		
	    val byteArrayOutputStream = ByteArrayOutputStream()
	    val buffer = ByteArray(sizeBlock.toInt())
	    var n = inputStream.read(buffer)
	
	    while (n != -1) {
	        byteArrayOutputStream.write(buffer, 0, n)
	        n = inputStream.read(buffer)
	    }
	
	    val arr = byteArrayOutputStream.toByteArray()
	    try {
	        byteArrayOutputStream.flush()
	    } catch (e: Exception) {
	        byteArrayOutputStream.close()
	    }
		
		return arr
	}
	
	fun saveBytesAsFileToCache(bytes: ByteArray?, context: Context, fileName: String): String? {
	    val cacheDir = context.getCacheDir()
	    val file = File(cacheDir, fileName)
	    try {
	        FileOutputStream(file).use { outputStream ->
	            outputStream.write(bytes)
	        }
	    } catch (e: Exception) {
	        e.printStackTrace()
	    }
		
		return file.absolutePath
	}
}