<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/popover?title=Popover"></Mobile>

# Popover
> 组件类型：UxPopoverComponentPublicInstance

支持任何位置弹出并且自动计算在适当的位置显示

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| targetId | String |  | 目标id |
| width | Any |  | 宽度 |
| offset | Any | 10 | 偏移 |
| align | Boolean | false | 与目标水平对齐 |
| backgroundColor | String | #000000 | 背景色 |
| showMask | Boolean | true | 显示遮罩 |
| showBlur | Boolean | false | 开启模糊背景 |
| blurRadius | Number | 10 | 模糊半径 |
| blurColor | String | rgba(10,10,10,0.3) | 模糊颜色 |
| opacity | Number | `$ux.Conf.maskAlpha` | 遮罩透明度0-1 |
| zIndex | Number | 10001 | 层级z-index |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| change | 显示状态改变时触发 |  |
  
  