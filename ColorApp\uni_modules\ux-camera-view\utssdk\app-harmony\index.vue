<template>
	<view></view>
</template>

<script lang="uts">

	export default {
		name: 'ux-camera-view',
		emits: ['take'],
		props: {
			mode: {
				type: String,
				default: 'take'
			}
		},
		data() {
			return {
				
			}
		},
		NVLoaded() {
			
		},
		NVBeforeUnload() {
			
		},
		methods: {
			open() {
				
			},
			close() {
				
			},
			take(correctOrientation: boolean) {
				
			},
			switch(isFront : boolean) {
				
			},
			flash(isFlash : boolean) {
				
			},
			torch(isTorch : boolean) {
				
			}
		},
	}
</script>

<style>

</style>