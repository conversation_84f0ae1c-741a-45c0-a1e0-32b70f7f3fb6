<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/sidebar?title=Sidebar"></Mobile>

# Sidebar
> 组件类型：UxSidebarComponentPublicInstance

支持滑动距离自动切换到锚点

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| default | Slot |  | 默认插槽 |
| [theme](#theme) | String | info | 按钮类型 |
| modelValue | Number | 0 | 双向绑定值 |
| datas | UxTab[] |  | sidebar列表 |
| [type](#type) | String | line | 类型 |
| [anim](#anim) | String | scroll | 动效类型 |
| anchorId | String |  | 锚点ScrollId |
| width | Any | auto | 宽度 |
| height | Any | auto | 高度 |
| tabWidth | Any | 100 | Tab宽度 |
| tabHeight | Any | 44 | Tab高度 |
| corner | Any | 0 | 圆角 |
| selectedColor | String | `$ux.Conf.fontColor` | 选中颜色 |
| unselectedColor | String | `$ux.Conf.fontColor` | 未选中颜色 |
| background | String | `$ux.Conf.backgroundColor` | 背景色 |
| [backgroundDark](#backgroundDark) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| lineColor | String |  | 线颜色selectedColor优先级更高 |
| lineWidth | Any | 2 | 线宽度0则自动适配 |
| lineHeight | Any | 30 | 线高度、点直径 |
| fontSize | Any | `$ux.Conf.fontSize` | 字体大小 |
| selectSize | Any | `$ux.Conf.fontSize` | 选中字体大小 |
| bold | Boolean | false | 字体加粗 |
| selectBold | Boolean | false | 选中字体加粗 |
| duration | Number | 300 | 过渡时间 |
| padding | Any | 12 | 内部padding |
| offset | Any | 0 | 锚点偏移距离 |

### [theme](#theme)

| 值   | 说明 |
|:------:|:----:|
| text | 文字
 |
| info | 默认
 |
| primary | 主要
 |
| success | 成功
 |
| warning | 警告
 |
| error | 错误
 |

### [type](#type)

| 值   | 说明 |
|:------:|:----:|
| none无
 |  |
| line下划线
 |  |
| point点
 |  |

### [anim](#anim)

| 值   | 说明 |
|:------:|:----:|
| none无
 |  |
| scroll左右滚动
 |  |
| push左右推压
 |  |

### [backgroundDark](#backgroundDark)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| change | 当前选择下标改变时触发 |  |
| anchor | 当要滚动目标时触发 |  |
  
  