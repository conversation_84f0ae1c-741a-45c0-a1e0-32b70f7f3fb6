/**
 * 表单分组样式工具函数
 * 统一管理设计模式和预览模式的分组样式
 */

/**
 * 获取分组样式类名
 * @param {string} groupClass - 分组样式类型 (card, border, background, collapse)
 * @param {boolean} isDesignMode - 是否为设计模式
 * @returns {string} CSS类名字符串
 */
export function getGroupStyleClass(groupClass, isDesignMode = false) {
  if (!groupClass) return '';
  
  // 基础样式映射
  const baseStyleMap = {
    'card': 'form-group-card',
    'border': 'form-group-border', 
    'background': 'form-group-background',
    'collapse': 'form-group-collapse'
  };
  
  const baseClass = baseStyleMap[groupClass] || '';
  
  // 如果是设计模式，添加设计模式特有的类名
  if (isDesignMode && baseClass) {
    return `${baseClass} ${baseClass}-design`;
  }
  
  return baseClass;
}

/**
 * 获取分组样式对象（用于动态绑定class）
 * @param {string} groupClass - 分组样式类型
 * @param {boolean} isDesignMode - 是否为设计模式
 * @returns {Object} 样式对象
 */
export function getGroupStyleObject(groupClass, isDesignMode = false) {
  if (!groupClass) return {};
  
  const styleClass = getGroupStyleClass(groupClass, isDesignMode);
  if (!styleClass) return {};
  
  // 将类名字符串转换为对象
  const classes = styleClass.split(' ');
  const styleObject = {};
  
  classes.forEach(className => {
    if (className.trim()) {
      styleObject[className.trim()] = true;
    }
  });
  
  return styleObject;
}

/**
 * 获取分组样式的内联样式（如果需要）
 * @param {string} groupClass - 分组样式类型
 * @param {boolean} isDesignMode - 是否为设计模式
 * @returns {Object} 内联样式对象
 */
export function getGroupInlineStyles(groupClass, isDesignMode = false) {
  // 目前主要使用CSS类，如果后续需要动态样式可以在这里扩展
  return {};
}

/**
 * 检查是否为有效的分组样式
 * @param {string} groupClass - 分组样式类型
 * @returns {boolean} 是否有效
 */
export function isValidGroupStyle(groupClass) {
  const validStyles = ['card', 'border', 'background', 'collapse'];
  return validStyles.includes(groupClass);
}

/**
 * 获取所有可用的分组样式选项
 * @returns {Array} 分组样式选项数组
 */
export function getGroupStyleOptions() {
  return [
    { value: '', label: '默认样式' },
    { value: 'card', label: '卡片样式' },
    { value: 'border', label: '边框样式' },
    { value: 'background', label: '背景样式' },
    { value: 'collapse', label: '折叠样式' }
  ];
}

/**
 * 获取分组样式的描述信息
 * @param {string} groupClass - 分组样式类型
 * @returns {string} 样式描述
 */
export function getGroupStyleDescription(groupClass) {
  const descriptions = {
    'card': '卡片样式 - 带边框、阴影和圆角的卡片效果',
    'border': '边框样式 - 简单的边框包围效果',
    'background': '背景样式 - 浅色背景区分效果',
    'collapse': '折叠样式 - 可折叠的分组标题栏'
  };

  return descriptions[groupClass] || '默认样式';
}

/**
 * 获取分组内字段项的样式类名
 * @param {Object} field - 字段对象
 * @returns {string} CSS类名字符串
 */
export function getGroupItemClass(field) {
  if (!field || !field.Class) return '';

  // 基础样式映射
  const itemStyleMap = {
    'col-span-full': 'col-span-full',  // 单行排列
    'col-span-2': 'col-span-2',        // 2行排列
    'col-span-3': 'col-span-3',        // 3行排列
    'col-span-4': 'col-span-4',        // 4行排列
    'col-span-6': 'col-span-6',        // 6行排列
    'col-span-12': 'col-span-12'       // 12行排列
  };

  return itemStyleMap[field.Class] || field.Class;
}
