<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/sprite?title=Sprite"></Mobile>

# Sprite
> 组件类型：UxSpriteComponentPublicInstance



## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| frames | Array |  | 精灵图集 |
| duration | Number | 100 | 每帧周期 |
| play | Boolean | false | 是否播放 |
| loop | Boolean | false | 循环播放 |
| autoplay | Boolean | false | 自动播放 |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| start | 开始时触发 |  |
| end | 结束时触发 |  |
  
  