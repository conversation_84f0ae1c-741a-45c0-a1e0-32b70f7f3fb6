
<p align="center">
    <img alt="logo" src="https://www.uxframe.cn/logo/logo.png" width="120" height="120" style="margin-bottom: 10px;">
</p>
<h3 align="center" style="margin: 30px 0 30px;font-weight: bold;font-size:40px;">UxFrame Plus原生SDK 1.1.4</h3>

## 特别说明

如果您已经购买了[UxFrame 低代码高性能UI框架](https://ext.dcloud.net.cn/plugin?id=16148), 则无需再次购买本插件，请点击上方`进入交流群`联系我免费获取离线版插件！

## 已实现功能 ⬇️

- 全局置灰 (Android、iOS、Web)
- 隐藏键盘 (Android、iOS、Web)
- 拨打电话 (Android、iOS、Web)
- 打开链接 (Android、iOS、Web)
- 打开Web页面 (Android、iOS、Web)
- 震动 (Android、iOS)
- 复制粘贴内容 (Android、iOS)
- 退出APP (Android、iOS)

### 案例

``` ts

import * as plus from "@/uni_modules/ux-plus"

function setGray(gray: number) {
	plus.setGray(gray)
}

function openURL() {
	let brand = uni.getSystemInfoSync().deviceBrand
	let pkgName = 'com.tencent.mm'
	let market = ''
		
	// #ifndef APP-IOS
	if (brand == 'huawei') {
		market = `market://details?id=${pkgName}`
	} else if (brand == 'xiaomi') {
		market = `mimarket://details?id=${pkgName}`
	} else if (brand == 'oppo') {
		market = `oppomarket://details?packagename=${pkgName}`
	} else if (brand == 'vivo') {
		market = `vivomarket://details?id=${pkgName}`
	} else if (brand == 'samsung') {
		market = `samsungapps://ProductDetail/${pkgName}`
	}
	// #endif
	
	// #ifdef APP-IOS
	market = `itms-apps://itunes.apple.com/app?id=${pkgName}`
	// #endif
	
	plus.openURL({
		url: market
	} as plus.UxOpenURLOptions)
}

function openWeb() {
	plus.openWeb({
	title: '在线文档',
	url: 'https://www.uxframe.cn/frame/guide.html',
	// blur: 1,
	success: () => {
		// 打开时回调
	},
	complete: () => {
		// 关闭时回调
	}
})
}

function makePhoneCall() {
	plus.makePhoneCall({
		phoneNumber: '18666888866'
	} as plus.UxMakePhoneCallOptions)
}

function vibrate1() {
	plus.vibrate({
		type: 'light'
	} as plus.UxVibrateOptions)
}

function vibrate2() {
	plus.vibrate({
		type: 'medium'
	} as plus.UxVibrateOptions)
}

function vibrate3() {
	plus.vibrate({
		type: 'heavy'
	} as plus.UxVibrateOptions)
}

function copyData() {
	plus.setClipboardData('哈哈哈哈哈')
}

function getData() {
	uni.showToast({
		title: plus.getClipboardData(),
		icon: 'none'
	})
}

function exitAPP() {
	plus.exit()
}

```

### 更多功能持续集成中...

## 文档教程 ⬇️

[https://www.uxframe.cn](https://www.uxframe.cn)
