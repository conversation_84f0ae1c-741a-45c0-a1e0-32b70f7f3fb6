
<p align="center">
    <img alt="logo" src="https://www.uxframe.cn/logo/logo.png" width="120" height="120" style="margin-bottom: 10px;">
</p>
<h3 align="center" style="margin: 30px 0 30px;font-weight: bold;font-size:40px;">UxFrame 企业微信SDK 1.0.2</h3>

## 前言介绍

欢迎使用UxFrame WxworkSDK！此插件是基于[weworksdk](https://developer.work.weixin.qq.com/document/path/91194)开发，轻松实现企业微信授权登录和企业微信分享等功能！

## 官方文档

[https://www.uxframe.cn](https://www.uxframe.cn)

```ts

import * as wxwork from "@/uni_modules/ux-wxwork"

// 注册
wxwork.register({
	appid: '您的appid',
	agentid: '您的agentid',
	schema: '您的schema',
	success: (res) => {
		console.log(res)
	}
} as UxRegisterOptions)

// 登录
wxwork.login({
	success: (res) => {
		console.log(res);
	},
	fail: (err) => {
		console.log(err);
	}
} as UxLoginOptions)

// 分享图文
wxwork.share({
	type: 0,
	title: 'UxFrame低代码高性能UI框架',
	summary: 'UxFrame是基于UNI-APP-X开发的低代码高性能原生UI框架',
	href: 'https://www.uxframe.cn',
	imageUrl: 'https://www.uxframe.cn/logo/logo.png',
	success: (res) => {
		console.log(res);
	},
	fail: (err) => {
		console.log(err);
	}
} as UxShareOptions)

// 分享文本
wxwork.share({
	type: 1,
	title: 'UxFrame低代码高性能UI框架',
	success: (res) => {
		console.log(res);
	},
	fail: (err) => {
		console.log(err);
	}
} as UxShareOptions)

// 分享图片
wxwork.share({
	type: 2,
	// imageUrl: '/static/logo.png'
	imageUrl: 'https://www.uxframe.cn/logo/logo.png',
	success: (res) => {
		console.log(res);
	},
	fail: (err) => {
		console.log(err);
	}
} as UxShareOptions)

// 分享视频
wxwork.share({
	type: 4,
	title: 'UxFrame低代码高性能UI框架',
	summary: 'UxFrame是基于UNI-APP-X开发的低代码高性能原生UI框架',
	// imageUrl: '/static/logo.png'
	imageUrl: 'https://www.uxframe.cn/logo/logo.png',
	mediaUrl: 'https://www.uxframe.cn/source/intro/demo.mp4',
	success: (res) => {
		console.log(res);
	},
	fail: (err) => {
		console.log(err);
	}
} as UxShareOptions)

// 分享文件
wxwork.share({
	type: 3,
	filePath: 'https://www.uxframe.cn/ux/ux.pdf',
	success: (res) => {
		console.log(res);
	},
	fail: (err) => {
		console.log(err);
	}
} as UxShareOptions)

// 分享小程序
wxwork.share({
	type: 5,
	title: 'UxFrame低代码高性能UI框架',
	summary: 'UxFrame是基于UNI-APP-X开发的低代码高性能原生UI框架',
	// imageUrl: '/static/logo.png'
	imageUrl: 'https://www.uxframe.cn/logo/logo.png',
	miniProgram: {
		id: '小程序id',
		type: 0,
		path: '',
		webUrl: 'https://www.uxframe.cn'
	} as UxMiniProgram,
	success: (res) => {
		console.log(res);
	},
	fail: (err) => {
		console.log(err);
	}
} as UxShareOptions)

// 打开企业微信
wxwork.openApp()

// 检测是否安装企业微信
wxwork.isInstalled()

```
