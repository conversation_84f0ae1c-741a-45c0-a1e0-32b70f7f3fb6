## ux-form表单组件示例代码：
`
<template>
	<ux-page :stack="showDoc">
		<ux-navbar :title="title" :bold="true">
			<template v-slot:right>
				<!-- #ifndef MP -->
				<ux-button theme="text" icon="/static/tip.png" :icon-size="22" @click="onDoc()"></ux-button>
				<!-- #endif -->
			</template>
		</ux-navbar>
		
		<ux-scroll>
			<ux-card direction="column" icon="flag-filled" title="表单" :bold="true">
				<ux-text text="支持任何组件，只作为数据校验"></ux-text>
			</ux-card>
			
			<ux-card direction="column" icon="arrowright" title="表单校验" :bold="true">
				<ux-form ref="uxFormRef" v-model="formData" @submit="change">
					<ux-form-item label="头像" field="avatar" :required="true" :min-height="68">
						<ux-upload v-model="(formData['avatar'] as string)" url="https://test.api.fdproxy.cn/file/upload/add" :count="1" mode="image" align="right" :rows="false" :width="42" :height="42"></ux-upload>
					</ux-form-item>
					
					<ux-form-item label="姓名" field="name" :required="true" :rules="nameRules">
						<ux-input v-model="(formData['name'] as string)" placeholder="请输入您的姓名" background="transparent" background-dark="transparent" align="right" border="none" :focus-border="false"></ux-input>
					</ux-form-item>
					
					<ux-form-item label="手机号" field="phone" :required="true" :rules="phoneRules">
						<ux-input v-model="(formData['phone'] as string)" :maxlength="11" placeholder="请输入您的手机号" background="transparent" background-dark="transparent" align="right" border="none" :focus-border="false"></ux-input>
					</ux-form-item>
					
					<ux-form-item label="登录密码" field="password" :required="true" :rules="passwordRules">
						<ux-input v-model="(formData['password'] as string)" :password="true" placeholder="请输入您的登录密码" background="transparent" background-dark="transparent" align="right" border="none" :focus-border="false"></ux-input>
					</ux-form-item>
					
					<ux-form-item label="性别" field="sex" :verify="false">
						<ux-row justify="right">
							<ux-radio-group v-model="(formData['sex'] as string)">
								<ux-radio value="0" text="女神" color="#333" dark-color="#fff" :mr="10"></ux-radio>
								<ux-radio value="1" text="男神" color="#333" dark-color="#fff"></ux-radio>
							</ux-radio-group>
						</ux-row>
					</ux-form-item>
					
					<ux-form-item label="年龄" field="age" :rules="ageRules">
						<ux-keyboard mode="number" v-model="(formData['age'] as string)" :max-length="10" :show-sub="false" :show-point="false" placeholder="请输入您的年龄">
							<ux-input v-model="(formData['age'] as string)" :readonly="true" placeholder="请输入您的年龄" suffix="岁" suffix-style="font-size: 12px" background="transparent" background-dark="transparent" align="right" border="none" :focus-border="false"></ux-input>
						</ux-keyboard>
					</ux-form-item>
					
					<ux-form-item label="出生日期" field="date" :rules="dateRules">
						<ux-datepicker selectStyle="box" mode="date" v-model="(formData['date'] as string)" btnType="bigger">
							<ux-input v-model="(formData['date'] as string)" :readonly="true" placeholder="请选择您的出生日期" suffix="arrowdown" :icon-size="18" background="transparent" background-dark="transparent" align="right" border="none" :focus-border="false"></ux-input>
						</ux-datepicker>
					</ux-form-item>
					
					<ux-form-item label="心情指数" field="star" :rules="starRules">
						<ux-row justify="right">
							<ux-rate theme="warning" v-model="(formData['star'] as number)"></ux-rate>
						</ux-row>
					</ux-form-item>
					
					<ux-form-item label="您同时有几个女朋友？" field="count" :rules="countRules">
						<ux-row justify="right">
							<ux-numberbox v-model="(formData['count'] as number)" :min="0" :max="10"></ux-numberbox>
						</ux-row>
					</ux-form-item>
					
					<ux-form-item label="您喜欢什么年龄段的女生？" field="ages" :rules="agesRules">
						<ux-row justify="right" style="width: 150px;">
							<ux-slider v-model="(formData['ages'] as number[])"  mode="range" :min="0" :max="60" unit="岁" :show-value="true"></ux-slider>
						</ux-row>
					</ux-form-item>
					
					<ux-form-item label="您结婚了吗？" field="married" :verify="false">
						<ux-row justify="right">
							<ux-switch v-model="(formData['married'] as boolean)"></ux-switch>
						</ux-row>
					</ux-form-item>
					
					<ux-form-item label="住址" field="address" :rules="addressRules" direction="vertical" :border="false">
						<ux-textarea :inner-padding="false" v-model="(formData['address'] as string)" placeholder="请输入您的详细住址" background="transparent" background-dark="transparent" border="none" :focus-border="false"></ux-textarea>
					</ux-form-item>
				</ux-form>
				
			</ux-card>
			
			<ux-placeholder :height="200">
				<ux-row justify="center" align="center" style="height: 100%;">
					<ux-text prefix-icon="soapbubble-filled" text="真的没有了~"></ux-text>
				</ux-row>
			</ux-placeholder>
		</ux-scroll>
		
		<view class="bottom" :style="backgroundColor">
			<ux-button style="flex: 1;" :margin="[0, 15]" theme="primary" text="提交" @click="submit()"></ux-button>
		</view>
	</ux-page>
</template>
`