<template>
	<view :id="myId" class="ux-tabs" :style="style">
		<view ref="leftRef">
			<slot name="left"></slot>
		</view>
		
		<!-- #ifdef MP -->
		<scroll-view ref="scrollRef" class="ux-tabs__scroll"
			direction="horizontal"
			:scroll-into-view="scrollviewId"
			:scroll-with-animation="true"
			:show-scrollbar="false">
			<view ref="tabsRef" :data-id="tab.id" :id="tab.id" class="ux-tabs__item" :style="tabStyle(tab)"
				v-for="(tab, index) in tabs" :key="index" @click="click(tab, index)">
				<text class="ux-tabs__text" :style="textStyle(tab)">{{ tab.name }}</text>
				<ux-badge :theme="theme" :value="tab.badge" :dot="tab.reddot" :size="10" :right="4" :top="4"></ux-badge>
			</view>
		</scroll-view>
		<view ref="rightRef">
			<slot name="right"></slot>
		</view>
		
		<view v-if="type == 'line'" class="ux-tabs__warp" :style="warpStyle">
			<view :class="`ux-tabs__line--${anim}`" :style="lineStyle"></view>
		</view>
		<view v-if="type == 'smile'" class="ux-tabs__warp" :style="warpStyle">
			<view ref="smileRef" class="ux-tabs__smile" :style="smileStyle">
				<canvas class="ux-tabs__smile--canvas" :id="canvasId" :canvas-id="canvasId"></canvas>
			</view>
		</view>
		<view v-if="type == 'point'" class="ux-tabs__warp" :style="warpStyle">
			<view ref="pointRef" class="ux-tabs__point" :style="pointStyle"></view>
		</view>
		<!-- #endif -->
		
		<!-- #ifndef MP -->
		<scroll-view ref="scrollRef" class="ux-tabs__scroll"
			direction="horizontal"
			:scroll-into-view="scrollviewId"
			:scroll-with-animation="true"
			:show-scrollbar="false">
			<view ref="tabsRef" :data-id="tab.id" :id="tab.id" class="ux-tabs__item" :style="tabStyle(tab)"
				v-for="(tab, index) in tabs" :key="index" @click="click(tab, index)">
				<text class="ux-tabs__text" :style="textStyle(tab)">{{ tab.name }}</text>
				<ux-badge :theme="theme" :value="tab.badge" :dot="tab.reddot" :size="10" :right="4" :top="4"></ux-badge>
			</view>
			<view v-if="type == 'line'" class="ux-tabs__warp" :style="warpStyle">
				<view :class="`ux-tabs__line--${anim}`" :style="lineStyle"></view>
			</view>
			<view v-if="type == 'smile'" class="ux-tabs__warp" :style="warpStyle">
				<view ref="smileRef" class="ux-tabs__smile" :style="smileStyle">
					<!-- #ifdef WEB -->
					<canvas class="ux-tabs__smile--canvas" :id="canvasId" :canvas-id="canvasId"></canvas>
					<!-- #endif -->
				</view>
			</view>
			<view v-if="type == 'point'" class="ux-tabs__warp" :style="warpStyle">
				<view ref="pointRef" class="ux-tabs__point" :style="pointStyle"></view>
			</view>
		</scroll-view>
		<view ref="rightRef">
			<slot name="right"></slot>
		</view>
		<!-- #endif -->
	</view>
</template>

<script setup>
	
	/**
	 * Tabs 标签导航栏
	 * @description 支持点、下划线、微笑类型，下划线支持左右滚动、左右推压效果
	 * @demo pages/component/tabs.uvue
	 * @tutorial https://www.uxframe.cn/component/tabs.html
	 * @property {Slot}				left								Slot | 左插槽
	 * @property {Slot}				right								Slot | 右插槽
	 * @property {String}			theme=[text|info|primary|success|warning|error]		String | 按钮类型 (默认 info)
	 * @value text			文字
	 * @value info   		默认
	 * @value primary   	主要
	 * @value success  		成功
	 * @value warning   	警告
	 * @value error  		错误
	 * @property {Number}			modelValue							Number | 双向绑定值 (默认 0)
	 * @property {UxTab[]}			datas								UxTab[] | tabs列表
	 * @property {String}			type = [none|line|point|smile]		String | 类型 (默认 line)
	 * @value none 无
	 * @value line 下划线
	 * @value point 点
	 * @value smile 微笑
	 * @property {String}			anim = [none|scroll|push]			String | 动效类型 (默认 scroll)
	 * @value none 无
	 * @value scroll 左右滚动
	 * @value push 左右推压
	 * @property {String}			anchorId							String | 锚点ScrollId
	 * @property {Boolean}			anchor = [true|false]				Boolean | 自动滚动到锚点 (默认 false)
	 * @property {Any}				width								Any | 宽度 (默认 auto)
	 * @property {Any}				height								Any | 高度 (默认 44)
	 * @property {Any}				corner								Any | 圆角 (默认 0)
	 * @property {String}			selectedColor						String | 选中颜色 (默认 $ux.Conf.fontColor)
	 * @property {String}			unselectedColor						String | 未选中颜色 (默认 $ux.Conf.fontColor)
	 * @property {String}			background							String | 背景色 (默认 $ux.Conf.backgroundColor)
	 * @property {String} 			backgroundDark=[none|auto|color]	String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {String}			lineColor							String | 线颜色 selectedColor 优先级更高
	 * @property {Any}				lineWidth							Any | 线宽度 0则自动适配 (默认 20)
	 * @property {Any}				lineHeight							Any | 线高度、点直径 (默认 2)
	 * @property {Any}				fontSize							Any | 字体大小 (默认 $ux.Conf.fontSize)
	 * @property {Any}				selectSize							Any | 选中字体大小 (默认 $ux.Conf.fontSize)
	 * @property {Boolean}			bold = [true|false]					Boolean | 字体加粗 (默认 false)
	 * @property {Boolean}			selectBold = [true|false]			Boolean | 选中字体加粗 (默认 false)
	 * @property {Any}				padding								Any | 内部padding (默认 12)
	 * @property {Number}			duration							Number | 过渡时间 (默认 300)
	 * @property {Any}				offset								Any | 锚点偏移距离 (默认 0)
	 * @event {Function}			change								Function | 当前选择下标改变时触发
	 * @event {Function}			anchor								Function | 当要滚动目标时触发
	 * <AUTHOR>
	 * @date 2024-05-18 09:31:35
	 */
	
	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	import { UxTab, UxNodeInfo } from '../../libs/types/types.uts'
	import { useThemeColor, useForegroundColor, useFontColor, useFontSize, UxThemeType } from '../../libs/use/style.uts'
	import { useResize } from '../../libs/use/resize'
	
	defineOptions({
		name: 'ux-tabs',
		mixins: [xstyleMixin]
	})
	
	const emit = defineEmits(['update:modelValue', 'change', 'anchor'])
	
	const props = defineProps({
		modelValue: {
			type: Number,
			default: 0
		},
		datas: {
			type: Array as PropType<UxTab[]>,
			default: () : UxTab[] => [] as UxTab[]
		},
		theme: {
			type: String,
			default: 'primary'
		},
		type: {
			type: String,
			default: 'line'
		},
		anim: {
			type: String,
			default: 'scroll'
		},
		anchorId: {
			type: String,
			default: ''
		},
		anchor: {
			type: Boolean,
			default: false
		},
		width: {
			default: 0
		},
		height: {
			default: 44
		},
		corner: {
			default: 0
		},
		background: {
			type: String,
			default: ''
		},
		backgroundDark: {
			type: String,
			default: ''
		},
		selectedColor: {
			type: String,
			default: ''
		},
		unselectedColor: {
			type: String,
			default: ''
		},
		lineColor: {
			type: String,
			default: ''
		},
		lineWidth: {
			default: 20
		},
		lineHeight: {
			default: 0
		},
		lineBottom: {
			default: 0
		},
		fontSize: {
			default: 0
		},
		selectSize: {
			default: 0
		},
		bold: {
			type: Boolean,
			default: false
		},
		selectBold: {
			type: Boolean,
			default: false
		},
		padding:{
			default: 12
		},
		duration: {
			type: Number,
			default: 300
		},
		offset: {
			default: 5
		}
	})
	
	const ins = getCurrentInstance()?.proxy
	const myId = `ux-tabs-${ $ux.Random.uuid() }`
	let ctx: any | null = null
	const canvasId = `ux-tabs-canvas-${ $ux.Random.uuid() }`
	const dpr = uni.getWindowInfo().pixelRatio
	const scrollRef = ref<Element | null>(null)
	const leftRef = ref<Element | null>(null)
	const rightRef = ref<Element | null>(null)
	const smileRef = ref<Element | null>(null)
	const pointRef = ref<Element | null>(null)
	const tabsRef = ref<Element[] | null>(null)
	const tabs = ref<UxTab[]>([] as UxTab[])
	const tabIndex = ref(0)
	const lastIndex = ref(0)
	const lineLeft = ref(0)
	const lineWidth = ref(20)
	const scrollviewId = ref('')
	const scrollWidth = ref(0)
	const scrollLeft = ref(0)
	const scrollRight = ref(0)
	const leftWidth = ref(0)
	const rightWidth = ref(0)
	
	// #ifdef MP
	const totalWidth = computed(() => {
		let w = 0
		for (let i = 0; i < tabs.value.length; i++) {
			w += (tabs.value[i].node?.width ?? 0)
		}
		
		return w
	})
	// #endif
	
	// #ifndef MP
	const totalWidth = ref(0)
	// #endif
	
	const backgroundColor = computed(() : string => {
		return useForegroundColor(props.background, props.backgroundDark)
	})
	
	const selectedColor = computed(() : string => {
		if(props.selectedColor == '') {
			return useThemeColor(props.theme as UxThemeType)
		} else {
			return props.selectedColor
		}
	})
	
	const unselectedColor = computed(() : string => {
		return useFontColor(props.unselectedColor, '')
	})
	
	const lineColor = computed(() : string => {
		if(props.lineColor == '') {
			return selectedColor.value
		} else {
			return props.lineColor
		}
	})
	
	const fontSize = computed(() : number => {
		if($ux.Util.getPx(props.fontSize) > 0) {
			return $ux.Util.getPx(props.fontSize)
		} else {
			return useFontSize($ux.Util.getPx(props.fontSize), 0)
		}
	})
	
	const selectSize = computed(() : number => {
		if($ux.Util.getPx(props.selectSize) > 0) {
			return $ux.Util.getPx(props.selectSize)
		} else {
			return useFontSize($ux.Util.getPx(props.selectSize), 0)
		}
	})
	
	const lineHeight = computed(() : number => {
		if(props.type == 'smile') {
			return 12
		} else if(props.type == 'point') {
			return $ux.Util.getPx(props.lineHeight) > 0? $ux.Util.getPx(props.lineHeight) : 5
		} else {
			return $ux.Util.getPx(props.lineHeight) > 0? $ux.Util.getPx(props.lineHeight) : 2
		}
	})
	
	const style = computed(():Map<string, any> => {
		let css = new Map<string, any>()
		
		if($ux.Util.getPx(props.width) > 0) {
			css.set('width', $ux.Util.addUnit(props.width))
		}
		
		css.set('height', $ux.Util.addUnit(props.height))
		css.set('background-color', backgroundColor.value)
		css.set('border-radius', $ux.Util.addUnit(props.corner))
		
		return css
	})
	
	function tabStyle(tab: UxTab):Map<string, any> {
		let css = new Map<string, any>()
		
		if(tab.disabled ?? false) {
			css.set('opacity', 0.6)
		}
		
		css.set('padding-left', $ux.Util.addUnit(props.padding))
		css.set('padding-right', $ux.Util.addUnit(props.padding))
		
		return css
	}
	
	function textStyle(tab: UxTab):Map<string, any> {
		let css = new Map<string, any>()
		
		if(tabIndex.value == tab.index) {
			css.set('font-size', $ux.Util.addUnit(selectSize.value))
			css.set('color', selectedColor.value)
			css.set('font-weight', props.selectBold ? 'bold' : 'normal')
		} else {
			css.set('font-size', $ux.Util.addUnit(fontSize.value))
			css.set('color', unselectedColor.value)
			css.set('font-weight', 'normal')
		}
		
		css.set('font-size', $ux.Util.addUnit(fontSize.value))
		css.set('font-weight', 'normal')
		
		return css
	}
	
	const warpStyle = computed(() => {
		let css = new Map<string, any>()
		
		// #ifdef MP
		if(totalWidth.value > 0) {
			css.set('width', `${totalWidth.value}px`)
		}
		// #endif
		
		// #ifndef MP
		css.set('width', `${totalWidth.value}px`)
		// #endif
		
		css.set('height', $ux.Util.addUnit(lineHeight.value + 2))
		css.set('left', $ux.Util.addUnit(leftWidth.value))
		css.set('bottom', $ux.Util.addUnit(props.lineBottom))
		
		return css
	})
	
	const lineStyle = computed(():Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('left', `${lineLeft.value}px`)
		
		// #ifdef MP
		if(lineWidth.value > 0) {
			css.set('width', `${lineWidth.value}`)
		}
		// #endif
		
		// #ifndef MP
		css.set('width', `${lineWidth.value}px`)
		// #endif
		
		css.set('height', $ux.Util.addUnit(lineHeight.value))
		css.set('background-color', lineColor.value)
		css.set('transition-duration', `${props.duration}ms`)
		
		return css
	})
	
	const smileStyle = computed(() => {
		let css = new Map<string, any>()
		
		css.set('left', `${$ux.Util.addUnit(lineLeft.value)}`)
		css.set('width', `${$ux.Util.addUnit(20)}`)
		css.set('height', `${$ux.Util.addUnit(lineHeight.value)}`)
		css.set('transition-duration', `${props.duration}ms`)
		
		return css
	})
	
	const pointStyle = computed(() => {
		let css = new Map<string, any>()
		
		css.set('bottom', `${$ux.Util.addUnit(1)}`)
		css.set('left', `${$ux.Util.addUnit(lineLeft.value)}`)
		css.set('width', `${$ux.Util.addUnit(lineHeight.value)}`)
		css.set('height', `${$ux.Util.addUnit(lineHeight.value)}`)
		css.set('border-radius', `${$ux.Util.addUnit(lineHeight.value)}`)
		css.set('background-color', lineColor.value)
		css.set('transition-duration', `${props.duration}ms`)
		
		return css
	})
	
	const modelValue = computed(():number => {
		return props.modelValue
	})
	
	// #ifdef MP
	function scrollTo() {
		let node = tabs.value![tabIndex.value].node
		let left = scrollLeft.value - leftWidth.value
		let right = scrollRight.value - rightWidth.value
		
		let i: number
		
		if (left <= (node?.left ?? 0)) {
			i = Math.max(0, tabIndex.value - 1)
		} else if (right <= (node?.right ?? 0)) {
			i = Math.min(tabs.value.length - 1, tabIndex.value + 1)
		} else {
			i = Math.max(0, tabIndex.value - 1)
		}
		
		scrollviewId.value = tabs.value[i].id ?? ''
		
		return i
	}
	// #endif
	
	// #ifndef MP
	function scrollTo() {
		let node = tabsRef.value![tabIndex.value]
		let rect = node.getBoundingClientRect()
		let left = scrollRef.value!.getBoundingClientRect().left - (leftRef.value?.getBoundingClientRect()?.width ?? 0)
		let right = scrollRef.value!.getBoundingClientRect().right - (rightRef.value?.getBoundingClientRect()?.width ?? 0)
		
		if (left <= rect.left) {
			let i = Math.max(0, tabIndex.value - 1)
			scrollviewId.value = tabsRef.value![i].getAttribute('id')! as string
		} else if (right <= rect.right) {
			let i = Math.min(tabsRef.value!.length - 1, tabIndex.value + 1)
			scrollviewId.value = tabsRef.value![i].getAttribute('id')! as string
		} else {
			let i = Math.max(0, tabIndex.value - 1)
			scrollviewId.value = tabsRef.value![i].getAttribute('id')! as string
		}
	}
	// #endif
	
	// #ifdef MP
	/**
	 * 微信小程序异步 查询慢
	 * 固手动计算滚动偏移
	 */
	function getScrollLeft(index: number) {
		let _scrollLeft = 0
		for (let i = 0; i < tabs.value.length; i++) {
			let width = tabs.value[i].node?.width ?? 0
			
			if(i < index) {
				_scrollLeft += width
			}
		}
		
		if(_scrollLeft + scrollWidth.value >= totalWidth.value) {
			_scrollLeft = totalWidth.value - scrollWidth.value
		}
		
		_scrollLeft = Math.max(_scrollLeft, 0)
		
		return _scrollLeft
	}
	
	function getLineLeft(index: number, id: string) {
		const scrollLeft = getScrollLeft(index)
		
		let lineLeft = 0 - scrollLeft
		let cur = false
		
		for (let i = 0; i < tabs.value.length; i++) {
			let width = tabs.value[i].node?.width ?? 0
			
			if (id == tabs.value[i].id) {
				cur = true
			} else {
				if(!cur) {
					lineLeft += width
				}
			}
		}
		
		return lineLeft
	}
	
	function animTo(id : string) {
		if (tabs.value.length == 0 || props.type == 'none') return
		
		const index = scrollTo()
		
		let _lineLeft = getLineLeft(index, id)
		
		let nodeWidth = tabs.value[tabIndex.value].node?.width ?? 0
		let _lineWidth = $ux.Util.getPx(props.lineWidth) == 0 ? nodeWidth * 0.6 : $ux.Util.getPx(props.lineWidth)
		_lineLeft += (nodeWidth - _lineWidth) / 2
		
		if(props.type == 'line') {
			if(props.anim == 'scroll') {
				lineWidth.value = _lineWidth
				lineLeft.value = _lineLeft
			} else if(props.anim == 'push') {
				let _targetWidth = 0
				
				for (let i = 0; i < tabs.value.length; i++) {
					let width = tabs.value[i].node?.width ?? 0
					if(lastIndex.value < tabIndex.value) {
						if(i >= lastIndex.value && i <= tabIndex.value) {
							_targetWidth += width * 0.7
						}
					} else {
						if(i <= lastIndex.value && i >= tabIndex.value) {
							_targetWidth += width * 0.7
						}
					}
				}
				
				lineWidth.value = _targetWidth
				
				if(lastIndex.value > tabIndex.value) {
					lineLeft.value = _lineLeft
					setTimeout(() => {
						lineWidth.value = _lineWidth
					}, props.duration - 30);
				} else {
					setTimeout(() => {
						lineWidth.value = _lineWidth
						lineLeft.value = _lineLeft
					}, props.duration - 30);
				}
			}
		} else if(props.type == 'smile') {
			lineLeft.value = _lineLeft + 5
		} else if(props.type == 'point') {
			lineLeft.value = _lineLeft + 8
		}
	}
	// #endif
	
	// #ifndef MP
	function animTo(id : string) {
		if (tabs.value.length == 0 || props.type == 'none') return
		
		let _totalWidth = 0
		let _lineLeft = 0
		let cur = false
	
		tabsRef.value!.forEach((el : Element, _ : number) => {
			let nodeWidth = el.getBoundingClientRect().width
			
			_totalWidth += nodeWidth
			if (id == el.getAttribute('id')!) {
				cur = true
			} else {
				if(!cur) {
					_lineLeft += nodeWidth
				}
			}
		})
		
		if(_totalWidth == 0) {
			return
		}
		
		totalWidth.value = _totalWidth
		
		let nodeWidth = tabsRef.value![tabIndex.value].getBoundingClientRect().width
		let childWidth = tabsRef.value![tabIndex.value].children[0].getBoundingClientRect().width
		let _lineWidth = ($ux.Util.getPx(props.lineWidth) == 0 ? (Math.min(nodeWidth, childWidth) * 0.6) : $ux.Util.getPx(props.lineWidth))
		_lineLeft += (nodeWidth - _lineWidth) / 2
		
		if(props.type == 'line') {
			if(props.anim == 'scroll') {
				lineWidth.value = _lineWidth
				lineLeft.value = _lineLeft
			} else if(props.anim == 'push') {
				let _targetWidth = 0
				tabsRef.value!.forEach((el : Element, index : number) => {
					if(lastIndex.value < tabIndex.value) {
						if(index >= lastIndex.value && index <= tabIndex.value) {
							_targetWidth += el.getBoundingClientRect().width * 0.7
						}
					} else {
						if(index <= lastIndex.value && index >= tabIndex.value) {
							_targetWidth += el.getBoundingClientRect().width * 0.7
						}
					}
				})
				
				lineWidth.value = _targetWidth
				
				if(lastIndex.value > tabIndex.value) {
					lineLeft.value = _lineLeft
					setTimeout(() => {
						lineWidth.value = _lineWidth
					}, props.duration - 30);
				} else {
					setTimeout(() => {
						lineWidth.value = _lineWidth
						lineLeft.value = _lineLeft
					}, props.duration - 30);
				}
			}
		} else if(props.type == 'smile') {
			lineLeft.value = _lineLeft + 5
		} else if(props.type == 'point') {
			lineLeft.value = _lineLeft + 8
		}
		
		scrollTo()
	}
	// #endif
	
	// #ifdef MP
	async function initTabsHeight() {
		let index = 0
		
		let f = async () => {}
		f = async () => {
			if(index == tabs.value.length) {
				return
			}
			
			let tab = tabs.value[index]
			if(tab.height == null && tab.anchorId != null) {
				let rect = await uni.getElementById(tab.anchorId!)?.getBoundingClientRectAsync()!
				tab.height = rect.height
			}
			
			index++
			await f()
		}
		
		await f()
	}
	// #endif
	
	// #ifndef MP
	async function initTabsHeight() {
		tabs.value.forEach((tab: UxTab) => {
			if(tab.height == null && tab.anchorId != null) {
				let rect = uni.getElementById(tab.anchorId!)?.getBoundingClientRect()
				tab.height = rect?.height
			}
		})
	}
	// #endif
	
	async function toAnchor(e: ScrollEvent) {
		await initTabsHeight()
		
		let scrollTop = e.detail.scrollTop
		
		let f = () => {
			let top = 0
			for (let i = 0; i < tabs.value.length; i++) {
				let h = tabs.value[i].height ?? 0
				if(top <= scrollTop && scrollTop <= top + h && i != tabIndex.value) {
					tabIndex.value = i
					animTo(tabs.value[tabIndex.value].id ?? '')
					break
				}
				
				top += h
			}
		}
		
		$ux.Util.debouncek(f, 20, 'ux-tabs')
	}
	
	async function scrollAnchor() {
		await initTabsHeight()
		
		let el = uni.getElementById(props.anchorId)
		if(el != null) {
			let scrollTop = 0
			for (let i = 0; i < tabs.value.length; i++) {
				let h = tabs.value[i].height ?? 0
				if(i < tabIndex.value) {
					scrollTop += h
				}
			}
			
			scrollTop -= $ux.Util.getPx(props.offset)
			
			// #ifdef APP
			el.setAttribute('scroll-top', `${scrollTop}`)
			// #endif
			// #ifndef APP
			el.scrollTop = scrollTop
			// #endif
			
			emit('anchor', scrollTop)
		}
	}
	
	/**
	 * ctx
	 */
	// #ifdef APP
	function getCtx(): DrawableContext {
		return ctx! as DrawableContext
	}
	// #endif
	
	// #ifndef APP
	function getCtx(): CanvasRenderingContext2D {
		return ctx! as CanvasRenderingContext2D
	}
	// #endif
	
	function draw() {
		let f = () => {
			const _ctx = getCtx()
			
			_ctx.beginPath()
			_ctx.arc(5, 5, 6, 0, Math.PI)
			_ctx.strokeStyle = lineColor.value
			_ctx.stroke()
			
			// #ifdef APP
			_ctx.update()
			// #endif
		}
		
		// #ifdef APP
		ctx = smileRef.value!.getDrawableContext()
		f()
		// #endif
		// #ifndef APP
		uni.createCanvasContextAsync({
			id: canvasId,
			component: ins,
			success: (context : CanvasContext) => {
				ctx = context.getContext('2d')
				ctx!.canvas.width = ctx!.canvas.offsetWidth * dpr
				ctx!.canvas.height = ctx!.canvas.offsetHeight * dpr
				ctx!.scale(dpr, dpr)
				f()
			}
		})
		
		// #endif
	}
	
	function click(tab : UxTab, index : number) {
		if (tab.disabled ?? false) {
			return;
		}
		
		lastIndex.value = tabIndex.value
		tabIndex.value = index
		
		if(props.anchor) {
			scrollAnchor()
		}
		
		emit('update:modelValue', index);
		emit('change', index);
	
		animTo(tab.id ?? '')
	}
	
	const tabsData = computed((): UxTab[] => {
		return props.datas
	})
	
	function initTabs() {
		let _tabs = [] as UxTab[]
			
		tabsData.value.forEach((e : UxTab, i : number) => {
			_tabs.push({
				id: e.id ?? `${myId}-${i}`,
				anchorId: e.anchorId ?? '',
				name: e.name,
				badge: e.badge ?? 0,
				reddot: e.reddot ?? false,
				index: i,
				disabled: e.disabled,
			} as UxTab)
		})
		
		tabs.value = _tabs
		
		// #ifdef MP
		let f2 = async (e: UniElement, i: number) => {
			const rect = await e.getBoundingClientRectAsync()!
			tabs.value[i].node = {
				left: rect?.left ?? 0,
				right: rect?.right ?? 0,
				top: rect?.top ?? 0,
				bottom: rect?.bottom ?? 0,
				width: rect?.width ?? 0,
				height: rect?.height ?? 0,
			} as UxNodeInfo
		}
		
		let f = async () => {
			tabsRef.value?.forEach((e, i) => {
				f2(e, i)
			})
			
			const rect = await scrollRef.value!.getBoundingClientRectAsync()!
			scrollWidth.value = rect?.width ?? 0
			scrollLeft.value = rect?.left ?? 0
			scrollRight.value = rect?.right ?? 0
			
			const lrect = await leftRef.value?.getBoundingClientRectAsync()!
			const rrect = await rightRef.value?.getBoundingClientRectAsync()!
			leftWidth.value = lrect?.width ?? 0
			rightWidth.value = rrect?.width ?? 0
		}
		
		setTimeout(() => {
			f()
		}, 50)
		// #endif
	}
	
	watch(tabsData, () => {
		initTabs()
	}, {immediate: true, deep: true})
	
	watch(modelValue, () => {
		if (modelValue.value == tabIndex.value) {
			return
		}
		
		lastIndex.value = tabIndex.value
		tabIndex.value = modelValue.value
		
		let index = tabs.value.findIndex((el : UxTab) : boolean => el.index == tabIndex.value)
		if (index != -1) {
			emit('change', index);
			animTo(tabs.value[index].id ?? '')
		}
	})
	
	onMounted(() => {
		lastIndex.value = tabIndex.value
		tabIndex.value = modelValue.value
		
		nextTick(() => {
			if(props.type == 'smile') {
				setTimeout(function() {
					draw()
				}, 1000);
			}
			
			let index = tabs.value.findIndex((el : UxTab) : boolean => el.index == tabIndex.value)
			if (index != -1) {
				setTimeout(function() {
					animTo(tabs.value[index].id ?? '')
				}, 100);
			}
		})
		
		useResize(uni.getElementById(myId), () => {
			if(tabs.value.length == 0) {
				return
			}
			
			animTo(tabs.value[tabIndex.value].id ?? '')
		})
	})
	
	defineExpose({
		toAnchor
	})
</script>


<style lang="scss">
	.ux-tabs {
		width: 100%;
		display: flex;
		flex-direction: row;
		align-items: center;
		position: relative;
		
		&__scroll {
			flex: 1;
			height: 100%;
			display: flex;
			flex-direction: row;
			flex-wrap: nowrap;
			justify-content: flex-start;
			position: relative;
		}
		
		&__item {
			height: 100%;
			display: flex;
			flex-direction: row;
			flex-wrap: nowrap;
			justify-content: center;
			align-items: center;
			position: relative;
		}
		
		&__text {
			transition-duration: 300ms;
			transition-property: color;
			transition-timing-function: cubic-bezier(0, 0.55, 0.45, 1);
		}
		
		&__warp {
			position: absolute;
			left: 0;
		}
		
		&__line--scroll {
			position: absolute;
			left: 0px;
			bottom: 0;
			width: 20px;
			border-radius: 6px;
			transition-duration: 300ms;
			transition-property: left, width;
			transition-timing-function: cubic-bezier(0, 0.55, 0.45, 1);
		}
		
		&__line--push {
			position: absolute;
			left: 0px;
			bottom: 0;
			width: 20px;
			border-radius: 6px;
			transition-duration: 300ms;
			transition-property: left, width;
		}
		
		&__smile {
			position: absolute;
			left: 0px;
			bottom: 0;
			width: 16px;
			transition-duration: 300ms;
			transition-property: left;
			transition-timing-function: cubic-bezier(0, 0.55, 0.45, 1);
			
			&--canvas {
				width: 100%;
				height: 100%;
			}
		}
		
		&__point {
			position: absolute;
			left: 0px;
			bottom: 0;
			width: 5px;
			height: 5px;
			border-radius: 5px;
			transition-duration: 300ms;
			transition-property: transform, opacity;
		}
	}
	
</style>