
import { OnNetworkStatusChange, OffNetworkStatusChange, UxNetworkStatusChangeOptions } from '../interface.uts'

// @UTSJS.keepAlive
// export const onNetworkStatusChange: OnNetworkStatusChange = (options: UxNetworkStatusChangeOptions): void => {
// 	UxNetwork.on(options.onListener)
// }

@UTSJS.keepAlive
export function onNetworkStatusChange(options: UxNetworkStatusChangeOptions) {
	UxNetwork.on(options.onListener)
}

export const offNetworkStatusChange: OffNetworkStatusChange = (): void => {
	UxNetwork.off()
}