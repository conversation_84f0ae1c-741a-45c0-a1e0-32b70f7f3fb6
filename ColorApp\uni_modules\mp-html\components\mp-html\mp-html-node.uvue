<template>
  <!-- 文本节点 -->
  <text 
    v-if="node.type === 'text'" 
    :style="textStyle"
    :selectable="config.selectable"
    class="mp-html-text"
  >{{ node.text }}</text>
  
  <!-- 元素节点 -->
  <view v-else-if="node.type === 'element'" :style="elementStyle" :class="getElementClass()">
    <!-- 图片 -->
    <image 
      v-if="node.name === 'img'"
      :src="getImageSrc()"
      :style="imageStyle"
      mode="widthFix"
      class="mp-html-img"
      @tap="onImageTap"
      @error="onImageError"
    />
    
    <!-- 链接 -->
    <view 
      v-else-if="node.name === 'a'"
      class="mp-html-link"
      :style="linkStyle"
      @tap="onLinkTap"
    >
      <mp-html-node 
        v-for="(child, index) in node.children" 
        :key="index"
        :node="child"
        :config="config"
        @link-tap="$emit('link-tap', $event)"
        @img-tap="$emit('img-tap', $event)"
      />
    </view>
    
    <!-- 换行 -->
    <text v-else-if="node.name === 'br'" class="mp-html-br">\n</text>
    
    <!-- 水平线 -->
    <view v-else-if="node.name === 'hr'" class="mp-html-hr" :style="hrStyle"></view>
    
    <!-- 列表 -->
    <view 
      v-else-if="node.name === 'ul' || node.name === 'ol'"
      class="mp-html-list"
      :style="listStyle"
    >
      <mp-html-node 
        v-for="(child, index) in node.children" 
        :key="index"
        :node="child"
        :config="config"
        :list-type="node.name"
        :list-index="index"
        @link-tap="$emit('link-tap', $event)"
        @img-tap="$emit('img-tap', $event)"
      />
    </view>
    
    <!-- 列表项 -->
    <view 
      v-else-if="node.name === 'li'"
      class="mp-html-li"
      :style="liStyle"
    >
      <text class="mp-html-li-marker" :style="liMarkerStyle">{{ getListMarker() }}</text>
      <view class="mp-html-li-content">
        <mp-html-node 
          v-for="(child, index) in node.children" 
          :key="index"
          :node="child"
          :config="config"
          @link-tap="$emit('link-tap', $event)"
          @img-tap="$emit('img-tap', $event)"
        />
      </view>
    </view>
    
    <!-- 其他块级元素 -->
    <view 
      v-else-if="isBlockElement()"
      :class="getBlockClass()"
      :style="blockStyle"
    >
      <mp-html-node 
        v-for="(child, index) in node.children" 
        :key="index"
        :node="child"
        :config="config"
        @link-tap="$emit('link-tap', $event)"
        @img-tap="$emit('img-tap', $event)"
      />
    </view>
    
    <!-- 内联元素 -->
    <text 
      v-else
      :style="inlineStyle"
      :selectable="config.selectable"
      :class="getInlineClass()"
    >
      <mp-html-node 
        v-for="(child, index) in node.children" 
        :key="index"
        :node="child"
        :config="config"
        @link-tap="$emit('link-tap', $event)"
        @img-tap="$emit('img-tap', $event)"
      />
    </text>
  </view>
</template>

<script setup lang="uts">
import { HtmlNodeType, ParserConfigType, LinkTapEventType, ImgTapEventType } from '../../libs/types.uts'
import { StyleUtils } from '../../libs/style-utils.uts'

// 定义属性
interface Props {
  /** 节点数据 */
  node: HtmlNodeType
  /** 配置 */
  config: ParserConfigType
  /** 列表类型 */
  listType?: string
  /** 列表索引 */
  listIndex?: number
}

const props = withDefaults(defineProps<Props>(), {
  listType: '',
  listIndex: 0
})

// 定义事件
const emit = defineEmits<{
  'link-tap': [event: LinkTapEventType]
  'img-tap': [event: ImgTapEventType]
}>()

// 计算样式
const computedStyle = computed((): UTSJSONObject => {
  if (props.node.type == 'text') {
    return {}
  }
  
  const defaultStyle = StyleUtils.getDefaultStyle(props.node.name ?? '')
  const customStyle = StyleUtils.convertStyle(props.node.style)
  return StyleUtils.mergeStyles(defaultStyle, customStyle)
})

// 文本样式
const textStyle = computed((): UTSJSONObject => {
  return computedStyle.value
})

// 元素样式
const elementStyle = computed((): UTSJSONObject => {
  return computedStyle.value
})

// 图片样式
const imageStyle = computed((): UTSJSONObject => {
  const style = computedStyle.value
  // 确保图片不超出容器
  if (!style.hasOwnProperty('maxWidth')) {
    style['maxWidth'] = '100%'
  }
  return style
})

// 链接样式
const linkStyle = computed((): UTSJSONObject => {
  return computedStyle.value
})

// 水平线样式
const hrStyle = computed((): UTSJSONObject => {
  const style = computedStyle.value
  if (!style.hasOwnProperty('height')) {
    style['height'] = '1px'
  }
  if (!style.hasOwnProperty('backgroundColor')) {
    style['backgroundColor'] = '#E5E5E5'
  }
  if (!style.hasOwnProperty('marginTop')) {
    style['marginTop'] = '8px'
  }
  if (!style.hasOwnProperty('marginBottom')) {
    style['marginBottom'] = '8px'
  }
  return style
})

// 列表样式
const listStyle = computed((): UTSJSONObject => {
  return computedStyle.value
})

// 列表项样式
const liStyle = computed((): UTSJSONObject => {
  const style = computedStyle.value
  style['flexDirection'] = 'row'
  style['alignItems'] = 'flex-start'
  return style
})

// 列表项标记样式
const liMarkerStyle = computed((): UTSJSONObject => {
  return {
    marginRight: '8px',
    fontSize: computedStyle.value.getString('fontSize') ?? '16px'
  }
})

// 块级元素样式
const blockStyle = computed((): UTSJSONObject => {
  return computedStyle.value
})

// 内联元素样式
const inlineStyle = computed((): UTSJSONObject => {
  return computedStyle.value
})

// 获取图片地址
const getImageSrc = (): string => {
  if (props.node.attrs == null) {
    return ''
  }
  return props.node.attrs.getString('src') ?? ''
}

// 获取列表标记
const getListMarker = (): string => {
  if (props.listType == 'ol') {
    return (props.listIndex + 1) + '.'
  } else {
    return '•'
  }
}

// 判断是否是块级元素
const isBlockElement = (): boolean => {
  const blockElements = ['div', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'blockquote', 'pre', 'section', 'article', 'header', 'footer', 'main', 'aside', 'nav']
  return blockElements.includes(props.node.name ?? '')
}

// 获取元素类名
const getElementClass = (): string => {
  return `mp-html-${props.node.name}`
}

// 获取块级元素类名
const getBlockClass = (): string => {
  return `mp-html-block mp-html-${props.node.name}`
}

// 获取内联元素类名
const getInlineClass = (): string => {
  return `mp-html-inline mp-html-${props.node.name}`
}

// 处理图片点击
const onImageTap = () => {
  const src = getImageSrc()
  if (src != '') {
    emit('img-tap', { src })
  }
}

// 处理图片加载错误
const onImageError = (event: any) => {
  console.warn('图片加载失败:', getImageSrc())
}

// 处理链接点击
const onLinkTap = () => {
  if (props.node.attrs == null) {
    return
  }
  
  const href = props.node.attrs.getString('href') ?? ''
  if (href != '') {
    emit('link-tap', { href })
  }
}
</script>

<style scoped>
.mp-html-text {
  flex-wrap: wrap;
}

.mp-html-img {
  max-width: 100%;
}

.mp-html-link {
  flex-direction: row;
  flex-wrap: wrap;
}

.mp-html-br {
  width: 100%;
}

.mp-html-hr {
  width: 100%;
}

.mp-html-list {
  flex-direction: column;
}

.mp-html-li {
  flex-direction: row;
  align-items: flex-start;
  margin-top: 4px;
  margin-bottom: 4px;
}

.mp-html-li-marker {
  margin-right: 8px;
  flex-shrink: 0;
}

.mp-html-li-content {
  flex: 1;
  flex-direction: column;
}

.mp-html-block {
  flex-direction: column;
  width: 100%;
}

.mp-html-inline {
  flex-wrap: wrap;
}
</style>