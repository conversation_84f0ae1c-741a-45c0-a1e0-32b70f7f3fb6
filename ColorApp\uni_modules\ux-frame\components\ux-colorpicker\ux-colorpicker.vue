<template>
	<ux-drawer ref="uxDrawerRef" direction="top" :height="`${pickerHeight}px`" :touchable="touchable" :opacity="opacity" :maskClose="maskClose" :border="false" @close="onMaskClose">
		<view @click="click()">
			<slot></slot>
		</view>
		<template v-slot:drawer>
			<view class="ux-picker" :style="[style]">
				<view class="ux-picker__title" :style="titleBorder">
					<text v-if="title != ''" class="ux-picker__title--text" :style="titleStyle">{{ title }}</text>
					<view v-if="btnPositon == 'top'" :class="btnType == 'normal' || title != '' ? 'ux-picker__btn--cancel' : 'ux-picker__btn--cancel--bigger'"
						hover-class="ux-picker__hover" :hover-start-time="20" :hover-stay-time="200" @click="close()">
						<text class="ux-picker__btn--text" :style="[cancelStyle]">取消</text>
					</view>
					<view v-if="btnPositon == 'top'" :class="btnType == 'normal' || title != '' ? 'ux-picker__btn--confirm' : 'ux-picker__btn--confirm--bigger'"
						:style="confirmBorder" hover-class="ux-picker__hover" :hover-start-time="20" :hover-stay-time="200" @click="onConfirm()">
						<text class="ux-picker__btn--text" :style="[confirmStyle]">{{ confirm }}</text>
					</view>
					<view v-if="btnPositon == 'bottom'" class="ux-picker__close" @click="close()">
						<ux-icon type="close"></ux-icon>
					</view>
				</view>
				<view id="paletteRef" class="palette" :style="paletteStyle" @touchstart="touchstart" @touchmove="touchmove"
					@touchend="touchend" @touchcancel="touchend" @mousedown="touchstartPc" @mousemove="touchmovePc"
					@mouseup="touchendPc" @mouseleave="touchendPc">
					<view class="palette-white"></view>
					<view class="palette-black"></view>
					<view class="palette-point" :style="pointStyle"></view>
				</view>
				<view class="control">
					<view class="control-alpha">
						<image class="control-alpha-color" src="/uni_modules/ux-frame/static/alpha-one.png"
							mode="widthFix"></image>
						<view class="control-alpha-color" :style="alphaStyle"></view>
					</view>
					<view class="control-item">
						<view id="colorRef" class="control-item-drag" @touchstart="touchstart_color"
							@touchmove="touchmove_color" @touchend="touchend_color" @touchcancel="touchend_color"
							@mousedown="touchstartPc_color" @mousemove="touchmovePc_color" @mouseup="touchendPc_color"
							@mouseleave="touchendPc_color">
							<view class="control-item-drag-hue">
								<view style="width:16.8%;height:100%;background-image:linear-gradient(to right, #f00, #ff0);">
								</view>
								<view style="width:16%;height:100%;background-image:linear-gradient(to right, #ff0, #0f0);">
								</view>
								<view style="width:16.8%;height:100%;background-image:linear-gradient(to right, #0f0, #0ff);">
								</view>
								<view style="width:16.8%;height:100%;background-image:linear-gradient(to right, #0ff, #00f);">
								</view>
								<view style="width:16.8%;height:100%;background-image:linear-gradient(to right, #00f, #f0f);">
								</view>
								<view style="width:16.8%;height:100%;background-image:linear-gradient(to right, #f0f, #f00);">
								</view>
							</view>
							<view class="control-item-drag-circle" :style="{left: `${colorLeft - 10}px`}"></view>
						</view>
						<view id="alphaRef" class="control-item-drag" @touchstart="touchstart_alpha"
							@touchmove="touchmove_alpha" @touchend="touchend_alpha" @touchcancel="touchend_alpha"
							@mousedown="touchstartPc_alpha" @mousemove="touchmovePc_alpha" @mouseup="touchendPc_alpha"
							@mouseleave="touchendPc_alpha">
							<view class="control-item-drag-transparent">
								<image class="control-item-drag-image"
									src="/uni_modules/ux-frame/static/alpha-three.png"></image>
								<image class="control-item-drag-image"
									src="/uni_modules/ux-frame/static/alpha-three.png"></image>
								<image class="control-item-drag-image"
									src="/uni_modules/ux-frame/static/alpha-three.png"></image>
							</view>
							<view class="control-item-drag-alpha" :style="dragAlphaStyle"></view>
							<view class="control-item-drag-circle" :style="{left: `${alphaLeft - 10}px`}"></view>
						</view>
					</view>
				</view>
				<view class="result">
					<view class="result-select" hover-class="uv-hover-class" @click="showHex = !showHex">
						<text class="result-select-text" :style="modeStyle">切换</text>
						<text class="result-select-text" :style="modeStyle">模式</text>
					</view>
					<view v-if="showHex" class="result-item">
						<view class="result-item-value" :style="focusStyle(hexFocus)">
							<input type="text" class="result-item-value-text" :style="hexStyle" :value="hex" placeholder="输入16进制的颜色值"
								placeholder-class="placeholderClass" confirm-type="done" maxlength="7" @input="hexInput"
								@focus="hexFocus = true" @blur="hexFocus = false" />
						</view>
						<view class="result-item-hex">
							<text class="result-item-hex-text">HEX</text>
						</view>
					</view>
					<template v-else>
						<view class="result-item">
							<view class="result-item-value" :style="focusStyle(rgbaRFocus)">
								<input type="text" class="result-item-value-text" :style="hexStyle" :value="rgba.r" placeholder="0~255"
									placeholder-class="placeholderClass" confirm-type="done" maxlength="3"
									@input="rgbaInput('r',$event as UniInputEvent)" @focus="rgbaRFocus = true"
									@blur="rgbaRFocus = false" />
							</view>
							<view class="result-item-rgba">
								<text class="result-item-rgba-text">R</text>
							</view>
						</view>
						<view class="result-item">
							<view class="result-item-value" :style="focusStyle(rgbaGFocus)">
								<input type="text" class="result-item-value-text" :style="hexStyle" :value="rgba.g" placeholder="0~255"
									placeholder-class="placeholderClass" confirm-type="done" maxlength="3"
									@input="rgbaInput('g',$event as UniInputEvent)" @focus="rgbaGFocus = true"
									@blur="rgbaGFocus = false" />
							</view>
							<view class="result-item-rgba">
								<text class="result-item-rgba-text">G</text>
							</view>
						</view>
						<view class="result-item">
							<view class="result-item-value" :style="focusStyle(rgbaBFocus)">
								<input type="text" class="result-item-value-text" :style="hexStyle" :value="rgba.b" placeholder="0~255"
									placeholder-class="placeholderClass" confirm-type="done" maxlength="3"
									@input="rgbaInput('b',$event as UniInputEvent)" @focus="rgbaBFocus = true"
									@blur="rgbaBFocus = false" />
							</view>
							<view class="result-item-rgba">
								<text class="result-item-rgba-text">B</text>
							</view>
						</view>
						<view class="result-item">
							<view class="result-item-value" :style="focusStyle(rgbaAFocus)">
								<input type="text" class="result-item-value-text" :style="hexStyle" :value="rgba.a" placeholder="0~1"
									placeholder-class="placeholderClass" confirm-type="done" maxlength="4"
									@input="rgbaInput('a',$event as UniInputEvent)" @focus="rgbaAFocus = true"
									@blur="rgbaAFocus = false" />
							</view>
							<view class="result-item-rgba">
								<text class="result-item-rgba-text">A</text>
							</view>
						</view>
					</template>
				</view>
				<view class="prefab">
					<view class="prefab-items">
						<view class="prefab-item" v-for="(prefab, index) in colors.slice(0, 10)" :key="index">
							<image class="prefab-item-color prefab-item-image"
								src="/uni_modules/ux-frame/static/alpha-two.png" mode="widthFix"></image>
							<view class="prefab-item-color"
								:style="{backgroundColor: `rgba(${prefab.r},${prefab.g},${prefab.b},${prefab.a})`}"
								@click="setPrefabColor(prefab,'yx')"></view>
						</view>
					</view>
					<view class="prefab-items">
						<view class="prefab-item" v-for="(prefab, index) in colors.slice(10)" :key="index">
							<image class="prefab-item-color prefab-item-image"
								src="/uni_modules/ux-frame/static/alpha-two.png" mode="widthFix"></image>
							<view class="prefab-item-color"
								:style="{backgroundColor: `rgba(${prefab.r},${prefab.g},${prefab.b},${prefab.a})`}"
								@click="setPrefabColor(prefab,'yx')"></view>
						</view>
					</view>
				</view>
				<view v-if="btnPositon == 'bottom'" class="ux-picker__btns">
					<view class="ux-picker__btns--cancel" :style="cancelBtnStyle" hover-class="ux-picker__hover" :hover-start-time="20" :hover-stay-time="200" @click="close()">
						<text class="ux-picker__btns--text" :style="cancelTextStyle">取消</text>
					</view>
					<view class="ux-picker__btns--confirm" :style="confirmBtnStyle" hover-class="ux-picker__hover" :hover-start-time="20" :hover-stay-time="200" @click="onConfirm()">
						<text class="ux-picker__btns--text" :style="confirmTextStyle">{{ confirm }}</text>
					</view>
				</view>
			</view>
		</template>
	</ux-drawer>
</template>

<script setup lang="ts">
	/**
	 * ColorPicker 颜色选择器
	 * @description 支持单色、渐变色、透明度
	 * @demo pages/component/colorpicker.uvue
	 * @tutorial https://www.uxframe.cn/component/colorpicker.html
	 * @property {String}		value								String | 值 (rgba(255,0,0,1))
	 * @property {Array}		colors								RGBA[] | 预设颜色值
	 * @property {String}		title								String | 标题
	 * @property {String}		titleColor							String | 标题颜色 (默认 $ux.Conf.titleColor)
	 * @property {Any}			size								Any | 内容大小 (默认 $ux.Conf.fontSize)
	 * @property {String}		color								String | 内容颜色 (默认 $ux.Conf.fontColor)
	 * @property {String} 		darkColor=[none|auto|color]			String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {String}		confirm								String | 确定文字 (默认 确定)
	 * @property {String}		confirmColor						Strng | 确定文字颜色 (默认 $ux.Conf.primaryColor)
	 * @property {Any}			btnSize								Any | 按钮大小 (默认 $ux.Conf.fontSize)
	 * @property {String}		btnType=[normal|bigger]				String | 按钮类型 (默认 normal)
	 * @value normal 正常
	 * @value bigger 大按钮
	 * @property {String}		btnPositon=[top|bottom]				String | 按钮位置 (默认 bottom)
	 * @value top 顶部按钮
	 * @value bottom 底部按钮
	 * @property {Any}			radius								Any | 圆角 (默认 $ux.Conf.radius)
	 * @property {Number}		opacity								Number | 遮罩透明度 0-1 (默认 $ux.Conf.maskAlpha)
	 * @property {Boolean}		touchable=[true|false]				Boolean | 允许滑动关闭 (默认 false)
	 * @value true
	 * @value false
	 * @property {Boolean}		maskClose=[true|false]				Boolean | 遮罩层关闭 (默认 true)
	 * @value true
	 * @value false
	 * @property {Boolean}		disabled=[true|false]				Boolean | 是否禁用 (默认 false)
	 * @value true
	 * @value false
	 * @event {Function}		change								Function | 确定选择时触发
	 * @event {Function}		changing							Function | 选择时触发
	 * @event {Function}		close								Function | 关闭时触发
	 * <AUTHOR>
	 * @date 2024-12-02 18:00:46
	 */
	
	import { ref, computed, nextTick, watch, onMounted, getCurrentInstance } from 'vue'
	import { $ux } from '../../index'
	import { useThemeColor, useForegroundColor, useBorderColor, useFontSize, useSubTitleColor, useCancelColor, useRadius, useFontColor } from '../../libs/use/style.uts'
	import { RGB, RGBA, HSB } from '../../libs/types/color.uts'
	
	defineOptions({
		name: 'ux-colorpicker'
	})
	
	const emit = defineEmits(['change', 'changing', 'update:modelValue', 'close'])
	
	const props = defineProps({
		modelValue: {
			type: String,
			default: ''
		},
		value: {
			type: String,
			default: ''
		},
		colors: {
			type: Array as PropType<RGBA[]>,
			default: (): RGBA[] => {
				return [
					{ r: 60, g: 156, b: 255, a: 1 },
					{ r: 249, g: 174, b: 61, a: 1 },
					{ r: 90, g: 199, b: 37, a: 1 },
					{ r: 233, g: 30, b: 99, a: 1 },
					{ r: 156, g: 39, b: 176, a: 1 },
					{ r: 103, g: 58, b: 183, a: 1 },
					{ r: 63, g: 81, b: 181, a: 1 },
					{ r: 0, g: 188, b: 212, a: 1 },
					{ r: 0, g: 150, b: 136, a: 1 },
					{ r: 139, g: 195, b: 74, a: 1 },
					{ r: 205, g: 220, b: 57, a: 1 },
					{ r: 255, g: 235, b: 59, a: 1 },
					{ r: 255, g: 193, b: 7, a: 1 },
					{ r: 255, g: 152, b: 0, a: 1 },
					{ r: 255, g: 87, b: 34, a: 1 },
					{ r: 121, g: 85, b: 72, a: 1 },
					{ r: 48, g: 49, b: 51, a: 1 },
					{ r: 158, g: 158, b: 158, a: 1 },
					{ r: 0, g: 0, b: 0, a: 0.5 },
					{ r: 0, g: 0, b: 0, a: 0 },
				] as RGBA[]
			}
		},
		title: {
			type: String,
			default: ''
		},
		titleColor: {
			type: String,
			default: ''
		},
		size: {
			default: 0
		},
		color: {
			type: String,
			default: ''
		},
		darkColor: {
			type: String,
			default: ''
		},
		btnSize: {
			default: 0
		},
		btnType: {
			type: String,
			default: 'normal'
		},
		btnPositon: {
			type: String,
			default: 'bottom'
		},
		confirm: {
			type: String,
			default: '确定'
		},
		confirmColor: {
			type: String,
			default: ''
		},
		radius: {
			default: -1
		},
		opacity: {
			type: Number,
			default: 0.1
		},
		touchable: {
			type: Boolean,
			default: false
		},
		maskClose: {
			type: Boolean,
			default: true
		},
		disabled: {
			type: Boolean,
			default: false
		}
	})

	const uxDrawerRef = ref<UxDrawerComponentPublicInstance | null>(null)
	
	const value = ref('')
	
	const hex = ref('') // hex 格式颜色值
	const rgba = ref<RGBA>({ r: 255, g: 255, b: 255, a: 1 } as RGBA) // rgba 格式颜色值
	const hsb = ref<HSB>({ h: 0, s: 0, b: 0 } as HSB) // hsb 格式颜色值
	const bgcolor = ref<RGBA>({ r: 255, g: 0, b: 0, a: 1 } as RGBA) // 颜色选区背景颜色

	const selectPos = ref<number[]>([0, 0] as number[]) // 颜色圆圈的位置信息
	const paletteHeight = ref(200) // 颜色选区 height 值
	const colorLeft = ref(0) // 七彩色圆圈 left 值
	const alphaLeft = ref(0) // 透明度圆圈 left 值
	const selectedHue = ref(0) // 透明度圆圈 left 值
	const isPressed = ref(false) // 当前是否按下
	const showHex = ref(true) // 显示 hex 模式
	const hexFocus = ref(false) // hex 输入框焦点
	const rgbaRFocus = ref(false) // rgba.r 输入框焦点
	const rgbaGFocus = ref(false) // rgba.g 输入框焦点
	const rgbaBFocus = ref(false) // rgba.b 输入框焦点
	const rgbaAFocus = ref(false) // rgba.a 输入框焦点
	
	const pickerHeight = computed(():number => {
		const itemH = 390
		const topH = 50
		const bottomH = 70
		
		if(props.btnPositon == 'bottom') {
			return itemH + topH + bottomH
		} else {
			return itemH + topH + 20
		}
	})
	
	const modelValue = computed((): string => {
		return props.modelValue
	})
	
	const fontSize = computed(():number => {
		return useFontSize($ux.Util.getPx(props.btnSize), 0)
	})
	
	const titleColor = computed(():string => {
		return useSubTitleColor(props.titleColor, 'auto')
	})
	
	const borderColor = computed(():string => {
		return useBorderColor('', '')
	})
	
	const radius = computed(():number => {
		return useRadius($ux.Util.getPx(props.radius), 2)
	})
	
	const confirmColor = computed(():string => {
		if(props.confirmColor == '') {
			return useThemeColor('primary')
		} else {
			return props.confirmColor
		}
	})
	
	const cancelColor = computed(():string => {
		return useCancelColor('', 'auto')
	})
	
	const backgroundColor = computed(() : string => {
		return useForegroundColor('#ffffff', 'auto')
	})
	
	const modeColor = computed(() : string => {
		return useFontColor('#999', '#f0f0f0')
	})
	
	const hexColor = computed(() : string => {
		return useFontColor('#000', '#fff')
	})
	
	const style = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('border-top-left-radius', $ux.Util.addUnit(radius.value))
		css.set('border-top-right-radius', $ux.Util.addUnit(radius.value))
		css.set('height', `${pickerHeight.value}px`)
		css.set('background-color', backgroundColor.value)
		
		// #ifdef WEB
		css.set('cursor', props.disabled? 'not-allowed' : 'pointer')
		// #endif
		
		return css
	})
	
	const titleStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('color', titleColor.value)
		
		return css
	})
	
	const titleBorder = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		if(props.btnPositon == 'bottom' || props.btnType == 'normal' || props.title != '') {
			
		} else {
			css.set('border-bottom', `1px solid ${borderColor.value}`)
		}
		
		return css
	})
	
	const cancelStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('font-size', $ux.Util.addUnit(fontSize.value))
		css.set('color', cancelColor.value)
		
		return css
	})
	
	const confirmStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('font-size', $ux.Util.addUnit(fontSize.value))
		css.set('color', confirmColor.value)
		
		return css
	})
	
	const confirmBorder = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		if(props.btnPositon == 'bottom' || props.btnType == 'normal' || props.title != '') {
			
		} else {
			css.set('border-left', `1px solid ${borderColor.value}`)
		}
		
		return css
	})
	
	const confirmBtnStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('background-color', confirmColor.value)
		
		return css
	})
	
	const confirmTextStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('color', '#ffffff')
		
		return css
	})
	
	const cancelBtnStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('background-color', $ux.Color.getRgba(confirmColor.value, 0.05))
		css.set('border', `1px solid ${$ux.Color.getRgba(confirmColor.value, 0.8)}`)
		
		return css
	})
	
	const cancelTextStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('color', confirmColor.value)
		
		return css
	})
	
	const modeStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('color', modeColor.value)
		
		return css
	})
	
	const hexStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('color', hexColor.value)
		
		return css
	})
	
	function focusStyle(focus: boolean) : Map<string, any> {
		let css = new Map<string, any>()
		
		css.set('border', `1px solid ${focus? useThemeColor('primary') : '#f1f1f1'}`)
		
		return css
	}
	
	// 颜色选择区域动态样式
	const paletteStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		css.set('background', `rgb(${bgcolor.value.r},${bgcolor.value.g},${bgcolor.value.b})`)
		css.set('height', `${paletteHeight.value}px`)
		return css
	})
	
	// 颜色选择区域圆圈位置
	const pointStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		css.set('left', `${selectPos.value[0]}px`)
		css.set('top', `${selectPos.value[1]}px`)
		return css
	})
	
	//  颜色预览圆圈
	const alphaStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		css.set('background', `rgba(${rgba.value.r},${rgba.value.g},${rgba.value.b},${rgba.value.a})`)
		return css
	})
	
	// 透明度样式
	const dragAlphaStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		css.set('background', `linear-gradient(to right, rgba(0, 0, 0, 0) 0%, rgb(${rgba.value.r},${rgba.value.g},${rgba.value.b}))`)
		return css
	})
	
	function prefabStyle(prefab: RGBA): Map<string, any> {
		let css = new Map<string, any>()
		
		css.set('background', `rgba(${prefab.r},${prefab.g},${prefab.b},${prefab.a})`)
		
		return css
	}

	// 将RGB颜色转换为十六进制颜色
	function rgbToHex(r : number, g : number, b : number) : string {
		return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1).toUpperCase()}`;
	}
	
	// 将十六进制颜色转换为RGB
	function hexToRgb(hex : string) : RGB {
		const bigint = parseInt(hex.slice(1), 16);
		return { r: (bigint >> 16) & 255, g: (bigint >> 8) & 255, b: bigint & 255 } as RGB;
	}
	
	// 将RGB转换为HSB
	function rgbToHsb(r : number, g : number, b : number) : HSB {
		let rCopy = r;
		let gCopy = g;
		let bCopy = b;

		rCopy /= 255;
		gCopy /= 255;
		bCopy /= 255;

		const max = Math.max(rCopy, gCopy, bCopy);
		const min = Math.min(rCopy, gCopy, bCopy);
		let h : number = max;
		let s : number;
		let v : number = max;

		const d = max - min;
		s = max == 0 ? 0 : d / max;

		if (max == min) {
			h = 0;
		}
		else {
			switch (max) {
				case rCopy: h = (gCopy - bCopy) / d + (gCopy < bCopy ? 6 : 0); break;
				case gCopy: h = (bCopy - rCopy) / d + 2; break;
				case bCopy: h = (rCopy - gCopy) / d + 4; break;
			}
			h /= 6;
		}
		return { h: h * 360, s: s, b: v } as HSB;
	}
	
	// 将HSB转换为RGB
	function hsbToRgb(h : number, s : number, b : number) : RGB {
		let r : number = 0, g : number = 0, l : number = 0;
		const i = Math.floor(h * 6);
		const f = h * 6 - i;
		const p = b * (1 - s);
		const q = b * (1 - f * s);
		const t = b * (1 - (1 - f) * s);
		switch (i % 6) {
			case 0: r = b; g = t; l = p; break;
			case 1: r = q; g = b; l = p; break;
			case 2: r = p; g = b; l = t; break;
			case 3: r = p; g = q; l = b; break;
			case 4: r = t; g = p; l = b; break;
			case 5: r = b; g = p; l = q; break;
		}
		return { r: Math.round(r * 255), g: Math.round(g * 255), b: Math.round(l * 255) } as RGB;
	}

	// 颜色转换方法
	async function updatePos() {
		// 计算颜色选择圆圈位置
		const { width, height } = await $ux.Util.getBoundingClientRect('#paletteRef', getCurrentInstance()?.proxy)
		
		selectPos.value[0] = Math.max(-6, Math.min((hsb.value.s * width) - 6, width - 6)) //  left 最小为圆圈的一半，就是中心点位置，最大不能超过宽度减去圆圈的宽度
		selectPos.value[1] = Math.max(-6, Math.min(((1 - hsb.value.b) * height) - 6, height - 6)) //  top 最小为圆圈的一半，就是中心点位置，最大不能超过高度减去圆圈的高度
		// 计算色相位置
		const colorRect = await $ux.Util.getBoundingClientRect('#colorRef', getCurrentInstance()?.proxy)
		let _left = hsb.value.h / 360 * colorRect.width
		colorLeft.value = Math.max(0, Math.min(_left, colorRect.width))
		// 根据色相位置，计算背景颜色
		const rgb = hsbToRgb(hsb.value.h / 360, 1, 1)
		bgcolor.value = { r: rgb.r, g: rgb.g, b: rgb.b, a: 1 } as RGBA
		// 计算透明度位置
		const alphaRect = await $ux.Util.getBoundingClientRect('#alphaRef', getCurrentInstance()?.proxy)
		_left = rgba.value.a * alphaRect.width
		alphaLeft.value = Math.max(0, Math.min(_left, alphaRect.width))
	}

	// 预选颜色点击切换方法
	async function setPrefabColor(_rgba : RGBA, type : string) {
		rgba.value = _rgba
		hsb.value = rgbToHsb(rgba.value.r, rgba.value.g, rgba.value.b)
		hex.value = rgbToHex(rgba.value.r, rgba.value.g, rgba.value.b)
		selectedHue.value = hsb.value.h

		//  根据透明度调整模式显示
		if (type == 'yx') {
			if (_rgba.a != 1) {
				showHex.value = false
			}
			else {
				showHex.value = true
			}
		}
		
		await updatePos()

		if (_rgba.a < 1) {
			emit('changing', `rgba(${rgba.value.r},${rgba.value.g},${rgba.value.b},${rgba.value.a})`)
		}
		else {
			emit('changing', hex.value)
		}
	}
	
	// 颜色选取区域拖动统一处理方法
	async function selectColor(x : number, y : number) {
		const { top, left, width, height } = await $ux.Util.getBoundingClientRect('#paletteRef', getCurrentInstance()?.proxy)

		let _left = Math.max(0, Math.min(x - left, width))
		let _top = Math.max(0, Math.min(y - top, height))

		// 计算饱和度和亮度
		const saturation = _left / width
		const brightness = 1 - _top / height

		// 将HSB转换为RGB
		const rgb = hsbToRgb(selectedHue.value / 360, saturation, brightness)
		rgba.value = { r: rgb.r, g: rgb.g, b: rgb.b, a: 1 } as RGBA
		// 将RGB转换为Hex
		hex.value = rgbToHex(rgb.r, rgb.g, rgb.b)

		// 切换十六进制模式
		showHex.value = true

		// 每次选择颜色，都把透明度颜色指示移到最右边
		const alphaRect = await $ux.Util.getBoundingClientRect('#alphaRef', getCurrentInstance()?.proxy)
		alphaLeft.value = alphaRect.width

		// 更新颜色选择指示器的位置
		selectPos.value = [_left, _top] as number[]

		emit('changing', hex.value)
	}
	
	// 颜色渐变区域拖动统一处理方法
	async function setColor(x : number) {
		const { left, width } = await $ux.Util.getBoundingClientRect('#colorRef', getCurrentInstance()?.proxy)

		let _left = Math.max(0, Math.min(x - left, width))
		colorLeft.value = _left

		// 根据点击位置计算色相值
		const hue = ((x - left) / width) * 360

		// 使用算法将色相转换为RGB
		const rgb = hsbToRgb(hue / 360, 1, 1)
		bgcolor.value = { r: rgb.r, g: rgb.g, b: rgb.b, a: 1 } as RGBA
		rgba.value = { r: rgb.r, g: rgb.g, b: rgb.b, a: 1 } as RGBA

		// 将RGB转换为Hex
		hex.value = rgbToHex(rgb.r, rgb.g, rgb.b)
		selectedHue.value = hue

		// 每次选择颜色，都把透明度颜色指示移到最右边
		const alphaRect = await $ux.Util.getBoundingClientRect('#alphaRef', getCurrentInstance()?.proxy)
		alphaLeft.value = alphaRect.width
		showHex.value = true // 切换十六进制模式

		emit('changing', hex.value)
	}
	
	// 透明度拖动统一处理方法
	async function setAlpha(x : number) {
		showHex.value = false // 切换透明度模式
		const { left, width } = await $ux.Util.getBoundingClientRect('#alphaRef', getCurrentInstance()?.proxy)

		let _left = Math.max(0, Math.min(x - left, width))
		alphaLeft.value = _left

		rgba.value.a = parseFloat((alphaLeft.value / width).toFixed(1))

		emit('changing', `rgba(${rgba.value.r},${rgba.value.g},${rgba.value.b},${rgba.value.a})`)
	}

	// 颜色选择触摸开始
	function touchstart(e : TouchEvent) {
		e.stopPropagation()
		e.preventDefault()
		isPressed.value = true
		selectColor(e.changedTouches[0].clientX, e.changedTouches[0].clientY)
	}
	
	// 颜色选择触摸移动
	function touchmove(e : TouchEvent) {
		e.stopPropagation()
		e.preventDefault()
		if (!isPressed.value) {
			return
		}
		selectColor(e.changedTouches[0].clientX, e.changedTouches[0].clientY)
	}
	
	// 颜色选择触摸结束
	function touchend(e : TouchEvent) {
		isPressed.value = false
	}
	
	// 鼠标在颜色选择上点击后触发
	function touchstartPc(e : MouseEvent) {
		isPressed.value = true
		selectColor(e.clientX, e.clientY)
	}
	
	// 鼠标在颜色选择上移动时触发
	function touchmovePc(e : MouseEvent) {
		if (!isPressed.value) {
			return
		}
		selectColor(e.clientX, e.clientY)
	}
	
	// 鼠标主按钮在颜色选择上松开时触发
	function touchendPc(e : MouseEvent) {
		isPressed.value = false
	}

	// 颜色拖动区域触摸开始
	function touchstart_color(e : TouchEvent) {
		e.stopPropagation()
		e.preventDefault()
		isPressed.value = true
		setColor(e.changedTouches[0].clientX)
	}
	
	// 颜色拖动区域触摸移动
	function touchmove_color(e : TouchEvent) {
		e.stopPropagation()
		e.preventDefault()
		if (!isPressed.value) {
			return
		}
		setColor(e.changedTouches[0].clientX)
	}
	
	// 颜色拖动区域触摸结束
	function touchend_color(e : TouchEvent) {
		isPressed.value = false
	}
	
	// 鼠标在颜色拖动区域上点击后触发
	function touchstartPc_color(e : MouseEvent) {
		isPressed.value = true
		setColor(e.clientX)
	}
	
	// 鼠标在颜色拖动区域上移动后触发
	function touchmovePc_color(e : MouseEvent) {
		if (!isPressed.value) {
			return
		}
		setColor(e.clientX)
	}
	
	// 鼠标在颜色拖动区域上移动结束后触发
	function touchendPc_color(e : MouseEvent) {
		isPressed.value = false
	}

	// 透明度开始滑动
	function touchstart_alpha(e : TouchEvent) {
		e.stopPropagation()
		e.preventDefault()
		isPressed.value = true
		setAlpha(e.changedTouches[0].clientX)
	}
	
	// 透明度移动
	function touchmove_alpha(e : TouchEvent) {
		e.stopPropagation()
		e.preventDefault()
		if (!isPressed.value) {
			return
		}
		setAlpha(e.changedTouches[0].clientX)
	}
	
	// 透明度触摸结束
	function touchend_alpha(e : TouchEvent) {
		isPressed.value = false
	}
	
	// 透明度鼠标开始
	function touchstartPc_alpha(e : MouseEvent) {
		isPressed.value = true
		setAlpha(e.clientX)
	}
	
	// 透明度鼠标开始滑动
	function touchmovePc_alpha(e : MouseEvent) {
		if (!isPressed.value) {
			return
		}
		setAlpha(e.clientX)
	}
	
	// 透明度鼠标滑动结束
	function touchendPc_alpha(e : MouseEvent) {
		isPressed.value = false
	}

	// hex 输入框
	function hexInput(e : UniInputEvent) {
		const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
		if (e.detail.value.length == 7 && hexColorRegex.test(e.detail.value)) {
			let _rgb : RGB = hexToRgb(e.detail.value) // 先转换 rgb
			setPrefabColor({ r: _rgb.r, g: _rgb.g, b: _rgb.b, a: 1 } as RGBA, 'up')
		}
		else {
			return
		}
	}
	
	// rgba 输入框
	function rgbaInput(type : string, e : UniInputEvent) {
		let inputVal : number = parseFloat(e.detail.value)
		if (inputVal <= 255 && inputVal >= 0) {
			let iRgba : RGBA = { r: rgba.value.r, g: rgba.value.g, b: rgba.value.b, a: rgba.value.a } as RGBA
			if (type == 'r') {
				iRgba.r = inputVal
			}
			else if (type == 'g') {
				iRgba.g = inputVal
			}
			else if (type == 'b') {
				iRgba.b = inputVal
			}
			else if (type == 'a') {
				iRgba.a = inputVal
			}
			setPrefabColor(iRgba, 'up')
		}
		else {
			return
		}
	}
	
	function init() {
		let _rgb : RGB = hexToRgb(props.value)
		rgba.value = { r: _rgb.r, g: _rgb.g, b: _rgb.b, a: 1 } as RGBA
		hsb.value = rgbToHsb(_rgb.r, _rgb.g, _rgb.b)
		hex.value = props.value
		selectedHue.value= hsb.value.h
		
		nextTick(() => {
			// 颜色选区，高度是等于宽度的一半
			let f = async () => {
				const { width } = await $ux.Util.getBoundingClientRect('#paletteRef', getCurrentInstance()?.proxy)
				paletteHeight.value = width / 2
			}
			f()
			updatePos()
		})
	}
	
	function click() {
		if (props.disabled) {
			return
		}
	
		setTimeout(() => {
			if(modelValue.value == '' && props.value != '') {
				value.value = props.value
			}
			
			init()
			
			uxDrawerRef.value!.open()
		}, 50);
	}
	
	function open() {
		click()
	}
	
	function close() {
		uxDrawerRef.value!.close()
		emit('close')
	}
	
	function onMaskClose() {
		emit('close')
	}
	
	function onConfirm() {
		emit('update:modelValue', hex.value)
		emit('change', hex.value)
	
		close()
	}
	
	watch(modelValue, () => {
		if(modelValue.value != '') {
			value.value = modelValue.value
			init()
		}
	}, {immediate: true})

	onMounted(() => {
		
	})
	
	defineExpose({
		open,
		close
	})
</script>

<style lang="scss" scoped>
	/* #ifdef H5 */
	uni-view {
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: stretch;
		align-content: stretch;
		justify-content: flex-start;
		box-sizing: border-box;
		min-height: 0px;
		min-width: 0px;
		overflow: hidden;
	}
	/* #endif */
	
	.ux-picker {
		position: relative;
		width: 100%;
		/* #ifdef WEB */
		cursor: pointer;
		/* #endif */
		
		&__title {
			width: 100%;
			height: 40px;
			padding: 0 15px;
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
		
			&--text {
				font-size: 13px;
				color: #333;
			}
		}
		
		&__close {
			position: absolute;
			width: 50px;
			height: 40px;
			right: 0;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		
		&__btn--cancel {
			position: absolute;
			left: 8px;
			padding: 8px 13px;
		}
		
		&__btn--cancel--bigger {
			position: absolute;
			left: 0;
			width: 50%;
			height: 100%;
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
		}
		
		&__btn--confirm {
			position: absolute;
			right: 8px;
			padding: 8px 13px;
		}
		
		&__btn--confirm--bigger {
			position: absolute;
			right: 0;
			width: 50%;
			height: 100%;
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
		}
		
		&__btn--text {
			font-size: 14px;
		}
		
		&__btns {
			width: 100%;
			height: 70px;
			padding: 0 15px;
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
			
			&--cancel {
				margin: 0 8px;
				flex: 1;
				height: 42px;
				display: flex;
				flex-direction: row;
				justify-content: center;
				align-items: center;
				border-radius: 42px;
			}
			
			&--confirm {
				margin: 0 8px;
				flex: 1;
				height: 42px;
				display: flex;
				flex-direction: row;
				justify-content: center;
				align-items: center;
				border-radius: 42px;
			}
			
			&--text {
				font-size: 14px;
			}
		}
		
		&__hover {
			opacity: 0.6;
		}
	}

	.palette {
		width: 100%;
		min-height: 100px;
		position: relative;
	}

	.palette-white {
		position: absolute;
		width: 100%;
		height: 100%;
		background: linear-gradient(to right, #fff, rgba(255, 255, 255, 0));
	}

	.palette-black {
		position: absolute;
		width: 100%;
		height: 100%;
		background: linear-gradient(to top, #000, rgba(0, 0, 0, 0));
	}

	.palette-point {
		position: absolute;
		top: -6px;
		left: -6px;
		z-index: 2;
		width: 12px;
		height: 12px;
		border: 1px solid #fff;
		border-radius: 6px;
		box-shadow: 0 0 6px 1px rgba(0, 0, 0, 0.2);
	}

	.control {
		width: 100%;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		padding: 12px;
	}

	.control-alpha {
		width: 50px;
		height: 50px;
		border-radius: 25px;
		border: 1px #f1f1f1 solid;
		overflow: hidden;
		position: relative;
		box-sizing: border-box;
	}

	.control-alpha-color {
		width: 48px;
		height: 48px;
		position: absolute;
		left: 0px;
		top: 0px;
		border-radius: 25px;
	}

	.control-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: space-between;
		height: 50px;
		padding: 3px 0 3px 22px;
		overflow: visible;
	}

	.control-item-drag {
		width: 100%;
		position: relative;
		height: 18px;
		background-color: #fff;
		overflow: visible;
	}

	.control-item-drag-transparent {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		display: flex;
		flex-direction: row;
		overflow: hidden;
	}

	.control-item-drag-image {
		width: 390px;
		min-width: 390px;
		height: 18px;
	}

	.control-item-drag-hue {
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: row;
	}

	.control-item-drag-alpha {
		width: 100%;
		height: 100%;
	}

	.control-item-drag-circle {
		position: absolute;
		top: 0;
		width: 18px;
		height: 18px;
		box-sizing: border-box;
		border-radius: 10px;
		background: #fff;
		box-shadow: 0 0 6px 1px rgba(0, 0, 0, 0.2);
	}

	.result {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
	}

	.result-select {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 50px;
		height: 50px;
		margin-right: 12px;
		margin-left: 12px;
		border-radius: 5px;
		border: 1px solid #f1f1f1;
		padding-top: 2px;
	}

	.result-select-text {
		font-size: 13px;
		color: #000;
	}

	.result-item {
		flex: 1;
		height: 50px;
		margin-right: 12px;
	}

	.result-item-value {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		height: 28px;
		border-radius: 2px;
		border: 1px solid #f1f1f1;
	}

	.result-item-value-text {
		font-size: 14px;
		color: #000;
		flex: 1;
		text-align: center;
	}

	.result-item-hex {
		height: 22px;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
	}

	.result-item-hex-text {
		line-height: 22px;
		font-size: 12px;
		color: #888;
	}

	.result-item-rgba {
		height: 22px;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
	}

	.result-item-rgba-text {
		line-height: 22px;
		font-size: 12px;
		color: #888;
	}

	.prefab {
		padding: 12px;
		display: flex;
		flex-direction: column;
	}

	.prefab-items {
		margin-top: 6px;
		width: 100%;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
	}

	.prefab-item {
		width: 24px;
		height: 24px;
		border-radius: 3px;
		position: relative;
		box-sizing: border-box;
	}

	.prefab-item-color {
		position: absolute;
		top: 0;
		left: 0;
		width: 24px;
		height: 24px;
		border-radius: 3px;
		box-sizing: border-box;
	}

	.prefab-item-image {
		border: 0.5px solid #f1f1f1;
	}

	.placeholderClass {
		font-size: 13px;
	}
</style>