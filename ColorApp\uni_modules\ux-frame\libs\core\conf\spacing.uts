import { ref } from 'vue'

export class SpacingBase {
	
	/**
	 * 间距
	 */
	normal = ref(0)
	
	/**
	 * 间距较小值
	 */
	small = ref(0)
	
	/**
	 * 间距较大值
	 */
	large = ref(0)
	
	constructor(spacing: number) {
		this.normal.value = spacing
		this.small.value = spacing - 5
		this.large.value = spacing + 5
	}
	
	/**
	 * 重置间距
	 */
	reset(spacing: number) {
		this.normal.value = spacing
		this.small.value = spacing - 5
		this.large.value = spacing + 5
	}
}