
import Intent from 'android.content.Intent'
import Uri from 'android.net.Uri'
import Context from 'android.content.Context';
import File from 'java.io.File';
import FileProvider from 'androidx.core.content.FileProvider'

import { ShareWithSystem, UxShareWithSystemOptions } from '../interface.uts';
import { UxShareSuccessCallbackImpl, UxShareErrorCallbackImpl } from '../unierror.uts'

/**
 * 系统分享
 * @param UxShareWithSystemOptions options 参数
 */
export const shareWithSystem: ShareWithSystem = function(options: UxShareWithSystemOptions) {
	let action = new Intent()
	action.setAction(Intent.ACTION_SEND)
	
	if (options.type == 'image') {
		if (options.imageUrl == null) {
			const err = new UxShareErrorCallbackImpl('shareWithSystem:imageUrl is null')
			options.fail?.(err)
			options.complete?.(err)
			return;
		}
		
		let file = new File(options.imageUrl!)
		if(!file.exists()) {
			file = new File(UTSAndroid.getResourcePath(options.imageUrl!))
			
			if(!file.exists()) {
				file = new File(UTSAndroid.convert2AbsFullPath(options.imageUrl!))
			}
		}
		
		let uri = FileProvider.getUriForFile(UTSAndroid.getAppContext()!, UTSAndroid.getAppContext()!.getPackageName() + '.uxShare.fileprovider', file);
		action.putExtra(Intent.EXTRA_STREAM, uri);
		
		action.setType('image/*')
	} else if (options.type == 'file') {
		const err = new UxShareErrorCallbackImpl('shareWithSystem:android not support')
		options.fail?.(err)
		options.complete?.(err)
		return;
	} else {
		let summary = options.summary ?? ''
		let href = options.href ?? ''
		
		if(href != '') {
			if(summary == '') {
				summary = href
			} else {
				summary += '\n' + href
			}
		}
		
		action.setType('text/plain')
		action.putExtra(Intent.EXTRA_TEXT, summary)
	}
	
	UTSAndroid.getUniActivity()!.startActivity(Intent.createChooser(action, options.title ?? ''))
	
	const res = new UxShareErrorCallbackImpl('shareWithSystem:success')
	options.success?.(res)
	options.complete?.(res)
}