<template>
	<view class="ux-grid" :style="style">
		<slot></slot>
	</view>
</template>

<script setup lang="ts">
	
	/**
	 * Grid 宫格布局
	 * @description 宫格布局 需搭配 ux-grid-item 使用
	 * @demo pages/component/grid.uvue
	 * @tutorial https://www.uxframe.cn/component/grid.html
	 * @property {Number}			col							Number | 宫格的列数（默认 3 ）
	 * @property {Boolean}			border						Boolean | 是否显示宫格的边框（默认 false ）
	 * @property {String}			align=[left|center|right]	String | 宫格对齐方式，表现为数量少的时候，靠左，居中，还是靠右 （默认 left ）
	 * @value left 左对齐
	 * @value center 居中
	 * @value right 右对齐
	 * @property {String}			background					String | 背景色（默认 transparent ）
	 * <AUTHOR>
	 * @date 2023-12-31 18:58:08
	 */
	
	import { computed, getCurrentInstance, ref, watch } from 'vue'
	import { $ux } from '../../index'
	import { useResize } from '../../libs/use/resize'
	
	defineOptions({
		name: 'ux-grid'
	})
	
	const props = defineProps({
		col: {
			type: Number,
			default: 3
		},
		border: {
			type: Boolean,
			default: false
		},
		align: {
			type: String,
			default: 'left'
		},
		background: {
			type: String,
			default: 'transparent'
		}
	})
	
	const instance = getCurrentInstance()?.proxy
	
	const nodes = ref<Array<UxGridItemComponentPublicInstance>>([] as Array<UxGridItemComponentPublicInstance>) 
	
	const style = computed((): string => {
		let _align = props.align as string
		switch (_align) {
			case 'left':
				return `justify-content: flex-start;background-color: ${props.background};`
			case 'center':
				return `justify-content: center;background-color: ${props.background};`
			case 'right':
				return `justify-content: flex-end;background-color: ${props.background};`
		}
		
		return `justify-content: flex-start;background-color: ${props.background};`
	})
	
	const parentData = computed((): UTSJSONObject => {
		return {
			col: props.col,
			border: props.border,
		} as UTSJSONObject
	})
	
	function setData(child : UxGridItemComponentPublicInstance) {
		child._.exposed.setData(parentData.value)
	}
	
	function register(child : UxGridItemComponentPublicInstance) {
		nodes.value.push(child)
		setData(child)
	}
	
	watch(parentData, () => {
		nodes.value.forEach((child : UxGridItemComponentPublicInstance) => {
			setData(child)
		})
	})
	
	defineExpose({
		register
	})
</script>

<style lang="scss">

	.ux-grid {
		width: 100%;
		padding-bottom: 10px;
		box-sizing: border-box;
		overflow: hidden;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		flex-wrap: wrap;
	}
</style>