<template>
	<!-- #ifdef APP-NVUE -->
	<cell class="ux-list-item">
		<slot></slot>
	</cell>
	<!-- #endif -->
	<!-- #ifndef APP-NVUE -->
	<view class="ux-list-item" :style="{'backgroundColor': _render ? '' : 'green'}">
		<!--  :style="{'visibility': _render ? 'visible' : 'hidden'}" -->
		<slot></slot>
		<!-- <view v-else :style="style"></view> -->
	</view>
	<!-- #endif -->
</template>

<script setup lang="ts">
	
	/**
	 * list-item
	 * @description ListView 列表容器子项
	 * @demo pages/component/list.uvue
	 * @tutorial https://www.uxframe.cn/component/list.html
	 * @property {Number}			height  				Number | 项目固定高度
	 * <AUTHOR>
	 * @date 2023-11-04 01:12:28
	 */
	
	import { computed, getCurrentInstance, inject, onMounted, ref, watch } from 'vue'
	import { $ux } from '../../index'
	
	defineOptions({
		name: 'ux-list-item'
	})
	
	const props = defineProps({
		height: {
			type: Number,
			default: 0
		}
	})
	
	const _render = ref(true)
	const _height = ref(0)
	
	const style = computed(() => {
		let css = {}
		
		css['height'] = `${_height.value}px`
		
		return css
	})
	
	function getHeight() {
		return _height.value
	}
	
	function render(b: boolean) {
		_render.value = b
	}
	
	async function init() {
		const rect = await $ux.Util.getBoundingClientRect('.ux-list-item', getCurrentInstance()?.proxy)
		_height.value = rect.height
	}
	
	onMounted(() => {
		// #ifndef APP-NVUE
		if(props.height > 0) {
			_height.value = props.height
		} else {
			setTimeout(() => {
				init()
			}, 100)
		}
		// #endif
	})
	
	defineExpose({
		render,
		getHeight
	})
</script>

<style lang="scss">
	.ux-list-item {
		
	}
</style>