<template>
	<scroll-view ref="scroll" class="ux-virtual-list" direction="vertical" :show-scrollbar="false" :refresher-enabled="true"
		:refresher-triggered="isRefresh" :refresher-max-drag-distance="80" refresher-default-style="none"
		refresher-background="transparent" @refresherpulling="refresherpulling" @refresherrefresh="refresherrefresh"
		@refresherrestore="refresherrestore" @refresherabort="refresherabort" @scroll="onScroll">
		
		<view :style="{ height: placeholderHeight + 'px' }">
			<view :style="{ top: containerTop + 'px' }">
				<slot name="header"></slot>
				<slot :items="items"></slot>
			</view>
		</view>

		<view slot="refresher" style="width: 100%;">
			<ux-refresher :states="refresherStates" :state="refresherState"></ux-refresher>
		</view>
	</scroll-view>
</template>

<script>
	
	/**
	 * 虚拟列表
	 * @description 用于超长列表的展示
	 * @demo pages/component/virtual-list.uvue
	 * @tutorial https://www.uxframe.cn/component/virtual-list.html
	 * @property {Array}			list								Any[] | 列表数据
	 * @property {Function}			refresh								(callback: () => void) => void | 刷新事件
	 * @property {Function}			load								(callback: () => void) => void | 加载事件
	 * <AUTHOR>
	 * @date 2025-04-03 20:31:11
	 */
	
	type UxListCallback = (callback: () => void) => void
	
	export default {
		name: "ux-virtual-list",
		data() {
			return {
				items: [] as any[],
				containerTop: 0,
				scrollElementHeight: 0,
				placeholderHeight: 0,
				offsetThreshold: [0, 0, 0, 0], // -5, -3, 3, 5 屏对应的offset
				cachedSize: new Map<any | null, number>(),
				initialized: false,
				hasDefaultSize: false,
				defaultItemSize: 40,
				lastScrollTop: 0,
				rearrangeQueue: [] as number[],
				isRefresh: false,
				refresherState: 0,
				refresherStates: ['下拉刷新', '释放刷新', '刷新中...', '刷新成功'],
				isLoading: false,
				lastY: 0,
			};
		},
		props: {
			list: {
				type: Array as PropType<any[]>,
				default: [] as any[]
			},
			refresh: {
				type: Function as PropType<UxListCallback | null>,
			},
			load: {
				type: Function as PropType<UxListCallback | null>,
			}
		},
		provide() {
			return {
				setCachedSize: (item : any |  null, size : number) => {
					if(item == null) {
						return
					}
					
					if (!this.hasDefaultSize) {
						this.defaultItemSize = size
						this.hasDefaultSize = true
					}
					
					this.cachedSize.set(item, size)
				}
			}
		},
		watch: {
			list: {
				handler(a : any[], b: any[]) {
					this.cachedSize.clear()
					this.init()
				},
				immediate: true,
				deep: true
			},
			defaultItemSize() {
				this.rearrange(this.lastScrollTop)
			}
		},
		created() {
			this.placeholderHeight = this.list.length * this.defaultItemSize
		},
		mounted() {
			this.init()
		},
		methods: {
			init() {
				this.placeholderHeight = this.list.length * this.defaultItemSize
				
				nextTick(() => {
					uni.createSelectorQuery().in(this).select('.ux-virtual-list').boundingClientRect().exec((ret) => {
						let height = (ret[0] as NodeInfo).height ?? 0
						this.scrollElementHeight = height
						this.rearrange(0)
						this.initialized = true
					})
				})
			},
			refresherpulling(e : RefresherEvent) {
				if (this.refresherState == 0 || this.refresherState == 1) {
					if (e.detail.dy > 45) {
						this.refresherState = 1
					} else {
						this.refresherState = 0
					}
				}
			},
			refresherrefresh(e : RefresherEvent) {
				this.refresherState = 2
				this.isRefresh = true
				this.lastY = 0
				
				let callback = () => {
					setTimeout(() => {
						this.refresherState = 3
						setTimeout(() => {
							this.isRefresh = false
						}, 200);
					}, 100);
				}
				
				if(this.refresh != null) {
					(this.refresh! as UxListCallback)(callback)
				} else {
					callback()
				}
			},
			refresherrestore(e : RefresherEvent) {
				setTimeout(() => {
					this.refresherState = 0
				}, 50);
			},
			refresherabort(e : RefresherEvent) {
				setTimeout(() => {
					this.refresherState = 0
				}, 50);
			},
			onScroll(e : UniScrollEvent) {
				if (!this.initialized) {
					return
				}
				
				const scrollTop = e.detail.scrollTop
				
				let y = Math.abs(scrollTop)
				if (y - this.lastY > 300 && !this.isLoading) {
					this.lastY = y
					this.isLoading = true
					
					let callback = () => {
						this.isLoading = false
					}
					
					if(this.load != null) {
						(this.load! as UxListCallback)(callback)
					} else {
						callback()
					}
				}
				
				if (scrollTop === this.lastScrollTop || scrollTop < 0) {
					return
				}
				this.lastScrollTop = scrollTop
				if (scrollTop < this.offsetThreshold[1] || scrollTop > this.offsetThreshold[2]) {
					this.queue(scrollTop)
				}
			},
			queue(scrollTop : number) {
				/*
				 * rearrange内为大量同步逻辑，在上次rearrange未执行完毕的情况下将后续多个rearrange合并成一次执行，即仅执行最后一次
				 * 由于滚动机制差异，此优化仅在web端才有意义。
				 * 如何测试：push后console.log(this.rearrangeQueue.length) 输出结果大于1时触发优化
				 */
				this.rearrangeQueue.push(scrollTop)
				setTimeout(() => {
					this.flush()
				}, 1)
			},
			flush() {
				const queueLength = this.rearrangeQueue.length
				if (queueLength == 0) {
					return
				}
				const lastScrollTop = this.rearrangeQueue[queueLength - 1]
				this.rearrange(lastScrollTop)
				this.rearrangeQueue = [] as number[]
			},
			rearrange(scrollTop : number) {
				this.offsetThreshold[0] = Math.max(scrollTop - this.scrollElementHeight * 5, 0)
				this.offsetThreshold[1] = Math.max(scrollTop - this.scrollElementHeight * 3, 0)
				this.offsetThreshold[2] = Math.min(scrollTop + this.scrollElementHeight * 4, this.placeholderHeight)
				this.offsetThreshold[3] = Math.min(scrollTop + this.scrollElementHeight * 6, this.placeholderHeight)
				const offsetStart = this.offsetThreshold[0]
				const offsetEnd = this.offsetThreshold[3]
				const items = [] as any[]
				const defaultItemSize = this.defaultItemSize
				const cachedSize = this.cachedSize
				const list = this.list
				let tempTotalHeight = 0
				let containerTop = 0
				let start = false, end = false
				for (let i = 0; i < list.length; i++) {
					const item = list[i]
					let itemSize = defaultItemSize
					const cachedItemSize = cachedSize.get(item)
					if (cachedItemSize != null) {
						itemSize = cachedItemSize
					}
					tempTotalHeight += itemSize
					if (end) {
						continue
					}
					if (tempTotalHeight < offsetStart) {
						containerTop = tempTotalHeight
					} else if (tempTotalHeight >= offsetStart && tempTotalHeight <= offsetEnd) {
						if (start == false) {
							start = true
						}
						items.push(item)
					} else {
						if (!end) {
							end = true
						}
					}
				}
				this.placeholderHeight = tempTotalHeight
				this.items = items
				this.containerTop = containerTop
			}
		}
	}
</script>

<style lang="scss">
	
	.ux-virtual-list {
		flex: 1;
	}
	
</style>