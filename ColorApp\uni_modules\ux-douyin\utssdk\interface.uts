/**
 * 注册
 */
export declare function register(options : UxDouyinRegisterOptions): void

/**
 * 登录
 */
export declare function login(options : UxDouyinLoginOptions): void

/**
 * 分享
 */
export declare function share(options : UxDouyinShareOptions): void

/**
 * 打开抖音
 */
export declare function openApp(): void

/**
 * 抖音app是否安装
 */
export declare function isInstalled(): boolean

/**
 * 错误码
 * [9010000, 'unregister']
 * [9010001, 'register fail']
 * [9010002, 'share fail']
 * [9010003, 'login fail']
 * [9010004, 'auth denied']
 * [9010005, 'user cancel']
 * [9010006, 'unsupport']
 * [9010007, 'uninstall douyin']
 * [9010008, 'unknow error']
 * [9010009, 'pay fail']
 */
export type UxDouyinErrorCode = 9010000 | 9010001 | 9010002 | 9010003 | 9010004 | 9010005 | 9010006 | 9010007 | 9010008 | 9010009;

/**
 * 错误回调参数
 */
export interface UxDouyinFail extends IUniError {
  errCode : UxDouyinErrorCode
};

/**
 * 成功回调
 */
export type UxDouyinSuccess = {
	code : string,
	msg : any,
}

/**
 * 注册
 */
export type UxDouyinRegisterOptions = {
	/**
	 * 抖音appid
	 */
	appid : string
	/**
	* 接口调用成功的回调
	*/
	success ?: (res : UxDouyinSuccess) => void
	/**
	* 接口调用失败的回调函数
	*/
	fail ?: (res : UxDouyinFail) => void
}

/**
 * 登录
 */
export type UxDouyinLoginOptions = {
	/**
	 * 用于保持请求和回调的状态，授权请求后原样带回给第三方，可传任意 String
	 */
	state ?: string
	/**
	* 接口调用成功的回调
	*/
	success ?: (res : UxDouyinSuccess) => void
	/**
	* 接口调用失败的回调函数
	*/
	fail ?: (err : UxDouyinFail) => void
}

/**
 * 分享
 */
export type UxDouyinShareOptions = {
	/**
	 * 分享形式，如网页、图片、小程序等
	 * 0	网页
	 * 1	图片
	 * 2	小程序
	 */
	type ?: number
	/**
	 * 分享内容的标题
	 */
	title ?: string
	/**
	 * 分享内容的摘要，type 为 1 时必选
	 */
	summary ?: string
	/**
	 * 跳转链接，type 为 0 时必选
	 */
	href ?: string
	/**
	 * 图片地址，type为0时，推荐使用小于20Kb的图片，type 为 1时必选且只支持本地图片
	 */
	imageUrl ?: string
	/**
	 * 分享小程序必要参数，type 为 2 时必选
	 */
	miniProgram ?: UxDouyinMiniProgram
	/**
	* 接口调用成功的回调
	*/
	success ?: (res : UxDouyinSuccess) => void
	/**
	* 接口调用失败的回调函数
	*/
	fail ?: (res : UxDouyinFail) => void
}

export type UxDouyinMiniProgram = {
	/**
	 * 抖音小程序原始id
	 */
	id : string
	/**
	 * 小程序卡片标题
	 */
	title ?: string
	/**
	 * 点击链接进入的页面
	 */
	path : string
	/**
	 * 点击小程序落地页的query
	 */
	query : string
	/**
	 * 小程序卡片的封面图，通过OpenAPI获取，必传
	 */
	imageId ?: string
	/**
	 * 自定义状态
	 */
	state ?: string
}