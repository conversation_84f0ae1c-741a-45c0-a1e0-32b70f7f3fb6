<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/upload?title=Upload"></Mobile>

# Upload
> 组件类型：UxUploadComponentPublicInstance

支持图片视频，支持删除、排序功能，支持进度显示

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| list | Array |  | 默认列表 |
| name | String | file | 文件对应的key,开发者在服务器端通过这个key可以获取到文件二进制内容 |
| url | String |  | 上传地址 |
| header | UTSJSONObject |  | HTTP请求Header,header中不能设置Referer |
| formData | UTSJSONObject |  | HTTP请求中其他额外的formdata |
| timeout | Number | 120000 | 超时时间，单位ms |
| retry | Number | 3 | 重试次数 |
| successCode | Number | 200 | 上传成功状态码 |
| [mode](#mode) | String | image | 模式 |
| count | Number | 9 | 最大数量 |
| sizeType | Array | ['original','compressed'] | original原图，compressed压缩图Web不支持 |
| sourceType | Array | ['album','camera'] | album从相册选图，camera使用相机 |
| extension | Array |  | 文件拓展名过滤仅Web支持 |
| [align](#align) | String | left | 对齐方向 |
| rows | Boolean | true | 多行 |
| col | Number | 4 | 列数，rows=true时生效 |
| gutter | Any |  | 间隔默认5) |
| width | Any | 65 | 文件宽，rows=false时生效 |
| height | Any | 65 | 文件高，rows=false时生效 |
| radius | Any | 8 | 圆角 |
| background | String |  | 按钮背景色 |
| [backgroundDark](#backgroundDark) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| borderColor | String |  | 按钮边框色 |
| [borderColorDark](#borderColorDark) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| waitingText | String | 等待上传 | 等待上传文案 |
| failText | String | 上传失败 | 等待失败文案 |
| frontBtn | Boolean | false | 前置上传按钮 |
| showDel | Boolean | true | 显示删除按钮 |
| beforeDel | Function |  | 删除钩子 |
| beforeUpload | Function |  | 上传前钩子 |
| afterUpload | Function |  | 上传完成钩子 |
| autoUpload | Boolean | true | 自动上传 |
| disabled | Boolean | false | 禁用 |

### [mode](#mode)

| 值   | 说明 |
|:------:|:----:|
| image图片
 |  |
| video视频
 |  |
| media媒体
 |  |
| file文件
 |  |

### [align](#align)

| 值   | 说明 |
|:------:|:----:|
| left居左
 |  |
| right居右
 |  |

### [backgroundDark](#backgroundDark)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [borderColorDark](#borderColorDark)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| click | 点击时触发 |  |
| change | 值改变时触发 |  |
| error | 发生错误时触发 |  |
  
  