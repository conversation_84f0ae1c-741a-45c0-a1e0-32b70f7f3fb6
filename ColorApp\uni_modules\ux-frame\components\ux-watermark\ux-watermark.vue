<template>
	<view :id="myId" class="ux-watermark" :style="{'position': fullscreen ? 'fixed' : ''}">
		<text v-if="mode == 'text'" :id="itemId" class="ux-watermark__item" :style="itemStyle">{{ content }}</text>
		<image v-if="mode == 'img'" :id="itemId" class="ux-watermark__item" :style="itemStyle" :src="content" mode="aspectFit"></image>
		
		<view ref="uxWatermarkRef" class="ux-watermark__body" :style="style">
			<canvas class="ux-watermark__canvas" :id="canvasId" :canvas-id="canvasId"></canvas>
		</view>
	</view>
</template>

<script setup lang="ts">
	
	/**
	 * Watermark 水印
	 * @description 支持局部水印，支持全屏水印
	 * @demo pages/component/watermark.uvue
	 * @tutorial https://www.uxframe.cn/component/watermark.html
	 * @property {String}			mode=[text|img]			String | 模式 (默认 text)
	 * @value text 文本
	 * @value img 图片 暂不支持
	 * @property {String}			content					String | 水印文字
	 * @property {String}			color					String | 水印颜色 (默认 rgba(0, 0, 0, 0.2))
	 * @property {String}			darkColor				String | 深色 (默认 rrgba(255, 255, 255, 0.8))
	 * @property {Any}				size					Any | 文字大小 (默认 16)
	 * @property {Any}				imgWidth				Any | 图片宽 (默认 60)
	 * @property {Any}				imgHeight				Any | 图片高 (默认 30)
	 * @property {Any}				gutter					Any | 间隙 (默认 25)
	 * @property {Boolean}			fullscreen				Boolean | 全屏
	 * <AUTHOR>
	 * @date 2024-12-03 19:16:24
	 */
	
	import { computed, getCurrentInstance, onMounted, ref, watch } from 'vue'
	import { $ux } from '../../index'
	import { useFontColor } from '../../libs/use/style.uts'
	import { useResize } from '../../libs/use/resize'
	
	type Position = {
		x : number,
		y : number
	}
	
	defineOptions({
		name: 'ux-watermark'
	})
	
	const props = defineProps({
		mode: {
			type: String,
			default: 'text'
		},
		content: {
			type: String,
			default: ''
		},
		color: {
			type: String,
			default: 'rgba(0, 0, 0, 0.2)'
		},
		darkColor: {
			type: String,
			default: 'rgba(255, 255, 255, 0.8)'
		},
		size: {
			default: 16
		},
		imgWidth: {
			default: 60
		},
		imgHeight: {
			default: 30
		},
		gutter: {
			default: 25
		},
		fullscreen: {
			type: Boolean,
			default: false
		}
	})
	
	let instance = getCurrentInstance()?.proxy
	
	const myId = `ux-watermark-${$ux.Random.uuid()}`
	const itemId = `ux-watermark-item-${$ux.Random.uuid()}`
	const canvasId = `ux-watermark-canvas-${$ux.Random.uuid()}`
	const dpr = uni.getWindowInfo().pixelRatio
	const uxWatermarkRef = ref<Element | null>(null)
	const ctx = ref<any | null>(null)
	const width = ref(0)
	const height = ref(0)
	const textWidth = ref(0)
	const textHeight = ref(0)
	const isOk = ref(false)
	
	const isDark = computed((): boolean => {
		return $ux.Conf.darkMode.value
	})
	
	const content = computed((): string => {
		return props.content
	})
	
	const fontColor = computed(() : string => {
		return useFontColor(props.color, props.darkColor)
	})
	
	const style = computed(() => {
		let css = {}
		
		if(props.fullscreen) {
			css['top'] = `${35}%`
		} else {
			css['left'] = `${15}%`
		}
		
		css['width'] = `${width.value}px`
		css['height'] = `${height.value}px`
		
		css['opacity'] = isOk.value ? 1 : 0
		
		if(isOk.value) {
			css['transform'] = 'rotate(-45deg)'
		}
		
		return css
	})
	
	const itemStyle = computed(() => {
		let css = {}
		
		if(props.mode == 'text') {
			css['font-size'] = `${$ux.Util.getPx(props.size)}px`
		} else {
			css['width'] = `${$ux.Util.getPx(props.imgWidth)}px`
			css['height'] = `${$ux.Util.getPx(props.imgHeight)}px`
		}
		
		return css
	})
	
	function calcTexts(): Position[] {
		
		let w = textWidth.value + $ux.Util.getPx(props.gutter)
		let h = textHeight.value + $ux.Util.getPx(props.gutter)
		
		let maxCol = Math.ceil(width.value / w)
		let maxRow = Math.ceil(height.value / h)
		
		let texts = [] as Position[]
		for (let i = 0; i < maxRow; i++) {
			for (let j = 0; j < maxCol; j++) {
				texts.push({
					x: j * w,
					y: i * h
				} as Position)
			}
		}
		
		return texts
	}
	
	function draw() {
		const _ctx = ctx.value
		
		_ctx.fillStyle = 'transparent'
		_ctx.fillRect(0, 0, width.value, height.value)
		
		// #ifdef APP
		_ctx.font = `${$ux.Util.getPx(props.size)}px`
		// #endif
		// #ifdef WEB
		_ctx.font = `${$ux.Util.getPx(props.size)}px Arial`
		// #endif
		
		_ctx.fillStyle = fontColor.value
		_ctx.textAlign = 'left'
		
		let texts = calcTexts()
		texts.forEach((pos : Position) => {
			_ctx.fillText(content.value, pos.x, pos.y)
		})
		
		 _ctx.draw()
	}
	
	function initText() {
		uni.createSelectorQuery().in(instance).select(`#${itemId}`).boundingClientRect((rect) => {
			let node = rect as NodeInfo
			textWidth.value = node.width ?? 0
			textHeight.value = node.height ?? 0
			
			draw()
			
			isOk.value = true
		}).exec()
	}
	
	async function init() {
		isOk.value = false
		
		const rect = await $ux.Util.getBoundingClientRect(`#${myId}`, getCurrentInstance()?.proxy)
		
		width.value = rect.width ?? uni.getWindowInfo().windowWidth
		height.value = rect.height ?? uni.getWindowInfo().windowHeight
		
		width.value = Math.max(width.value, height.value)
		height.value = Math.max(width.value, height.value)
		
		// #ifdef WEB
		// @ts-ignore
		ctx.value = uni.createCanvasContext(canvasId, getCurrentInstance()?.proxy)
		ctx.value.width = width.value * dpr
		ctx.value.height = height.value * dpr
		// ctx.value.scale(dpr, dpr)
		// #endif
		
		initText()
	}
	
	watch(content, () => {
		init()
	})
	
	watch(isDark, () => {
		init()
	})
	
	onMounted(() => {
		init()
		setTimeout(() => {
			init()
		}, 100)
		
		useResize(uni.getElementById(myId), () => {
			init()
		})
	})
</script>


<style lang="scss" scoped>
	/* #ifdef H5 */
	uni-view {
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: stretch;
		align-content: stretch;
		justify-content: flex-start;
		box-sizing: border-box;
		min-height: 0px;
		min-width: 0px;
		overflow: hidden;
	}
	/* #endif */
	
	.ux-watermark {
		/* #ifdef WEB */
		pointer-events: none;
		/* #endif */
		width: 100%;
		height: 100%;
		top: 0;
		background-color: transparent;
		
		&__item {
			position: fixed;
			opacity: 0;
			display: flex;
		}
		
		&__body {
			position: absolute;
			background-color: transparent;
			transform-origin: left center;
			overflow: hidden;
		}
		
		&__canvas {
			width: 100%;
			height: 100%;
			background-color: transparent;
			overflow: hidden;
		}
	}
</style>