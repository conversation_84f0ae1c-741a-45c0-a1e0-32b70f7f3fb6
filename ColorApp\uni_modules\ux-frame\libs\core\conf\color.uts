// #ifndef UNI-APP-X
import { ref } from 'vue'
// #endif

import { Color } from "./../color/index"
const colorUtil = new Color()

export class ColorBase {
	
	/**
	 * 颜色值
	 */
	color = ref('')
	
	/**
	 * 深色颜色值
	 */
	darkColor = ref('')
	
	/**
	 * 浅色
	 */
	light = ref('')
	
	/**
	 * 深色
	 */
	dark = ref('')
	
	/**
	 * 禁止色
	 */
	disabled = ref('')
	
	/**
	 * 禁止色-深色
	 */
	disabledDark = ref('')
	
	constructor(color: string, darkColor: string) {
		this.color.value = color
		this.darkColor.value = darkColor
		this.light.value = this.getLightColor(color)
		this.dark.value = darkColor == '' ? this.getDarkColor(color) : darkColor
		this.disabled.value = this.getDisabledColor(color)
		this.disabledDark.value = this.getDarkColor(this.disabled.value)
		
		// console.log(color, this.light.value, this.dark.value, this.disabled.value, this.disabledDark.value);
	}
	
	/**
	 * 重置颜色值
	 */
	reset(color: string, darkColor: string) {
		this.color.value = color
		this.darkColor.value = darkColor
		this.light.value = this.getLightColor(color)
		this.dark.value = darkColor == '' ? this.getDarkColor(color) : darkColor
		this.disabled.value = this.getDisabledColor(color)
		this.disabledDark.value = this.getDarkColor(this.disabled.value)
	}
	
	/**
	 * 反色
	 * 深色 -> 浅色
	 * 浅色 -> 深色
	 */
	getDarkColor(color: string): string {
		if(colorUtil.isDarkColor(color)) {
			return colorUtil.lightenColor(color, 80)
		} else {
			return colorUtil.darkenColor(color, 80)
		}
	}
	
	/**
	 * 浅色
	 */
	getLightColor(color: string): string {
		return colorUtil.lightenColor(color, 20)
	}
	
	/**
	 * 禁止色
	 */
	getDisabledColor(color: string): string {
		return colorUtil.getRgba(color, 0.6)
	}
}