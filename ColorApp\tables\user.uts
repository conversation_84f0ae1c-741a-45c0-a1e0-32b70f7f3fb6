
import { Table, TableColumn, Column } from '@/uni_modules/ux-orm'

export class UserColumn extends TableColumn {
	
	readonly id = Column.int('id').primaryKey()
	readonly uuid = Column.string('uuid')
	readonly nickname = Column.string('nickname').aliased('name')
	readonly phone = Column.string('phone')
	readonly avatar = Column.blob('avatar')
	readonly sex = Column.short('sex')
	readonly age = Column.float('age')
	readonly vip = Column.boolean('is_vip').default(false)
	readonly extInfo = Column.json('ext_info').default({'province': '安徽'})
	readonly createAt = Column.datetime('create_at').default(new Date())
	readonly updateAt = Column.timestamp('update_at')
	
	public constructor() {
	    super()
	}
}

export class UserTable extends Table {
	// 覆写表名称
	override name = 'tb_user'
	// 覆写表结构
	override columns: TableColumn = new UserColumn()
	
	// 字段映射
	id ?: number = null
	uuid ?: string = null
	nickname ?: string = null
	phone ?: string = null
	avatar ?: ByteArray = null
	sex ?: number = null
	age ?: number = null
	vip ?: boolean = null
	extInfo ?: UTSJSONObject = null
	createAt ?: Date = null
	updateAt ?: number = null

	public constructor() {
		super()
	}
}