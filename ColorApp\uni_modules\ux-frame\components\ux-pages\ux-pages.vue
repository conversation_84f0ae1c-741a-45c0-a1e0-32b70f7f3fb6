<template>
	<view class="ux-pages">
		<view class="ux-pages__item" v-if="pageList[0].init" :style="{visibility: index == 0 ? 'visible' : 'hidden'}">
			<slot name="0">
				<view class="ux-pages__error">
					<text class="ux-pages__error__text">页面未配置</text>
				</view>
			</slot>
		</view>
		<view class="ux-pages__item" v-if="pageList[1].init" :style="{visibility: index == 1 ? 'visible' : 'hidden'}">
			<slot name="1">
				<view class="ux-pages__error">
					<text class="ux-pages__error__text">页面未配置</text>
				</view>
			</slot>
		</view>
		<view class="ux-pages__item" v-if="pageList[2].init" :style="{visibility: index == 2 ? 'visible' : 'hidden'}">
			<slot name="2">
				<view class="ux-pages__error">
					<text class="ux-pages__error__text">页面未配置</text>
				</view>
			</slot>
		</view>
		<view class="ux-pages__item" v-if="pageList[3].init" :style="{visibility: index == 3 ? 'visible' : 'hidden'}">
			<slot name="3">
				<view class="ux-pages__error">
					<text class="ux-pages__error__text">页面未配置</text>
				</view>
			</slot>
		</view>
		<view class="ux-pages__item" v-if="pageList[4].init" :style="{visibility: index == 4 ? 'visible' : 'hidden'}">
			<slot name="4">
				<view class="ux-pages__error">
					<text class="ux-pages__error__text">页面未配置</text>
				</view>
			</slot>
		</view>
	</view>
</template>

<script setup lang="ts">

	/**
	 * 页面组
	 * @description 需搭配Tabbar组件使用
	 * @demo pages/component/pages.uvue
	 * @tutorial https://www.uxframe.cn/component/pages.html
	 * @property {Slot}				0						Slot | 第1个页面插槽
	 * @property {Slot}				1						Slot | 第2个页面插槽
	 * @property {Slot}				2						Slot | 第3个页面插槽
	 * @property {Slot}				3						Slot | 第4个页面插槽
	 * @property {Slot}				4						Slot | 第5个页面插槽
	 * @property {Number}			index					Number | 当前页面下标 (默认 0)
	 * @property {Number}			pages					Number | 页面数量 (默认 0)
	 * <AUTHOR>
	 * @date 2023-10-17 18:28:22
	 */

	import { ref, computed, watch, onMounted } from 'vue'
	
	type Page = {
		index : number,
		init : boolean
	}
	
	defineOptions({
		name: 'ux-pages'
	})
	
	const props = defineProps({
		index: {
			type: Number,
			default: 0
		},
		pages: {
			type: Number,
			default: 0
		},
	})
	
	const pageList = ref<Page[]>([{
		index: 0,
		init: true
	},{
		index: 1,
		init: false
	},{
		index: 2,
		init: false
	},{
		index: 3,
		init: false
	},{
		index: 4,
		init: false
	}] as Page[])
	
	function init() {
		if (props.pages > 5) {
			console.warn('[ux-pages]配置警告: 页面最大支持5页');
		}
	}
	
	const index = computed((): number => {
		return props.index
	})
	
	const pages = computed((): number => {
		return props.pages
	})
	
	watch(index, () => {
		if (index.value <= pageList.value.length - 1) {
			pageList.value[index.value].init = true
		}
	})
	
	watch(pages, () => {
		init()
	})
	
	onMounted(() => {
		init()
	})
</script>

<style lang="scss" scoped>
	
	.ux-pages {
		position: relative;
		flex: 1;

		&__item {
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
		}

		&__error {
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;

			&__text {
				font-size: 15px;
				color: gray;
			}
		}
	}
</style>