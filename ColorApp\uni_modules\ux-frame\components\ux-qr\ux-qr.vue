<template>
	<view class="ux-qr" :style="style">
		<web-view v-if="qr == ''" :id="myId" class="ux-qr__webview" :src="src" @message="onMessage"></web-view>
		<image v-else class="ux-qr__webview" :src="qr" mode="widthFix"></image>
		
		<ux-loading v-if="showLoading && isLoading">{{ loadingText }}</ux-loading>
		
		<view class="ux-qr__mask" @click="click"></view>
	</view>
</template>

<script setup lang="ts">
	
	/**
	 * 二维码
	 * @description 支持修改尺寸、背景色、前景色、增加Logo
	 * @demo pages/component/qr.uvue
	 * @tutorial https://www.uxframe.cn/component/qr.html
	 * @property {String}		text  								String | 二维码内容
	 * @property {Any}			size  								Any | 二维码尺寸 (默认 128)
	 * @property {String}		background  						String | 背景色 (默认 #fff)
	 * @property {String}		foreground  						String | 前景色 (默认 #000)
	 * @property {String}		img  								String | 二维码图标，仅支持网络图片
	 * @property {Any}			imgSize  							Any | 二维码图标尺寸 (默认 45)
	 * @property {Any}			border  							Any | 边框 (默认 4)
	 * @property {Boolean}		showLoading=[true|false]  			Boolean | 显示加载 (默认 true)
	 * @property {String}		loadingText  						String | 加载文案 (默认 加载中...)
	 * @property {Number}		delay  								Number | 延迟生成 单位ms (默认 0)
	 * @property {Array}		margin								Array | 边距 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}			mt									Any | 距上 单位px
	 * @property {Any}			mr									Any | 距右 单位px
	 * @property {Any}			mb									Any | 距下 单位px
	 * @property {Any}			ml									Any | 距左 单位px
	 * @property {Array}		padding								Array | 填充 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}			pt									Any | 上内边距 单位px
	 * @property {Any}			pr									Any | 右内边距 单位px
	 * @property {Any}			pb									Any | 下内边距 单位px
	 * @property {Any}			pl									Any | 左内边距 单位px
	 * @property {Array}		xstyle								Array<any> | 自定义样式
	 * @event {Function}		click								Function | 被点击时触发
	 * @event {Function}		change								Function | 调用make生成二维码时触发
	 * @event {Function}		error								Function | 发生错误时触发
	 * <AUTHOR>
	 * @date 2024-12-03 01:40:45
	 */
	
	import { computed, onMounted, ref, watch } from 'vue'
	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	
	type Callback = {
		cb: (img: string) => void
	}
	
	defineOptions({
		name: 'ux-qr',
		mixins: [xstyleMixin]
	})
	
	const emit = defineEmits(['click', 'change', 'error'])
	
	const props = defineProps({
		text: {
			type: String,
			default: ''
		},
		size: {
			type: Number,
			default: 128
		},
		background: {
			type: String,
			default: '#fff'
		},
		foreground: {
			type: String,
			default: '#000'
		},
		img: {
			type: String,
			default: ''
		},
		imgSize: {
			default: 35
		},
		border: {
			default: 4
		},
		showLoading: {
			type: Boolean,
			default: true
		},
		loadingText: {
			type: String,
			default: '加载中...'
		},
		delay: {
			type: Number,
			default: 0
		}
	})
	
	const myId = `ux-qr-${$ux.Random.uuid()}`
	const ctx = ref<WebviewContext | null>(null)
	const src = '/uni_modules/ux-frame/hybrid/html/qr.html'
	const qr = ref('')
	const isLoading = ref(false)
	const callback = ref<Callback | null>(null)
	
	const text = computed((): string => {
		return props.text
	})
	
	const size = computed((): number => {
		return props.size
	})
	
	const img = computed((): string => {
		return props.img
	})
	
	const style = computed(():Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('width', `${size.value}px`)
		css.set('height', `${size.value}px`)
		
		css = $ux.Util.xStyle(css, props.margin, props.mt, props.mr, props.mb, props.ml, props.padding, props.pt, props.pr, props.pb, props.pl)
		
		return css
	})
	
	const qrData = computed((): UTSJSONObject => {
		let _size = size.value
		let scale = size.value / _size
		
		return {
			text: props.text,
			size: _size,
			img: props.img,
			imgSize: $ux.Util.getPx(props.imgSize) / scale,
			background: props.background,
			foreground: props.foreground,
			scale: scale
		} as UTSJSONObject
	})
	
	function click(e : MouseEvent) {
		emit('click', e)
	}
	
	function change() {
		callback.value?.cb!(qr.value)
		emit('change', qr.value)
	}
	
	function make(cb ?: (img: string) => void) {
		if(cb != null) {
			callback.value = {
				cb: cb
			} as Callback
		}
		
		if(qr.value != '') {
			callback.value.cb(qr.value)
			return
		}
		
		// #ifdef APP
		ctx.value = uni.createWebviewContext(myId, getCurrentInstance()?.proxy);
		ctx.value!.evalJS(`gen()`);
		// #endif
		
		// #ifdef WEB
		let iframe = document.getElementById(myId) as HTMLElement;
		iframe.contentWindow['gen']();
		// #endif
	}
	
	function gen() {
		isLoading.value = true
		
		// #ifdef APP
		if(ctx.value == null) {
			ctx.value = uni.createWebviewContext(myId, getCurrentInstance()?.proxy);
		} 
		ctx.value?.evalJS(`make(${JSON.stringify(qrData.value)})`);
		// #endif
		
		// #ifdef WEB
		let iframe = document.getElementById(myId) as HTMLElement;
		iframe.contentWindow['make'](qrData.value);
		// #endif
		
		setTimeout(() => {
			make()
		}, 50);
		
		isLoading.value = false
	}
	
	function onMessage(e : WebViewMessageEvent) {
		// #ifdef APP
		const data = e.detail.data[0]
		
		if (data['action'] == 'init') {
			gen()
		} else if(data['action'] == 'error') {
			isLoading.value = false
			console.error('[ux-qr] 加载失败', data['msg']);
			emit('error', data['msg'])
		} else if(data['action'] == 'gen') {
			let url = data['url'] ?? ''
			qr.value = `${url}`
			change()
		};
		// #endif
	}
	
	watch(text, () => {
		gen()
	})
	
	watch(size, () => {
		gen()
	})
	
	watch(img, () => {
		gen()
	})
	
	onMounted(() => {
		// #ifdef WEB
		window.addEventListener('message', (e) => {
			if (e.data.iframeId == myId) {
				if (e.data.action == 'init') {
					gen()
				} else if(e.data.action == 'error') {
					isLoading.value = false
					console.error('[ux-qr] 加载失败', e.data.msg);
					emit('error', e.data.msg)
				} else if (e.data.action == 'gen') {
					qr.value = `${e.data.url}`
					change()
				}
			}
		});
		// #endif
	})
	
	defineExpose({
		make
	})
</script>

<style lang="scss" scoped>
	/* #ifdef H5 */
	uni-view {
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: stretch;
		align-content: stretch;
		justify-content: flex-start;
		box-sizing: border-box;
		min-height: 0px;
		min-width: 0px;
		overflow: hidden;
	}
	/* #endif */
	
	.ux-qr {
		width: 100%;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: white;
		position: relative;

		&__webview {
			position: absolute;
			top: 0;
			width: 100%;
			height: 100%;
		}
		
		&__mask {
			position: absolute;
			top: 0;
			width: 100%;
			height: 100%;
			opacity: 1;
		}
	}
</style>