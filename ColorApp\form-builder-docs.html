<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FormBuilder 组件测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }
        .content {
            padding: 30px;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #333;
            font-size: 20px;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #667eea;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            padding-left: 20px;
            position: relative;
        }
        .feature-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #667eea;
            font-weight: bold;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 15px 0;
        }
        .field-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .field-type {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #667eea;
        }
        .field-type h4 {
            margin: 0 0 8px 0;
            color: #333;
            font-size: 16px;
        }
        .field-type p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
        .demo-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .demo-section h3 {
            margin-top: 0;
            color: #333;
        }
        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .note strong {
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>FormBuilder 动态表单组件</h1>
            <p>基于 uni-app-x 的动态表单渲染解决方案</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>组件概述</h2>
                <p>FormBuilder 是一个强大的动态表单构建器，支持根据配置文件动态渲染各种类型的表单字段。该组件专为 uni-app-x 框架设计，完全兼容其语法规范。</p>
            </div>
            
            <div class="section">
                <h2>主要特性</h2>
                <ul class="feature-list">
                    <li>支持多种字段类型：文本、数字、日期、选择器、开关等</li>
                    <li>完整的表单验证机制：必填、长度、格式、数值范围验证</li>
                    <li>灵活的分组布局：支持字段分组显示</li>
                    <li>响应式设计：适配各种屏幕尺寸</li>
                    <li>数据源支持：静态数据、JSON 数据、动态数据源</li>
                    <li>事件驱动：完整的字段变化和表单提交事件</li>
                    <li>uni-app-x 兼容：完全符合 uni-app-x 语法规范</li>
                </ul>
            </div>
            
            <div class="section">
                <h2>组件架构</h2>
                <div class="field-types">
                    <div class="field-type">
                        <h4>FormBuilder.uvue</h4>
                        <p>主组件，负责整体表单渲染和数据管理</p>
                    </div>
                    <div class="field-type">
                        <h4>FormField.uvue</h4>
                        <p>字段组件，负责单个字段的渲染和验证</p>
                    </div>
                    <div class="field-type">
                        <h4>FormGroup.uvue</h4>
                        <p>分组组件，负责字段分组布局</p>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>支持的字段类型</h2>
                <div class="field-types">
                    <div class="field-type">
                        <h4>文本输入</h4>
                        <p>Text, InputEditor, PhoneFormat, NumberInput</p>
                    </div>
                    <div class="field-type">
                        <h4>文本域</h4>
                        <p>TextArea, SmallEditor</p>
                    </div>
                    <div class="field-type">
                        <h4>选择器</h4>
                        <p>Select, Picker, RadioGroup, CheckboxGroup</p>
                    </div>
                    <div class="field-type">
                        <h4>日期时间</h4>
                        <p>Date, DateTime, DateRange, DateTimeRange</p>
                    </div>
                    <div class="field-type">
                        <h4>开关控件</h4>
                        <p>Switch, Checkbox</p>
                    </div>
                    <div class="field-type">
                        <h4>按钮组</h4>
                        <p>ButtonGroup (渲染为 RadioGroup)</p>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>基本用法</h2>
                <div class="code-block">
&lt;template&gt;
  &lt;FormBuilder
    :fields="formFields"
    v-model="formData"
    :showActions="true"
    :showReset="true"
    @submit="handleSubmit"
    @change="handleChange"
  /&gt;
&lt;/template&gt;

&lt;script setup lang="uts"&gt;
  import FormBuilder from '@/components/FormBuilder.uvue'
  
  const formFields = ref([
    {
      Id: 'name',
      Name: 'name',
      Title: '姓名',
      Type: 'Text',
      Required: true,
      Order: 1
    },
    {
      Id: 'email',
      Name: 'email',
      Title: '邮箱',
      Type: 'InputEditor',
      InputType: 'email',
      Required: true,
      Order: 2
    }
  ])
  
  const formData = ref({})
  
  const handleSubmit = (result) => {
    console.log('表单提交:', result)
  }
  
  const handleChange = (data) => {
    console.log('字段变化:', data)
  }
&lt;/script&gt;
                </div>
            </div>
            
            <div class="section">
                <h2>字段配置示例</h2>
                <div class="code-block">
{
  "Id": "username",
  "Name": "username", 
  "Title": "用户名",
  "Type": "Text",
  "Required": true,
  "MinLength": 3,
  "MaxLength": 20,
  "Placeholder": "请输入用户名",
  "Tooltip": "用户名长度为3-20个字符",
  "Order": 1,
  "Group": "基本信息",
  "Validation": "^[a-zA-Z0-9_]+$",
  "ValidationType": "pattern",
  "ValidMesasge": "用户名只能包含字母、数字和下划线"
}
                </div>
            </div>
            
            <div class="section">
                <h2>数据源配置</h2>
                <div class="code-block">
// 静态数据源
{
  "Type": "Select",
  "DefaultDataSource": "选项1,选项2,选项3"
}

// JSON 数据源
{
  "Type": "Select",
  "DataSourceType": "Json",
  "DataSource": "[{\"id\":\"1\",\"text\":\"选项1\"},{\"id\":\"2\",\"text\":\"选项2\"}]"
}

// 数组数据源
{
  "Type": "Select",
  "DataSource": [
    {"id": "1", "text": "选项1"},
    {"id": "2", "text": "选项2"}
  ]
}
                </div>
            </div>
            
            <div class="note">
                <strong>注意：</strong> 由于 uni-app-x 不支持 component 动态组件，本实现使用 v-if/v-else-if 条件渲染来实现动态字段类型切换。这种方式在性能和兼容性方面都有很好的表现。
            </div>
            
            <div class="demo-section">
                <h3>组件文件位置</h3>
                <ul>
                    <li><strong>FormBuilder.uvue</strong> - /components/FormBuilder.uvue</li>
                    <li><strong>FormField.uvue</strong> - /components/FormField.uvue</li>
                    <li><strong>FormGroup.uvue</strong> - /components/FormGroup.uvue</li>
                </ul>
                
                <h3>使用步骤</h3>
                <ol>
                    <li>将三个组件文件放置到项目的 components 目录下</li>
                    <li>在需要使用的页面中导入 FormBuilder 组件</li>
                    <li>准备字段配置数组</li>
                    <li>绑定表单数据和事件处理函数</li>
                    <li>根据需要自定义样式</li>
                </ol>
            </div>
        </div>
    </div>
</body>
</html>