<!DOCTYPE html>
<html lang="zh">
	<head>
		<meta http-equiv="content-type" content="text/html; charset=UTF-8">
		<meta charset="UTF-8">
		<meta name="viewport"
			content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
		<meta http-equiv="X-UA-Compatible" content="ie=edge">
		<title></title>
		<style type="text/css">
			html,
			body,
			canvas {
				padding: 0;
				margin: 0;
				overflow-y: hidden;
				background-color: transparent;
				width: 100%;
				height: 100%;
			}
		</style>
		<script src="./js/uni.webview.1.5.5.js"></script>
	</head>
	<body>
		<canvas id="tabbar" style="width:100%;height: 100%;"></canvas>
		
		<script type="text/javascript">

			function resetDraw() {
				const canvas = document.getElementById('tabbar')
				const _ctx = canvas.getContext('2d')
				
				_ctx.clearRect(0, 0, document.documentElement.clientWidth, document.documentElement.clientHeight)
			}
			
			function draw1({dpr, safeAreaHeight, radius, corner, backgroundColor, borderColor}) {
				const canvas = document.getElementById('tabbar')
				const _ctx = canvas.getContext('2d')
				
				canvas.width = document.documentElement.clientWidth * dpr
				canvas.height = document.documentElement.clientHeight * dpr
				_ctx.scale(dpr, dpr)
				
				const h = safeAreaHeight + 54
				const sw = document.documentElement.clientWidth
				let width = radius + 10
				let height = radius + 10
				let startX = sw / 2 - width
				let offset = 20
				
				// 开始绘制
				_ctx.beginPath()
				
				if (corner > 0) {
					// 圆角
				
					// 起点
					let lc_s1 = 0
					let lc_s2 = corner * 2
				
					// 控制点1
					let lc_c1_1 = 0
					let lc_c1_2 = 0
				
					// 控制点2
					let lc_c2_1 = 0
					let lc_c2_2 = 0
				
					// 终点
					let lc_e1 = corner * 2
					let lc_e2 = 0
				
					_ctx.moveTo(lc_s1, lc_s2)
					_ctx.bezierCurveTo(lc_c1_1, lc_c1_2, lc_c2_1, lc_c2_2, lc_e1, lc_e2)
				} else {
					// 原点开始
					_ctx.moveTo(0, 0)
				}
				
				// 左边弧
				// 起点
				let l_s1 = startX - offset
				let l_s2 = 0
				
				// 控制点1
				let l_c1_1 = startX
				let l_c1_2 = l_s2
				
				// 控制点2
				let l_c2_x = width * (1 / 5)
				let l_c2_y = height
				let l_c2_1 = startX + l_c2_x
				let l_c2_2 = l_s2 + l_c2_y
				
				// 终点
				let l_e1 = startX + width
				let l_e2 = height
				
				_ctx.lineTo(l_s1, l_s2)
				_ctx.bezierCurveTo(l_c1_1, l_c1_2, l_c2_1, l_c2_2, l_e1, l_e2)
				
				// 右边弧
				// 起点是上一个终点
				
				// 控制点1
				let r_c1_1 = startX + width * 2 - l_c2_x
				let r_c1_2 = l_c2_2
				
				// 控制点2
				let r_c2_1 = startX + width * 2
				let r_c2_2 = l_c1_2
				
				// 终点
				let r_e1 = startX + width * 2 + offset
				let r_e2 = 0
				
				_ctx.bezierCurveTo(r_c1_1, r_c1_2, r_c2_1, r_c2_2, r_e1, r_e2)
				
				// 向右画线
				if (corner > 0) {
					// 圆角
					// 起点
					let lc_s1 = sw - corner
					let lc_s2 = 0
				
					// 控制点1
					let lc_c1_1 = sw
					let lc_c1_2 = 0
				
					// 控制点2
					let lc_c2_1 = sw
					let lc_c2_2 = 0
				
					// 终点
					let lc_e1 = sw
					let lc_e2 = corner
				
					_ctx.bezierCurveTo(lc_c1_1, lc_c1_2, lc_c2_1, lc_c2_2, lc_e1, lc_e2)
				} else {
					_ctx.lineTo(sw, 0)
				}
				
				// 向下画线
				_ctx.lineTo(sw, h)
				
				// 向左画线
				_ctx.lineTo(0, h)
				
				// 回到原点
				if (corner > 0) {
					// 圆角
					_ctx.lineTo(0, corner * 2)
				} else {
					_ctx.lineTo(0, 0)
				}
				
				_ctx.closePath()
				
				_ctx.fillStyle = backgroundColor
				_ctx.fill()
				
				// 上边框
				if (this.border) {
					// 开始绘制
					_ctx.beginPath()
				
					if (corner > 0) {
						// 圆角
				
						// 起点
						let lc_s1 = 0
						let lc_s2 = corner * 2
				
						// 控制点1
						let lc_c1_1 = 0
						let lc_c1_2 = 0
				
						// 控制点2
						let lc_c2_1 = 0
						let lc_c2_2 = 0
				
						// 终点
						let lc_e1 = corner * 2
						let lc_e2 = 0
				
						_ctx.moveTo(lc_s1, lc_s2)
						_ctx.bezierCurveTo(lc_c1_1, lc_c1_2, lc_c2_1, lc_c2_2, lc_e1, lc_e2)
					} else {
						// 原点开始
						_ctx.moveTo(0, 0)
					}
				
					// 左边弧
					// 起点
					l_s1 = startX - offset
					l_s2 = 0
				
					// 控制点1
					l_c1_1 = startX
					l_c1_2 = l_s2
				
					// 控制点2
					l_c2_x = width * (1 / 5)
					l_c2_y = height
					l_c2_1 = startX + l_c2_x
					l_c2_2 = l_s2 + l_c2_y
				
					// 终点
					l_e1 = startX + width
					l_e2 = height
				
					_ctx.lineTo(l_s1, l_s2)
					_ctx.bezierCurveTo(l_c1_1, l_c1_2, l_c2_1, l_c2_2, l_e1, l_e2)
				
					// 右边弧
					// 起点是上一个终点
				
					// 控制点1
					r_c1_1 = startX + width * 2 - l_c2_x
					r_c1_2 = l_c2_2
				
					// 控制点2
					r_c2_1 = startX + width * 2
					r_c2_2 = l_c1_2
				
					// 终点
					r_e1 = startX + width * 2 + offset
					r_e2 = 0
				
					_ctx.bezierCurveTo(r_c1_1, r_c1_2, r_c2_1, r_c2_2, r_e1, r_e2)
				
					// 向右画线
					if (corner > 0) {
						// 圆角
						// 起点
						let lc_s1 = sw - corner
						let lc_s2 = 0
				
						// 控制点1
						let lc_c1_1 = sw
						let lc_c1_2 = 0
				
						// 控制点2
						let lc_c2_1 = sw
						let lc_c2_2 = 0
				
						// 终点
						let lc_e1 = sw
						let lc_e2 = corner
				
						_ctx.bezierCurveTo(lc_c1_1, lc_c1_2, lc_c2_1, lc_c2_2, lc_e1, lc_e2)
					} else {
						_ctx.lineTo(sw, 0)
					}
					
					_ctx.strokeStyle = borderColor
					_ctx.stroke()
					
					_ctx.fillStyle = backgroundColor
				}
			}
			
			function waterAnim({dpr, size, color, _old, _new, isIos}) {
				const canvas = document.getElementById('tabbar')
				const _ctx = canvas.getContext('2d')
				
				canvas.width = document.documentElement.clientWidth * dpr
				canvas.height = document.documentElement.clientHeight * dpr
				_ctx.scale(dpr, dpr)
				
				// 宽高
				let w = document.documentElement.clientWidth / size
				let width = w * 0.7
				let height = 12
				let _height = height
				
				// 居左
				let left = (w - width) / 2
				
				// 起点
				let startX = w * _old + left
				
				// 终点
				let endX = w * _new + left
				
				// 水滴
				let dropStart = height - 10
				let dropHeight = 35
				let dropEnd = dropStart + dropHeight
				let dropStep = isIos ? 1 : 2
				let droprRadius = 5
				
				// 前进 后退
				let forward = startX < endX
				
				// 步长
				let step = (isIos ? 2 : 4) * Math.abs(_new - _old)
				
				let moveAnim = (_) => {}
				moveAnim = (drop) => {
					// 百分比
					let per = forward ? (startX / endX) : (endX / startX)
					per = per < 0.3 ? 0.3 : (per > 1 ? 1 : per)
				
					_ctx.clearRect(0, 0, document.documentElement.clientWidth, document.documentElement.clientHeight)
				
					// 开始绘制
					_ctx.beginPath()
				
					// 水滴缓冲动效
					if (drop) {
						let dp = 1 - (dropEnd - dropStart) / dropHeight
						height = dp < 0.5 ? _height * (1 + dp) : _height * dp
				
						if (height < _height) {
							height = _height
						}
					}
				
					// 左边弧
					// 起点
					let l_s1 = startX
					let l_s2 = 0
				
					// 控制点1
					let l_c1_x = width / 2 * (2 / 3)
					let l_c1_y = height * (1 / 5)
					let l_c1_1 = l_s1 + l_c1_x
					let l_c1_2 = l_s2 + l_c1_y
				
					// 控制点2
					let l_c2_x = width / 2 * (4 / 5)
					let l_c2_y = height
					let l_c2_1 = l_s1 + l_c2_x
					let l_c2_2 = l_s2 + l_c2_y
				
					// 终点
					let l_e1 = l_s1 + width / 2
					let l_e2 = height
				
					_ctx.moveTo(l_s1, l_s2)
					_ctx.bezierCurveTo(l_c1_1, l_c1_2, l_c2_1, l_c2_2, l_e1, l_e2)
				
					// 右边弧
					// 起点是上一个终点
				
					// 控制点1
					let r_c1_1 = startX + width - l_c2_x
					let r_c1_2 = l_c2_2
				
					// 控制点2
					let r_c2_1 = startX + width - l_c1_x
					let r_c2_2 = l_c1_2
				
					// 终点
					let r_e1 = startX + width
					let r_e2 = 0
				
					_ctx.bezierCurveTo(r_c1_1, r_c1_2, r_c2_1, r_c2_2, r_e1, r_e2)
				
					// 结束绘制
					_ctx.closePath()
				
					// 填充
					_ctx.fillStyle = color
					
					_ctx.fill()
				
					// 水滴动效
					if (drop) {
						let dp = (dropEnd - dropStart) / dropHeight
						dp = dp < 0 ? 0 : dp
				
						let dx = startX + (width / 2) - droprRadius
						let dy = dropStart
						
						if(dy >= dropHeight) {
							droprRadius = 0
						}
				
						// 开始绘制
						_ctx.beginPath()
				
						// 中点
						_ctx.moveTo(dx, dy)
				
						// 水滴
						_ctx.arc(dx + droprRadius, dy + droprRadius, droprRadius, 0, Math.PI * 2, false)
				
						// 结束绘制
						_ctx.closePath()
				
						// 填充
						_ctx.fillStyle = color
						_ctx.fill()
					}
				
					// 模拟动画
					if (forward) {
						if (startX < endX) {
							// 右移
							setTimeout(() => {
								moveAnim(false)
							}, 0)
				
							startX += step
				
							if (endX - startX < step) {
								startX = endX
							}
						} else {
							// 开始水滴动效
							if (dropStart < dropEnd) {
								setTimeout(() => {
									moveAnim(true)
								}, 0)
							}
				
							dropStart += dropStep
						}
					} else {
						if (startX > endX) {
							// 左移
							setTimeout(() => {
								moveAnim(false)
							}, 0)
				
							startX -= step
				
							if (startX - endX < step) {
								startX = endX
							}
						} else {
							// 开始水滴动效
							if (dropStart < dropEnd) {
								setTimeout(() => {
									moveAnim(true)
								}, 0)
							}
				
							dropStart += dropStep
						}
					}
				}
				
				moveAnim(false)
			}
		</script>
	</body>
</html>