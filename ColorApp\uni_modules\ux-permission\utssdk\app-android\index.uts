import { UxOpenAppAuthorizeSettingOptions, OpenAppAuthorizeSetting, UxRequestPermissionTipsOptions, SetRequestPermissionTips, RequestPermissions, UxRequestPermissionsOptions } from '../interface.uts';

/**
 * 权限
 */
import Permission from './Permission.uts'
const permission = new Permission()

/**
 * 设置请求权限的提示
 * @param UxRequestPermissionTipsOptions[] options 参数
 */
export const setRequestPermissionTips : SetRequestPermissionTips = function (options : UxRequestPermissionTipsOptions[]) : void {
	permission.setRequestPermissionTips(options)
}

/**
 * 请求权限
 * @param UxRequestPermissionsOptions options 参数
 */
export const requestPermissions : RequestPermissions = function (options : UxRequestPermissionsOptions) : void {
	permission.requestPermissions(options)
}

/**
 * 打开app权限设置
 * @param UxOpenAppAuthorizeSettingOptions options 参数
 */
export const openAppAuthorizeSetting : OpenAppAuthorizeSetting = function (options : UxOpenAppAuthorizeSettingOptions) : void {
	permission.openAppAuthorizeSetting(options)
}