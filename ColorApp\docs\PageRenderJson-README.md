# PageRender 组件使用说明

## 概述

PageRender 组件是一个基于 JSON 配置的动态页面渲染器，可以根据配置文件动态生成完整的页面布局和功能。它与 DynamicComponent 组件配合使用，实现了完全的配置化页面开发。

## 功能特性

### 1. 页面布局渲染
- 支持基于 JSON 配置的页面布局
- 支持 flex 布局系统
- 支持自定义样式和主题

### 2. 组件动态渲染
- 支持多种基础组件类型
- 支持组件嵌套和层级结构
- 支持组件属性和事件配置

### 3. 数据源管理
- 支持多种数据源类型（API、本地数据等）
- 支持数据绑定和自动加载
- 支持数据源之间的关联

### 4. 事件处理系统
- 支持组件事件配置
- 支持表单验证、API 请求、页面跳转等事件类型
- 支持事件链和条件执行

## 组件结构

### PageRender 组件

主要负责页面级别的渲染和管理：

```typescript
// 组件属性
interface PageRenderProps {
  config: PageConfig | string  // 页面配置对象或配置文件路径
  autoLoad?: boolean          // 是否自动加载配置
}

// 页面配置类型
interface PageConfig {
  version: string            // 配置版本
  pageId: string            // 页面ID
  header?: HeaderConfig     // 页面头部配置
  dataSources?: DataSources // 数据源配置
  router?: RouterConfig     // 路由配置
  pageLayout?: LayoutConfig // 页面布局配置
  components?: ComponentConfig[] // 组件配置数组
}
```

### DynamicComponent 组件

负责具体组件的动态渲染：

```typescript
// 组件属性
interface DynamicComponentProps {
  config: ComponentConfig    // 组件配置
  dataSource?: UTSJSONObject // 数据源
}

// 组件配置类型
interface ComponentConfig {
  id: string                 // 组件ID
  component: string         // 组件类型
  props?: UTSJSONObject     // 组件属性
  children?: ComponentConfig[] // 子组件
  value?: UTSJSONObject | string // 组件值
  dataSource?: string       // 数据源引用
  events?: UTSJSONObject    // 事件配置
}
```

## 支持的组件类型

### 1. 基础组件
- **view**: 容器组件，支持布局和样式
- **text**: 文本组件，支持多语言
- **image**: 图片组件，支持多种显示模式
- **button**: 按钮组件，支持事件处理

### 2. 表单组件
- **input**: 输入框组件，支持数据绑定
- **textarea**: 多行文本输入
- **switch**: 开关组件
- **picker**: 选择器组件

### 3. 布局组件
- **scroll-view**: 滚动视图
- **list-view**: 列表视图
- **flex-layout**: 弹性布局容器

## 配置示例

### 基础页面配置

```json
{
  "version": "1.0",
  "pageId": "loginPage",
  "header": {
    "title": "用户登录",
    "showBackButton": true
  },
  "dataSources": {
    "loginForm": {
      "id": "loginForm",
      "type": "local",
      "data": {
        "username": "",
        "password": ""
      },
      "autoLoad": false
    }
  },
  "pageLayout": {
    "id": "mainLayout",
    "component": "flex-layout",
    "props": {
      "flexDirection": "column",
      "justifyContent": "center",
      "alignItems": "center",
      "class": "bg-primary"
    }
  },
  "components": [
    {
      "id": "formContainer",
      "component": "view",
      "props": {
        "class": "form-container"
      },
      "dataSource": "loginForm",
      "children": [
        {
          "id": "usernameField",
          "component": "input",
          "props": {
            "type": "text",
            "label": "用户名",
            "dataBind": "username",
            "required": true
          }
        },
        {
          "id": "submitBtn",
          "component": "button",
          "value": {
            "zh-CN": "登录",
            "en-US": "Login"
          },
          "events": {
            "click": [
              {
                "type": "validateForm",
                "target": "formContainer"
              },
              {
                "type": "apiRequest",
                "options": {
                  "endpoint": "/api/login",
                  "method": "POST"
                },
                "payload": "loginForm",
                "onSuccess": {
                  "type": "redirect",
                  "path": "/dashboard"
                }
              }
            ]
          }
        }
      ]
    }
  ]
}
```

## 使用方法

### 1. 基本使用

```vue
<template>
  <PageRender 
    :config="pageConfig"
    :auto-load="true"
    @loaded="onPageLoaded"
    @error="onPageError"
    @event="onPageEvent"
  />
</template>

<script setup>
import PageRender from '@/components/PageRender.uvue'

const pageConfig = {
  // 页面配置对象
}

const onPageLoaded = (config) => {
  console.log('页面加载完成:', config)
}

const onPageError = (error) => {
  console.error('页面加载错误:', error)
}

const onPageEvent = (eventData) => {
  console.log('页面事件:', eventData)
}
</script>
```

### 2. 从文件加载配置

```vue
<template>
  <PageRender 
    config="/static/configs/login-page.json"
    :auto-load="true"
  />
</template>
```

### 3. 动态更新配置

```vue
<script setup>
import { ref } from 'vue'
import PageRender from '@/components/PageRender.uvue'

const pageRenderRef = ref()

// 动态加载新配置
const loadNewConfig = async () => {
  await pageRenderRef.value.loadPageConfig(newConfig)
}

// 更新数据源
const updateDataSource = (id, data) => {
  pageRenderRef.value.loadDataSource(id, data)
}
</script>

<template>
  <PageRender 
    ref="pageRenderRef"
    :config="pageConfig"
  />
</template>
```

## 数据源配置

### 1. 本地数据源

```json
{
  "dataSources": {
    "localData": {
      "id": "localData",
      "type": "local",
      "data": {
        "key1": "value1",
        "key2": "value2"
      },
      "autoLoad": false
    }
  }
}
```

### 2. API 数据源

```json
{
  "dataSources": {
    "apiData": {
      "id": "apiData",
      "type": "api",
      "options": {
        "endpoint": "/api/data",
        "method": "GET",
        "params": {}
      },
      "data": {},
      "autoLoad": true
    }
  }
}
```

## 事件系统

### 支持的事件类型

1. **validateForm**: 表单验证
2. **apiRequest**: API 请求
3. **redirect**: 页面跳转
4. **showModal**: 显示模态框
5. **updateData**: 更新数据

### 事件配置示例

```json
{
  "events": {
    "click": [
      {
        "type": "validateForm",
        "target": "formId"
      },
      {
        "type": "apiRequest",
        "options": {
          "endpoint": "/api/submit",
          "method": "POST"
        },
        "payload": "dataSourceId",
        "onSuccess": {
          "type": "redirect",
          "path": "/success"
        },
        "onError": {
          "type": "showModal",
          "message": "提交失败"
        }
      }
    ]
  }
}
```

## 样式系统

### 1. 内置样式类

- `bg-primary`: 主色背景
- `text-white`: 白色文字
- `text-center`: 居中对齐
- `flex-column`: 垂直布局
- `flex-row`: 水平布局
- `justify-center`: 主轴居中
- `align-center`: 交叉轴居中

### 2. 自定义样式

```json
{
  "props": {
    "class": "custom-class",
    "backgroundColor": "#ffffff",
    "borderRadius": "8px",
    "padding": "20px"
  }
}
```

## 最佳实践

### 1. 配置文件组织
- 将复杂的页面配置拆分为多个文件
- 使用模块化的组件配置
- 建立配置文件的版本管理

### 2. 数据源管理
- 合理设计数据源结构
- 避免过度的数据源依赖
- 实现数据源的缓存机制

### 3. 性能优化
- 使用懒加载减少初始加载时间
- 合理使用组件缓存
- 避免过深的组件嵌套

### 4. 错误处理
- 实现完善的错误边界
- 提供友好的错误提示
- 建立错误日志收集机制

## 扩展开发

### 1. 添加新组件类型

在 DynamicComponent 中添加新的组件类型：

```vue
<!-- 新组件类型 -->
<custom-component 
  v-else-if="config.component === 'custom-component'"
  :prop1="config.props?.prop1"
  :prop2="config.props?.prop2"
  @event="handleEvent('event', $event)"
/>
```

### 2. 扩展事件类型

在 PageRender 中添加新的事件处理：

```typescript
const executeEvent = (eventConfig: UTSJSONObject) => {
  const eventType = eventConfig.get('type') as string
  
  switch (eventType) {
    case 'customEvent':
      // 自定义事件处理逻辑
      handleCustomEvent(eventConfig)
      break
    // ... 其他事件类型
  }
}
```

### 3. 自定义数据源

```typescript
const loadDataSource = async (id: string, config: UTSJSONObject) => {
  const type = config.get('type') as string
  
  switch (type) {
    case 'customDataSource':
      // 自定义数据源加载逻辑
      await loadCustomDataSource(id, config)
      break
    // ... 其他数据源类型
  }
}
```

## 注意事项

1. **类型安全**: 确保配置数据的类型正确性
2. **性能考虑**: 避免过度复杂的配置结构
3. **兼容性**: 注意不同平台的组件支持差异
4. **安全性**: 验证外部配置文件的安全性
5. **调试**: 提供充分的调试信息和日志

## 相关文件

- `components/PageRender.uvue` - 页面渲染器组件
- `components/DynamicComponent.uvue` - 动态组件渲染器
- `pages/page-render-demo.uvue` - 使用示例页面
- `docs/page_design/page.json` - 配置示例文件