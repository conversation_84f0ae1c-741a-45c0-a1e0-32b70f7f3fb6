<template>
	<view>
		<slot></slot>
	</view>
</template>

<script lang="uts">
	/**
	* 辉光组件
	* @description 可用于骨架屏辉光效果等场景
	* @demo pages/component/shimmer.uvue
	* @tutorial https://www.uxframe.cn/component/shimmer.html
	* @property {Number} 			duration				Number | 周期 (默认 2000)
	* @property {Number} 			alpha					Number | 透明度 (默认 0.5)
	* <AUTHOR>
	* @date 2025-04-16 12:10:21
	*/
   
	import Shimmer from 'com.facebook.shimmer.Shimmer';
	import ShimmerFrameLayout from 'com.facebook.shimmer.ShimmerFrameLayout';
	import View from 'android.view.View';
	import ValueAnimator from 'android.animation.ValueAnimator';

	export default {
		name: "ux-shimmer",
		data() {
			return {
				shimmer: null as ShimmerFrameLayout | null
			}
		},
		props: {
			duration: {
				type: Number,
				default: 2000
			},
			alpha: {
				type: Number,
				default: 0.5
			},
		},
		expose: ['show', 'hide'],
		methods: {
			show() {
				this.shimmer?.startShimmer()
			},
			hide() {
				this.shimmer?.stopShimmer();
				this.shimmer?.setVisibility(View.GONE)
			}
		},
		NVLoad() : ShimmerFrameLayout {
			this.shimmer = new ShimmerFrameLayout($androidContext!);
			let builder = new Shimmer.AlphaHighlightBuilder()
				.setAutoStart(true)
			    .setDuration(this.duration.toLong())
			    .setRepeatCount(ValueAnimator.INFINITE)
				.setBaseAlpha(this.alpha.toFloat())
			    .build();
			this.shimmer!.setShimmer(builder);
			return this.shimmer!
		},
		NVBeforeUnload() {
			this.hide()
			this.shimmer = null
		},
		NVMeasure(size : UTSSize) : UTSSize {
			return size;
		}
	}
</script>

<style>

</style>