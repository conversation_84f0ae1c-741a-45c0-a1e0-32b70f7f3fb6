<template>
	<ux-drawer ref="uxDrawerRef" direction="top" :height="`${keyboardHeight}px`" :touchable="touchable" :opacity="opacity" :maskClose="maskClose" :border="false" @close="onMaskClose">
		<view style="position: relative;" @click.stop="open()">
			<slot></slot>
		</view>
	
		<template v-slot:drawer>
			<view :id="myId" class="ux-keyboard" :style="style">
				<view class="ux-keyboard__input">
					<text v-if="texts.length == 0 && placeholder != ''" class="ux-keyboard__input--placeholder">{{ placeholder }}</text>
					<view class="ux-keyboard__input--texts" v-for="(text, index) in texts" :key="index" @click="onCursor(index)">
						<text class="ux-keyboard__input--text" :style="inputStyle">{{ toEncrypt(text) }}</text>
						<view v-if="index == cursor" class="ux-keyboard__input--cursor" :style="cursorStyle"></view>
					</view>
					<view v-if="showClose" class="ux-keyboard__close" @click="close()">
						<ux-icon type="closecircle-outline" :color="inputColor" :size="20"></ux-icon>
					</view>
				</view>
				<view v-if="mode == 'number' || mode == 'safety'" class="ux-keyboard__key">
					<view class="ux-keyboard__key--keys">
						<view class="ux-keyboard__key--row" v-for="(rows, row) in numKeys" :key="rows">
							<view class="ux-keyboard__key--item" :style="keyStyle" v-for="(k, index) in rows" :key="index" hover-class="hover" :hover-start-time="0" :hover-stay-time="150" @click="input(k)">
								<text class="ux-keyboard__key--text" :style="keyTextStyle">{{ k }}</text>
							</view>
						</view>
					</view>
					<view class="ux-keyboard__key--btns">
						<view class="ux-keyboard__key--del" :style="keyStyle" hover-class="hover" :hover-start-time="0" :hover-stay-time="150" @click="del()">
							<ux-icon type="delete-outline" :size="26" :color="btnTextColor"></ux-icon>
						</view>
						<view class="ux-keyboard__key--confirm" :style="confirmStyle" hover-class="hover" :hover-start-time="0" :hover-stay-time="150" @click="confirm()">
							<text class="ux-keyboard__key--confirm--text">{{ confirmText }}</text>
						</view>
					</view>
				</view>
				<view v-else-if="mode == 'letter'" class="ux-keyboard__key">
					<view class="ux-keyboard__key--keys">
						<view class="ux-keyboard__key--row" v-for="(rows, row) in letterKeys" :key="rows">
							<view class="ux-keyboard__key--item" :style="[keyStyle, keysStyle(k, row, index)]" v-for="(k, index) in rows" :key="index" hover-class="hover" :hover-start-time="0" :hover-stay-time="150" @click="input(toUpper(k))">
								<ux-icon v-if="k == 'shift'" :type="isShift ? 'to-bottom' : 'to-top'" :size="20" :color="btnTextColor">></ux-icon>
								<ux-icon v-else-if="k == 'del'" type="delete-outline" :size="24" :color="btnTextColor">></ux-icon>
								<text v-else class="ux-keyboard__key--text" :style="[keyTextStyle, keysTextStyle(k)]">{{ k == 'confirm' ? confirmText : (k == 'space' ? spaceText : toUpper(k)) }}</text>
							</view>
						</view>
					</view>
				</view>
				<view v-else-if="mode == 'calculator'" class="ux-keyboard__key">
					<view class="ux-keyboard__key--keys">
						<view class="ux-keyboard__key--row" v-for="(rows, row) in calculatorKeys" :key="rows">
							<view class="ux-keyboard__key--item" :style="[keyStyle, keysStyle(k, row, index)]" v-for="(k, index) in rows" :key="index" hover-class="hover" :hover-start-time="0" :hover-stay-time="150" @click="input(toUpper(k))">
								<ux-icon v-if="k == '%'" type="percentage" :size="20" :color="btnTextColor">></ux-icon>
								<ux-icon v-else-if="k == 'x'" type="close" :size="18" :color="btnTextColor">></ux-icon>
								<text v-else class="ux-keyboard__key--text" :style="[keyTextStyle, keysTextStyle(k)]">{{ k == 'confirm' ? confirmText : (k == 'space' ? spaceText : toUpper(k)) }}</text>
							</view>
						</view>
					</view>
					<view class="ux-keyboard__key--btns">
						<view class="ux-keyboard__key--calc" :style="[keyStyle, calcKeysStyle(k, index)]" v-for="(k, index) in calcSymbolKeys" :key="index" hover-class="hover" :hover-start-time="0" :hover-stay-time="150" @click="input(k == '-' ? 'm' : k)">
							<ux-icon v-if="k == 'del'" type="delete-outline" :size="24" :color="btnTextColor">></ux-icon>
							<ux-icon v-else-if="k == '+'" type="plus" :size="24" :color="btnTextColor">></ux-icon>
							<ux-icon v-else-if="k == '-'" type="minus" :size="24" :color="btnTextColor">></ux-icon>
							<text v-else class="ux-keyboard__key--text" :style="[keyTextStyle, keysTextStyle(k)]">{{ k }}</text>
						</view>
						<view class="ux-keyboard__key--confirm" :style="confirmStyle" hover-class="hover" :hover-start-time="0" :hover-stay-time="150" @click="confirm()">
							<text class="ux-keyboard__key--confirm--text">{{ confirmText }}</text>
						</view>
					</view>
				</view>
				<view v-else-if="mode == 'platenumber'" class="ux-keyboard__key">
					<view class="ux-keyboard__key--keys">
						<view class="ux-keyboard__key--row" v-for="(rows, row) in platenumberKeys" :key="rows">
							<view class="ux-keyboard__key--item" :style="[keyStyle, keysStyle(k, row, index)]" v-for="(k, index) in rows" :key="index" hover-class="hover" :hover-start-time="0" :hover-stay-time="150" @click="input(toUpper(k))">
								<ux-icon v-if="k == 'del'" type="delete-outline" :size="24" :color="btnTextColor">></ux-icon>
								<text v-else class="ux-keyboard__key--text" :style="[keyTextStyle, keysTextStyle(k)]">{{ k == 'confirm' ? confirmText : toUpper(k) }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</template>
	</ux-drawer>
</template>

<script setup lang="ts">
	
	 /**
	 * Keyboard 键盘
	 * @description 支持数字键盘、字母键盘、计算器键盘、密码键盘、安全键盘、车牌键盘
	 * @demo pages/component/keyboard.uvue
	 * @tutorial https://www.uxframe.cn/component/keyboard.html
	 * @property {String}			theme=[text|info|primary|success|warning|error]		String | 主题 (默认 primary)
	 * @value text			文字
	 * @value info   		默认
	 * @value primary   	主要
	 * @value success  		成功
	 * @value warning   	警告
	 * @value error  		错误
	 * @property {String}			mode=[number|letter|calculator|safety|platenumber]		String | 模式 (默认 number)
	 * @value number		数字键盘
	 * @value letter   		英文字母键盘
	 * @value calculator   	计算器键盘
	 * @value safety   		安全键盘
	 * @value platenumber  	车牌键盘
	 * @property {Boolean}			showSub									Boolean | 显示负号 数字键盘有效 (默认 true )
	 * @property {Boolean}			showPoint								Boolean | 显示点 数字键盘有效 (默认 true )
	 * @property {Boolean}			showNum									Boolean | 显示数字键盘 字母键盘有效 (默认 true )
	 * @property {Boolean}			showSymbol								Boolean | 显示标点符号 字母键盘和密码键盘有效 (默认 true )
	 * @property {Boolean}			showSapce								Boolean | 等于false时，空格不起作用，但是占位还在 (默认 true )
	 * @property {Boolean}			encrypt									Boolean | 加密显示 安全键盘有效 (默认 true )
	 * @property {Boolean}			verify									Boolean | 内容合理性校验 (默认 true )
	 * @property {Boolean}			upper									Boolean | 保持字母大写显示 字母键盘有效 (默认 false )
	 * @property {String}			scrollId								String | scoll-view 或 list-view 的Id
	 * @property {String}			adjustPosition							String | 键盘弹起时滚动到目标Id 需配置父级ID scrollId
	 * @property {Number}			maxLength								Number | 输入最大长度 (默认 -1 不限制)
	 * @property {Any}				inputSize								Any | 输入内容大小 (默认 15)
	 * @property {String}			inputColor								String | 输入内容颜色 (默认 #999 )
	 * @property {String} 			inputDarkColor=[none|auto|color]		String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {String}			placeholder								String | 占位内容
	 * @property {Any}				size									Any | 按钮文字大小 (默认 20)
	 * @property {String}			color									String | 按钮文字颜色 (默认 #333 )
	 * @property {String} 			darkColor=[none|auto|color]				String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {String}			btnColor								String | 按钮背景颜色
	 * @property {String} 			btnDarkColor=[none|auto|color]			String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {String}			confirmText								String | 确定按钮文字
	 * @property {String}			confirmColor							String | 确定按钮背景颜色
	 * @property {String} 			confirmDarkColor=[none|auto|color]		String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {String}			spaceText								String | 空格按钮文字
	 * @property {String}			background								String | 背景颜色
	 * @property {String} 			backgroundDark=[none|auto|color]		String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Boolean}			showClose = [true|false]				Boolean | 显示关闭按钮 (默认 true )
	 * @property {Any}				radius									Any | 圆角 (默认 20)
	 * @property {Number}			opacity									Number | 遮罩透明度 0-1 (默认 0.2)
	 * @property {Boolean}			touchable=[true|false]					Boolean | 允许滑动关闭 (默认 false)
	 * @value true	
	 * @value false	
	 * @property {Boolean}			maskClose=[true|false]					Boolean | 遮罩层关闭 (默认 true)
	 * @value true	
	 * @value false	
	 * @property {Boolean}			disabled=[true|false]					Boolean | 是否禁用 (默认 false)
	 * @value true
	 * @value false
	 * @event {Function}			change									Function | 输入时触发
	 * @event {Function}			confirm									Function | 确定时触发
	 * @event {Function}			close									Function | 关闭时触发
	 * @event {Function}			overlimit								Function | 超出字数限制时触发
	 * <AUTHOR>
	 * @date 2024-12-01 23:35:45
	 */
	
	import { ref, computed, watch } from 'vue'
	import { $ux } from '../../index'
	import { useForegroundColor, useFontColor, useThemeColor } from '../../libs/use/style.uts'
	
	defineOptions({
		name: 'ux-keyboard'
	})
	
	const emit = defineEmits(['update:modelValue', 'change', 'confirm', 'close', 'overlimit'])
	
	const props = defineProps({
		modelValue: {
			type: String,
			default: ''
		},
		mode: {
			type: String,
			default: 'number'
		},
		showSub: {
			type: Boolean,
			default: true
		},
		showPoint: {
			type: Boolean,
			default: true
		},
		showNum: {
			type: Boolean,
			default: true
		},
		showSymbol: {
			type: Boolean,
			default: true
		},
		showSapce: {
			type: Boolean,
			default: true
		},
		encrypt: {
			type: Boolean,
			default: true
		},
		verify: {
			type: Boolean,
			default: true
		},
		upper: {
			type: Boolean,
			default: false
		},
		adjustPosition: {
			type: String,
			default: ''
		},
		scrollId: {
			type: String,
			default: ''
		},
		maxLength: {
			type: Number,
			default: -1
		},
		inputSize: {
			default: 15
		},
		inputColor: {
			type: String,
			default: ''
		},
		inputDarkColor: {
			type: String,
			default: ''
		},
		placeholder: {
			type: String,
			default: ''
		},
		size: {
			default: 20
		},
		color: {
			type: String,
			default: '#333'
		},
		darkColor: {
			type: String,
			default: 'auto'
		},
		btnColor: {
			type: String,
			default: '#f6f6f6'
		},
		btnDarkColor: {
			type: String,
			default: '#444'
		},
		confirmText: {
			type: String,
			default: '确定'
		},
		confirmColor: {
			type: String,
			default: ''
		},
		confirmDarkColor: {
			type: String,
			default: ''
		},
		spaceText: {
			type: String,
			default: '空格'
		},
		background: {
			type: String,
			default: ''
		},
		backgroundDark: {
			type: String,
			default: ''
		},
		showClose: {
			type: Boolean,
			default: true
		},
		radius: {
			default: 20
		},
		opacity: {
			type: Number,
			default: 0.2
		},
		touchable: {
			type: Boolean,
			default: false
		},
		maskClose: {
			type: Boolean,
			default: true
		},
		disabled: {
			type: Boolean,
			default: false
		}
	})
	
	const uxDrawerRef = ref<UxDrawerComponentPublicInstance | null>(null)
	const myId = `ux-keyboard-${$ux.Random.uuid()}`
	
	const value = ref('')
	const lastValue = ref('')
	const cursor = ref(0)
	const cursorOpacity = ref(1)
	const isShift = ref(false)
	const isSymbol = ref(false)
	const symbol = ref('')
	
	const texts = computed((): string[] => {
		return value.value.split('')
	})
	
	const nums = ref<string[]>([] as string[])
	
	const numKeys = computed((): string[][] => {
		let _nums = nums.value
		
		let keys = [] as string[][]
		if(nums.value.length == 0) {
			return keys
		}
		
		for (var i = 0; i < _nums.length - 1; i = i+3) {
			keys.push([_nums[i], _nums[i+1], _nums[i+2]])
		}
		
		let point = props.showPoint ? '.' : ''
		
		if(props.mode == 'number' && props.showSub) {
			keys.push(['-', '0', point])
		} else if(props.mode == 'safety') {
			keys.push(['', _nums[_nums.length - 1], point])
		} else {
			keys.push(['00', '0', point])
		}
		
		return keys
	})
	
	const calculatorKeys = computed((): string[][] => {
		return [
			['AC', '%', 'x'],
			['1', '2', '3'],
			['4', '5', '6'],
			['7', '8', '9'],
			['00', '0', '.']
		] as string[][]
	})
	
	const calcSymbolKeys = computed((): string[] => {
		return ['del', '-', '+', '=']
	})
	
	const symbolKeys = computed((): string[][] => {
		let keys = [] as string[][]
		
		if(props.showNum) {
			keys.push(['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'])
		}
		
		keys.push(['+', '-', '*', '/', '%','=', '.', '?'])
		keys.push(['!', '@', '#', '$', '^', '&', '(', ')'])
		keys.push([':', ',', ';', '|', '~', '`', 'del'])
		keys.push(['abc', '_', '[', ']', '{', '}', 'confirm'])
		
		return keys
	})
	
	const letterKeys = computed((): string[][] => {
		if(isSymbol.value) {
			return symbolKeys.value
		}
		
		let keys = [] as string[][]
		
		if(props.showNum) {
			keys.push(['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'])
		}
		
		keys.push(['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p'])
		keys.push(['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l'])
		keys.push(['z', 'x', 'c', 'v', 'b', 'n', 'm', 'del'])
		
		let row = [] as string[]
		
		if(!props.upper) {
			row.push('shift')
		}
		
		if(props.showSymbol) {
			row.push(',.')
		}
		
		row.push('space')
		row.push('confirm')
		
		keys.push(row)
		
		return keys
	})
	
	const hasPlateFirst = computed((): boolean => {
		return texts.value.length > 0
	})
	
	const platenumberKeys = computed((): string[][] => {
		let keys = [] as string[][]
		
		keys.push(['京', '沪', '粤', '津', '冀', '豫', '云', '辽'])
		keys.push(['皖', '鲁', '新', '苏', '浙', '赣', '鄂', '桂'])
		keys.push(['甘', '陕', '吉', '闽', '贵', '渝', '川', '湘'])
		keys.push(['晋', '琼', '宁', '蒙', '黑', '青', '藏', '使'])
		
		if(hasPlateFirst.value) {
			keys = [] as string[][]
			keys.push(['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'])
			keys.push(['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p'])
			keys.push(['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l'])
			keys.push(['z', 'x', 'c', 'v', 'b', 'n', 'm', 'del'])
			keys.push(['港', '澳', '学', '挂', 'confirm'])
		}
		
		return keys
	})
	
	const modelValue = computed((): string => {
		return props.modelValue
	})
	
	const spaceText = computed((): string => {
		return props.showSapce ? props.spaceText : ''
	})
	
	const keyboardHeight = computed((): number => {
		let keyHeight = 50
		let keyBottom = 5
		let inputHeight = 40
		let paddingBottom = 15
		
		let rows = 4
		if(props.mode == 'number' || props.mode == 'safety') {
			rows = numKeys.value.length
		} else if(props.mode == 'letter') {
			rows = letterKeys.value.length
		} else if(props.mode == 'platenumber') {
			rows = platenumberKeys.value.length
		} else if(props.mode == 'calculator') {
			rows = calculatorKeys.value.length
		}
		
		let height = rows * keyHeight + (rows - 1) * keyBottom
		
		return height + inputHeight + paddingBottom
	})
	
	const backgroundColor = computed((): string => {
		return useForegroundColor(props.background, props.backgroundDark)
	})
	
	const inputColor = computed((): string => {
		return useFontColor(props.inputColor, props.inputDarkColor)
	})
	
	const btnColor = computed((): string => {
		return useFontColor(props.btnColor, props.btnDarkColor)
	})
	
	const btnTextColor = computed((): string => {
		return useFontColor(props.color, props.darkColor)
	})
	
	const confirmColor = computed((): string => {
		if(props.confirmColor == '') {
			return useThemeColor('primary')
		} else {
			return useFontColor(props.confirmColor, props.confirmDarkColor)
		}
	})
	
	const style = computed(() => {
		let css = {}
		
		css['height'] = `${keyboardHeight.value}px`
		css['background-color'] = backgroundColor.value
		css['border-top-left-radius'] = $ux.Util.addUnit(props.radius)
		css['border-top-right-radius'] = $ux.Util.addUnit(props.radius)
		
		return css
	})
	
	const cursorStyle = computed(() => {
		let css = {}
		
		css['background-color'] = confirmColor.value
		css['opacity'] = cursorOpacity.value
		
		return css
	})
	
	const inputStyle = computed(() => {
		let css = {}
		
		css['font-size'] = $ux.Util.addUnit(props.inputSize)
		css['color'] = inputColor.value
		
		return css
	})
	
	const keyStyle = computed(() => {
		let css = {}
		
		css['background-color'] = btnColor.value
		
		return css
	})
	
	function keysStyle(key: string, row: number, index: number) {
		let css = {}
		
		if(key == 'space') {
			css['flex'] = 4
		} else if(key == 'confirm') {
			css['flex'] = props.mode == 'platenumber' ? 4 : 2
			css['background-color'] = confirmColor.value
		}
		
		if(props.mode == 'letter') {
			if(index == letterKeys.value[row].length - 1) {
				css['margin-right'] = '0px'
			}
		}
		
		if(props.mode == 'platenumber' && ['港', '澳', '学', '挂'].indexOf(key) != -1) {
			if(texts.value.length < 6
					|| texts.value.indexOf('港') != -1
					|| texts.value.indexOf('澳') != -1
					|| texts.value.indexOf('学') != -1
					|| texts.value.indexOf('挂') != -1) {
				css['opacity'] = 0.6
			}
		}
		
		if(props.mode == 'calculator') {
			if(key == symbol.value) {
				css['background-color'] = '#FFB65D'
			}
		}
		
		return css
	}
	
	function calcKeysStyle(key: string, index: number) {
		let css = {}
		
		if(props.mode == 'calculator') {
			if(key == symbol.value) {
				css['background-color'] = '#FFB65D'
			}
		}
		
		return css
	}
	
	const keyTextStyle = computed(() => {
		let css = {}
		
		css['color'] = btnTextColor.value
		css['font-size'] = $ux.Util.addUnit(props.size)
		
		return css
	})
	
	function keysTextStyle(key: string) {
		let css = {}
		
		if(key == 'confirm') {
			css['color'] = 'white'
			css['font-size'] = '15px'
		} else if(key == 'space') {
			css['font-size'] = '15px'
		}
		
		if(props.mode == 'platenumber') {
			if(texts.value.length == 0 || ['港', '澳', '学', '挂'].indexOf(key) != -1) {
				css['font-size'] = '15px'
			}
		}
		
		if(props.mode == 'calculator') {
			if(key == 'AC') {
				css['font-size'] = '18px'
			} else if(key == '+' || key == '-') {
				css['font-size'] = '24px'
			}
		}
		
		return css
	}
	
	const confirmStyle = computed(() => {
		let css = {}
		
		css['background-color'] = confirmColor.value
		
		return css
	})
	
	function toUpper(key: string): string {
		if(key == 'shift' || key == 'del' || key == 'confirm' || key == 'space' || key == ',.') {
			return key
		}
		
		return isShift.value || props.upper || props.mode == 'platenumber' ? key.toUpperCase() : key
	}
	
	function toEncrypt(key: string): string {
		return props.mode == 'safety' && props.encrypt ? '*' : key
	}
	
	function shuffle() {
		let _nums = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0']
		
		if(props.mode == 'safety') {
			_nums = _nums.sort((a: string, b: string): number => Math.random() - 0.5)
		}
		
		nums.value = _nums
	}
	
	function setCursor(index: number) {
		if(index == 0) {
			cursor.value = texts.value.length - 1
		} else if(index == -1) {
			cursor.value -= 1
		} else if(index == 1) {
			cursor.value += 1
		}
		
		if(cursor.value < 0) {
			cursor.value = 0
		} else if(cursor.value > texts.value.length - 1) {
			cursor.value = texts.value.length - 1
		} 
	}
	
	function onCursor(index: number) {
		cursor.value = index
	}
	
	function showCursor() {
		cursorOpacity.value = 1
		
		setTimeout(() => {
			cursorOpacity.value = 0
			
			setTimeout(() => {
				showCursor()
			}, 500);
		}, 500);
	}
	
	async function scrollTo() {
		if(props.scrollId == '' || props.adjustPosition == '') {
			return
		}
		
		let scrollEle = uni.getElementById(props.scrollId)
		if(scrollEle == null) {
			return
		}
		
		let targetEle = uni.getElementById(props.adjustPosition)
		if(targetEle == null) {
			return
		}
		
		let rect = await targetEle.getBoundingClientRectAsync()
		let top = rect.top
		
		//TODO Bug
		
		// #ifdef APP
		// scrollEle!.setAttribute('scroll-top', `${top}`)
		// #endif
		// #ifndef APP
		// scrollEle!.scrollTop = top
		// #endif
	}
	
	function calc(_symbol: string) {
		if(symbol.value == '' || value.value == '' || lastValue.value == '') {
			return
		}
		
		let v1 = parseFloat(value.value)
		let v2 = parseFloat(lastValue.value)
		
		let v = 0
		if(symbol.value == '%') {
			v = v2 / v1
		} else if(symbol.value == 'x') {
			v = v2 * v1
		} else if(symbol.value == '+') {
			v = v2 + v1
		} else if(symbol.value == '-') {
			v = v2 - v1
		}
		
		value.value = `${v}`
		lastValue.value = ''
		symbol.value = _symbol
		
		setCursor(0)
		
		emit('update:modelValue', value.value)
		emit('change', value.value)
	}
	
	function close() {
		uxDrawerRef.value!.close()
		emit('close')
	}
	
	function onMaskClose() {
		emit('close')
	}
	
	function open() {
		if (props.disabled) {
			return
		}
		
		let f = () => {
			value.value = modelValue.value
			setCursor(0)
			shuffle()
			scrollTo()
			
			uxDrawerRef.value!.open()
			showCursor()
		}
		
		$ux.Util.debounce(f, 250)
	}
	
	function confirm() {
		calc('')
		
		setTimeout(() => {
			emit('confirm', value.value)
			close()
		}, 30);
	}
	
	function check(key: string): boolean {
		if(props.disabled) {
			return false
		}
		
		if(props.verify) {
			if(props.mode == 'number' || props.mode == 'calculator') {
				if(key == '-') {
					if(value.value != '') {
						return false
					}
					
					if(value.value.indexOf('-') != -1) {
						return false
					}
				}
				
				if(key == '.') {
					if(value.value == '') {
						return false
					}
					
					if(value.value.indexOf('.') != -1) {
						return false
					}
					
					if(value.value == '-') {
						return false
					}
				}
			}
			
			if(props.mode == 'calculator') {
				if(texts.value.length == 0 && ['%', 'x', '+', 'm'].indexOf(key) != -1) {
					return false
				}
			}
		}
		
		return true
	}
	
	function del() {
		if(props.disabled) {
			return
		}
		
		if(symbol.value != '' && lastValue.value != '') {
			if(value.value == '') {
				symbol.value = ''
				value.value = lastValue.value
				lastValue.value = ''
			}
		} else {
			symbol.value = ''
		}
		
		let arr = value.value.split('')
		
		if(texts.value.length == 0) {
			return
		}
		
		arr.splice(cursor.value, 1)
		
		value.value = arr.join('')
		setCursor(-1)
		
		emit('update:modelValue', value.value)
		emit('change', value.value)
	}
	
	function clear() {
		if(props.disabled) {
			return
		}
		
		value.value = ''
		lastValue.value = ''
		symbol.value = ''
		setCursor(0)
		
		emit('update:modelValue', value.value)
		emit('change', value.value)
	}
	
	function input(key: string) {
		if(!check(key)) {
			return
		}
		
		if(key == 'AC') {
			clear()
			return
		}
		
		if(key == 'del') {
			del()
			return
		}
		
		if(key == 'confirm') {
			confirm()
			return
		}
		
		if(key == 'shift') {
			isShift.value = !isShift.value
			return
		}
		
		if(key == ',.') {
			isSymbol.value = true
			return
		}
		
		if(key == 'abc') {
			isSymbol.value = false
			return
		}
		
		if(key == 'space') {
			if(props.showSapce) {
				key = ' '
			} else {
				return
			}
		}
		
		if(props.mode == 'platenumber') {
			if(texts.value.length >= 8) {
				return
			}
			
			if(['港', '澳', '学', '挂'].indexOf(key) != -1) {
				if(texts.value.length < 6 
					|| texts.value.indexOf('港') != -1
					|| texts.value.indexOf('澳') != -1
					|| texts.value.indexOf('学') != -1
					|| texts.value.indexOf('挂') != -1) {
					return
				}
			}
		} else {
			if(props.maxLength > -1) {
				if(texts.value.length >= props.maxLength) {
					emit('overlimit', texts.value.length)
					return
				}
			}
		}
		
		
		if(props.mode == 'calculator') {
			if(key == '=') {
				calc('')
				return
			}
			
			if(key == '%') {
				if(lastValue.value != '') {
					calc('%')
				} else {
					symbol.value = symbol.value == key ? '' : key
				}
				
				return
			}
			
			if(key == 'x') {
				if(lastValue.value != '') {
					calc('x')
				} else {
					symbol.value = symbol.value == key ? '' : key
				}
				
				return
			}
			
			if(key == '+') {
				if(lastValue.value != '') {
					calc('+')
				} else {
					symbol.value = symbol.value == key ? '' : key
				}
				
				return
			}
			
			if(key == 'm') {
				if(lastValue.value != '') {
					calc('-')
				} else {
					symbol.value = symbol.value == '-' ? '' : '-'
				}
				
				return
			}
			
			if(symbol.value != '' && lastValue.value == '') {
				lastValue.value = value.value
				value.value = ''
			}
		}
		
		let arr = value.value.split('')
		arr.splice(cursor.value + 1, 0, key)
		
		value.value = arr.join('')
		setCursor(1)
		
		emit('update:modelValue', value.value)
		emit('change', value.value)
	}
	
	defineExpose({
		open,
		close
	})
</script>

<style lang="scss" scoped>
	
	/* #ifdef H5 */
	uni-view {
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: stretch;
		align-content: stretch;
		justify-content: flex-start;
		box-sizing: border-box;
		min-height: 0px;
		min-width: 0px;
		overflow: hidden;
	}
	/* #endif */
	
	.ux-keyboard {
		width: 100%;
		height: 270px;
		padding: 0 15px 15px 15px;
		display: flex;
		flex-direction: column;
		background-color: #e0e0e0;
		
		&__input {
			width: 100%;
			height: 40px;
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
			
			&--placeholder {
				position: absolute;
				font-size: 13px;
				color: #999;
				text-align: center;
			}
			
			&--texts {
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: center;
			}
			
			&--text {
				font-size: 14px;
				min-width: 2px;
			}
			
			&--cursor {
				width: 1.5px;
				border-radius: 2px;
				height: 18px;
				transition-duration: 200ms;
				transition-property: opacity;
			}
		}
		
		&__close {
			/* #ifdef WEB */
			cursor: pointer;
			/* #endif */
			position: absolute;
			top: 0;
			right: 0;
			padding: 10px;
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
		}
		
		&__key {
			flex: 1;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: flex-start;
			box-sizing: border-box;
			
			&--keys {
				flex: 4;
				display: flex;
				flex-direction: column;
			}
			
			&--row {
				margin-bottom: 5px;
				flex: 1;
				display: flex;
				flex-direction: row;
				justify-content: space-between;
			}
			
			&--item {
				/* #ifdef WEB */
				cursor: pointer;
				/* #endif */
				margin-right: 5px;
				flex: 1;
				height: 50px;
				display: flex;
				flex-direction: row;
				justify-content: center;
				align-items: center;
				background-color: white;
				border-radius: 4px;
				box-sizing: border-box;
			}
			
			&--text {
				font-size: 20px;
				font-weight: bold;
			}
			
			&--btns {
				flex: 1;
				height: 100%;
			}
			
			&--calc {
				/* #ifdef WEB */
				cursor: pointer;
				/* #endif */
				margin-bottom: 5px;
				flex: 1;
				display: flex;
				flex-direction: row;
				justify-content: center;
				align-items: center;
				background-color: white;
				border-radius: 4px;
			}
			
			&--del {
				/* #ifdef WEB */
				cursor: pointer;
				/* #endif */
				margin-bottom: 5px;
				flex: 1;
				display: flex;
				flex-direction: row;
				justify-content: center;
				align-items: center;
				background-color: white;
				border-radius: 4px;
			}
			
			&--confirm {
				/* #ifdef WEB */
				cursor: pointer;
				/* #endif */
				flex: 1;
				display: flex;
				flex-direction: row;
				justify-content: center;
				align-items: center;
				border-radius: 4px;
				
				&--text {
					color: white;
					font-size: 15px;
				}
			}
		}
	}
	
	.hover {
		opacity: 0.5;
	}
</style>