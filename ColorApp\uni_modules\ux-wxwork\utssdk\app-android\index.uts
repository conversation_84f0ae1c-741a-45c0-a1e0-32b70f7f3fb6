import { UxRegisterOptions, UxLoginOptions, UxShareOptions } from '../interface.uts'

import { Weixin} from './src/Weixin.uts'

/**
 * 企业微信
 */
let weixin = new Weixin()

/**
* 注册
* @param {UxRegisterOptions} options
* @tutorial https://www.uxframe.cn/api/wxwork.html#register
*/
export const register = function (options : UxRegisterOptions) {
	weixin.register(options)
}

/**
* 登录
* @param {UxLoginOptions} options
* @tutorial https://www.uxframe.cn/api/wxwork.html#login
*/
export const login = function (options : UxLoginOptions) {
	weixin.login(options)
}

/**
* 分享
* @param {UxShareOptions} options
* @tutorial https://www.uxframe.cn/api/wxwork.html#share
*/
export const share = function (options : UxShareOptions) {
	weixin.share(options)
}

/**
* 微信app是否安装
* @tutorial https://www.uxframe.cn/api/wxwork.html#isInstalled
*/
export const isInstalled = function () : boolean {
	return weixin.isInstalled()
}

/**
* 打开微信app
* @tutorial https://www.uxframe.cn/api/wxwork.html#openApp
*/
export const openApp = function () {
	weixin.openApp()
}