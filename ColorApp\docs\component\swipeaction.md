<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/swipeaction?title=Swipeaction"></Mobile>

# Swipeaction
> 组件类型：UxSwipeactionComponentPublicInstance

支持左侧/右侧滑动操作栏，支持独立和同时配置

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| left | Slot |  | 左侧按钮插槽 |
| right | Slot |  | 右侧按钮插槽 |
| [backgroundDark](#backgroundDark) | String | auto | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| threshold | Number | 30 | 临界值 |
| duration | Number | 200 | 过度时间 |
| disabled | Boolean | false | 禁用 |

### [backgroundDark](#backgroundDark)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

