<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/slider?title=Slider"></Mobile>

# Slider
> 组件类型：UxSliderComponentPublicInstance

支持双滑块、竖向滑动，可设置步长和最小值、最大值

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| name | String |  | 标识符，在回调事件中返回 |
| [theme](#theme) | String |  | 主题颜色 |
| [mode](#mode) | String | normal | 模式 |
| [direction](#direction) | String | horizontal | 方向 |
| min | Number | 0 | 最小值 |
| max | Number | 100 | 最大值 |
| step | Number | 1 | 步长，取值必须大于0，并且可被(max-min)整除 |
| value | Number |  | 当前取值 |
| activeColor | String | `$ux.Conf.primaryColor` | 已选择部分的线条颜色 |
| [activeDark](#activeDark) | String | none | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| backgroundColor | String | #e0e0e0 | 背景条颜色 |
| [backgroundDark](#backgroundDark) | String | #a9a9a9 | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| blockSize | Any | 18 | 滑块的大小 |
| blockColor | String | #ffffff | 滑块颜色 |
| [blockDark](#blockDark) | String | none | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| borderColor | String | #e6e6e6 | 滑块边框颜色 |
| showValue | Boolean | false | 是否显示当前value |
| [showMode](#showMode) | String | top | 显示方式 |
| size | Any | 2 | 尺寸 |
| corner | Any | 0 | 圆角仅web支持 |
| unit | String | % | 单位 |
| disabled | Boolean |  | 是否禁用 |
| margin | Array |  | 边距[上右下左][上下左右][上下左右] |
| mt | Any |  | 距上单位px |
| mr | Any |  | 距右单位px |
| mb | Any |  | 距下单位px |
| ml | Any |  | 距左单位px |
| padding | Array |  | 填充[上右下左][上下左右][上下左右] |
| pt | Any |  | 上内边距单位px |
| pr | Any |  | 右内边距单位px |
| pb | Any |  | 下内边距单位px |
| pl | Any |  | 左内边距单位px |

### [theme](#theme)

| 值   | 说明 |
|:------:|:----:|
| primary | 主色
 |
| warning | 警告
 |
| success | 成功
 |
| error | 错误
 |
| info | 文本
 |

### [mode](#mode)

| 值   | 说明 |
|:------:|:----:|
| normal正常
 |  |
| range范围
 |  |

### [direction](#direction)

| 值   | 说明 |
|:------:|:----:|
| horizontal水平
 |  |
| vertical垂直
 |  |

### [activeDark](#activeDark)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [backgroundDark](#backgroundDark)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [blockDark](#blockDark)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [showMode](#showMode)

| 值   | 说明 |
|:------:|:----:|
| up上方跟随
 |  |
| bottom下方跟随
 |  |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| change | 完成一次拖动后触发的事件 |  |
| changing | 拖动过程中触发的事件 |  |
  
  