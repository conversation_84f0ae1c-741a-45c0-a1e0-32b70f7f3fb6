
<p align="center">
    <img alt="logo" src="https://www.uxframe.cn/logo/logo.png" width="120" height="120" style="margin-bottom: 10px;">
</p>
<h3 align="center" style="margin: 30px 0 30px;font-weight: bold;font-size:40px;">UxFrame MQTT SDK 1.0.1</h3>

## 特别说明

如果您已经购买了[UxFrame 低代码高性能UI框架](https://ext.dcloud.net.cn/plugin?id=16148), 则无需再次购买本插件，请点击上方`进入交流群`联系我免费获取离线版插件！

### 使用方法

``` ts

import { UxMqtt, UxMqttOptions, UxMqttConnectOptions, UxMqttSubscribeOptions, UxMqttPublishOptions } from '@/uni_modules/ux-mqtt'

const online = ref(false)
const msgList = ref<string[]>([])

const mqtt = new UxMqtt({
	brokerUrl: 'tcp://localhost:1883',
	clientId: '1',
	connectionTimeout: 1,
} as UxMqttOptions)

function connect() {
	mqtt.connect({
		statusListener: (status: boolean, err: string) => {
			online.value = status
			
			if(err != '') {
				uni.showModal({
					content: err
				})
			}
		},
		messageListener: (topic: string, msg: string) => {
			msgList.value.push(topic + ': ' + msg)
		}
	} as UxMqttConnectOptions)
}

function disconnect() {
	mqtt.disconnect()
}

function subscribe() {
	mqtt.subscribe({
		topic: 'test',
		success: () => {
			console.log('subscribe success');
		},
		fail: (err: string) => {
			console.log(err);
		}
	} as UxMqttSubscribeOptions)
}

function publish() {
	mqtt.publish({
		topic: 'test',
		msg: JSON.stringify({
			name: 'hh',
			value: 1
		})
	})
}

```

## 文档教程 ⬇️

[https://www.uxframe.cn](https://www.uxframe.cn)
