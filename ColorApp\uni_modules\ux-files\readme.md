
<p align="center">
    <img alt="logo" src="https://www.uxframe.cn/logo/logo.png" width="120" height="120" style="margin-bottom: 10px;">
</p>
<h3 align="center" style="margin: 30px 0 30px;font-weight: bold;font-size:40px;">UxFrame 本地文件选择器SDK 1.0.1</h3>

## 特别说明

如果您已经购买了[UxFrame 低代码高性能UI框架](https://ext.dcloud.net.cn/plugin?id=16148), 则无需再次购买本插件，请点击上方`进入交流群`联系我免费获取离线版插件！

## 说明

本插件支持Android、iOS、Web，仅Android支持文件搜索！

### 示例

``` ts

import * as uxFiles from '@/uni_modules/ux-files'
import { UxFile, UxOpenFileManagerOptions, UxSearchFilesOptions } from '@/uni_modules/ux-files'

// 打开文件管理器
uxFiles.openFileManager({
	mimeTypes: ['image'], // 支持类型 'image' | 'video' | 'audio' | 'txt' | 'pdf' | 'doc' | 'xls' | 'ppt' | 'zip' | 'other'
	multiple: true,
	success: (files) => {
		console.log(files)
	}
} as UxOpenFileManagerOptions)

// 搜索文件
uxFiles.searchFiles({
	mimeTypes: ['image'], // 支持类型 'image' | 'video' | 'audio' | 'txt' | 'pdf' | 'doc' | 'xls' | 'ppt' | 'zip' | 'other'
	success: (file) => {
		console.log(file)
	}
} as UxSearchFilesOptions)

// 停止搜索
uxFiles.stopSearch()

```

## 文档教程 ⬇️

[https://www.uxframe.cn](https://www.uxframe.cn)
