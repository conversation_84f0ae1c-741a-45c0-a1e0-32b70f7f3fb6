<template>
	<view class="ux-row" :style="[style, xstyle]" @click="click">
		<slot />
	</view>
</template>

<script setup lang="ts">
	/**
	 * Row 水平布局
	 * @demo pages/component/layout.uvue
	 * @tutorial https://www.uxframe.cn/component/row.html
	 * @property {Boolean}		flex											Boolean | 弹性盒子 (默认 false)
	 * @property {Any}			gutter											Any | 栅格间隔 (默认 0 )
	 * @property {String}		justify = [left|right|center|around|between]	String | 水平排列方式  (默认 left ) 
	 * @value left 左对齐
	 * @value right 右对齐
	 * @value center 居中
	 * @value around 间隔相等
	 * @value between 两端对齐
	 * @property {String}		align = [top|center|bottom|stretch]				String | 垂直对齐方式  (默认 center )
	 * @value top 上对齐
	 * @value center 居中
	 * @value bottom 下对齐
	 * @value stretch 填充
	 * @property {Array}		margin			Array | 边距 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}			mt				Any | 距上 单位px
	 * @property {Any}			mr				Any | 距右 单位px
	 * @property {Any}			mb				Any | 距下 单位px
	 * @property {Any}			ml				Any | 距左 单位px
	 * @property {Array}		padding			Array | 填充 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}			pt				Any | 上内边距 单位px
	 * @property {Any}			pr				Any | 右内边距 单位px
	 * @property {Any}			pb				Any | 下内边距 单位px
	 * @property {Any}			pl				Any | 左内边距 单位px
	 * @property {Array}		xstyle			Array<any> | 自定义样式
	 * @event {Function}		click			Function | 被点击时触发
	 * <AUTHOR>
	 * @date 2024-11-28 15:20:35
	 */
	
	import { ref, computed } from 'vue'
	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'

	defineOptions({
		name: 'ux-row',
		mixins: [xstyleMixin]
	})

	const emit = defineEmits(['click'])

	const props = defineProps({
		flex: {
			type: Boolean,
			default: false
		},
		gutter: {
			default: 0
		},
		justify: {
			type: String,
			default: 'start'
		},
		align: {
			type: String,
			default: 'center'
		},
	})

	const justify = computed((): string => {
		if (props.justify == 'left' || props.justify == 'start') {
			return 'flex-start'
		} else if (props.justify == 'right' || props.justify == 'end') {
			return 'flex-end'
		} else if (props.justify == 'around' || props.justify == 'between') {
			return `space-${props.justify}`
		}
			
		return props.justify
	})
	
	const alignItem = computed((): string => {
		if (props.align == 'top' || props.align == 'start') {
			return 'flex-start'
		} else if (props.align == 'bottom' || props.align == 'end') {
			return 'flex-end'
		} else if (props.align == 'around' || props.align == 'between') {
			return `space-${props.align}`
		}
		
		return props.align
	})
	
	const style = computed(() => {
		let css = {}
		
		if(props.flex) {
			css['flex'] = 1
		}
		
		css['justify-content'] = justify.value
		css['align-items'] = alignItem.value
		
		css = $ux.Util.xStyle(css, props.margin, props.mt, props.mr, props.mb, props.ml, props.padding, props.pt, props.pr, props.pb, props.pl)
		
		let _gutter = $ux.Util.getPx(props.gutter)
		if(_gutter > 0) {
			css['padding-left'] = `${-_gutter/2}px`
			css['padding-right'] = `${-_gutter/2}px`
		}
		
		return css
	})

	function click(e: MouseEvent) {
		emit('click', e)
	}
</script>

<style lang="scss" scoped>
	.ux-row {
		display: flex;
		flex-direction: row;
	}
</style>