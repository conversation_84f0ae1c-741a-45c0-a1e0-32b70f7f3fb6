export default {
	title: 'Uniapp-X Low Code High Performance UI Framework',
	notice: 'Thank you for using and experiencing the UxFrame framework! The latest version is now released, the total number of developed components: {0}, the total number of apis: {1}, the total number of templates: {2}, for more information, please click me for details 😁',
	privacy: {
		title: "用户协议及隐私政策",
		content: "",
		agree: "同意",
		disagree: "不同意",
	},
	setting: {
		title: 'Setting',
		theme: 'Theme',
		darkMode: 'Dark Mode',
		grayMode: 'Gray Mode',
		followSys: 'Following System',
		primaryColor: 'Primary Color',
		selectPrimaryColor: 'Select Primary Color',
		themeChooser: 'Theme Chooser',
		language: 'Language',
		CN: 'CN',
		US: 'EN',
	},
	component: {
		native: 'Native Component',
		camera: 'Camera',
		blur: 'Blur',
		lottie: 'Lottie',
		movable: 'Movable',
		shimmer: 'Shimmer',
		svg: 'SVG',
		sprite: 'Sprite Frame',
		
		basic: 'Basic Component',
		text: 'Text',
		richtext: 'RichText',
		icon: 'Icon',
		image: 'Image',
		button: 'Button',
		cell: 'Cell',
		transition: 'Transition',
		layout: 'Layout',
		
		forms: 'Form Component',
		form: 'Form',
		input: 'Input',
		textarea: 'Textarea',
		keyboard: 'Keyboard',
		colorpicker: 'ColorPicker',
		dropdown: 'Dropdown',
		radio: 'Radio',
		switch: 'Switch',
		slider: 'Slider',
		picker: 'Picker',
		datepicker: 'DatePicker',
		areapicker: 'AeraPicker',
		calendar: 'Calendar',
		rate: 'Rate',
		numberbox: 'NumberBox',
		upload: 'Upload',
		
		prompt: 'Prompt Component',
		modal: 'Modal',
		toast: 'Toast',
		popover: 'Popover',
		swipeaction: 'SwipeAction',
		actionsheet: 'ActionSheet',
		floatbutton: 'FloatButton',
		
		display: 'Display Component',
		echarts: 'ECharts',
		chartBar: 'Bar',
		chartLine: 'Line',
		chartRing: 'Ring',
		tree: 'Tree',
		table: 'Table',
		badge: 'Badge',
		tag: 'Tag',
		loading: 'Loading',
		noticebar: 'NoticeBar',
		countdown: 'CountDown',
		countto: 'CountTo',
		qrcode: 'QrCode',
		watermark: 'Watermark',
		empty: 'Empty',
		avatar: 'Avatar',
		
		layouts: 'Layout Component',
		movableArea: 'Movable Area',
		waterfall: 'Waterfall',
		chatlist: 'Chatlist',
		pages: 'Pages',
		grid: 'Grid',
		card: 'Card',
		scroll: 'ScrollView',
		list: 'ListView',
		virtualList: 'VirtualList',
		drawer: 'Drawer',
		skeleton: 'Skeleton',
		divider: 'Divider',
		collapse: 'Collapse',
		overlay: 'Overlay',
		placeholder: 'Placeholder',
		steps: 'Steps',
		swiper: 'Swiper',
		
		tabs: 'Tab Component',
		navbar: 'NavBar',
		tabbar: 'TabBar',
		tab: 'Tabs',
		anchortabs: 'AnchorTabs',
		sidebar: 'SideBar',
		indexbar: 'IndexBar',
		
		other: 'Other Component',
		guide: 'Guide',
		signature: 'Signature',
		refresher: 'Refresher',
		loadmore: 'Loadmore',
		backtop: 'Backtop',
		cut: 'Cut',
		readmore: 'Readmore'
	},
	api: {
		library: 'Core Library',
		conf: 'Config',
		i18n: 'i18n',
		autocss: 'AutoCSS',
		color: 'Color',
		util: 'Util',
		
		ext: 'Ext Library',
		encrypt: 'Encrypt',
		toolkit: 'Toolkit',
		store: 'Store',
		date: 'Date',
		format: 'Format',
		random: 'Random',
		verify: 'Verify',
		
		device: 'Device',
		soter: 'Soter',
		keyboard: 'Keyboard',
		vibrate: 'Vibrate',
		clipboard: 'Clipboard',
		exit: 'Exit APP',
		call: 'MakePhoneCall',
		permission: 'APP Permission',
		
		network: 'Network',
		networkStatus: 'Network Status',
		mqtt: 'MQTT',
		
		media: 'Media',
		file: 'File',
		
		social: 'Social',
		share: 'Share Width System',
		weixin: 'Weixin SDK',
		wxwork: 'Wxwork SDK',
		douyin: 'Douyin SDK',
		kuaishou: 'Kuaishou SDK',
		
		data: 'Data Store',
		orm: 'ORM Model',
		sqlite: 'SQLite',
		
		other: 'Other API',
		storekit: 'StoreKit',
		setGray: 'Set Gray',
		openUrl: 'Open Url',
		openWeb: 'Open Web',
		notification: 'Notification',
		pinyin: 'To Pinyin',
	},
	template: {
		media: 'Media Template',
		files: 'File Selector',
		
		login: 'Login Template',
		
		other: 'Other Template',
		privacy: 'Privacy',
		upgrade: 'Upgrade',
	},
	ai: {
		
	},
	more: 'More',
	nomore: 'No More ~',
	stayTuned: 'Stay Tuned',
	test: {
		hello: "Hello，UxFrame",
		time: "current time：{time} ",
	},
};