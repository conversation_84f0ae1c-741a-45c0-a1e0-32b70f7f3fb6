<template>
	<view :id="id" class="ux-sticky-section" :style="style">
		<slot></slot>
	</view>
</template>

<script setup lang="ts">
	
	/**
	 * StickySection 吸顶布局容器 仅支持作为ux-scroll、ux-list、ux-indexbar列表组件的子节点
	 * @demo pages/component/sticky.uvue
	 * @tutorial https://www.uxframe.cn/component/sticky.html
	 * @property {String}		type						String | 类型
	 * @property {Number}		index						Number | 下标
	 * @property {String}		id							String | id
	 * @property {Boolean}		pushPinnedHeader			Boolean | sticky-section元素重叠时是否继续上推 (默认 true)
	 * @property {Number[]}		padding						Number[] | 长度为 4 的数组，按 top、right、bottom、left 顺序指定内边距
	 * <AUTHOR>
	 * @date 2024-12-05 15:20:35
	 */
	
	import { ref, computed, PropType, onMounted, inject, watch, provide, onBeforeUnmount } from 'vue'
	import { $ux } from '../../index'
	import { onPageScroll } from '@dcloudio/uni-app'

	defineOptions({
		name: 'ux-sticky-section',
	})

	const props = defineProps({
		type: {
			type: String,
			default: ''
		},
		index: {
			type: Number,
			default: 0
		},
		id: {
			type: String,
			default: ''
		},
		pushPinnedHeader: {
			type: Boolean,
			default: false
		},
		padding: {
			type: Array as PropType<number[]>,
			default: () => [] as number[]
		}
	})
	
	const key = `ux-sticky-section-${props.type}`
	const top = ref(-1)
	const nodes = ref([] as any[]) 
	const scrollY = inject('scrollY') ?? ref(0)
	
	provide('pushPinnedHeader', props.pushPinnedHeader)
	provide('index', props.index)
	provide('scrollTop', top)
	
	watch(scrollY, () => {
		nodes.value.forEach((e, index) => {
			e._.exposed.uptStatus(scrollY.value, index)
		})
	})
	
	const style = computed(() => {
		let css = {}
		
		if(props.padding.length > 0) {
			css['padding'] = props.padding.map(e => $ux.Util.addUnit(e))
		}
		
		return css
	})
	
	function register(child : any, _top: number) {
		const data = uni.getStorageSync(key)
		if(data != '') {
			top.value = data as number
		} else {
			top.value = _top
			uni.setStorageSync(key, _top)
		}
		
		nodes.value.push(child)
	}
	
	onBeforeUnmount(() => {
		uni.removeStorageSync(key)
	})

	defineExpose({
		register
	})
</script>

<style lang="scss" scoped>
	
	.ux-sticky-section {
		position: relative;
	}
</style>