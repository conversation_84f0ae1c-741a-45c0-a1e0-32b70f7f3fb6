<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/numberbox?title=Numberbox"></Mobile>

# Numberbox
> 组件类型：UxNumberboxComponentPublicInstance

支持整数、小数步进，可长按快速步进

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| name | String |  | 标识符，在回调事件中返回 |
| [theme](#theme) | String |  | 主题颜色 |
| color | String | `$ux.Conf.placeholderColor` | 颜色 |
| [darkColor](#darkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| inputColor | String | `$ux.Conf.placeholderColor` | 输入框背景颜色 |
| [inputDarkColor](#inputDarkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| fontColor | String | `$ux.Conf.placeholderColor` | 输入框背景颜色 |
| [fontDarkColor](#fontDarkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| value | Number |  | 值 |
| min | Number | 0 | 最小值 |
| max | Number | 999999999 | 最大值 |
| step | Number | 1 | 步长 |
| [decimal](#decimal) | Boolean |  | 允许输入小数默认false) |
| precision | Number | 1 | 小数点精度 |
| width | Any | 50 | 输入框宽度 |
| [radius](#radius) | Boolean | false | 圆角按钮 |
| [minus](#minus) | Boolean | true | 显示-按钮 |
| [plus](#plus) | Boolean | true | 显示+按钮 |
| [longpress](#longpress) | Boolean | true | 开启长按加减手势 |
| [disablePlus](#disablePlus) | Boolean | false | 禁用+ |
| [disableMinus](#disableMinus) | Boolean | false | 禁用- |
| disabled | Boolean | false | 禁用 |
| margin | Array |  | 边距[上右下左][上下左右][上下左右] |
| mt | Any |  | 距上单位px |
| mr | Any |  | 距右单位px |
| mb | Any |  | 距下单位px |
| ml | Any |  | 距左单位px |
| padding | Array |  | 填充[上右下左][上下左右][上下左右] |
| pt | Any |  | 上内边距单位px |
| pr | Any |  | 右内边距单位px |
| pb | Any |  | 下内边距单位px |
| pl | Any |  | 左内边距单位px |
| xstyle | Array |  | 自定义样式 |

### [theme](#theme)

| 值   | 说明 |
|:------:|:----:|
| primary | 主色
 |
| warning | 警告
 |
| success | 成功
 |
| error | 错误
 |
| info | 文本
 |

### [darkColor](#darkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [inputDarkColor](#inputDarkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [fontDarkColor](#fontDarkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [decimal](#decimal)

| 值   | 说明 |
|:------:|:----:|
| true
 |  |
| false
 |  |

### [radius](#radius)

| 值   | 说明 |
|:------:|:----:|
| true
 |  |
| false
 |  |

### [minus](#minus)

| 值   | 说明 |
|:------:|:----:|
| true
 |  |
| false
 |  |

### [plus](#plus)

| 值   | 说明 |
|:------:|:----:|
| true
 |  |
| false
 |  |

### [longpress](#longpress)

| 值   | 说明 |
|:------:|:----:|
| true
 |  |
| false
 |  |

### [disablePlus](#disablePlus)

| 值   | 说明 |
|:------:|:----:|
| true
 |  |
| false
 |  |

### [disableMinus](#disableMinus)

| 值   | 说明 |
|:------:|:----:|
| true
 |  |
| false
 |  |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| change | 值改变时触发 |  |
  
  