<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/form-item?title=Form-item"></Mobile>

# Form-item
> 组件类型：UxFormItemComponentPublicInstance

支持任何组件，只作为数据校验

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| label | String |  | 表单名称 |
| field | String |  | 表单字段 |
| verify | Boolean | true | 开启校验 |
| required | Boolean | false | 必填项 |
| rules | Array |  | 校验规则 |
| [direction](#direction) | String | horizontal | 布局方向 |
| width | Any | 自适应 | 宽度 |
| size | Any | `$ux.Conf.fontSize` | 字体大小 |
| errorSize | Any | 12 | 错误提示字体大小 |
| color | String | `$ux.Conf.fontColor` | 字体颜色 |
| [darkColor](#darkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| border | Boolean | true | 下边框 |
| padding | Any | 8 | 内边距 |
| vPadding | Any | 12 | 垂直方法内边距 |
| minHeight | Any | 50 | 最小高度 |
| disabled | Boolean | false | 是否禁用 |

### [direction](#direction)

| 值   | 说明 |
|:------:|:----:|
| horizontal水平
 |  |
| vertical垂直
 |  |

### [darkColor](#darkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

