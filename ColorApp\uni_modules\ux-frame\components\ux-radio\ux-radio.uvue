<template>
	<view class="ux-radio" :style="[style, xstyle]" @click="click">
		<template v-if="_mode == 'radio'">
			<view class="ux-radio__icon" :style="radioStyle">
				<view class="ux-radio__checked" :style="checkedStyle" :class="[checked ? 'ux-radio__checked--activted' : '']"></view>
			</view>
			<slot>
				<text :style="[textStyle]">{{ text }}</text>
			</slot>
		</template>
		<template v-else-if="_mode == 'button'">
			<slot>
				<ux-button :theme="checked? _theme : 'info'" :text="text" :size="_size" :corner="_shape == 'circle' ? 50 : 5" :hover="false" :disabled="_disabled"></ux-button>
			</slot>
		</template>
		<template v-else="_mode == 'cell'">
			<ux-cell :title="text" :padding="cellPadding" :title-style="titleStyle" :border="border" :size="_size" :color="_color" :disabled="_disabled">
				<template v-if="showCellLeft" v-slot:left>
					<slot name="left">
						{{text}}
					</slot>
				</template>
				<template v-slot:right>
					<slot>
						<view class="ux-radio__icon" :style="radioStyle">
							<view class="ux-radio__checked" :style="checkedStyle" :class="[checked ? 'ux-radio__checked--activted' : '']"></view>
						</view>
					</slot>
				</template>
			</ux-cell>
		</template>
	</view>
</template>

<script setup>
	/**
	 * Radio 单选
	 * @demo pages/component/radio.uvue
	 * @tutorial https://www.uxframe.cn/component/radio.html
	 * @property {String} 			name												String | 标识符，在回调事件中返回
	 * @property {String} 			theme=[primary|warning|success|error|info]			String | 主题颜色
	 * @value primary 	主色
	 * @value warning 	警告
	 * @value success 	成功
	 * @value error 	错误
	 * @value info 		文本
	 * @property {Boolean}			checked										Boolean | 选择状态 优先级最高
	 * @property {String}			value										String | 值
	 * @property {String}			mode = [radio|button|cell]					String | 单选模式 (默认 radio )
	 * @value radio 单选
	 * @value button 按钮
	 * @value cell 单元格
	 * @property {String}			shape = [circle|square]						String | 形状，mode=cell时无效 (默认 radio )
	 * @value circle 圆形
	 * @value square 方形
	 * @property {Any} 				radius										Any | 直径（默认 17 ）
	 * @property {String} 			text										String | 文案
	 * @property {Any} 				size										Any | 字体大小（默认 $ux.Conf.fontSize ）
	 * @property {String} 			color										String | 文本颜色（默认 $ux.Conf.fontColor ）
	 * @property {String} 			darkColor=[none|auto|color]					String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {String} 			checkedColor								String | 选择颜色（默认 #ffffff ）
	 * @property {String} 			checkedColorDark=[none|auto|color]			String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {String} 			backgroundColor								String | radio背景色（默认 $ux.Conf.backgroundColor ）
	 * @property {String} 			backgroundDark=[none|auto|color]			String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {String} 			activeBackgroundColor						String | radio背景选择颜色（默认 $ux.Conf.primaryColor ）
	 * @property {String} 			activeBackgroundDark=[none|auto|color]		String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {Boolean} 			border=[true|false]							Boolean | 显示cell下划线（默认 true ）
	 * @property {String} 			borderColor									String | radio边框色（默认 $ux.Conf.borderColor ）
	 * @property {String} 			activeBorderColor							String | radio边框选择颜色（默认 $ux.Conf.primaryColor ）
	 * @property {String} 			titleStyle									String | cell 标题样式
	 * @property {String} 			cellPadding									String | cell padding
	 * @property {Boolean} 			readonly=[true|false]						Boolean | 只读（默认 false ）
	 * @property {Boolean} 			disabled=[true|false]						Boolean | 是否禁用（默认 false ）
	 * @property {Array}			margin										Array | 边距 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}				mt											Any | 距上 单位px
	 * @property {Any}				mr											Any | 距右 单位px
	 * @property {Any}				mb											Any | 距下 单位px
	 * @property {Any}				ml											Any | 距左 单位px
	 * @property {Array}			padding										Array | 填充 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}				pt											Any | 上内边距 单位px
	 * @property {Any}				pr											Any | 右内边距 单位px
	 * @property {Any}				pb											Any | 下内边距 单位px
	 * @property {Any}				pl											Any | 左内边距 单位px
	 * @property {Array}			xstyle										Array<any> | 自定义样式
	 * @event {Function}			change										Function | 值改变时触发
	 * <AUTHOR>
	 * @date 2024-01-19 13:21:16
	 */

	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	import { useFontColor, useFontSize, useForegroundColor, useBorderColor, useThemeColor, useDisabledColor, usePlaceholderColor, UxThemeType } from '../../libs/use/style.uts'

	defineOptions({
		name: 'ux-radio',
		mixins: [xstyleMixin]
	})

	const emit = defineEmits(['update:modelValue', 'change'])

	const props = defineProps({
		name: {
			type: String,
			default: ''
		},
		theme: {
			type: String,
			default: "primary"
		},
		checked: {
			type: Boolean,
			default: false
		},
		value: {
			type: String,
			default: ''
		},
		modelValue: {
			type: String,
			default: ''
		},
		mode: {
			type: String,
			default: 'radio'
		},
		shape: {
			type: String,
			default: 'circle'
		},
		radius: {
			default: 17
		},
		text: {
			type: String,
			default: ''
		},
		size: {
			default: 0
		},
		color: {
			type: String,
			default: ''
		},
		darkColor: {
			type: String,
			default: ''
		},
		checkedColor: {
			type: String,
			default: '#ffffff'
		},
		checkedColorDark: {
			type: String,
			default: '#ffffff'
		},
		backgroundColor: {
			type: String,
			default: ''
		},
		backgroundDark: {
			type: String,
			default: ''
		},
		activeBackgroundColor: {
			type: String,
			default: ''
		},
		activeBackgroundDark: {
			type: String,
			default: ''
		},
		border: {
			type: Boolean,
			default: true
		},
		borderColor: {
			type: String,
			default: ''
		},
		activeBorderColor: {
			type: String,
			default: ''
		},
		titleStyle: {
			type: String,
			default: ''
		},
		cellPadding: {
			type: String,
			default: ''
		},
		readonly: {
			type: Boolean,
			default: false
		},
		disabled: {
			type: Boolean,
			default: false
		},
	})

	const showCellLeft = computed(() => {
		let slots = getCurrentInstance()?.slots
		return slots?.['left'] != null
	})

	const checked = ref(false)
	const instance = getCurrentInstance()?.proxy! as ComponentPublicInstance

	const parentData = inject('parentData', ref(new Map<string, any>())) as Ref<Map<string, any>>

	const _checked = computed(() : boolean => {
		return props.checked
	})

	const _mode = computed(() : string => {
		let value = parentData.value.has('mode') ? parentData.value.get('mode') as string : ''
		return value == '' ? props.mode : value
	})

	const _theme = computed(() : string => {
		let value = parentData.value.has('theme') ? parentData.value.get('theme') as string : ''
		return value == '' ? props.theme : value
	})

	const _shape = computed(() : string => {
		let value = parentData.value.has('shape') ? parentData.value.get('shape') as string : ''
		return value == '' ? props.shape : value
	})

	const _size = computed(() : number => {
		let value = parentData.value.has('size') ? parentData.value.get('size') as number : 0
		return value == 0 ? $ux.Util.getPx(props.size) : value
	})

	const _color = computed(() : string => {
		let value = parentData.value.has('color') ? parentData.value.get('color') as string : ''
		return value == '' ? props.color : value
	})

	const _darkColor = computed(() : string => {
		let value = parentData.value.has('darkColor') ? parentData.value.get('darkColor') as string : ''
		return value == '' ? props.darkColor : value
	})

	const _checkedColor = computed(() : string => {
		let value = parentData.value.has('checkedColor') ? parentData.value.get('checkedColor') as string : ''
		return value == '' ? props.checkedColor : value
	})

	const _checkedColorDark = computed(() : string => {
		let value = parentData.value.has('checkedColorDark') ? parentData.value.get('checkedColorDark') as string : ''
		return value == '' ? props.checkedColorDark : value
	})

	const _backgroundColor = computed(() : string => {
		let value = parentData.value.has('backgroundColor') ? parentData.value.get('backgroundColor') as string : ''
		return value == '' ? props.backgroundColor : value
	})

	const _backgroundDark = computed(() : string => {
		let value = parentData.value.has('backgroundDark') ? parentData.value.get('backgroundDark') as string : ''
		return value == '' ? props.backgroundDark : value
	})

	const _activeBackgroundColor = computed(() : string => {
		let value = parentData.value.has('activeBackgroundColor') ? parentData.value.get('activeBackgroundColor') as string : ''
		return value == '' ? props.activeBackgroundColor : value
	})

	const _activeBackgroundDark = computed(() : string => {
		let value = parentData.value.has('activeBackgroundDark') ? parentData.value.get('activeBackgroundDark') as string : ''
		return value == '' ? props.activeBackgroundDark : value
	})

	const _borderColor = computed(() : string => {
		let value = parentData.value.has('borderColor') ? parentData.value.get('borderColor') as string : ''
		return value == '' ? props.borderColor : value
	})

	const _activeBorderColor = computed(() : string => {
		let value = parentData.value.has('activeBorderColor') ? parentData.value.get('activeBorderColor') as string : ''
		return value == '' ? props.activeBorderColor : value
	})

	const _inverse = computed(() : boolean => {
		return parentData.value.has('inverse') ? parentData.value.get('inverse') as boolean : true
	})

	const _disabled = computed(() : boolean => {
		let value = parentData.value.has('disabled') ? parentData.value.get('disabled') as boolean : false
		return value == false ? props.disabled : value
	})

	const _readonly = computed(() : boolean => {
		let value = parentData.value.has('readonly') ? parentData.value.get('readonly') as boolean : false
		return value == false ? props.readonly : value
	})

	const fontSize = computed(() : number => {
		return useFontSize(_size.value, 1)
	})

	const fontColor = computed(() : string => {
		if (_color.value != '' || _theme.value == '') {
			return useFontColor(_color.value, _darkColor.value)
		} else {
			return useThemeColor(_theme.value as UxThemeType)
		}
	})

	const checkedColor = computed(() : string => {
		return useForegroundColor(_checkedColor.value, _checkedColorDark.value)
	})

	const backgroundColor = computed(() : string => {
		return useForegroundColor(_backgroundColor.value, _backgroundDark.value)
	})

	const activeBackgroundColor = computed(() : string => {
		if (_activeBackgroundColor.value != '' || _theme.value == '') {
			return useForegroundColor(_activeBackgroundColor.value, _activeBackgroundDark.value)
		} else {
			return useThemeColor(_theme.value as UxThemeType)
		}
	})

	const borderdColor = computed(() : string => {
		if (_borderColor.value != '' || _theme.value == '') {
			return useBorderColor(_borderColor.value, '')
		} else {
			return useThemeColor(_theme.value as UxThemeType)
		}
	})

	const activeBorderdColor = computed(() : string => {
		if (_activeBorderColor.value != '' || _theme.value == '') {
			return useBorderColor(_activeBorderColor.value, '')
		} else {
			return useThemeColor(_theme.value as UxThemeType)
		}
	})

	const disabledColor = computed(() : string => {
		return useDisabledColor(fontColor.value)
	})

	const style = computed(() : Map<string, any> => {
		let css = new Map<string, any>()

		if (_mode.value == 'radio') {
			css.set('flex-direction', 'row')
			css.set('align-items', 'center')
		}

		// #ifdef WEB
		css.set('cursor', props.disabled ? 'not-allowed' : 'pointer')
		// #endif
		
		css = $ux.Util.xStyle(css, props.margin, props.mt, props.mr, props.mb, props.ml, props.padding, props.pt, props.pr, props.pb, props.pl)

		return css
	})

	const radioStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()

		css.set('width', $ux.Util.addUnit(props.radius))
		css.set('height', $ux.Util.addUnit(props.radius))

		if (_shape.value == 'circle') {
			css.set('border-radius', '100px')
		} else {
			css.set('border-radius', '4px')
		}

		if (checked.value) {
			css.set('background-color', _disabled.value ? useDisabledColor(activeBackgroundColor.value) : activeBackgroundColor.value)
			css.set('border-color', _disabled.value ? useDisabledColor(activeBorderdColor.value) : activeBorderdColor.value)
		} else {
			css.set('background-color', _disabled.value ? useDisabledColor(backgroundColor.value) : backgroundColor.value)
			css.set('border-color', _disabled.value ? useDisabledColor(borderdColor.value) : borderdColor.value)
		}

		return css
	})

	const checkedStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()

		css.set('border-right', `1.5px ${checkedColor.value} solid`)
		css.set('border-bottom', `1.5px ${checkedColor.value} solid`)

		return css
	})

	const textStyle = computed(() : Map<string, any> => {
		let css = new Map<string, any>()

		css.set('color', _disabled.value ? disabledColor.value : fontColor.value)
		css.set('font-size', $ux.Util.addUnit(fontSize.value))
		css.set('margin-left', $ux.Util.addUnit(5))

		return css
	})

	const modelValue = computed(() : string => {
		return props.modelValue
	})

	watch(modelValue, () => {
		checked.value = props.value == modelValue.value
	}, { immediate: true })

	watch(_checked, () => {
		checked.value = _checked.value
	}, { immediate: true })

	watch(parentData, () => {
		if(parentData.value.has('value')) {
			checked.value = props.value == parentData.value.get('value')
		}
	}, { immediate: true })

	onMounted(() => {
		$ux.Util.$dispatch(instance, 'ux-radio-group', 'register', instance)
	})

	function getValue() : string {
		return checked.value ? props.value : ''
	}

	function click() {
		if (!_disabled.value && !_readonly.value) {
			if (checked.value) {
				if (_inverse.value) {
					checked.value = false
				} else {
					return
				}
			} else {
				checked.value = true
			}

			// 自身事件
			emit('update:modelValue', getValue())
			emit('change', getValue())

			// 群组事件
			$ux.Util.$dispatch(instance, 'ux-radio-group', 'closeAll', instance, getValue())
		}
	}

	defineExpose({
		getValue
	})
</script>

<style lang="scss">
	.ux-radio {
		display: flex;

		&__icon {
			width: 17px;
			height: 17px;
			border-radius: 17px;
			border-style: solid;
			border-width: 1px;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		&__checked {
			width: 4px;
			height: 8px;
			opacity: 0;
			transform: rotate(45deg);
			border-right: 1.5px #fff solid;
			border-bottom: 1.5px #fff solid;
			margin-top: -3px;
			transition-property: opacity;
			transition-duration: 0.2s;

			&--activted {
				opacity: 1;
			}
		}
	}
</style>