<template>
	<view class="main">
		<view class="header">
			<text class="title">ColorApps - uni-app x</text>
		</view>
		
		<!-- #ifdef APP -->
		<scroll-view class="content" scroll-y="true">
		<!-- #endif -->
			<view class="demo-list">
				<view class="demo-item" @tap="goToPageRenderDemo">
				<view class="demo-content">
					<text class="demo-title">🚀 PageRender 组件</text>
					<text class="demo-desc">基于 JSON 配置的动态页面渲染器</text>
					<view class="demo-tags">
						<text class="demo-tag">动态渲染</text>
						<text class="demo-tag">JSON配置</text>
					</view>
				</view>
				<text class="demo-arrow">></text>
			</view>
			
			<view class="demo-item" @tap="goToPageRenderTest">
				<view class="demo-content">
					<text class="demo-title">🧪 PageRender 测试</text>
					<text class="demo-desc">测试 PageRender 组件的基础功能和错误修复</text>
					<view class="demo-tags">
						<text class="demo-tag">测试</text>
						<text class="demo-tag">调试</text>
					</view>
				</view>
				<text class="demo-arrow">></text>
			</view>
			
			<view class="demo-item" @tap="goToFormBuilderDemo">
				<view class="demo-content">
					<text class="demo-title">📝 动态表单构建器</text>
					<text class="demo-desc">基于 FieldConfig 配置的动态表单渲染器</text>
					<view class="demo-tags">
						<text class="demo-tag">动态表单</text>
						<text class="demo-tag">字段配置</text>
						<text class="demo-tag">表单验证</text>
					</view>
				</view>
				<text class="demo-arrow">></text>
			</view>
				
				<view class="demo-item" @tap="goToMpHtmlDemo">
					<view class="demo-content">
						<text class="demo-title">📄 mp-html 组件</text>
						<text class="demo-desc">HTML解析与原生渲染组件</text>
					</view>
					<text class="demo-arrow">></text>
				</view>
				
				<view class="demo-item" @tap="goToComponentTest">
					<view class="demo-content">
						<text class="demo-title">🧪 组件动态测试</text>
						<text class="demo-desc">动态加载和测试项目中的组件</text>
					</view>
					<text class="demo-arrow">></text>
				</view>
				
				<view class="demo-item">
					<view class="demo-content">
						<text class="demo-title">🔮 更多组件</text>
						<text class="demo-desc">敬请期待...</text>
					</view>
					<text class="demo-arrow">></text>
				</view>
			</view>
		<!-- #ifdef APP -->
		</scroll-view>
		<!-- #endif -->
	</view>
</template>

<script setup lang="uts">
	function goToPageRenderDemo() {
		uni.navigateTo({
			url: '/pages/page-render-demo'
		})
	}
	
	function goToMpHtmlDemo() {
		uni.navigateTo({
			url: '/pages/mp-html-demo'
		})
	}
	
	function goToComponentTest() {
		uni.navigateTo({
			url: '/pages/component-test'
		})
	}
	
	function goToPageRenderTest() {
		uni.navigateTo({
			url: '/pages/page-render-test'
		})
	}
	
	function goToFormBuilderDemo() {
		uni.navigateTo({
			url: '/pages/form-builder-demo'
		})
	}
	
	onLoad(() => {
		console.log('首页加载完成')
	})
</script>

<style lang="scss">
	.main {
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		background-color: #f8f8f8;
	}
	
	.header {
		padding: 20px;
		background-color: #007AFF;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.title {
		color: white;
		font-size: 18px;
		font-weight: bold;
	}
	
	.content {
		flex: 1;
		padding: 20px;
	}
	
	.demo-list {
		display: flex;
		flex-direction: column;
		gap: 15px;
	}
	
	.demo-item {
		background-color: white;
		padding: 20px;
		border-radius: 12px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
		display: flex;
		flex-direction: row;
		align-items: center;
		cursor: pointer;
		transition: all 0.3s ease;
		border: 1px solid #f0f0f0;
	}
	
	.demo-item:active {
		background-color: #f8f9ff;
		transform: scale(0.98);
		box-shadow: 0 4px 12px rgba(0, 122, 255, 0.15);
	}
	
	.demo-content {
		flex: 1;
		display: flex;
		flex-direction: column;
		gap: 6px;
	}
	
	.demo-title {
		font-size: 16px;
		font-weight: bold;
		color: #333;
		line-height: 1.4;
	}
	
	.demo-desc {
		font-size: 14px;
		color: #666;
		line-height: 1.4;
	}
	
	.demo-tags {
		display: flex;
		flex-direction: row;
		gap: 8px;
		margin-top: 4px;
	}
	
	.demo-tag {
		background-color: #e8f4fd;
		color: #007AFF;
		font-size: 12px;
		padding: 4px 8px;
		border-radius: 12px;
		border: 1px solid #d1e9ff;
	}
	
	.demo-arrow {
		font-size: 18px;
		color: #007AFF;
		margin-left: 15px;
		font-weight: bold;
	}
</style>