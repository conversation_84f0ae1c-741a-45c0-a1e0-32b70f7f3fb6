<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/unimoduleUxSvg-Swift.h</key>
		<data>
		iuLdQz0uisFzUd26GYddxhWc/lc=
		</data>
		<key>Headers/unimoduleUxSvg.h</key>
		<data>
		FH2SRnQAHIO8AbDZPa5/Bz/CcdA=
		</data>
		<key>Info.plist</key>
		<data>
		tITodHoDL3/OWKKHZXBLOCSPag8=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		n5XmLvhy7xocXpEJCzOsFGFZ8j4=
		</data>
		<key>Modules/unimoduleUxSvg.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		LHo2x67jySd/JpArHy8gZE55zo8=
		</data>
		<key>Modules/unimoduleUxSvg.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		NsPOrijXQF5fn1hsz12nZR38Mtg=
		</data>
		<key>Modules/unimoduleUxSvg.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		gGOhu5WcV6Sftf4aZVK/ZxDFM3s=
		</data>
		<key>Modules/unimoduleUxSvg.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		Snb8xK7KZewYnaHv0piepl4iD6g=
		</data>
		<key>Modules/unimoduleUxSvg.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		gGOhu5WcV6Sftf4aZVK/ZxDFM3s=
		</data>
		<key>Modules/unimoduleUxSvg.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		yQ9WE+WXVsZMkoEklNF6DcHThv0=
		</data>
		<key>config.json</key>
		<data>
		xMhRlpkbZMEKsRtc9xOfdY9N1bw=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/unimoduleUxSvg-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			iuLdQz0uisFzUd26GYddxhWc/lc=
			</data>
			<key>hash2</key>
			<data>
			ALcWsDTPhp9h4MF3oEGP+F+kA+rnZDpbM+Nin70rQu8=
			</data>
		</dict>
		<key>Headers/unimoduleUxSvg.h</key>
		<dict>
			<key>hash</key>
			<data>
			FH2SRnQAHIO8AbDZPa5/Bz/CcdA=
			</data>
			<key>hash2</key>
			<data>
			XAsze1m7Z4PhyI2gj+RafZVS4xyqWjgvqraTawdVPMY=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			n5XmLvhy7xocXpEJCzOsFGFZ8j4=
			</data>
			<key>hash2</key>
			<data>
			+4OvazhN6H8yjmPLkrnoRHGaOyGculfzqpBqiMeaw9Q=
			</data>
		</dict>
		<key>Modules/unimoduleUxSvg.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			LHo2x67jySd/JpArHy8gZE55zo8=
			</data>
			<key>hash2</key>
			<data>
			t8KjixNq/eKf5J4NKg6xFAQSaCbemGcUN/HtfJVdFAw=
			</data>
		</dict>
		<key>Modules/unimoduleUxSvg.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			NsPOrijXQF5fn1hsz12nZR38Mtg=
			</data>
			<key>hash2</key>
			<data>
			PBWfw8/FKdSNGx6k3efIMRCoBO6e3Q9u0NuG37vkHqA=
			</data>
		</dict>
		<key>Modules/unimoduleUxSvg.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			gGOhu5WcV6Sftf4aZVK/ZxDFM3s=
			</data>
			<key>hash2</key>
			<data>
			3e7bJ2dljd3dsZrB90yNzHvg++ybzqCNIeGuoNJp1oI=
			</data>
		</dict>
		<key>Modules/unimoduleUxSvg.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			Snb8xK7KZewYnaHv0piepl4iD6g=
			</data>
			<key>hash2</key>
			<data>
			0V4XdsirtH9h0H3vktXG9g4Py9caQIFTJf/Mo2NWFSg=
			</data>
		</dict>
		<key>Modules/unimoduleUxSvg.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			gGOhu5WcV6Sftf4aZVK/ZxDFM3s=
			</data>
			<key>hash2</key>
			<data>
			3e7bJ2dljd3dsZrB90yNzHvg++ybzqCNIeGuoNJp1oI=
			</data>
		</dict>
		<key>Modules/unimoduleUxSvg.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			yQ9WE+WXVsZMkoEklNF6DcHThv0=
			</data>
			<key>hash2</key>
			<data>
			1IGmRexOVy1vYGgrZlv4eg3ICMRpLolfgAyFS13QFZc=
			</data>
		</dict>
		<key>config.json</key>
		<dict>
			<key>hash</key>
			<data>
			xMhRlpkbZMEKsRtc9xOfdY9N1bw=
			</data>
			<key>hash2</key>
			<data>
			PWRbOqNXvki/lpReimDNBpIwmJXrdGRf1WuzKyrZxfU=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
