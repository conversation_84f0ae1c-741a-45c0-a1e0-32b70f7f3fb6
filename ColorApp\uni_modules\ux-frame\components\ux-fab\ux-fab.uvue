<template>
	<view>

		<view :class="fabdirtn" v-for="item in items" :id="item.id">
			<text class="ux-mt ux-icon ux-color-blue4">{{item.icon}}</text>
		</view>

		<ux-image @tap="expand" :bg-class="fabtn" :width="100" :height="100" border-radius="110rpx"
			src="https://plus.unsplash.com/premium_photo-1708983589793-56673027592e?w=600"></ux-image>
	</view>
</template>

<script>
	type fablist = {
		id : String,
		icon : String
	}
	export default {
		name: "ux-fab",
		props: {
			items: {
				type: Array as PropType<Array<fablist>>,
				default: function () : Array<fablist> {
					return [{
						id: 'fab1',
						icon: "\ue613"
					}, {
						id: 'fab2',
						icon: "\ue6ad"
					}, {
						id: 'fab3',
						icon: "\ue666"
					}] as Array<fablist>;
				}

			},
			xp: { type: String, default: 'right' },
			yp: { type: String, default: 'bottom' },
			data: { type: Array, default: null },
			borderColor: { type: String, default: 'rgba(255,255,255,0)' },
		},
		data() {
			return {

				fabdirtn: "fabdir   ",
				fabtn: "fab  fabbutton",
				isextend: false,
				x: 0,
				y: 0
			}
		},
		mounted() {
			let obj = this;
			let query = uni.createSelectorQuery().in(this) as SelectorQuery;
			query.select('.fabbutton').boundingClientRect().exec((rets : Any) => {
				let node = rets as Array<NodeInfo>;
				console.log(node)
				obj.x = node[0].right as Number;
				obj.y = node[0].bottom as Number
			});
			this.fabtn += " fab-" + this.xp + this.yp;
			this.fabdirtn += "  fab-" + this.xp + this.yp;
			//	console.log(this.fabtn)


		},
		methods: {
			expand: function (event : UniPointerEvent) {
				let px = 0 as Number;
				let py = 0 as Number;
				let angle_step = 45;
				let angle_start = 0;
				let angle = 0;
				let radius =100;
				for (let i = 0; i < this.items.length; i++) {
					 
					 
					angle =angle_start + i * angle_step * (Math.PI / 180)  ;
					px = radius  * Math.cos(angle);
					py =radius * Math.sin(angle) ;
					var id = this.items[i].id;
					var fab1 = uni.getElementById(id) as UniElement;

					fab1?.style?.setProperty('transition-duration', "2014ms");
					fab1?.style?.setProperty('transition-property', "transform");
					if (this.isextend) {

						fab1?.style?.setProperty('transform', "");


					}
					else {

						fab1?.style?.setProperty('transform', " translate(-" + px + "px, -" + py + "px)");
					}
				}
				this.isextend = !this.isextend;
			}

		}
	}
</script>

<style lang="scss">
	.fabdir {
		width: 100rpx;
		background-color: #eee;
		display: flex;
		align-items: center;
		height: 100rpx;
		borderRadius: 110rpx;
		position: fixed;
		transition-property: transform;
		transition-duration: 2000;


	}

	.fabicon {
		font-size: 36px;
	}

	.fab {
		/* visibility: hidden; */

		position: fixed;
		z-index: 888;
	}

	.fab-rightbottom {
		right: 50px;
		bottom: 50px;
	}

	.fab-righttop {
		right: 50px;
		top: 50px;
	}

	.fab-lefttop {
		left: 50px;
		top: 50px;
	}

	.fab-leftbottom {
		left: 50px;
		bottom: 50px;
	}

	.ux-tag {
		text-align: center;
	}
</style>