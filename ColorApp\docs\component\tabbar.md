<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/tabbar?title=Tabbar"></Mobile>

# Tabbar
> 组件类型：UxTabbarComponentPublicInstance

支持正常形状和凹形，支持上下滚动、左右推压、水滴动画效果

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| center | Slot |  | 中间凸起按钮插槽 |
| [type](#type) | String | default | 类型 |
| [anim](#anim) | String | none | 动效类型 |
| index | Number | 0 | 默认选择下标 |
| data | UxTab[] |  | tab列表(导入:import{UxTab}from'@/uni_modules/ux-frame/libs/types/types.uts') |
| selectedColor | String | `$ux.Conf.fontColor` | 选中颜色 |
| unselectedColor | String | #111111 | 未选中颜色 |
| [selectedColorDark](#selectedColorDark) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| [unselectedColorDark](#unselectedColorDark) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| iconSize | Any | 18 | 图标大小单位px |
| fontSize | Any | 12 | 字体大小单位px |
| fontBold | Boolean | false | 字体加粗 |
| customFamily | String |  | 自定义字体family |
| border | Boolean | false | 显示上边框 |
| borderColor | String | `$ux.Conf.borderColor` | 上边框颜色 |
| corner | Any | 0 | 圆角 |
| background | String | #FFFFFF | 背景色 |
| [backgroundDark](#backgroundDark) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| centerColor | String | #FFFFFF | 中间按钮背景色 |
| showBlur | Boolean | false | 开启模糊背景仅支持normal类型 |
| blurRadius | Number | 10 | 模糊半径 |
| blurAlpha | Number | 0.5 | 模糊透明度 |
| zIndex | Number | 10000 | 层级z-index |
| hover | Boolean | true | 显示点击态 |
| fixed | Boolean | true | 固定位置 |

### [type](#type)

| 值   | 说明 |
|:------:|:----:|
| default正常
 |  |
| special特殊
 |  |

### [anim](#anim)

| 值   | 说明 |
|:------:|:----:|
| none无
 |  |
| scroll上下滚动
 |  |
| roll水平滚动
 |  |
| push左右推压
 |  |
| water水滴
 |  |

### [selectedColorDark](#selectedColorDark)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [unselectedColorDark](#unselectedColorDark)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [backgroundDark](#backgroundDark)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| click | 按钮点击时触发 |  |
| center | 中间按钮点击时触发 |  |
| change | 当前选择下标改变时触发 |  |
  
  