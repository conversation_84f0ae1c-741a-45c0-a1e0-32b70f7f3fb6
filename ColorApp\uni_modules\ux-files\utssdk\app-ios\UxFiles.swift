import UIKit
import Foundation
import UniformTypeIdentifiers
import DCloudUTSFoundation
import DCloudUniappRuntime

public class UxFiles {
    static var pickerDelegate: PickerDelegate?
	
    static func openFileManager(mimeTypes: [String], multiple: Bool, callback: @escaping (_ files: [UTSJSONObject]) -> Void) -> Bool {
		DispatchQueue.main.async {
		    let delegate = PickerDelegate(f: { files in
		        handleFiles(files: files, callback: callback)
		    })
		    
		    let documentPicker: UIDocumentPickerViewController
		    
		    if #available(iOS 14.0, *) {
		        var filter: [UTType] = []
		        for mimeType in mimeTypes {
		            if let contentType = UTType(filenameExtension: mimeType) {
		                filter.append(contentType)
		            }
		        }
		    
		        if filter.isEmpty {
		            filter.append(UTType.item)
		        }
		    	
		        documentPicker = UIDocumentPickerViewController(forOpeningContentTypes: filter, asCopy: true)
		    } else {
		        documentPicker = UIDocumentPickerViewController(documentTypes: ["public.item"], in:.import)
		    }
		    
		    documentPicker.delegate = delegate
		    documentPicker.allowsMultipleSelection = multiple
		    UTSiOS.getCurrentViewController().present(documentPicker, animated: true, completion: nil)
			
			// 精华，需要先缓存代理，要不然代理会被清理导致无法执行回调函数
			pickerDelegate = delegate
		}
		
        return true
    }
	
	static func handleFiles(files: [URL], callback: @escaping (_ files: [UTSJSONObject]) -> Void) {
		let userCacheDir = FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first!
		let keysToFetch: Set<URLResourceKey> = [.totalFileAllocatedSizeKey, .contentModificationDateKey]
		
		var _files: [UTSJSONObject] = []
		for fileURL in files {
		    do {
				let fileName = fileURL.lastPathComponent
				let fileExtension = fileURL.pathExtension
				
		        let resourceValues = try getResourceValues(for: fileURL, keys: keysToFetch)
		        let fileSize = resourceValues.totalFileAllocatedSize ?? 0
				let fileDate = resourceValues.contentModificationDate ?? Date()
				
				let dateFormatter = DateFormatter()
				dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
				let date = dateFormatter.string(from: fileDate)
				
		        let cacheURL = userCacheDir.appendingPathComponent(fileName)
		        try FileManager.default.copyItem(at: fileURL, to: cacheURL)
		        
		        let file = UTSJSONObject()
				file.set("name", fileName)
				file.set("path", cacheURL.path)
				file.set("extension", fileExtension)
				file.set("blob", nil)
				file.set("size", fileSize)
				file.set("date", date)
		        
		       _files.add(file)
		        
		    } catch {}
		}
		
		callback(_files)
	}
	
	static func getResourceValues(for url: URL, keys: Set<URLResourceKey>) throws -> URLResourceValues {
	    return try url.resourceValues(forKeys: keys)
	}
}

@MainActor
class PickerDelegate: NSObject, UIDocumentPickerDelegate {
    
    var callback : (_ files: [URL]) -> Void

    init(f: @escaping (_ files: [URL]) -> Void) {
        self.callback = f
		super.init()
    }

    func documentPicker(_ controller: UIDocumentPickerViewController, didPickDocumentsAt urls: [URL]) {
        self.callback(urls)
    }
    
    func documentPickerWasCancelled(_ controller: UIDocumentPickerViewController) {
        self.callback([])
    }
}

