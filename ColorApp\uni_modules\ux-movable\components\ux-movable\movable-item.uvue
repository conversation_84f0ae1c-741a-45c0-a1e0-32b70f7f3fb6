<template>
	<view :id="myId" class="movable" :style="style"
		@touchstart="touchstart"
		@touchmove="touchmove"
		@touchend="touchend"
		@mousedown="touchstart" 
		@mousemove="touchmove" 
		@mouseup="touchend" 
		@mouseleave="touchend">
		
		<view v-if="options.layout == 'grid'" class="grid" :style="gridStyle" @click="click">
			<image v-if="item.upload != null" class="img" :style="imgStyle" :src="item.upload!.url" mode="aspectFill"></image>
			<image v-else-if="item.url != null" class="img" :style="imgStyle" :src="item.url" mode="aspectFill"></image>
			
			<view v-if="item.label != null && !isLoading" class="label">
				<text class="text" :style="labelStyle">{{ item.label }}</text>
			</view>
			
			<image v-if="item.leftTop != null && !isLoading" class="left-top" :style="placeholderStyle(item.leftTop!)" :src="item.leftTop!.url" @click="onHolder(item.leftTop, $event)"></image>
			<image v-if="item.leftBottom != null && !isLoading" class="left-bottom" :style="placeholderStyle(item.leftBottom!)" :src="item.leftBottom!.url" @click="onHolder(item.leftBottom, $event)"></image>
			<image v-if="options.multiple && item.selected != null && !isLoading" class="right-top" :style="selectedStyle" :src="item.selected!.checked ? item.selected!.selectedUrl : item.selected!.unselectedUrl"></image>
			<image v-else-if="item.rightTop != null && !isLoading" class="right-top" :style="placeholderStyle(item.rightTop!)" :src="item.rightTop!.url" @click="onHolder(item.rightTop, $event)"></image>
			<image v-if="item.rightBottom != null && !isLoading" class="right-bottom" :style="placeholderStyle(item.rightBottom!)" :src="item.rightBottom!.url" @click="onHolder(item.rightBottom, $event)"></image>
			
			<view v-if="item.loading != null && item.loading!.show" class="loading">
				<view class="mask" :style="{'opacity': 1 - item.loading!.progress / 100}"></view>
				<text v-if="item.loading!.progress == 0" class="progress" :style="progressStyle">{{ item.loading!.waitingLabel ?? (item.loading!.progress + '%') }}</text>
				<text v-else class="progress" :style="progressStyle">{{ item.loading!.progress }}%</text>
			</view>
		</view>
		<view v-else class="list">
			<image v-if="item.url != null" class="img" :style="imgStyle" :src="item.url" mode="aspectFill"></image>
			<view class="body" :style="lineStyle">
				<view class="warp">
					<text v-if="item.label != null" class="label" :style="labelStyle">{{ item.label }}</text>
					<text v-if="item.summary != null" class="summary" :style="summaryStyle">{{ item.summary }}</text>
				</view>
				<image v-if="item.showDrag ?? true" class="drag" src="/uni_modules/ux-movable/static/ic_drag.png" mode=""></image>
			</view>
		</view>
		
		<view v-if="item.upload == null && options.editable" class="editable">
			<textarea class="input" type="text" :auto-height="true" v-model="input.content" :placeholder="input.placeholder ?? '请输入'" :maxlength="input.maxLength ?? 9" @input="onInput"></textarea>
		</view>
	</view>
</template>

<script setup lang="ts">
	/**
	 * 内部使用
	 */
	
	import { UxMovableOptions, UxMovableLayout, UxMovableItem, UxMovableStyle, UxMovablePlaceholder, UxMovableInput } from '@/uni_modules/ux-movable'
	
	defineOptions({
		name: 'movable-item'
	})

	const props = defineProps({
		item: {
			type: Object as PropType<UxMovableItem>,
			required: true
		},
		options: {
			type: Object as PropType<UxMovableOptions>,
			required: true
		},
	})
	
	const updatePos = inject('UpdatePos')! as (id: number, x: number, y: number, w: number, h: number, end: boolean) => void

	const myId = `movable-item-${uuid()}`
	const areaWidth = ref(0)
	const areaHeight = ref(0)
	const areaLeft = ref(0)
	const areaTop = ref(0)
	const draging = ref(false)
	const input = ref<UxMovableInput>({
		content: '',
		placeholder: '请输入',
		maxLength: 9,
	})
	const inputHeight = 40
	
	const index = computed(() => {
		return props.item.index
	})
	
	const col = computed(() : number => {
		return props.options.column
	})
	
	const spacing = computed(() : number => {
		return props.options.spacing
	})

	const width = computed(() : number => {
		if(props.options.layout == 'grid') {
			return (areaWidth.value - (col.value + 1) * spacing.value) / col.value
		} else {
			return areaWidth.value
		}
	})
	
	const height = computed(() : number => {
		if(props.options.layout == 'grid') {
			let h = width.value * (props.options.aspectRatio ?? 1)
			
			if(props.options.editable) {
				h += inputHeight
			}
			
			return h
		} else {
			return 45
		}
	})
	
	const style = computed(() => {
		
		let top = spacing.value
		let left = spacing.value
		
		if(props.options.layout == 'grid') {
			let row = Math.floor(index.value / col.value)
			let colIndex = index.value % col.value
			
			top += (height.value + spacing.value) * row
			left += (width.value + spacing.value) * colIndex
		} else {
			top += height.value * index.value
		}
		
		let css = new Map<string, any>()
		
		css.set('width', width.value + 'px')
		css.set('height', height.value + 'px')
		css.set('top', top + 'px')
		css.set('left', left + 'px')
		
		if(props.options.layout == 'grid' && props.item.radius != null) {
			css.set('border-radius', props.item.radius! + 'px')
		}
		
		return css
	})
	
	const gridStyle = computed(() => {
		let css = new Map<string, any>()
		
		if(props.options.layout == 'grid') {
			if(props.options.editable) {
				css.set('height', height.value - inputHeight + 'px')
			}
		}
		
		return css
	})
	
	const imgStyle = computed(() => {
		let css = new Map<string, any>()
		
		if(props.options.layout == 'grid') {
			if(props.options.editable) {
				css.set('height', height.value - inputHeight + 'px')
			}
		}
		
		if(props.item.radius != null) {
			css.set('border-radius', props.item.radius! + 'px')
		}
		
		return css
	})
	
	const lineStyle = computed(() => {
		let css = new Map<string, any>()
		
		let borderColor = '#f0f0f0'
		if(props.item.border != null && props.item.border!.color != null) {
			borderColor = props.item.border!.color!
		}
		
		css.set('border-bottom', `1px solid ${borderColor}`)
		
		return css
	})
	
	const labelStyle = computed(() => {
		let css = new Map<string, any>()
		
		let _style = props.item.labelStyle
		if(_style != null) {
			if(_style!.color != null) {
				css.set('color', _style!.color)
			}
			
			if(_style!.fontSize != null) {
				css.set('font-size', _style!.fontSize + 'px')
			}
			
			if(_style!.fontWeight != null) {
				css.set('font-weight', _style!.fontWeight! ? 'bold' : 'normal')
			}
			
			if(_style!.backgroundColor != null) {
				css.set('background-color', _style!.backgroundColor)
			}
		}
		
		return css
	})
	
	const summaryStyle = computed(() => {
		let css = new Map<string, any>()
		
		let _style = props.item.summaryStyle
		if(_style != null) {
			if(_style!.color != null) {
				css.set('color', _style!.color)
			}
			
			if(_style!.fontSize != null) {
				css.set('font-size', _style!.fontSize + 'px')
			}
			
			if(_style!.fontWeight != null) {
				css.set('font-weight', _style!.fontWeight! ? 'bold' : 'normal')
			}
			
			if(_style!.backgroundColor != null) {
				css.set('background-color', _style!.backgroundColor)
			}
		}
		
		return css
	})
	
	function placeholderStyle(holder : UxMovablePlaceholder) {
		let css = new Map<string, any>()
		
		css.set('width', holder.width + 'px')
		css.set('height', holder.height + 'px')
		
		if(holder.margin != null) {
			css.set('margin', holder.margin! + 'px')
		}
		
		if(holder.radius != null) {
			css.set('border-radius', holder.radius! + 'px')
		}
		
		return css
	}
	
	const selectedStyle = computed(() => {
		let css = new Map<string, any>()
		
		let holder = props.item.selected
		if(holder != null) {
			css.set('width', holder.width + 'px')
			css.set('height', holder.height + 'px')
			
			if(holder.margin != null) {
				css.set('margin', holder.margin! + 'px')
			}
		}
		
		return css
	})
	
	const progressStyle = computed(() => {
		let css = new Map<string, any>()
		
		let _style = props.item.loading!.style
		if(_style != null) {
			if(_style!.color != null) {
				css.set('color', _style!.color)
			}
			
			if(_style!.fontSize != null) {
				css.set('font-size', _style!.fontSize + 'px')
			}
			
			if(_style!.fontWeight != null) {
				css.set('font-weight', _style!.fontWeight! ? 'bold' : 'normal')
			}
			
			if(_style!.backgroundColor != null) {
				css.set('background-color', _style!.backgroundColor)
			}
		}
		
		if(props.item.loading!.progress == 0 && props.item.loading!.waitingLabel != null) {
			css.set('font-size', '12px')
		}
		
		return css
	})
	
	const isLoading = computed(() => {
		let loading = false
		
		if(props.options.layout == 'grid') {
			if(props.item.loading != null) {
				loading = props.item.loading!.show
			}
		}
		
		return loading
	})
	
	let timer = 0
	let lastX = 0
	let lastY = 0
	function touchstart(e: TouchEvent) {
		if(props.item.upload != null) {
			return
		}
		
		timer = setTimeout(() => {
			lastX = e.changedTouches[0].clientX - areaLeft.value
			lastY = e.changedTouches[0].clientY - areaTop.value
			uni.getElementById(myId)?.style.setProperty('z-index', 1)
			uni.getElementById(myId)?.style.setProperty('transition-duration', '0ms')
			uni.getElementById(myId)?.style.setProperty('transform', `translate(${5}px, ${5}px)`)
			draging.value = true
		}, 150)
	}
	
	function touchmove(e: TouchEvent) {
		if(props.item.upload != null) {
			return
		}
		
		if(e.changedTouches == null) {
			return
		}
		
		// 位移
		let clientX = e.changedTouches[0].clientX - areaLeft.value
		let clientY = e.changedTouches[0].clientY - areaTop.value
		clientX = Math.max(0, clientX)
		clientX = Math.min(areaWidth.value, clientX)
		clientY = Math.max(0, clientY)
		clientY = Math.min(areaHeight.value, clientY)
		
		let offsetX = clientX - lastX
		let offsetY = clientY - lastY
		uni.getElementById(myId)?.style.setProperty('transform', `translate(${offsetX}px, ${offsetY}px)`)
		
		// 下标排序
		updatePos(props.item.id, clientX, clientY, width.value, height.value, false);
	}
	
	function touchend(e: TouchEvent) {
		if(props.item.upload != null) {
			return
		}
		
		draging.value = false
		uni.getElementById(myId)?.style.setProperty('transition-duration', '400ms')
		setTimeout(() => {
			uni.getElementById(myId)?.style.setProperty('transform', `translate(${0}px, ${0}px)`)
			setTimeout(() => {
				uni.getElementById(myId)?.style.setProperty('z-index', 0)
			}, 400);
		}, 50);
		
		if(e.changedTouches == null) {
			return
		}
		
		// 下标排序
		let clientX = e.changedTouches[0].clientX - areaLeft.value
		let clientY = e.changedTouches[0].clientY - areaTop.value
		updatePos(props.item.id, clientX, clientY, width.value, height.value, true);
	}
	
	function click(e: MouseEvent) {
		props.item.click?.(props.item)
		e.stopPropagation()
	}
	
	function onHolder(holder: UxMovablePlaceholder, e: MouseEvent) {
		holder.click?.(props.item)
		
		if(holder.click != null) {
			e.stopPropagation()
		}
	}
	
	function onInput(e: UniInputEvent) {
		props.item.input?.input?.(e.detail.value)
	}
	
	onMounted(() => {
		let el = uni.getElementById('movableRef')
		areaWidth.value = el?.getBoundingClientRect().width ?? 0
		areaHeight.value = el?.getBoundingClientRect().height ?? 0
		areaLeft.value = el?.getBoundingClientRect().left ?? 0
		areaTop.value = el?.getBoundingClientRect().top ?? 0
		
		if(props.item.input != null) {
			input.value = props.item.input!
		}
	})

	function uuid() : string {
		const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('')
		const uuid : string[] = []
		
		for (let i = 0; i < 36; i++) {
			let r = 0 | Math.random() * 16
			let c = chars[(i == 19) ? (r & 0x3) | 0x8 : r]
			uuid.push(c)
		}
		
		// rfc4122标准要求返回的uuid中,某些位为固定的字符
		uuid[8] = '-'
		uuid[13] = '-'
		uuid[18] = '-'
		uuid[23] = '-'
		uuid[14] = '4'
		
		return `${uuid.join('')}`
	}
</script>

<style lang="scss">
	.movable {
		position: absolute;
		left: 0px;
		top: 0px;
		transition-timing-function: ease;
		transition-property: top, left, opacity, transform;
		transition-duration: 400ms;
		/* #ifdef WEB */
		cursor: grab;
		/* #endif */
	}

	/* #ifdef WEB */
	.movable:active {
		cursor: grabbing;
	}
	/* #endif */
	
	.grid {
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		
		.img {
			width: 100%;
			height: 100%;
		}
		
		.label {
			position: absolute;
			bottom: 0;
			width: 100%;
			height: 25px;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
			background-color: rgba(0, 0, 0, 0.5);
			
			.text {
				font-size: 13px;
				color: white;
			}
		}
		
		.left-top {
			position: absolute;
			left: 0;
			top: 0;
		}
		
		.left-bottom {
			position: absolute;
			left: 0;
			bottom: 0;
		}
		
		.right-top {
			position: absolute;
			right: 0;
			top: 0;
		}
		
		.right-bottom {
			position: absolute;
			right: 0;
			bottom: 0;
		}
		
		.loading {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			
			.mask {
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background-color: rgba(0, 0, 0, 0.8);
			}
			
			.progress {
				font-size: 16px;
				color: white;
				font-weight: bold;
			}
		}
	}
	
	.list {
		width: 100%;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		
		.img {
			width: 40px;
			height: 40px;
			margin-right: 6px;
		}
		
		.body {
			flex: 1;
			height: 100%;
			margin-right: 15px;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: space-between;
			border-bottom: 1px solid #E5E5E5;
			
			.warp {
				flex: 1;
				height: 100%;
				display: flex;
				flex-direction: column;
				justify-content: center;
				
				.label {
					font-size: 16px;
					font-weight: bold;
				}
				
				.summary {
					font-size: 14px;
					color: #808080;
				}
			}
			
			.drag {
				width: 24px;
				height: 24px;
				margin-left: 16px;
			}
		}
	}
		
	.editable {
		min-height: 20px;
		max-height: 80px;
		
		.input {
			width: 100%;
			padding: 2px 6px;
			font-size: 13px;
			margin-top: 4px;
			border-radius: 3px;
			background-color: #f0f0f0;
		}
	}
</style>