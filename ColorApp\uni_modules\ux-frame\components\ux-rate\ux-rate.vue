<template>
	<view id="uxRateRef" class="ux-rate" :style="[style, xstyle]" 
		@touchstart="touchstart" @mousedown="touchstart"
		@touchmove="touchmove" @mousemove="touchmovePc"
		@touchend="touchend" @mouseup="touchend" @mouseleave="touchend">
		<template v-for="i in count" :key="i">
			<ux-icon 
				:style="{'margin-right': `${i == count ? 0 : gutter}px`, 'cursor': disabled? 'not-allowed' : (readonly? '' : 'pointer')}"
				:type="i == Math.ceil(index) && showHalf ? halfIcon : icon" 
				:color="i <= Math.ceil(index) ? activeColor : color" 
				:size="fontSize" 
				:custom-family="customFamily"
				@click="(e: MouseEvent) => click(i, e)">
			</ux-icon>
		</template>
		<ux-text v-if="text != ''" :ml="8" :color="activeColor" :size="size" :text="text"></ux-text>
	</view>
</template>

<script setup lang="ts">
	
	/**
	* Rate 评分
	* @description 支持设置数量、最小选择、半星选择、触摸/鼠标拖动选择
	* @demo pages/component/rate.uvue
	* @tutorial https://www.uxframe.cn/component/rate.html
	* @property {String} 			name												String | 标识符，在回调事件中返回
	* @property {String} 			theme=[primary|warning|success|error|info]			String | 主题颜色
	* @value primary 	主色
	* @value warning 	警告
	* @value success 	成功
	* @value error 		错误
	* @value info 		文本
	* @property {Number} 			value												Number | 分数
	* @property {Number} 			count												Number | 数量 (默认 5)
	* @property {Number} 			minCount											Number | 最小选择数量 (默认 0)
	* @property {String} 			color												String | 颜色 (默认 $ux.Conf.placeholderColor)
	* @property {String} 			darkColor=[none|auto|color]							String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	* @value none 不显示
	* @value auto 自动适配深色模式
	* @value color 其他颜色
	* @property {String} 			activeColor											String | 选中颜色 优先级高于主题 (默认 $ux.Conf.primaryColor)
	* @property {String} 			activeDarkColor=[none|auto|color]					String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	* @value none 不显示
	* @value auto 自动适配深色模式
	* @value color 其他颜色
	* @property {Any} 				size												Any | 字体大小（默认 $ux.Conf.fontSize ）
	* @property {Any} 				gutter												Any | 间距（默认 5 ）
	* @property {String} 			type=[star|heart]									String | 类型（默认 star ）
	* @value star 星星
	* @value heart 爱心
	* @property {String}			icon												String | 星星图标 优先级高于type
	* @property {Boolean} 			half=[true|false]									Boolean | 允许半星 halfIcon配置时有效（默认 false ）
	* @property {String} 			halfIcon											String | 半星图标 half = true 时有效
	* @property {String}			customFamily										String | 字体family
	* @property {String}			valueFormat											String | 格式化文本，有则显示 (默认 __s1__分)
	* @property {Boolean} 			touchable=[true|false]								Boolean | 触摸滑动（默认 false ）
	* @property {Boolean} 			disabled=[true|false]								Boolean | 禁用（默认 false ）
	* @property {Boolean} 			readonly=[true|false]								Boolean | 只读（默认 false ）
	* @property {Array}				margin												Array | 边距 [上 右 下 左] [上下 左右] [上下左右]
	* @property {Any}				mt													Any | 距上 单位px
	* @property {Any}				mr													Any | 距右 单位px
	* @property {Any}				mb													Any | 距下 单位px
	* @property {Any}				ml													Any | 距左 单位px
	* @property {Array}				padding												Array | 填充 [上 右 下 左] [上下 左右] [上下左右]
	* @property {Any}				pt													Any | 上内边距 单位px
	* @property {Any}				pr													Any | 右内边距 单位px
	* @property {Any}				pb													Any | 下内边距 单位px
	* @property {Any}				pl													Any | 左内边距 单位px
	* @property {Array}				xstyle												Array<any> | 自定义样式
	* @event {Function} 			change 												Function | 值改变时触发
	* @event {Function} 			touchstart 											Function | 开始触摸时触发
	* @event {Function} 			touchmove 											Function | 正在触摸时触发
	* @event {Function} 			touchend 											Function | 触摸结束时触发
	* <AUTHOR>
	* @date 2024-12-03 10:12:28
	*/
   
	import { computed, getCurrentInstance, ref, watch } from 'vue'
	import { $ux } from '../../index'
	import { UxRateEvent } from '../../libs/types/types.uts'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	import { useFontSize, useFontColor, useThemeColor, usePlaceholderColor, UxThemeType } from '../../libs/use/style.uts'

	defineOptions({
		name: 'ux-rate',
		mixins: [xstyleMixin]
	})
	
	const emit = defineEmits(['update:modelValue', 'change', 'touchstart', 'touchmove', 'touchend'])
	
	const props = defineProps({
		modelValue: {
		    type: Number,
		    default: 0
		},
		name: {
			type: String,
			default: ''
		},
		theme: {
			type: String,
			default: 'primary',
		},
		value: {
		    type: Number,
		    default: 0
		},
		count: {
		    type: Number,
		    default: 5
		},
		minCount: {
		    type: Number,
		    default: 0
		},
		color: {
		    type: String,
		    default: ''
		},
		activeColor: {
		    type: String,
		    default: ''
		},
		darkColor: {
		    type: String,
		    default: ''
		},
		activeDarkColor: {
		    type: String,
		    default: ''
		},
		size: {
		    default: 0
		},
		gutter: {
		    default: 5
		},
		half: {
		    type: Boolean,
		    default: false
		},
		type: {
			type: String,
			default: 'star'
		},
		icon: {
		    type: String,
		    default: ''
		},
		halfIcon: {
		    type: String,
		    default: ''
		},
		customFamily: {
		    type: String,
		    default: ''
		},
		valueFormat: {
			type: String,
			default: ''
		},
		touchable: {
		    type: Boolean,
		    default: false
		},
		disabled: {
		    type: Boolean,
		    default: false
		},
		readonly: {
		    type: Boolean,
		    default: false
		},
	})
	
	const index = ref(0)
	const isPressed = ref(false)
	
	const fontSize = computed((): number => {
		return useFontSize($ux.Util.getPx(props.size), 2)
	})

	const icon = computed((): string => {
		
		if(props.icon != '') {
			return props.icon
		} else {
			if(props.type == 'heart') {
				return 'heart-filled'
			}
		}
		
		return 'star-filled'
	})
	
	const halfIcon = computed((): string => {
		
		if(props.halfIcon != '') {
			return props.halfIcon
		} else {
			return 'starhalf-filled'
		}
	})
	
	const showHalf = computed((): boolean => {
		
		if(props.half) {
			return index.value.toFixed(1).split('.')[1] == '5'
		} 
		
		return false
	})
	
	const color = computed((): string => {
		
		if(props.color != '') {
			return useFontColor(props.color, props.darkColor)
		} else {
			return usePlaceholderColor('', '')
		}
	})
	
	const activeColor = computed((): string => {
		
		if(props.activeColor != '') {
			return useFontColor(props.activeColor, props.activeDarkColor)
		} else {
			return useThemeColor(props.theme as UxThemeType)
		}
	})
	
	const text = computed((): string => {
		
		if(props.valueFormat.indexOf('__s1__') != -1 ) {
			let reg = new RegExp('\\__s1__', 'g')
			return props.valueFormat.replace(reg, index.value.toFixed(1))
		}
		
		return ''
	})
	
	const style = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css = $ux.Util.xStyle(css, props.margin, props.mt, props.mr, props.mb, props.ml, props.padding, props.pt, props.pr, props.pb, props.pl)
		
		return css
	})
	
	async function getX(i: number): Promise<number> {
		let rect = await $ux.Util.getBoundingClientRect('#uxRateRef', getCurrentInstance()?.proxy)
		let width = fontSize.value
		let left = rect.left
		return left + (i - 1) * (width + $ux.Util.getPx(props.gutter)) + (width / 2)
	}
	
	function touchstart(e: TouchEvent) {
		isPressed.value = true
		emit('touchstart', e)
	}
	
	async function _touchmove(x: number) {
		let rect = await $ux.Util.getBoundingClientRect('#uxRateRef', getCurrentInstance()?.proxy)
		
		let width = fontSize.value
		let left = rect.left
		let i = Math.floor((x - left) / (width + $ux.Util.getPx(props.gutter))) + 1
		
		if(props.half) {
			let _x = await getX(i)
			index.value = x < _x ? (i - 0.5) : i
		} else {
			index.value = i
		}
		
		if (index.value < props.minCount) {
			index.value = props.minCount
		}
		
		if (index.value > props.count) {
			index.value = props.count
		}
		
		emit('update:modelValue', index.value)
		
		emit('change', {
			name: props.name,
			value: index.value
		} as UxRateEvent)
	}
	
	function touchmove(e: TouchEvent) {
		if(!isPressed.value) {
			return
		}
		
		if(props.disabled || props.readonly || !props.touchable) {
			return
		}
		
		e.preventDefault()
		
		_touchmove(e.changedTouches[0].clientX)
		
		emit('touchmove', e)
	}
	
	function touchmovePc(e: MouseEvent) {
		if(!isPressed.value) {
			return
		}
		
		if(props.disabled || props.readonly || !props.touchable) {
			return
		}
		
		_touchmove(e.clientX)
		
		emit('touchmove', e)
	}
	
	function touchend(e: TouchEvent) {
		isPressed.value = false
		emit('touchend', e)
	}
	
	async function click(i: number, e: MouseEvent) {
		if(props.disabled || props.readonly) {
			return
		}
		
		if(props.half) {
			let _x = await getX(i)
			index.value = e.clientX < _x ? (i - 0.5) : i
		} else {
			index.value = i
		}
		
		if (index.value < props.minCount) {
			index.value = props.minCount
		}
		
		if(index.value > props.count) {
			index.value = props.count
		}
		
		emit('update:modelValue', index.value)
		
		emit('change', {
			name: props.name,
			value: index.value
		} as UxRateEvent)
	}
	
	const value = computed(():number => {
		return props.value
	})
	
	const modelValue = computed(():number => {
		return props.modelValue
	})
	
	watch(value, () => {
		index.value = props.value
		
		if (index.value < props.minCount) {
			index.value = props.minCount
		}
		
		if(index.value > props.count) {
			index.value = props.count
		}
		
	}, {immediate: true})
	
	watch(modelValue, () => {
		index.value = modelValue.value
		
		if (index.value < props.minCount) {
			index.value = props.minCount
		}
		
		if(index.value > props.count) {
			index.value = props.count
		}
		
	}, {immediate: true})
	
</script>

<style lang="scss" scoped>

	.ux-rate {
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
	}

</style>