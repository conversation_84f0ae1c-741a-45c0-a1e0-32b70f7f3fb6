<template>
	<view class="ux-overlay">
		<view :id="myId" ref="uxOverlayRef" class="ux-overlay__mask transform" :style="style" @click="close"></view>
		<ux-blur v-if="showBlur" v-show="show" class="ux-overlay__blur" :style="blurStyle" :radius="blurRadius" :color="blurColor" @click="close"></ux-blur>
		<slot></slot>
	</view>
</template>

<script setup lang="ts">
	/**
	 * 遮罩层
	 * @description 支持透明度、遮罩点击关闭
	 * @demo pages/component/overlay.uvue
	 * @tutorial https://www.uxframe.cn/component/overlay.html
	 * @property {Boolean}			show = [true|false]					Boolean | 显隐状态 (默认 fasle)
	 * @property {Boolean}			showBlur = [true|false]				Boolean | 开启模糊背景 (默认 false )
	 * @property {Number}			blurRadius							Number | 模糊半径 (默认 10 )
	 * @property {String}			blurColor							String | 模糊颜色  (默认 rgba(10, 10, 10, 0.3) )
	 * @property {Boolean}			maskClose = [true|false]			Boolean | 点击遮罩是否关闭 (默认 true)
	 * @property {Number}			opacity								Number | 遮罩透明度 0-1 (默认 $ux.Conf.maskAlpha)
	 * @property {Boolean}			fixedOpacity = [true|false]			Boolean | 固定透明度  (默认 true)
	 * @property {Number}			zIndex								Number | 层级z-index (默认 10001)
	 * @event {Function}			close								Function | 点击遮罩关闭时触发
	 * <AUTHOR>
	 * @date 2024-12-04 20:31:11
	 */

	import { computed, ref, watch } from 'vue'
	import { $ux } from '../../index'

	defineOptions({
		name: 'ux-overlay'
	})

	const emit = defineEmits(['close'])

	const props = defineProps({
		show: {
			type: Boolean,
			default: false
		},
		showBlur: {
			type: Boolean,
			default: false
		},
		blurRadius: {
			type: Number,
			default: 10
		},
		blurColor: {
			type: String,
			default: 'rgba(10, 10, 10, 0.3)'
		},
		maskClose: {
			type: Boolean,
			default: true
		},
		opacity: {
			type: Number,
			default: -1
		},
		fixedOpacity: {
			type: Boolean,
			default: true
		},
		zIndex: {
			type: Number,
			default: 10001
		},
	})
	
	const myId = `ux-overlay-${$ux.Random.uuid()}`
	const uxOverlayRef = ref<Element | null>(null)
	
	const blurAlpha = ref(0)
	
	const opacity = computed(() : number => {
		return props.opacity == -1 ? $ux.Conf.maskAlpha.value :  props.opacity
	})
	
	const style = computed(() : string => {
		return `z-index: ${props.zIndex};`
	})
	
	const blurStyle = computed(() : string => {
		return `z-index: ${props.zIndex + 1};`
	})

	const show = computed(() : boolean => {
		return props.show
	})

	function anim() {
		if (show.value) {
			blurAlpha.value = 0.8
			uni.getElementById(myId)?.style?.setProperty('width', `${uni.getWindowInfo().screenWidth}px`)
			uni.getElementById(myId)?.style?.setProperty('height', `${uni.getWindowInfo().screenHeight}px`)

			if (props.fixedOpacity) {
				uni.getElementById(myId)?.style?.setProperty('opacity', opacity.value)
			}
		} else {
			blurAlpha.value = 0
			if (props.fixedOpacity) {
				uni.getElementById(myId)?.style?.setProperty('opacity', 0)
			}

			setTimeout(() => {
				uni.getElementById(myId)?.style?.setProperty('width', `${0}px`)
				uni.getElementById(myId)?.style?.setProperty('height', `${0}px`)
			}, 400)
		}
	}

	function close() {
		if (props.maskClose) {
			emit('close')
		}
	}

	watch(show, () => {
		anim()
	})
	
	defineExpose({
		close
	})
</script>

<style lang="scss" scoped>
	/* #ifdef H5 */
	uni-view {
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: stretch;
		align-content: stretch;
		justify-content: flex-start;
		box-sizing: border-box;
		min-height: 0px;
		min-width: 0px;
		overflow: hidden;
	}
	/* #endif */
	
	.ux-overlay {

		.ux-overlay__mask {
			position: fixed;
			z-index: 10001;
			width: 100%;
			height: 100%;
			top: 0;
			right: 0;
			width: 0;
			height: 0;
			opacity: 0;
			background-color: #000000;
		}
		
		.ux-overlay__blur {
			position: fixed;
			z-index: 10002;
			width: 100%;
			height: 100%;
			top: 0;
			right: 0;
			left: 0;
			bottom: 0;
			opacity: 0;
			transition-property: opacity;
			transition-duration: 400ms;
			transition-timing-function: linear;
		}
	}

	.transform {
		transition-property: opacity;
		transition-duration: 400ms;
		transition-timing-function: linear;
	}
</style>