<template>
	<view class="ux-divider" :style="[style, xstyle]">
		<view class="ux-divider__line" :class="[$slots['default'] != null? `ux-divider__${direction}--left` : '']" :style="[lineStyle]"></view>
		<slot></slot>
		<view class="ux-divider__line" :class="[$slots['default'] != null? `ux-divider__${direction}--right` : '']" :style="[lineStyle]"></view>
	</view>
</template>

<script setup>
	/**
	 * Divider 分割线
	 * @description 支持横向、竖向，可配置非细线、虚线
	 * @demo pages/component/divider.uvue
	 * @tutorial https://www.uxframe.cn/component/divider.html
	 * @property {String}			theme=[text|info|primary|success|warning|error]	String | 按钮类型
	 * @value text			文字
	 * @value info   		默认
	 * @value primary   	主要
	 * @value success  		成功
	 * @value warning   	警告
	 * @value error  		错误
	 * @property {String}			color							String | 颜色 ( 默认 $ux.Conf.borderColor )
	 * @property {String} 			darkColor=[none|auto|color]		String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	 * @value none 不显示
	 * @value auto 自动适配深色模式
	 * @value color 其他颜色
	 * @property {String}			direction=[row|col]				String | 方向 (默认 row )
	 * @value row 横向
	 * @value col 竖向
	 * @property {Boolean}			hairline = [true|false]			Boolean | 细线条 (默认 true )
	 * @property {Boolean}			dashed = [true|false]			Boolean | 虚线 (默认 false )
	 * @property {Array}			margin							Array | 边距 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}				mt								Any | 距上 单位px
	 * @property {Any}				mr								Any | 距右 单位px
	 * @property {Any}				mb								Any | 距下 单位px
	 * @property {Any}				ml								Any | 距左 单位px
	 * @property {Array}			padding							Array | 填充 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}				pt								Any | 上内边距 单位px
	 * @property {Any}				pr								Any | 右内边距 单位px
	 * @property {Any}				pb								Any | 下内边距 单位px
	 * @property {Any}				pl								Any | 左内边距 单位px
	 * @property {Array}			xstyle							Array<any> | 自定义样式
	 * <AUTHOR>
	 * @date 2023-12-07 00:34:28
	 */
	
	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	import { useThemeColor, useBorderColor, UxThemeType } from '../../libs/use/style.uts'
	
	defineOptions({
		name: 'ux-divider',
		mixins: [xstyleMixin]
	})
	
	const emit = defineEmits(['click'])
	
	const props = defineProps({
		theme: {
			type: String,
			default: ''
		},
		color: {
			type: String,
			default: ''
		},
		darkColor: {
			type: String,
			default: ''
		},
		direction: {
			type: String,
			default: 'row'
		},
		hairline: {
			type: Boolean,
			default: true
		},
		dashed: {
			type: Boolean,
			default: false
		},
	})
	
	const borderColor = computed((): string => {
		if(props.theme != ''){
			return useThemeColor(props.theme as UxThemeType)
		} else {
			return useBorderColor(props.color, props.darkColor)
		}
	})
	
	const style = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('flex-direction', props.direction == 'row'?'row':'column')
		
		css = $ux.Util.xStyle(css, props.margin, props.mt, props.mr, props.mb, props.ml, props.padding, props.pt, props.pr, props.pb, props.pl)
		
		return css
	})
	
	const lineStyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		if (props.direction == 'row') {
			css.set('width', '100%')
			css.set('height', '1rpx')
			css.set('border-bottom', `${props.hairline && !props.dashed?'1rpx':'1px'} ${props.dashed ? 'dashed' : 'solid'} ${borderColor.value}`)
		} else {
			css.set('width', '1px')
			css.set('height', '100%')
			css.set('border-left', `${props.hairline && !props.dashed?'1rpx':'1px'} ${props.dashed ? 'dashed' : 'solid'} ${borderColor.value}`)
		}
		
		return css
	})
	
</script>

<style lang="scss">
	.ux-divider {
		display: flex;
		justify-content: center;
		align-items: center;
		
		&__line {
			flex: 1;
		}
		
		&__row--left{
			margin-right: 8px;
		}
		
		&__row--right{
			margin-left: 8px;
		}
		
		&__col--left{
			margin-bottom: 8px;
		}
		
		&__col--right{
			margin-top: 8px;
		}
	}
	
	
</style>