<template>
	<view class="ux-numberbox" :style="[style, xstyle]">
		<view v-if="minus" 
			class="ux-numberbox__minus" 
			:style="minusStyle" 
			hover-class="hover" 
			:hover-start-time="20" 
			:hover-stay-time="200" 
			@click="onMinus"
			@touchstart="touchstart('minus')"
			@touchend="touchend"
			@mousedown="touchstart('minus')"
			@mouseup="touchend"
			@mouseleave="touchend">
			<ux-icon class="ux-numberbox__text" type="minus" :color="iconColor"></ux-icon>
		</view>
		
		<view class="ux-numberbox__value" :style="inputStyle">
			<input class="ux-numberbox__input" 
				:style="inputText" 
				:type="decimal ? 'digit' : 'number'" 
				:value="innerValue" 
				:disabled="disabled"
				@input="input"/>
		</view>
		
		<view v-if="plus" class="ux-numberbox__plus" 
			:style="plusStyle" 
			hover-class="hover" 
			:hover-start-time="20" 
			:hover-stay-time="200" 
			@click="onPlus"
			@touchstart="touchstart('plus')"
			@touchend="touchend"
			@mousedown="touchstart('plus')"
			@mouseup="touchend"
			@mouseleave="touchend">
			<ux-icon class="ux-numberbox__text" type="plus" :color="iconColor"></ux-icon>
		</view>
	</view>
</template>

<script setup>
	/**
	* NumberBox 步进器
	* @description 支持整数、小数步进，可长按快速步进
	* @demo pages/component/numberbox.uvue
	* @tutorial https://www.uxframe.cn/component/numberbox.html
	* @property {String} 			name												String | 标识符，在回调事件中返回
	* @property {String} 			theme=[primary|warning|success|error|info]			String | 主题颜色
	* @value primary 	主色
	* @value warning 	警告
	* @value success 	成功
	* @value error 		错误
	* @value info 		文本
	* @property {String} 			color												String | 颜色 (默认 $ux.Conf.placeholderColor)
	* @property {String} 			darkColor=[none|auto|color]							String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	* @value none 不显示
	* @value auto 自动适配深色模式
	* @value color 其他颜色
	* @property {String} 			inputColor											String | 输入框背景颜色 (默认 $ux.Conf.placeholderColor)
	* @property {String} 			inputDarkColor=[none|auto|color]					String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	* @value none 不显示
	* @value auto 自动适配深色模式
	* @value color 其他颜色
	* @property {String} 			fontColor											String | 输入框背景颜色 (默认 $ux.Conf.placeholderColor)
	* @property {String} 			fontDarkColor=[none|auto|color]					String | 深色 none - 不显示，auto - 自动适配深色模式，其他 - 颜色
	* @value none 不显示
	* @value auto 自动适配深色模式
	* @value color 其他颜色
	* @property {Number} 			value												Number | 值
	* @property {Number} 			min													Number | 最小值  (默认 0)
	* @property {Number} 			max													Number | 最大值 (默认 999999999)
	* @property {Number} 			step												Number | 步长 (默认 1)
	* @property {Boolean} 			decimal=[true|false]								Boolean | 允许输入小数 默认 false ）
	* @value true
	* @value false
	* @property {Number} 			precision											Number | 小数点精度 (默认 1)
	* @property {Any} 				width												Any | 输入框宽度（默认 50 ）
	* @property {Boolean} 			radius=[true|false]									Boolean | 圆角按钮（默认 false ）
	* @value true
	* @value false
	* @property {Boolean} 			minus=[true|false]									Boolean | 显示 - 按钮（默认 true ）
	* @value true
	* @value false
	* @property {Boolean} 			plus=[true|false]									Boolean | 显示 + 按钮（默认 true ）
	* @value true
	* @value false
	* @property {Boolean} 			longpress=[true|false]								Boolean | 开启长按加减手势（默认 true ）
	* @value true
	* @value false
	* @property {Boolean} 			disablePlus=[true|false]							Boolean | 禁用 +（默认 false ）
	* @value true
	* @value false
	* @property {Boolean} 			disableMinus=[true|false]							Boolean | 禁用 -（默认 false ）
	* @value true
	* @value false
	* @property {Boolean} 			disabled=[true|false]								Boolean | 禁用（默认 false ）
	* @property {Array}				margin												Array | 边距 [上 右 下 左] [上下 左右] [上下左右]
	* @property {Any}				mt													Any | 距上 单位px
	* @property {Any}				mr													Any | 距右 单位px
	* @property {Any}				mb													Any | 距下 单位px
	* @property {Any}				ml													Any | 距左 单位px
	* @property {Array}				padding												Array | 填充 [上 右 下 左] [上下 左右] [上下左右]
	* @property {Any}				pt													Any | 上内边距 单位px
	* @property {Any}				pr													Any | 右内边距 单位px
	* @property {Any}				pb													Any | 下内边距 单位px
	* @property {Any}				pl													Any | 左内边距 单位px
	* @property {Array}				xstyle												Array<any> | 自定义样式
	* @event {Function} 			change 												Function | 值改变时触发
	* <AUTHOR>
	* @date 2024-04-16 00:33:25
	*/

	import { $ux } from '../../index'
	import { UxNumberboxEvent } from '../../libs/types/types.uts'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	import { useFontSize, useFontColor, useThemeColor, useForegroundColor, useInverseColor, UxThemeType } from '../../libs/use/style.uts'

	defineOptions({
		name: 'ux-numberbox',
		mixins: [xstyleMixin]
	})

	const emit = defineEmits(['change', 'update:modelValue'])

	const props = defineProps({
		modelValue: {
			type: Number,
			default: 0
		},
		name: {
			type: String,
			default: ''
		},
		theme: {
			type: String,
			default: '',
		},
		color: {
			type: String,
			default: '#f0f0f0'
		},
		darkColor: {
			type: String,
			default: '#666666'
		},
		inputColor: {
			type: String,
			default: '#f0f0f0'
		},
		inputDarkColor: {
			type: String,
			default: '#666666'
		},
		fontColor: {
			type: String,
			default: ''
		},
		fontDarkColor: {
			type: String,
			default: ''
		},
		value: {
			type: Number,
			default: 0
		},
		min: {
			type: Number,
			default: 0
		},
		max: {
			type: Number,
			default: 1000
		},
		step: {
			type: Number,
			default: 1
		},
		decimal: {
			type: Boolean,
			default: false
		},
		precision: {
			type: Number,
			default: 1
		},
		width: {
			default: 50
		},
		radius: {
			type: Boolean,
			default: false
		},
		minus: {
			type: Boolean,
			default: true
		},
		plus: {
			type: Boolean,
			default: true
		},
		longpress: {
			type: Boolean,
			default: true
		},
		disablePlus: {
			type: Boolean,
			default: false
		},
		disableMinus: {
			type: Boolean,
			default: false
		},
		disabledInput: {
			type: Boolean,
			default: false
		},
		disabled: {
			type: Boolean,
			default: false
		},
	})
	
	const backgroundColor = computed((): string => {
		if(props.theme != '') {
			return useThemeColor(props.theme as UxThemeType)
		}  else {
			return useForegroundColor(props.color == '' ? $ux.Conf.placeholderColor.color.value : props.color, props.darkColor)
		}
	})
	
	const backgroundDisabled = computed(() : string => {
		
		if($ux.Conf.darkMode.value) {
			return $ux.Color.getLightColor(backgroundColor.value, 20)
		} else {
			return backgroundColor.value
		}
	})
	
	const inputBackgroundColor = computed((): string => {
		return useForegroundColor(props.inputColor == '' ? $ux.Conf.placeholderColor.color.value : props.inputColor, props.inputDarkColor)
	})
	
	const inputBackgroundDisabled = computed(() : string => {
		if($ux.Conf.darkMode.value) {
			return $ux.Color.getLightColor(inputBackgroundColor.value, 20)
		} else {
			return inputBackgroundColor.value
		}
	})
	
	const inputColor = computed(() : string => {
		return useFontColor(props.fontColor, props.fontDarkColor)
	})
	
	const iconColor = computed(() : string => {
		return useInverseColor(backgroundColor.value)
	})
	
	const style = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css = $ux.Util.xStyle(css, props.margin, props.mt, props.mr, props.mb, props.ml, props.padding, props.pt, props.pr, props.pb, props.pl)
		
		return css
	})
	
	const minusStyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		if(props.radius) {
			css.set('border-radius', $ux.Util.addUnit(30))
		}
		
		css.set('background-color', props.disabled || props.disableMinus ? backgroundDisabled.value : backgroundColor.value)
		
		// #ifdef WEB
		css.set('cursor', props.disabled || props.disableMinus? 'not-allowed' : 'pointer')
		// #endif
		
		return css
	})
	
	const plusStyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		if(props.radius) {
			css.set('border-radius', $ux.Util.addUnit(30))
		}
		
		css.set('background-color', props.disabled || props.disablePlus ? backgroundDisabled.value : backgroundColor.value)
		
		// #ifdef WEB
		css.set('cursor', props.disabled || props.disablePlus? 'not-allowed' : 'pointer')
		// #endif
		
		return css
	})
	
	const inputStyle = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('background-color', props.disabled ? inputBackgroundDisabled.value : inputBackgroundColor.value)
		css.set('width', $ux.Util.addUnit(props.width))
		
		// #ifdef WEB
		css.set('cursor', props.disabled? 'not-allowed' : 'pointer')
		// #endif
		
		return css
	})
	
	const inputText = computed((): Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('color', inputColor.value)
		
		return css
	})
	
	const innerValue = ref('')
	const timer = ref(0)
	
	function parseNumber(num: string): number {
		if(num == '') return Math.min(0, props.min)
		
		num = num.replace(/[^0-9.-]/g, '')
		
		if(props.decimal) {
			return parseFloat(parseFloat(num).toFixed(props.precision))
		} else {
			return parseInt(num)
		}
	}
	
	function change() {
		let _value = parseNumber(innerValue.value)
		
		emit('update:modelValue', _value)
		
		emit('change', {
			name: props.name,
			value: _value
		} as UxNumberboxEvent)
	}
	
	function input(e: InputEvent) {
		let val = parseNumber(e.detail.value)
		
		if(val > props.max) {
			innerValue.value = `${props.max}`
		} else if(val < props.min) {
			innerValue.value = `${props.min}`
		} else {
			innerValue.value = `${val}`
		}
		
		setTimeout(() => {
			change()
		}, 0);
	}
	
	function add(num1: number, num2: number ): number {
		// 处理精度问题
		const cardinal = Math.pow(10, 3);
		const val = Math.round((num1 + num2) * cardinal) / cardinal
		return parseNumber(`${val}`)
	}
	
	function onMinus() {
		if(props.disabled || props.disableMinus)  {
			return
		}
		
		let val = parseNumber(innerValue.value)
		if(val - props.step < props.min) {
			return
		}
		
		innerValue.value = `${add(val, -props.step)}`
		
		change()
	}
	
	function onPlus() {
		if(props.disabled || props.disablePlus)  {
			return
		}
		
		let val = parseNumber(innerValue.value)
		if(val + props.step > props.max) {
			return
		}
		
		innerValue.value = `${add(val, props.step)}`
		
		change()
	}
	
	function touchstart(type: string) {
		if(props.disabled)  {
			return
		}
		
		if(!props.longpress) {
			return
		}
		
		clearTimeout(timer.value)
		
		timer.value = setInterval(() => {
			if(type == 'minus') {
				onMinus()
			} else {
				onPlus()
			}
		}, 250);
	}
	
	function touchend() {
		clearInterval(timer.value)
		timer.value = 0
	}
	
	const modelValue = computed((): number => {
		return props.modelValue
	})
	
	const value = computed((): number => {
		return props.value
	})
	
	watch(value, () => {
		if(modelValue.value == 0) {
			innerValue.value = `${value.value}`
		}
	}, {immediate: true})
	
	watch(modelValue, () => {
		if(value.value == 0) {
			innerValue.value = `${modelValue.value}`
		}
	}, {immediate: true})
</script>

<style lang="scss">
	.ux-numberbox {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: flex-start;
		
		&__minus {
			width: 34px;
			height: 34px;
			border-radius: 5px;
			background-color: #f0f0f0;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
		}
		
		&__plus {
			width: 34px;
			height: 34px;
			border-radius: 5px;
			background-color: #f0f0f0;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
		}
		
		&__text {
			font-size: 18px;
			color: black;
		}
		
		&__value {
			margin: 0 4px;
			width: 50px;
			height: 34px;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
			background-color: #f0f0f0;
			border-radius: 5px;
		}
		
		&__input {
			flex: 1;
			padding: 0 5px;
			font-size: 14px;
			text-align: center;
		}
		
		&__hover {
			background-color: #efefef;
		}
	}
</style>