<template>
	<view class="ux-shimmer" :class="showShimmer ? 'shimmer-item' : ''">
		<slot></slot>
	</view>
</template>

<script>
	/**
	* 辉光组件
	* @description 可用于骨架屏辉光效果等场景
	* @demo pages/component/shimmer.uvue
	* @tutorial https://www.uxframe.cn/component/shimmer.html
	* @property {Number} 			duration				Number | 周期 (默认 2000)
	* @property {Number} 			alpha					Number | 透明度 (默认 0.5)
	* <AUTHOR>
	* @date 2025-04-16 12:10:21
	*/
   
	export default {
		name: "ux-shimmer",
		expose: ['show', 'hide'],
		data() {
			return {
				showShimmer: true
			}
		},
		props: {
			duration: {
				type: Number,
				default: 2000
			},
			alpha: {
				type: Number,
				default: 0.5
			},
		},
		methods: {
			show() {
				this.showShimmer = true
			},
			hide() {
				this.showShimmer = false
			}
		},
	}
</script>

<style scoped>
	.ux-shimmer {
		width: 100%;
		height: 100%;
	}
	
	.shimmer-item {
		
	}

	.shimmer-item::after {
		content: '';
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg,
				transparent 25%,
				rgba(255, 255, 255, 0.6) 50%,
				transparent 75%);
		animation: shimmer 2s linear infinite;
	}

	@keyframes shimmer {
		0% {
		  left: -100%;
		}
		100% {
		  left: 100%;
		}
	}
</style>