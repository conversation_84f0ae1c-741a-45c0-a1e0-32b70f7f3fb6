<template>
	<view 
		class="form-group" 
		:class="getGroupStyleClass(group.Class, false)"
		v-if="visibleFields.length > 0"
	>
		<!-- 分组标题 -->
		<view v-if="group.Title" class="group-title" :class="titleClass">
			<text class="title-text">{{ group.Title }}</text>
			<text v-if="group.Description" class="title-description">{{ group.Description }}</text>
		</view>
		
		<!-- 分组描述 -->
		<view v-if="group.Description && !group.Title" class="group-description">
			<text class="description-text">{{ group.Description }}</text>
		</view>
		
		<!-- 分组字段 -->
		<view class="group-fields" :class="'cols-' + (cols || 1)">
			<FormField
				v-for="field in visibleFields"
				:key="field.Id"
				:field="field"
				:dataModel="modelValue"
				:titleClass="titleClass"
				:classes="getGroupItemClass(field)"
				:labelCell="24"
				@change="handleFieldChange"
			/>
		</view>
		
		<!-- 分组底部提示 -->
		<view v-if="group.BottomTip" class="group-bottom-tip">
			<text class="bottom-tip-text">{{ group.BottomTip }}</text>
		</view>
	</view>
</template>

<script setup lang="uts">
	import { computed } from "vue"
	import FormField from "./FormField.uvue"
	import { getGroupStyleClass, getGroupItemClass } from "../common/formGroupStyles.uts"
	
	// 定义组件选项
	defineOptions({
		name: 'FormGroup'
	})
	
	// 定义事件
	const emit = defineEmits<{
		change: [data: UTSJSONObject]
		fieldChange: [data: UTSJSONObject]
	}>()
	
	// 定义props - 严格按照Vue版本
	const props = defineProps({
		// 分组对象
		group: {
			type: Object as PropType<UTSJSONObject>,
			required: true
		},
		// 表单数据模型
		modelValue: {
			type: Object as PropType<UTSJSONObject>,
			required: true
		},
		// 验证规则
		rules: {
			type: Object as PropType<UTSJSONObject>,
			default: () => ({} as UTSJSONObject)
		},
		// 标题样式类
		titleClass: {
			type: String,
			default: ''
		},
		// 列数
		cols: {
			type: Number,
			default: 1
		}
	})
	
	// 计算属性 - 可见字段
	const visibleFields = computed((): UTSJSONObject[] => {
		const fields = props.group['Fields'] as UTSJSONObject[] || []
		return fields.filter((field: UTSJSONObject): boolean => {
			return !(field['Hide'] as boolean)
		})
	})
	
	// 方法
	const getFieldValue = (fieldId: string): any => {
		return props.modelValue[fieldId]
	}
	
	const updateFieldValue = (fieldId: string, value: any) => {
		const newModel = { ...props.modelValue }
		newModel[fieldId] = value
		emit('change', newModel)
	}
	
	const handleFieldChange = (data: UTSJSONObject) => {
		console.log('FormGroup handleFieldChange:', data)
		
		// 触发字段变化事件
		emit('fieldChange', data)
		
		// 触发整体变化事件
		emit('change', props.modelValue)
	}
	
	// 暴露方法
	defineExpose({
		getFieldValue,
		updateFieldValue,
		validate: () => {
			// 验证逻辑
			return true
		},
		clearErrors: () => {
			// 清除错误逻辑
		}
	})
</script>

<style lang="scss">
	.form-group {
		margin-bottom: 24px;
		
		&:last-child {
			margin-bottom: 0;
		}
		
		.group-title {
			margin-bottom: 16px;
			padding-bottom: 8px;
			border-bottom: 1px solid #e8e8e8;
			
			.title-text {
				font-size: 16px;
				font-weight: 600;
				color: #333;
				line-height: 1.5;
			}
			
			.title-description {
				display: block;
				font-size: 14px;
				color: #666;
				margin-top: 4px;
				line-height: 1.4;
			}
		}
		
		.group-description {
			margin-bottom: 16px;
			
			.description-text {
				font-size: 14px;
				color: #666;
				line-height: 1.4;
			}
		}
		
		.group-fields {
			display: flex;
			flex-wrap: wrap;
			gap: 16px;
			
			&.cols-1 {
				flex-direction: column;
				
				.form-group-item {
					width: 100%;
				}
			}
			
			&.cols-2 {
				.form-group-item {
					width: calc(50% - 8px);
					
					&.col-span-full {
						width: 100%;
					}
				}
			}
			
			&.cols-3 {
				.form-group-item {
					width: calc(33.333% - 11px);
					
					&.col-span-full {
						width: 100%;
					}
					
					&.col-span-2 {
						width: calc(66.666% - 5px);
					}
				}
			}
			
			&.cols-4 {
				.form-group-item {
					width: calc(25% - 12px);
					
					&.col-span-full {
						width: 100%;
					}
					
					&.col-span-2 {
						width: calc(50% - 8px);
					}
					
					&.col-span-3 {
						width: calc(75% - 4px);
					}
				}
			}
		}
		
		.group-bottom-tip {
			margin-top: 16px;
			padding: 12px;
			background-color: #f8f9fa;
			border-radius: 4px;
			border-left: 4px solid #007bff;
			
			.bottom-tip-text {
				font-size: 14px;
				color: #666;
				line-height: 1.4;
			}
		}
	}
	
	// 分组样式变体
	.form-group-card {
		background: #fff;
		border: 1px solid #e8e8e8;
		border-radius: 8px;
		padding: 20px;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
	}
	
	.form-group-border {
		border: 1px solid #d9d9d9;
		border-radius: 4px;
		padding: 16px;
	}
	
	.form-group-background {
		background-color: #fafafa;
		border-radius: 4px;
		padding: 16px;
	}
	
	.form-group-collapse {
		border: 1px solid #d9d9d9;
		border-radius: 4px;
		
		.group-title {
			margin: 0;
			padding: 12px 16px;
			background-color: #f5f5f5;
			border-bottom: 1px solid #d9d9d9;
			cursor: pointer;
			
			&:hover {
				background-color: #e8e8e8;
			}
		}
		
		.group-fields {
			padding: 16px;
		}
		
		.group-bottom-tip {
			margin: 0 16px 16px;
		}
	}
	
	// 字段项样式
	.col-span-full {
		width: 100% !important;
	}
	
	.col-span-2 {
		// 在不同列数下的宽度由父容器的cols类控制
	}
	
	.col-span-3 {
		// 在不同列数下的宽度由父容器的cols类控制
	}
	
	.col-span-4 {
		// 在不同列数下的宽度由父容器的cols类控制
	}
	
	.col-span-6 {
		// 在不同列数下的宽度由父容器的cols类控制
	}
	
	.col-span-12 {
		// 在不同列数下的宽度由父容器的cols类控制
	}
</style>