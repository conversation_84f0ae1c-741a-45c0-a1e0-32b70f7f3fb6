<template>
	<view
		class="bg-fff border-1 border-e8e8e8 rounded-12 p-20 shadow-sm"
		:class="getGroupStyleClass(group.Class, false)"
		v-if="visibleFields.length > 0"
	>
		<!-- 分组标题 -->
		<view v-if="group.Title" class="mb-16 pb-12 border-b-1 border-f0f0f0" :class="titleClass">
			<text class="fs-18 fw-600 c-333">{{ group.Title }}</text>
			<text v-if="group.Description" class="block fs-14 c-666 mt-4">{{ group.Description }}</text>
		</view>

		<!-- 分组描述 -->
		<view v-if="group.Description && !group.Title" class="mb-16 p-12 bg-f8f9fa rounded-8">
			<text class="fs-14 c-666 lh-20">{{ group.Description }}</text>
		</view>

		<!-- 分组字段 -->
		<view class="group-fields" :class="'cols-' + (cols || 1)">
			<FormField
				v-for="field in visibleFields"
				:key="field.Id"
				:field="field"
				:modelValue="modelValue"
				:titleClass="titleClass"
				:classes="getGroupItemClass(field)"
				:labelCell="24"
				@change="handleFieldChange"
			/>
		</view>

		<!-- 分组底部提示 -->
		<view v-if="group.BottomTip" class="mt-16 pt-12 border-t-1 border-f0f0f0">
			<text class="fs-14 c-666 lh-20">{{ group.BottomTip }}</text>
		</view>
	</view>
</template>

<script setup lang="uts">
	import { computed } from "vue"
	import FormField from "./FormField.uvue"
	import { getGroupStyleClass, getGroupItemClass } from "../common/formGroupStyles.uts"
	
	// 定义组件选项
	defineOptions({
		name: 'FormGroup'
	})
	
	// 定义事件
	const emit = defineEmits<{
		change: [data: UTSJSONObject]
		fieldChange: [data: UTSJSONObject]
	}>()
	
	// 定义props - 严格按照Vue版本
	const props = defineProps({
		// 分组对象
		group: {
			type: Object as PropType<UTSJSONObject>,
			required: true
		},
		// 表单数据模型
		modelValue: {
			type: Object as PropType<UTSJSONObject>,
			required: true
		},
		// 验证规则
		rules: {
			type: Object as PropType<UTSJSONObject>,
			default: () => ({} as UTSJSONObject)
		},
		// 标题样式类
		titleClass: {
			type: String,
			default: ''
		},
		// 列数
		cols: {
			type: Number,
			default: 1
		}
	})
	
	// 计算属性 - 可见字段
	const visibleFields = computed((): UTSJSONObject[] => {
		const fields = props.group['Fields'] as UTSJSONObject[] || []
		return fields.filter((field: UTSJSONObject): boolean => {
			return !(field['Hide'] as boolean)
		})
	})
	
	// 方法
	const getFieldValue = (fieldId: string): any => {
		return props.modelValue[fieldId]
	}
	
	const updateFieldValue = (fieldId: string, value: any) => {
		const newModel = { ...props.modelValue }
		newModel[fieldId] = value
		emit('change', newModel)
	}
	
	const handleFieldChange = (data: UTSJSONObject) => {
		console.log('FormGroup handleFieldChange:', data)
		
		// 触发字段变化事件
		emit('fieldChange', data)
		
		// 触发整体变化事件
		emit('change', props.modelValue)
	}
	
	// 暴露方法
	defineExpose({
		getFieldValue,
		updateFieldValue,
		validate: () => {
			// 验证逻辑
			return true
		},
		clearErrors: () => {
			// 清除错误逻辑
		}
	})
</script>

<style lang="scss">
	/* 使用autocss语法，减少自定义样式 */

	/* 兼容性样式 */
	.bg-fff {
		background-color: #ffffff;
	}

	.bg-f8f9fa {
		background-color: #f8f9fa;
	}

	.border-1 {
		border-width: 1px;
		border-style: solid;
	}

	.border-e8e8e8 {
		border-color: #e8e8e8;
	}

	.border-f0f0f0 {
		border-color: #f0f0f0;
	}

	.border-b-1 {
		border-bottom-width: 1px;
		border-bottom-style: solid;
	}

	.border-t-1 {
		border-top-width: 1px;
		border-top-style: solid;
	}

	.rounded-12 {
		border-radius: 12px;
	}

	.rounded-8 {
		border-radius: 8px;
	}

	.shadow-sm {
		box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
	}

	.fs-18 {
		font-size: 18px;
	}

	.fs-14 {
		font-size: 14px;
	}

	.fw-600 {
		font-weight: 600;
	}

	.c-333 {
		color: #333333;
	}

	.c-666 {
		color: #666666;
	}

	.lh-20 {
		line-height: 1.4;
	}

	.block {
		display: block;
	}
		
		.group-fields {
			display: flex;
			flex-wrap: wrap;
			gap: 16px;
			
			&.cols-1 {
				flex-direction: column;
				
				.form-group-item {
					width: 100%;
				}
			}
			
			&.cols-2 {
				.form-group-item {
					width: calc(50% - 8px);
					
					&.col-span-full {
						width: 100%;
					}
				}
			}
			
			&.cols-3 {
				.form-group-item {
					width: calc(33.333% - 11px);
					
					&.col-span-full {
						width: 100%;
					}
					
					&.col-span-2 {
						width: calc(66.666% - 5px);
					}
				}
			}
			
			&.cols-4 {
				.form-group-item {
					width: calc(25% - 12px);
					
					&.col-span-full {
						width: 100%;
					}
					
					&.col-span-2 {
						width: calc(50% - 8px);
					}
					
					&.col-span-3 {
						width: calc(75% - 4px);
					}
				}
			}
		}
		
		.group-bottom-tip {
			margin-top: 16px;
			padding: 12px;
			background-color: #f8f9fa;
			border-radius: 4px;
			border-left: 4px solid #007bff;
			
			.bottom-tip-text {
				font-size: 14px;
				color: #666;
				line-height: 1.4;
			}
		}
	}
	
	// 分组样式变体
	.form-group-card {
		background: #fff;
		border: 1px solid #e8e8e8;
		border-radius: 8px;
		padding: 20px;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
	}
	
	.form-group-border {
		border: 1px solid #d9d9d9;
		border-radius: 4px;
		padding: 16px;
	}
	
	.form-group-background {
		background-color: #fafafa;
		border-radius: 4px;
		padding: 16px;
	}
	
	.form-group-collapse {
		border: 1px solid #d9d9d9;
		border-radius: 4px;
		
		.group-title {
			margin: 0;
			padding: 12px 16px;
			background-color: #f5f5f5;
			border-bottom: 1px solid #d9d9d9;
			cursor: pointer;
			
			&:hover {
				background-color: #e8e8e8;
			}
		}
		
		.group-fields {
			padding: 16px;
		}
		
		.group-bottom-tip {
			margin: 0 16px 16px;
		}
	}
	
	// 字段项样式
	.col-span-full {
		width: 100% !important;
	}
	
	.col-span-2 {
		// 在不同列数下的宽度由父容器的cols类控制
	}
	
	.col-span-3 {
		// 在不同列数下的宽度由父容器的cols类控制
	}
	
	.col-span-4 {
		// 在不同列数下的宽度由父容器的cols类控制
	}
	
	.col-span-6 {
		// 在不同列数下的宽度由父容器的cols类控制
	}
	
	.col-span-12 {
		// 在不同列数下的宽度由父容器的cols类控制
	}
</style>