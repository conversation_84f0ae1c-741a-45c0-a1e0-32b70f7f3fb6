<template>
	<!-- #ifdef APP-ANDROID || APP-IOS -->
	<waterflow :id="myId" class="ux-waterfall" :style="style"
		:cross-axis-count="crossAxisCount"
		:cross-axis-gap="crossAxisGap"
		:main-axis-gap="mainAxisGap"
		:padding="padding"
		:associative-container="associativeContainer"
		:bounces="bounces"
		:upper-threshold="upperThreshold"
		:lower-threshold="lowerThreshold"
		:scroll-top="_scrollTop"
		:scroll-into-view="scrollIntoView"
		:scroll-with-animation="scrollWithAnimation"
		:refresher-enabled="refresherEnabled"
		:refresher-threshold="refresherThreshold"
		:refresher-max-drag-distance="refresherMaxDragDistance"
		refresher-default-style="none"
		:refresher-background="refresherBackground"
		:refresher-triggered="refresherTriggered"
		:show-scrollbar="showScrollbar"
		:custom-nested-scroll="customNestedScroll"
		@refresherpulling="refresherpulling"
		@refresherrefresh="refresherrefresh"
		@refresherrestore="refresherrestore"
		@refresherabort="refresherabort"
		@scrolltoupper="scrolltoupper"
		@scrolltolower="scrolltolower"
		@scroll="scroll"
		@scrollend="scrollend">

		<flow-item v-if="refresherEnabled" slot="refresher" :type="10000" style="width: 100%;">
			<ux-refresher :states="refresherStates" :state="refresherState"></ux-refresher>
		</flow-item>
		
		<slot></slot>

		<flow-item v-if="loadmoreEnabled" slot="load-more" :type="9998">
			<ux-loadmore :states="loadmoreStates" :state="loadmoreState"></ux-loadmore>
		</flow-item>
	</waterflow>
	<!-- #endif -->
	<!-- #ifndef APP-ANDROID || APP-IOS -->
	<scroll-view :id="myId" class="ux-waterfall" :style="style"
		direction="vertical"
		:associative-container="associativeContainer"
		:bounces="bounces"
		:upper-threshold="upperThreshold"
		:lower-threshold="lowerThreshold"
		:scroll-top="_scrollTop"
		:scroll-into-view="scrollIntoView"
		:scroll-with-animation="scrollWithAnimation"
		:refresher-enabled="refresherEnabled"
		:refresher-threshold="refresherThreshold"
		:refresher-max-drag-distance="refresherMaxDragDistance"
		refresher-default-style="none"
		:refresher-background="refresherBackground"
		:refresher-triggered="refresherTriggered" 
		:show-scrollbar="showScrollbar"
		:custom-nested-scroll="customNestedScroll"
		@refresherpulling="refresherpulling"
		@refresherrefresh="refresherrefresh"
		@refresherrestore="refresherrestore"
		@refresherabort="refresherabort"
		@scrolltoupper="scrolltoupper"
		@scrolltolower="scrolltolower"
		@scroll="scroll"
		@scrollend="scrollend">

		<view class="ux-waterfall__warp" :style="{'height': height + 'px'}">
			<slot></slot>
		</view>

		<ux-loadmore v-if="loadmoreEnabled" :states="loadmoreStates" :state="loadmoreState"></ux-loadmore>

		<view v-if="refresherEnabled" slot="refresher" style="width: 100%;">
			<ux-refresher :states="refresherStates" :state="refresherState"></ux-refresher>
		</view>
	</scroll-view>
	<!-- #endif -->
	
	<ux-backtop v-if="backtop && $slots['backtop'] == null" ref="uxBacktopRef" @click="toTop"></ux-backtop>
	<ux-backtop v-if="backtop && $slots['backtop'] != null" ref="uxBacktopRef" @click="toTop">
		<slot name="backtop"></slot>
	</ux-backtop>
</template>

<script setup>
	/**
	 * WaterFall 瀑布流
	 * @description 支持配置间距，自动计算子项高度，支持子项自定义高度
	 * @demo pages/component/waterfall.uvue
	 * @tutorial https://www.uxframe.cn/component/waterfall.html
	 * @property {Slot}			default  											Slot | 默认slot
	 * @property {Slot}			refresher  											Slot | refresher slot
	 * @property {Slot}			loadmore  											Slot | loadmore slot
	 * @property {Slot}			backtop  											Slot | backtop slot
	 * @property {Number}		crossAxisCount										Number | 交叉轴元素数量 (默认 2)
	 * @property {Number}		mainAxisGap											Number | 主轴方向间隔 (默认 0）
	 * @property {Number}		crossAxisGap										Number | 交叉轴方向间隔 (默认 0）
	 * @property {Array}		padding												Number[] | 长度为 4 的数组，按 top、right、bottom、left 顺序指定内边距 (默认 [0,0,0,0]）
	 * @property {String}		associativeContainer = [nested-scroll-view]			String | 关联的滚动容器
	 * @value nested-scroll-view	嵌套滚动
	 * @property {Boolean}  	bounces = [true|false]  				Boolean | 控制是否回弹效果 (默认 true)
	 * @property {Number}		upperThreshold  						Number | 距顶部/左边多远时（单位px），触发 scrolltoupper 事件（默认 50 ）
	 * @property {Number} 		lowerThreshold 							Number | 距底部/右边多远时（单位px），触发 scrolltolower 事件 （默认 50 ）
	 * @property {Number} 		scrollTop 								Number | 设置竖向滚动条位置 （默认 0 ）
	 * @property {String}  		scrollIntoView							String | 值应为某子元素id（id不能以数字开头）。设置哪个方向可滚动，则在哪个方向滚动到该元素
	 * @property {Boolean}  	scrollWithAnimation = [true|false]  	Boolean | 是否在设置滚动条位置时使用滚动动画，设置false没有滚动动画 (默认 true)
	 * @property {Boolean}  	refresherEnabled = [true|false]  		Boolean | 开启下拉刷新 (默认 false)
	 * @property {Number}  		refresherThreshold  					Number | 设置下拉刷新阈值（默认 45 ）
	 * @property {Number}		refresherMaxDragDistance				Number | 设置下拉最大拖拽距离（单位px）（默认 80 ）
	 * @property {String}		refresherBackground						String | 设置下拉刷新区域背景颜色 (默认 transparent)
	 * @property {Boolean}		refresherTriggered = [true|false]		Boolean | 设置当前下拉刷新状态，true 表示下拉刷新已经被触发，false 表示下拉刷新未被触发 (默认 false)
	 * @property {Array}		refresherStates							Array | 下拉刷新状态文案
	 * @property {Boolean}  	loadmoreEnabled = [true|false]  		Boolean | 开启上拉加载 (默认 false)
	 * @property {Array}		loadmoreStates							Array | 上拉加载状态文案
	 * @property {Boolean}		showScrollbar = [true|false]			Boolean | 控制是否出现滚动条 (默认 false)
	 * @property {Boolean}		customNestedScroll = [true|false]		Boolean | 子元素是否开启嵌套滚动 将滚动事件与父元素协商处理 (默认 false)
	 * @property {Boolean}		disabled = [true|false]					Boolean | 禁止滚动 (默认 false)
	 * @property {String}		background								String | 背景颜色 (默认 transparent)
	 * @property {Array}		xstyle									Array<any> | 自定义样式
	 * @event {Function}		refresherpulling						Function | 下拉刷新控件被下拉
	 * @event {Function}		refresherrefresh						Function | 下拉刷新被触发
	 * @event {Function}		refresherrestore						Function | 下拉刷新被复位
	 * @event {Function}		refresherabort							Function | 下拉刷新被中止
	 * @event {Function}		loadmore								Function | 上拉加载触发
	 * @event {Function}		scrolltoupper							Function | 滚动到顶部/左边，会触发 scrolltoupper 事件
	 * @event {Function}		scrolltolower							Function | 滚动到底部/右边，会触发 scrolltolower 事件
	 * @event {Function}		scroll									Function | 滚动时触发
	 * @event {Function}		scrollend								Function | 滚动结束时触发
	 * <AUTHOR>
	 * @date 2023-12-16 01:12:28
	 */

	import { $ux } from '../../index'
	import { UxWaterfallItemInfo } from '../../libs/types/types.uts'
	import { useResize } from '../../libs/use/resize'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'

	defineOptions({
		name: 'ux-waterfall',
		mixins: [xstyleMixin]
	})

	const emit = defineEmits([
		'refresherpulling',
		'refresherrefresh',
		'refresherrestore',
		'refresherabort',
		'loadmore',
		'scrolltoupper',
		'scrolltolower',
		'scroll',
		'scrollend'])

	const props = defineProps({
		crossAxisCount: {
			type: Number,
			default: 2
		},
		mainAxisGap: {
			type: Number,
			default: 0
		},
		crossAxisGap: {
			type: Number,
			default: 0
		},
		padding: {
			type: Array as PropType<number[]>,
			default: () => [0,0,0,0] as number[]
		},
		associativeContainer: {
			type: String,
			default: ''
		},
		bounces: {
			type: Boolean,
			default: true
		},
		upperThreshold: {
			type: Number,
			default: 50
		},
		lowerThreshold: {
			type: Number,
			default: 50
		},
		scrollTop: {
			type: Number,
			default: 0
		},
		showScrollbar: {
			type: Boolean,
			default: false,
		},
		scrollIntoView: {
			type: String,
			default: '',
		},
		scrollWithAnimation: {
			type: Boolean,
			default: true,
		},
		refresherEnabled: {
			type: Boolean,
			default: false,
		},
		refresherThreshold: {
			type: Number,
			default: 45,
		},
		refresherMaxDragDistance: {
			type: Number,
			default: 80,
		},
		refresherBackground: {
			type: String,
			default: 'transparent',
		},
		refresherTriggered: {
			type: Boolean,
			default: false,
		},
		refresherStates: {
			type: Array as PropType<Array<string>>,
			default: () : string[] => {
				return ['下拉刷新', '释放刷新', '刷新中...', '刷新成功']
			},
		},
		loadmoreEnabled: {
			type: Boolean,
			default: false,
		},
		loadmoreStates: {
			type: Array as PropType<Array<string>>,
			default: () : string[] => {
				return ['加载中...', '-- 我也是有底线的 --']
			},
		},
		customNestedScroll: {
			type: Boolean,
			default: false,
		},
		backtop: {
			type: Boolean,
			default: false
		},
		background: {
			type: String,
			default: 'transparent',
		}
	})

	let instance = getCurrentInstance()?.proxy

	const myId = `ux-waterfall-${$ux.Random.uuid()}`
	const width = ref(0)
	const height = ref(0)
	const scrollY = ref(0)
	const refresherState = ref(0)
	const loadmoreState = ref(0)
	const _scrollTop = ref(0)
	const uxBacktopRef = ref<UxBacktopComponentPublicInstance | null>(null)
	
	let nodes = [] as UxWaterfallItemInfo[]

	provide('scrollY', scrollY)

	const crossAxisCount = computed(() : number => {
		return props.crossAxisCount
	})

	const mainAxisGap = computed(() : number => {
		return props.mainAxisGap
	})

	const crossAxisGap = computed(() : number => {
		return props.crossAxisGap
	})

	provide('width', width)
	provide('crossAxisCount', crossAxisCount)
	provide('mainAxisGap', mainAxisGap)
	provide('crossAxisGap', crossAxisGap)

	const style = computed(() : Map<string, any> => {
		let css = new Map<string, any>()

		css.set('background-color', props.background)

		return css
	})

	const __scrollTop = computed(() => {
		return props.scrollTop
	})

	watch(__scrollTop, () => {
		_scrollTop.value = __scrollTop.value
	})

	const refresherTriggered = computed(() : boolean => {
		return props.refresherTriggered
	})

	watch(refresherTriggered, (a : boolean, b : boolean) => {
		if (a == false && b == true) {
			// 刷新完成
			refresherState.value = 3
		}
	})

	function scroll(e : ScrollEvent) {
		scrollY.value = e.detail.scrollTop

		// 显示置顶不卡顿
		if (props.backtop) {
			uxBacktopRef.value?.$callMethod('setShow', scrollY.value > 300)
		}

		emit('scroll', e)
	}

	function scrollend(e : ScrollEvent) {
		emit('scrollend', e)
	}

	function refresherpulling(e : RefresherEvent) {
		// 释放刷新
		if (refresherState.value == 0 || refresherState.value == 1) {
			if (e.detail.dy > props.refresherThreshold) {
				refresherState.value = 1
			} else {
				refresherState.value = 0
			}
		}

		emit('refresherpulling', e)
	}

	function refresherrefresh(e : RefresherEvent) {
		// 刷新中
		refresherState.value = 2

		emit('refresherrefresh', e)
	}

	function refresherrestore(e : RefresherEvent) {
		emit('refresherrestore', e)

		// 刷新结束
		setTimeout(() => {
			refresherState.value = 0
		}, 50);
	}

	function refresherabort(e : RefresherEvent) {
		emit('refresherabort', e)

		// 刷新结束
		setTimeout(() => {
			refresherState.value = 0
		}, 50);
	}

	function scrolltoupper(e : ScrollToUpperEvent) {
		emit('scrolltoupper', e)
	}

	function scrolltolower(e : ScrollToLowerEvent) {
		// 上拉触底加载
		if (props.loadmoreEnabled) {
			loadmoreState.value = 1
			emit('loadmore', e)
		}

		emit('scrolltolower', e)
	}

	function loadSuccess(lastPage : boolean) {
		loadmoreState.value = lastPage ? 2 : 0
	}

	function toTop() {
		_scrollTop.value = -1
		nextTick(() => {
			_scrollTop.value = 0
		})
	}

	function getHeight(heights: number[][], col: number) {
		return heights.length == 0 || heights[col].length == 0 ? 0 : heights[col].reduce((a: number, b: number): number => a + b)
	}

	function calcHeight() {
		let heights = [] as number[][]
		for (let i = 0; i < crossAxisCount.value; i++) {
			heights.push([])
		}
		
		for (let i = 0; i < nodes.length; i++) {
			const node = nodes[i]
			
			let row = Math.floor(i / crossAxisCount.value)
			let col = i % crossAxisCount.value
			let left = node.width * col + crossAxisGap.value * (col + 1)
			let top = getHeight(heights, col) + (row + 1) * mainAxisGap.value
			
			node.left = left
			node.top = top
			
			// #ifdef APP
			node.node.$callMethod('updateData', left, top)
			// #endif
			// #ifndef APP
			node.node._.exposed.updateData(left, top)
			// #endif
			
			heights[col].push(node.height)
		}
		
		let _h = 0
		for (let i = 0; i < crossAxisCount.value; i++) {
			let h = getHeight(heights, i)
			if(h > _h) {
				height.value = h
				_h = h
			}
		}
	}

	function register(item : UxWaterfallItemInfo) {
		let index = nodes.findIndex((el : UxWaterfallItemInfo) : boolean => el.id == item.id)
		if (index != -1) {
			nodes.splice(index, 1, item)
		} else {
			nodes.push(item)
		}
		
		calcHeight()
	}

	function unregister(id : string) {
		let index = nodes.findIndex((el : UxWaterfallItemInfo) : boolean => el.id == id)
		if (index != -1) {
			nodes.splice(index, 1)
		}

		calcHeight()
	}

	function init() {
		uni.createSelectorQuery().in(instance).select(`#${myId}`).boundingClientRect((rect) => {
			let node = rect as NodeInfo
			width.value = node.width ?? uni.getWindowInfo().windowWidth
		}).exec()
	}

	onMounted(() => {
		// #ifndef APP-ANDROID || APP-IOS
		init()

		useResize(uni.getElementById(myId), () => {
			init()
		})
		// #endif
	})

	defineExpose({
		register,
		unregister,
		loadSuccess
	})
</script>


<style lang="scss">
	.ux-waterfall {
		/* #ifndef MP */
		flex: 1;
		/* #endif */
		/* #ifdef MP */
		width: 100%;
		height: 100%;
		/* #endif */
		position: relative;
		
		&__warp {
			position: relative;
			width: 100%;
		}
	}
</style>