<script setup>
import Mobile from '../../src/components/mobile/index.vue'
</script>

<Mobile url="https://web.uxframe.cn/#pages/component/actionsheet?title=Actionsheet"></Mobile>

# Actionsheet
> 组件类型：UxActionsheetComponentPublicInstance

支持菜单自定义样式

## 平台兼容性

### UniApp X

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    √    |  √  |  √  |    √    |  √  |

### UniApp Vue Nvue

| Android | iOS | web | 鸿蒙 Next | 小程序 |
|:-------:|:---:|:---:|:-------:|:---:|
|    x    |  x  |  √  |    x    |  x  |

## Props

| 属性名   | 类型 | 默认值 | 说明 |
|:------:|:----:|:----:|:----:|
| menus | Array |  | 菜单 |
| [theme](#theme) | String | primary | 主题 |
| title | String |  | 标题 |
| titleSize | Any | 15 | 标题大小 |
| titleColor | String | #999 | 标题颜色 |
| [titleDarkColor](#titleDarkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| titleBold | Boolean | false | 标题加粗 |
| menuSize | Any | 15 | 按钮文字大小 |
| menuColor | String | #333 | 按钮文字颜色 |
| [menuDarkColor](#menuDarkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| cancelText | String | 取消 | 取消按钮文字 |
| cancelSize | Any | 15 | 取消按钮文字大小 |
| cancelColor | String | #999 | 取消按钮文字颜色 |
| [cancelDarkColor](#cancelDarkColor) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| background | String |  | 背景颜色 |
| [backgroundDark](#backgroundDark) | String |  | 深色none-不显示，auto-自动适配深色模式，其他-颜色 |
| showClose | Boolean | false | 自动关闭 |
| showCancel | Boolean | true | 点击关闭 |
| showBlur | Boolean | false | 开启模糊背景 |
| blurRadius | Number | 10 | 模糊半径 |
| blurColor | String | rgba(10,10,10,0.3) | 模糊颜色 |
| maxHeight | Any | 500 | 最大高度 |
| radius | Any | 20 | 圆角 |
| space | Any | 15 | 间隙 |
| opacity | Number | `$ux.Conf.maskAlpha` | 遮罩透明度0-1 |
| [touchable](#touchable) | Boolean | false | 允许滑动关闭 |
| [maskClose](#maskClose) | Boolean | true | 遮罩层关闭 |
| disabled | Boolean | false | 是否禁用 |

### [theme](#theme)

| 值   | 说明 |
|:------:|:----:|
| text | 文字
 |
| info | 默认
 |
| primary | 主要
 |
| success | 成功
 |
| warning | 警告
 |
| error | 错误
 |

### [titleDarkColor](#titleDarkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [menuDarkColor](#menuDarkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [cancelDarkColor](#cancelDarkColor)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [backgroundDark](#backgroundDark)

| 值   | 说明 |
|:------:|:----:|
| none不显示
 |  |
| auto自动适配深色模式
 |  |
| color其他颜色
 |  |

### [touchable](#touchable)

| 值   | 说明 |
|:------:|:----:|
| true | 
 |
| false | 
 |

### [maskClose](#maskClose)

| 值   | 说明 |
|:------:|:----:|
| true | 
 |
| false | 
 |

## Events

| 事件名   | 说明 | 参数 |
|:------:|:----:|:----:|
| click | 按钮点击时触发 |  |
| cancel | 取消按钮点击时触发 |  |
| close | 关闭按钮点击时触发发 |  |
  
  