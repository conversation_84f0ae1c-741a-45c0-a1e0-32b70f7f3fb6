import { HtmlParser } from '../libs/parser.uts'
import { StyleUtils } from '../libs/style-utils.uts'

/**
 * mp-html组件测试
 */
export class MpHtmlTest {
  
  /**
   * 测试HTML解析器
   */
  static testParser(): boolean {
    console.log('开始测试HTML解析器...')
    
    try {
      // 测试基本HTML解析
      const parser = new HtmlParser('<p>Hello <strong>World</strong>!</p>')
      const nodes = parser.parse()
      
      if (nodes.length != 1) {
        console.error('解析结果数量错误')
        return false
      }
      
      const pNode = nodes[0]
      if (pNode.type != 'element' || pNode.name != 'p') {
        console.error('p标签解析错误')
        return false
      }
      
      if (pNode.children == null || pNode.children.length != 2) {
        console.error('p标签子节点数量错误')
        return false
      }
      
      console.log('HTML解析器测试通过')
      return true
    } catch (error: any) {
      console.error('HTML解析器测试失败:', error)
      return false
    }
  }
  
  /**
   * 测试样式工具
   */
  static testStyleUtils(): boolean {
    console.log('开始测试样式工具...')
    
    try {
      // 测试样式转换
      const style = {
        'color': 'red',
        'font-size': '16px',
        'margin': '10px 20px'
      }
      
      const convertedStyle = StyleUtils.convertStyle(style)
      
      if (convertedStyle.getString('color') != 'red') {
        console.error('颜色转换错误')
        return false
      }
      
      if (convertedStyle.getString('fontSize') != '16px') {
        console.error('字体大小转换错误')
        return false
      }
      
      // 测试默认样式
      const defaultStyle = StyleUtils.getDefaultStyle('h1')
      if (defaultStyle.getString('fontSize') != '32px') {
        console.error('默认样式获取错误')
        return false
      }
      
      console.log('样式工具测试通过')
      return true
    } catch (error: any) {
      console.error('样式工具测试失败:', error)
      return false
    }
  }
  
  /**
   * 运行所有测试
   */
  static runAllTests(): boolean {
    console.log('开始运行mp-html组件测试...')
    
    const parserTest = this.testParser()
    const styleTest = this.testStyleUtils()
    
    const allPassed = parserTest && styleTest
    
    if (allPassed) {
      console.log('所有测试通过！')
    } else {
      console.log('部分测试失败！')
    }
    
    return allPassed
  }
}