import DCloudUTSFoundation
import DCloudUniappRuntime
import UIKit

public enum LayoutMode {
    case grid(columns: Int)
    case list(rowHeight: CGFloat)
}

enum GridItem {
    case item(UxMovableItem)
    case upload(UxMovableItem)
}

enum CornerPosition {
    case leftTop
    case leftBottom
    case rightTop
    case rightBottom
}

protocol UxMovableViewDelegate: AnyObject {
    func didSelectAddButton()
    func didUpdatePhotoOrder(items: [UxMovableItem])
}

public class UxMovableView: UIView {
    // MARK: - Public Properties
    // 布局模式
    private var layoutMode: LayoutMode = .grid(columns: 4)

    // 列数
    public var column: Int = 4

    // 间距
    public var spacing: CGFloat = 2

    // 宽高比
    public var aspectRatio: CGFloat = 1

    // 可编辑
    public var editable: Bool = false

    // 多选
    public var multiple: Bool = false

    // 滑动删除
    public var swipe: Bool = false

    // 禁用
    public var disabled: Bool = false
	
	// 点击事件回调
	public var events: ClickEventHandler?
	
	// 委托
    weak var delegate: UxMovableViewDelegate?
	
    // MARK: - Properties
    var collectionView: UICollectionView!
    var items: [GridItem] = []
    var autoScrollLink: CADisplayLink?
    var autoScrollDirection = 0
    var longPressGesture: UILongPressGestureRecognizer!

    // MARK: - Initialization
    init(frame: CGRect, options: UxMovableOptions, events: @escaping ClickEventHandler) {
        super.init(frame: frame)
		
		self.events = events
        self.column = options.column.intValue ?? 0
        self.spacing = CGFloat(options.spacing)
        self.aspectRatio = CGFloat(options.aspectRatio)
        self.editable = options.editable
        self.multiple = options.multiple
        self.swipe = options.swipe
        self.disabled = options.disabled
        if options.layout == "grid" {
            self.layoutMode = .grid(columns: self.column)
        } else {
            self.layoutMode = .list(rowHeight: 45)
        }
		
		_init()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
		_init()
    }
	
	func _init() {
	    setupCollectionView()
	    setupGesture()
	}
	
    // MARK: - Public Methods
    func setLayout(layout: String) {
        if layout == "grid" {
            self.layoutMode = .grid(columns: column)
        } else {
            self.layoutMode = .list(rowHeight: 45)
        }
		
		updateLayout()
    }

    func setColumn(column: Int) {
		if case .list = layoutMode {
		    return
		}
		
        self.column = column
        if case .grid = layoutMode {
            layoutMode = .grid(columns: column)
        }
        updateLayout()
    }

    func setSpacing(spacing: CGFloat) {
        self.spacing = spacing

        if let flowLayout = collectionView.collectionViewLayout as? UICollectionViewFlowLayout {
            flowLayout.minimumInteritemSpacing = self.spacing
            flowLayout.minimumLineSpacing = self.spacing
            flowLayout.sectionInset = UIEdgeInsets(
                top: self.spacing,
                left: self.spacing,
                bottom: self.spacing,
                right: self.spacing
            )
			
            updateLayout()
        }
    }

    func setAspectRatio(aspectRatio: CGFloat) {
        self.aspectRatio = aspectRatio
        updateLayout()
    }

    func setEditable(editable: Bool) {
		if case .list = layoutMode {
		    return
		}
		
        self.editable = editable
        updateLayout()
    }

    func setMultiple(multiple: Bool) {
		if case .list = layoutMode {
		    return
		}
		
        self.multiple = multiple
		
		for (index, item) in self.items.enumerated() {
		    guard
		        let cell = self.collectionView.cellForItem(
		            at: IndexPath(item: index, section: 0)) as? ItemCell
		    else {
		        continue
		    }
		
		    cell.updateMultiple(multiple: multiple)
		}
    }

    func setSwipe(swipe: Bool) {
		if case .grid = layoutMode {
		    return
		}
		
        self.swipe = swipe
    }

    func setDisabled(disabled: Bool) {
        self.disabled = disabled
		
        longPressGesture.isEnabled = !disabled
		
		for (index, item) in self.items.enumerated() {
		    guard
		        let cell = self.collectionView.cellForItem(
		            at: IndexPath(item: index, section: 0)) as? ItemCell
		    else {
		        continue
		    }
		
		    cell.updateInputView(disabled: disabled)
		}
    }

    func setData(_ data: [UxMovableItem]) {
        items = data.map { $0.upload == nil ? .item($0) : .upload($0) }

        DispatchQueue.main.async { [weak self] in
            self?.collectionView.reloadData()
        }
    }

    func addData(_ data: UxMovableItem) {
        items.append(.item(data))

        DispatchQueue.main.async { [weak self] in
            self?.collectionView.reloadData()
        }
    }

    func delData(_ data: UxMovableItem) {
        guard
            let index = items.firstIndex(where: {
                if case .item(let item) = $0, item.id == data.id {
                    return true
                }
                return false
            })
        else {
            return
        }

        items.remove(at: index)

        DispatchQueue.main.async { [weak self] in
            self?.collectionView.performBatchUpdates(
                {
                    self?.collectionView.deleteItems(at: [IndexPath(item: index, section: 0)])
                }, completion: nil)
        }
    }

    func selectData(_ data: UxMovableItem) {
		if case .list = layoutMode {
		    return
		}
		
        guard
            let index = items.firstIndex(where: {
                if case .item(let item) = $0, item.id == data.id {
                    return true
                }
                return false
            })
        else {
            return
        }

        items[index] = .item(data)
		
        if case .item(var existingItem) = items[index] {
            DispatchQueue.main.async { [weak self] in
                guard
                    let cell = self?.collectionView.cellForItem(
                        at: IndexPath(item: index, section: 0)) as? ItemCell
                else {
                    return
                }
        
                cell.updateSelected(checked: data.selected?.checked ?? false)
            }
        }
    }

    func upload(_ data: UxMovableItem) {
		if case .list = layoutMode {
		    return
		}
		
        guard
            let index = items.firstIndex(where: {
                if case .item(let item) = $0, item.id == data.id {
                    return true
                }
                return false
            })
        else {
            return
        }

        items[index] = .item(data)

        if case .item(var existingItem) = items[index] {
            DispatchQueue.main.async { [weak self] in
                guard
                    let cell = self?.collectionView.cellForItem(
                        at: IndexPath(item: index, section: 0)) as? ItemCell
                else {
                    return
                }

                cell.updateProgress(with: existingItem)
            }
        }
    }

    func updateLayout() {
        updateLayoutForCurrentMode()
        UIView.performWithoutAnimation {
            self.collectionView.reloadData()
        }
    }

    // MARK: - Setup
    func setupCollectionView() {
        let layout = UICollectionViewFlowLayout()
        layout.minimumInteritemSpacing = self.spacing
        layout.minimumLineSpacing = self.spacing

        layout.sectionInset = UIEdgeInsets(
            top: self.spacing,
            left: self.spacing,
            bottom: self.spacing,
            right: self.spacing
        )

        collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.register(ItemCell.self, forCellWithReuseIdentifier: "ItemCell")
        collectionView.register(UploadCell.self, forCellWithReuseIdentifier: "UploadCell")
        collectionView.register(ListItemCell.self, forCellWithReuseIdentifier: "ListItemCell")
		
        collectionView.dataSource = self
        collectionView.delegate = self
        collectionView.backgroundColor = .clear
        collectionView.translatesAutoresizingMaskIntoConstraints = false

        addSubview(collectionView)
        NSLayoutConstraint.activate([
            collectionView.topAnchor.constraint(equalTo: topAnchor),
            collectionView.leadingAnchor.constraint(equalTo: leadingAnchor),
            collectionView.trailingAnchor.constraint(equalTo: trailingAnchor),
            collectionView.bottomAnchor.constraint(equalTo: bottomAnchor),
        ])
    }

    func setupGesture() {
        longPressGesture = UILongPressGestureRecognizer(
            target: self, action: #selector(handleLongPress))
        collectionView.addGestureRecognizer(longPressGesture)
    }

    // MARK: - Layout
    private func updateLayoutForCurrentMode() {
        guard let flowLayout = collectionView.collectionViewLayout as? UICollectionViewFlowLayout
        else { return }

        switch layoutMode {
        case .grid:
            flowLayout.scrollDirection = .vertical
        case .list:
            flowLayout.scrollDirection = .vertical
        }

        setNeedsLayout()
        collectionView.collectionViewLayout.invalidateLayout()
    }

    override public func layoutSubviews() {
        super.layoutSubviews()
        guard let flowLayout = collectionView.collectionViewLayout as? UICollectionViewFlowLayout
        else { return }

        switch layoutMode {
        case .grid(let columns):
            let contentWidth = bounds.width - 2 * self.spacing
            let totalSpacing = CGFloat(columns - 1) * self.spacing
            let itemWidth = (contentWidth - totalSpacing) / CGFloat(columns)
            var itemHeight = itemWidth * aspectRatio
            if editable {
                itemHeight += 40
            }
            flowLayout.itemSize = CGSize(width: itemWidth, height: itemHeight)
        case .list(let rowHeight):
            let contentWidth = bounds.width - 2 * self.spacing
            flowLayout.itemSize = CGSize(width: contentWidth, height: rowHeight)
        }
    }

    // MARK: - Auto Scroll
    func setupAutoScroll() {
        guard autoScrollLink == nil else { return }
        autoScrollLink = CADisplayLink(target: self, selector: #selector(handleAutoScroll))
        autoScrollLink?.add(to: .current, forMode: .common)
    }

    @objc func handleAutoScroll() {
        guard autoScrollDirection != 0 else {
            autoScrollLink?.invalidate()
            autoScrollLink = nil
            return
        }

        let scrollSpeed: CGFloat = 15.0
        let offsetY = collectionView.contentOffset.y
        let contentHeight = collectionView.contentSize.height
        let height = collectionView.frame.height

        var newOffset = offsetY + CGFloat(autoScrollDirection) * scrollSpeed
        newOffset = max(0, min(newOffset, contentHeight - height))

        collectionView.contentOffset.y = newOffset

        if let gesture = longPressGesture {
            handleLongPress(gesture)
        }
    }

    // MARK: - Drag Handling
    private var initialCellCenter: CGPoint?
    @objc func handleLongPress(_ gesture: UILongPressGestureRecognizer) {
        guard !disabled else { return }

        let location = gesture.location(in: collectionView)

        switch gesture.state {
        case .began:
            guard let indexPath = collectionView.indexPathForItem(at: location),
                indexPath.item < items.count - 1,
                let cell = collectionView.cellForItem(at: indexPath)
            else { return }

            initialCellCenter = cell.center
            collectionView.beginInteractiveMovementForItem(at: indexPath)
            setupAutoScroll()

        case .changed:
            let viewLocation = gesture.location(in: self)
            var targetLocation = location

            // 列表模式下修正位置
            if case .list = layoutMode, let initialCenter = initialCellCenter {
                targetLocation.x = initialCenter.x
            }

            let topTrigger: CGFloat = 150
            let bottomTrigger = bounds.height - 150

            autoScrollDirection = 0
            if viewLocation.y < topTrigger {
                autoScrollDirection = -1
            } else if viewLocation.y > bottomTrigger {
                autoScrollDirection = 1
            }

            if autoScrollLink == nil && autoScrollDirection != 0 {
                setupAutoScroll()
            }

            collectionView.updateInteractiveMovementTargetPosition(targetLocation)

        case .ended:
            initialCellCenter = nil
            collectionView.endInteractiveMovement()
            autoScrollDirection = 0

        case .cancelled, .failed:
            initialCellCenter = nil
            collectionView.cancelInteractiveMovement()
            autoScrollDirection = 0

        default:
            break
        }
    }
}

// MARK: - UICollectionView DataSource
extension UxMovableView: UICollectionViewDataSource {
    public func collectionView(
        _ collectionView: UICollectionView, numberOfItemsInSection section: Int
    ) -> Int {
        return items.count
    }

    public func collectionView(
        _ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath
    ) -> UICollectionViewCell {
        switch (layoutMode, items[indexPath.item]) {
		case (.grid, .item(let item)):
		    let cell =
		        collectionView.dequeueReusableCell(withReuseIdentifier: "ItemCell", for: indexPath)
		        as! ItemCell
		    cell.configure(
		        with: item, multiple: self.multiple, editable: self.editable, disabled: self.disabled,
		        cellSize: calculateCellSize(), events: self.events)
		    return cell
        case (.list, .item(let item)):
            let cell =
                collectionView.dequeueReusableCell(withReuseIdentifier: "ListItemCell", for: indexPath)
                as! ListItemCell
            cell.configure(
                with: item, disabled: self.disabled, events: self.events)
            return cell
        case (.grid, .upload(let item)):
            let cell =
                collectionView.dequeueReusableCell(
                    withReuseIdentifier: "UploadCell", for: indexPath) as! UploadCell
            cell.configure(with: item, disabled: self.disabled, cellSize: calculateCellSize(), events: self.events)
            return cell
			
		@unknown default:
		    fatalError("Unhandled cell type combination")
		}
    }

    private func calculateCellSize() -> CGSize {
        guard case .grid(let columns) = layoutMode else { return .zero }

        let contentWidth = bounds.width - 2 * self.spacing
        let totalSpacing = CGFloat(columns - 1) * self.spacing
        let itemWidth = (contentWidth - totalSpacing) / CGFloat(columns)
        let itemHeight = itemWidth * aspectRatio

        return CGSize(width: itemWidth, height: itemHeight)
    }

    public func collectionView(
        _ collectionView: UICollectionView, canMoveItemAt indexPath: IndexPath
    ) -> Bool {
        switch items[indexPath.item] {
        case .upload:
            return false  // 上传按钮不可移动
        case .item:
            return true  // 普通项目可移动
        }
    }

    public func collectionView(
        _ collectionView: UICollectionView, moveItemAt sourceIndexPath: IndexPath,
        to destinationIndexPath: IndexPath
    ) {
        // 双重保护：确保源和目标都是可移动项
        guard case .item = items[sourceIndexPath.item],
            case .item = items[destinationIndexPath.item]
        else {
            collectionView.reloadData()
            return
        }

        let movedObject = items[sourceIndexPath.item]
        items.remove(at: sourceIndexPath.item)
        items.insert(movedObject, at: destinationIndexPath.item)

        delegate?.didUpdatePhotoOrder(
            items: items.compactMap {
                if case .item(let item) = $0 { return item }
                return nil
            })
    }
}

// MARK: - UICollectionView Delegate
extension UxMovableView: UICollectionViewDelegate {
    public func collectionView(
        _ collectionView: UICollectionView,
        targetIndexPathForMoveFromItemAt originalIndexPath: IndexPath,
        toProposedIndexPath proposedIndexPath: IndexPath
    ) -> IndexPath {
        // 找到所有上传按钮的位置
        let uploadIndices = items.enumerated()
            .filter {
                if case .upload = $0.element { return true }
                return false
            }
            .map { $0.offset }

        // 检查目标位置是否是上传按钮的位置
        if uploadIndices.contains(proposedIndexPath.item) {
            // 找到最近的可移动位置
            let validPositions = Array(0..<items.count).filter { !uploadIndices.contains($0) }
            guard
                let closest = validPositions.min(by: {
                    abs($0 - proposedIndexPath.item) < abs($1 - proposedIndexPath.item)
                })
            else { return originalIndexPath }

            return IndexPath(item: closest, section: 0)
        }
        return proposedIndexPath
    }

    public func collectionView(
        _ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath
    ) {
        guard case .upload = items[indexPath.item] else { return }
        delegate?.didSelectAddButton()
    }
}

// MARK: - Custom Cells
class ItemCell: UICollectionViewCell, UITextViewDelegate {
    // MARK: - UI Elements
    private var button = UIButton()
    private var labelContainer = UIView()
    private var label = UILabel()
    private var leftTopImageView = UIImageView()
    private var leftBottomImageView = UIImageView()
    private var rightTopImageView = UIImageView()
    private var rightBottomImageView = UIImageView()
    private var loadingView = UIView()
    private var maskLayer = UIView()
    private var progressLabel = UILabel()
    private var textView = UITextView()
    private var placeholderLabel = UILabel()

    // MARK: - Constraints
    private var buttonWidthConstraint: NSLayoutConstraint!
    private var buttonHeightConstraint: NSLayoutConstraint!
    private var leftTopConstraints = CornerViewConstraints()
    private var leftBottomConstraints = CornerViewConstraints()
    private var rightTopConstraints = CornerViewConstraints()
    private var rightBottomConstraints = CornerViewConstraints()
    private var textViewHeightConstraint: NSLayoutConstraint!

    // MARK: - Properties
    var item: UxMovableItem?
	var events: ClickEventHandler?
    var multiple = false
    var isLoading = false

    // MARK: - Lifecycle
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not be en implemented")
    }

    override func layoutSubviews() {
        super.layoutSubviews()
    }

    // MARK: - UI Setup
    func setupUI() {
        // Button 配置
        button.contentMode = .scaleAspectFill
        button.clipsToBounds = true
        button.tintColor = .white

        // 标签
        labelContainer.backgroundColor = UIColor(white: 0, alpha: 0.5)
        labelContainer.isHidden = true

        // 文本
        label.font = UIFont.systemFont(ofSize: 13)
        label.textColor = .white
        label.textAlignment = .center

        // 角标视图通用设置
        [leftTopImageView, leftBottomImageView, rightTopImageView, rightBottomImageView].forEach {
            $0.contentMode = .scaleAspectFill
            $0.isUserInteractionEnabled = true
            $0.isHidden = true
            $0.translatesAutoresizingMaskIntoConstraints = false
        }

        // 容器设置
        // loading
        loadingView.isHidden = true
        loadingView.translatesAutoresizingMaskIntoConstraints = false

        // 遮罩层
        maskLayer.backgroundColor = UIColor(white: 0, alpha: 0.8)
        maskLayer.translatesAutoresizingMaskIntoConstraints = false

        // 进度标签
        progressLabel.font = UIFont.boldSystemFont(ofSize: 16)
        progressLabel.textColor = .white
        progressLabel.textAlignment = .center
        progressLabel.translatesAutoresizingMaskIntoConstraints = false

        // 输入框
        textView.delegate = self
		textView.isEditable = true
        textView.textContainer.maximumNumberOfLines = 2
        textView.textContainer.lineBreakMode = .byWordWrapping
        textView.isScrollEnabled = false
        textView.font = UIFont.systemFont(ofSize: 13)
        textView.textColor = .black
        textView.layer.cornerRadius = 4
        textView.layer.borderWidth = 0
        textView.backgroundColor = UTSiOS.colorWithString("#f0f0f0")
        textView.textContainerInset = UIEdgeInsets(top: 2, left: 4, bottom: 2, right: 4)
        textView.textContainer.lineFragmentPadding = 0
        textView.translatesAutoresizingMaskIntoConstraints = false

        // 占位符
        placeholderLabel.numberOfLines = 2
        placeholderLabel.lineBreakMode = .byWordWrapping
        placeholderLabel.preferredMaxLayoutWidth = textView.bounds.width - 8
        placeholderLabel.textColor = .lightGray
        placeholderLabel.font = UIFont.systemFont(ofSize: 13)
        placeholderLabel.translatesAutoresizingMaskIntoConstraints = false

        // 层级结构
        contentView.addSubview(button)
        labelContainer.addSubview(label)
        contentView.addSubview(labelContainer)
        contentView.addSubview(leftTopImageView)
        contentView.addSubview(leftBottomImageView)
        contentView.addSubview(rightTopImageView)
        contentView.addSubview(rightBottomImageView)
        loadingView.addSubview(maskLayer)
        loadingView.addSubview(progressLabel)
        contentView.addSubview(loadingView)
        textView.addSubview(placeholderLabel)
        contentView.addSubview(textView)

        // 自动布局约束
        button.translatesAutoresizingMaskIntoConstraints = false
        labelContainer.translatesAutoresizingMaskIntoConstraints = false
        label.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            // 主按钮
            button.centerXAnchor.constraint(equalTo: contentView.centerXAnchor),
            button.topAnchor.constraint(equalTo: contentView.topAnchor),

            // 底部标签容器
            labelContainer.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            labelContainer.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            labelContainer.bottomAnchor.constraint(equalTo: button.bottomAnchor),
            labelContainer.heightAnchor.constraint(equalToConstant: 25),

            // 标签文本
            label.centerXAnchor.constraint(equalTo: labelContainer.centerXAnchor),
            label.centerYAnchor.constraint(equalTo: labelContainer.centerYAnchor),

            // 左上角标
            leftTopImageView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            leftTopImageView.topAnchor.constraint(equalTo: contentView.topAnchor),

            // 左下角标
            leftBottomImageView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            leftBottomImageView.bottomAnchor.constraint(equalTo: button.bottomAnchor),

            // 右上角标
            rightTopImageView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            rightTopImageView.topAnchor.constraint(equalTo: contentView.topAnchor),

            // 右下角标
            rightBottomImageView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            rightBottomImageView.bottomAnchor.constraint(equalTo: button.bottomAnchor),

            // Loading容器
            loadingView.topAnchor.constraint(equalTo: contentView.topAnchor),
            loadingView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            loadingView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            loadingView.bottomAnchor.constraint(equalTo: button.bottomAnchor),

            // 遮罩层
            maskLayer.topAnchor.constraint(equalTo: loadingView.topAnchor),
            maskLayer.leadingAnchor.constraint(equalTo: loadingView.leadingAnchor),
            maskLayer.trailingAnchor.constraint(equalTo: loadingView.trailingAnchor),
            maskLayer.bottomAnchor.constraint(equalTo: button.bottomAnchor),

            // 进度标签
            progressLabel.centerXAnchor.constraint(equalTo: button.centerXAnchor),
            progressLabel.centerYAnchor.constraint(equalTo: button.centerYAnchor),

            // 输入框
            textView.topAnchor.constraint(equalTo: button.bottomAnchor, constant: 4),
            textView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            textView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),

            // 占位符
            placeholderLabel.leadingAnchor.constraint(equalTo: textView.leadingAnchor, constant: 5),
            placeholderLabel.topAnchor.constraint(equalTo: textView.topAnchor, constant: 2),
            placeholderLabel.trailingAnchor.constraint(
                equalTo: textView.trailingAnchor, constant: -5),
        ])

        buttonWidthConstraint = button.widthAnchor.constraint(equalToConstant: 0)
        buttonHeightConstraint = button.heightAnchor.constraint(equalToConstant: 0)
        buttonWidthConstraint.isActive = true
        buttonHeightConstraint.isActive = true

        textViewHeightConstraint = textView.heightAnchor.constraint(equalToConstant: 22)
        textViewHeightConstraint.isActive = true
    }

    func configureCornerStyle(
        view: UIImageView,
        placeholder: UxMovablePlaceholder,
        position: CornerPosition
    ) {
        // 尺寸约束
        NSLayoutConstraint.activate([
            view.widthAnchor.constraint(equalToConstant: CGFloat(placeholder.width)),
            view.heightAnchor.constraint(equalToConstant: CGFloat(placeholder.height)),
        ])

        // 边距处理
        let margin = CGFloat(placeholder.margin ?? 0)
        switch position {
        case .leftTop:
            updateConstraints(for: leftTopConstraints, margin: margin, position: position)
        case .leftBottom:
            updateConstraints(for: leftBottomConstraints, margin: margin, position: position)
        case .rightTop:
            updateConstraints(for: rightTopConstraints, margin: margin, position: position)
        case .rightBottom:
            updateConstraints(for: rightBottomConstraints, margin: margin, position: position)
        }

        // 圆角设置
        view.layer.cornerRadius = CGFloat(placeholder.radius ?? 0)
        view.clipsToBounds = true

        // 显示视图
        view.isHidden = false
    }

    private func updateConstraints(
        for constraints: CornerViewConstraints,
        margin: CGFloat,
        position: CornerPosition
    ) {
        // 停用旧约束
        NSLayoutConstraint.deactivate(
            [
                constraints.leading,
                constraints.trailing,
                constraints.top,
                constraints.bottom,
            ].compactMap { $0 })

        // 创建新约束
        switch position {
        case .leftTop:
            constraints.leading = leftTopImageView.leadingAnchor.constraint(
                equalTo: contentView.leadingAnchor, constant: margin)
            constraints.top = leftTopImageView.topAnchor.constraint(
                equalTo: contentView.topAnchor, constant: margin)
            NSLayoutConstraint.activate([constraints.leading!, constraints.top!])

        case .leftBottom:
            constraints.leading = leftBottomImageView.leadingAnchor.constraint(
                equalTo: contentView.leadingAnchor, constant: margin)
            constraints.bottom = leftBottomImageView.bottomAnchor.constraint(
                equalTo: contentView.bottomAnchor, constant: -margin)
            NSLayoutConstraint.activate([constraints.leading!, constraints.bottom!])

        case .rightTop:
            constraints.trailing = rightTopImageView.trailingAnchor.constraint(
                equalTo: contentView.trailingAnchor, constant: -margin)
            constraints.top = rightTopImageView.topAnchor.constraint(
                equalTo: contentView.topAnchor, constant: margin)
            NSLayoutConstraint.activate([constraints.trailing!, constraints.top!])

        case .rightBottom:
            constraints.trailing = rightBottomImageView.trailingAnchor.constraint(
                equalTo: contentView.trailingAnchor, constant: -margin)
            constraints.bottom = rightBottomImageView.bottomAnchor.constraint(
                equalTo: contentView.bottomAnchor, constant: -margin)
            NSLayoutConstraint.activate([constraints.trailing!, constraints.bottom!])
        }
    }

    func configure(with item: UxMovableItem, multiple: Bool, editable: Bool, disabled: Bool, cellSize: CGSize, events: ClickEventHandler?) {
		self.events = events
        self.item = item
        self.multiple = multiple

        // 按钮尺寸
        buttonWidthConstraint.constant = cellSize.width
        buttonHeightConstraint.constant = cellSize.height

        // 背景图片
        ImageLoader.loadImage(from: item.url ?? "") { image in
            self.button.setImage(image, for: .normal)
            self.button.imageView?.contentMode = .scaleAspectFill
        }

        // 圆角设置
        button.layer.cornerRadius = CGFloat(item.radius ?? 0)

        // 标签
        if let labelText = item.label, !labelText.isEmpty {
            label.text = labelText
			if(item.labelStyle != nil) {
				label.font = UIFont.systemFont(ofSize: CGFloat(item.labelStyle!.fontSize ?? 13))
				label.textColor = UTSiOS.colorWithString(item.labelStyle!.color ?? "#ffffff")
			}
            labelContainer.isHidden = false
        } else {
            labelContainer.isHidden = true
        }

        labelContainer.layer.cornerRadius = button.layer.cornerRadius
        labelContainer.layer.maskedCorners = [.layerMinXMaxYCorner, .layerMaxXMaxYCorner]
        labelContainer.clipsToBounds = true

        // 左上角标
        leftTopImageView.isHidden = item.leftTop == nil
        if let leftTop = item.leftTop {
            // 配置样式
            configureCornerStyle(view: leftTopImageView, placeholder: leftTop, position: .leftTop)

            // 加载图片
            ImageLoader.loadImage(from: leftTop.url) { [weak self] in
                self?.leftTopImageView.image = $0
            }
        }

        // 左下角标
        leftBottomImageView.isHidden = item.leftBottom == nil
        if let leftBottom = item.leftBottom {
            // 配置样式
            configureCornerStyle(
                view: leftBottomImageView, placeholder: leftBottom, position: .leftBottom)

            // 加载图片
            ImageLoader.loadImage(from: leftBottom.url) { [weak self] in
                self?.leftBottomImageView.image = $0
            }
        }

        // 右上角标（多选优先）
        updateMultiple(multiple: multiple)

        // 右下角标
        rightBottomImageView.isHidden = item.rightBottom == nil
        if let rightBottom = item.rightBottom {
            // 配置样式
            configureCornerStyle(
                view: rightBottomImageView, placeholder: rightBottom, position: .rightBottom)

            // 加载图片
            ImageLoader.loadImage(from: rightBottom.url) { [weak self] in
                self?.rightBottomImageView.image = $0
            }
        }

        // 输入框
		textView.isEditable = !disabled
        if editable {
            textView.isHidden = false
            textView.text = item.input!.content
            placeholderLabel.text = item.input!.placeholder ?? "请输入"
            placeholderLabel.isHidden = !textView.text.isEmpty
            updateTextViewHeight()
        } else {
            textView.isHidden = true
        }

        // loading
        updateProgress(with: item)

        // 添加点击事件
        addTapGestures()
    }
	
    func updateProgress(with item: UxMovableItem) {
        isLoading = item.loading != nil && item.loading!.show

        labelContainer.isHidden = isLoading
        rightBottomImageView.isHidden = isLoading
        leftBottomImageView.isHidden = isLoading
        rightTopImageView.isHidden = isLoading
        leftTopImageView.isHidden = isLoading
        loadingView.isHidden = !isLoading

        if isLoading {
            let loading = item.loading!

            maskLayer.layer.cornerRadius = CGFloat(item.radius ?? 0)
            maskLayer.alpha = 1 - CGFloat(loading.progress) / 100

            if loading.style != nil {
                progressLabel.font = UIFont.boldSystemFont(
                    ofSize: CGFloat(loading.style!.fontSize ?? 16))
                progressLabel.textColor = UTSiOS.colorWithString(loading.style!.color ?? "#ffffff")
            }

            if loading.progress == 0 {
                progressLabel.text = loading.waitingLabel ?? "\(loading.progress!)%"
				if(loading.waitingLabel != nil) {
					progressLabel.font = UIFont.boldSystemFont(ofSize: CGFloat(13))
				}
            } else {
                progressLabel.text = "\(loading.progress!)%"
            }
        }
    }
	
	func updateMultiple(multiple: Bool) {
		self.multiple = multiple
		
		guard let item = item else { return }
		
		if multiple, let selected = item.selected {
		    rightTopImageView.isHidden = false
		    let url = selected.checked ? selected.selectedUrl : selected.unselectedUrl
		    ImageLoader.loadImage(from: url!) { [weak self] in
		        self?.rightTopImageView.image = $0
		    }
		} else {
		    rightTopImageView.isHidden = item.rightTop == nil
		    if let rightTop = item.rightTop {
		        // 配置样式
		        configureCornerStyle(
		            view: rightTopImageView, placeholder: rightTop, position: .rightTop)
		
		        // 加载图片
		        ImageLoader.loadImage(from: rightTop.url!) { [weak self] in
		            self?.rightTopImageView.image = $0
		        }
		    }
		}
	}
	
	func updateSelected(checked: Bool) {
		guard let item = item else { return }
		
		if let selected = item.selected {
			selected.checked = checked
		    let url = selected.checked ? selected.selectedUrl : selected.unselectedUrl
		    ImageLoader.loadImage(from: url!) { [weak self] in
		        self?.rightTopImageView.image = $0
		    }
		}
	}
	
	func updateInputView(disabled: Bool) {
		textView.isEditable = !disabled
	}

    // MARK: - UITextViewDelegate
    func textViewDidChange(_ textView: UITextView) {
        item?.input!.content = textView.text
        item?.input?.input?(textView.text)
        placeholderLabel.isHidden = !textView.text.isEmpty
        events?(5, item!)
        updateTextViewHeight()
    }

    func textView(
        _ textView: UITextView, shouldChangeTextIn range: NSRange, replacementText text: String
    ) -> Bool {
        let newText = (textView.text as NSString).replacingCharacters(in: range, with: text)
        return newText.count <= 9
    }

    private func updateTextViewHeight() {
        // 使用布局宽度而非当前bounds（解决自动布局时序问题）
        let contentWidth = contentView.bounds.width - 8  // 左右各4pt边距

        // 计算精确单行高度
        guard let font = textView.font else { return }
        let singleLineHeight = font.lineHeight

        // 计算最大允许高度（两行高度 + 内边距）
        let maxHeight =
            singleLineHeight * 2 + textView.textContainerInset.top
            + textView.textContainerInset.bottom

        // 生成精确计算尺寸
        let newSize = textView.sizeThatFits(
            CGSize(
                width: contentWidth,
                height: .greatestFiniteMagnitude
            ))

        // 应用约束高度（限制最大高度）
        textViewHeightConstraint.constant = min(newSize.height, maxHeight)

        // 通知集合视图更新布局
        if let collectionView = self.superview as? UICollectionView {
            UIView.performWithoutAnimation {
                collectionView.collectionViewLayout.invalidateLayout()
            }
        }
    }

    // MARK: - Gestures
    private func addTapGestures() {
        // 按钮点击事件
        button.addTarget(self, action: #selector(handleButtonTap), for: .touchUpInside)

        // 四角点击事件
        [leftTopImageView, leftBottomImageView, rightTopImageView, rightBottomImageView].forEach {
            $0.gestureRecognizers?.forEach($0.removeGestureRecognizer)
        }
        addTapGesture(for: leftTopImageView, identifier: "1")
        if self.multiple && item?.selected != nil {} else {
            addTapGesture(for: rightTopImageView, identifier: "2")
        }
        addTapGesture(for: rightBottomImageView, identifier: "3")
        addTapGesture(for: leftBottomImageView, identifier: "4")
    }

    private func addTapGesture(for view: UIView, identifier: String) {
        let tap = UITapGestureRecognizer(target: self, action: #selector(handleCornerTap(_:)))
        tap.accessibilityValue = identifier
        view.addGestureRecognizer(tap)
    }

    @objc private func handleButtonTap() {
        events?(0, item!)
    }

    @objc private func handleCornerTap(_ sender: UITapGestureRecognizer) {
        guard let identifier = sender.accessibilityValue else { return }
		
		if(self.multiple) {
			events?(0, item!)
		} else {
			events?(Int(identifier)!, item!)
		}
    }

    // MARK: - Reuse
    override func prepareForReuse() {
        super.prepareForReuse()
        [leftTopImageView, leftBottomImageView, rightTopImageView, rightBottomImageView]
            .forEach {
                $0.image = nil
            }
        button.setImage(nil, for: .normal)
        label.text = nil
        labelContainer.isHidden = true
        isLoading = false
    }
}

class ListItemCell: UICollectionViewCell {
    // MARK: - UI Elements
    private let imageView = UIImageView()
    private let labelStack = UIStackView()
    private let titleLabel = UILabel()
    private let summaryLabel = UILabel()
    private let dragImageView = UIImageView()
    private let underline = UIView()
    
	// MARK: - Properties
	var item: UxMovableItem?
	var events: ClickEventHandler?
	
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        // 左侧图片
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 4
        
        // 文字区域
        labelStack.axis = .vertical
        labelStack.spacing = 0
        titleLabel.font = UIFont.systemFont(ofSize: 16)
		titleLabel.textColor = .black
        summaryLabel.font = UIFont.systemFont(ofSize: 14)
        summaryLabel.textColor = .gray
        
        // 拖拽图标
        if let imagePath = Bundle.main.path(forResource: "ic_drag", ofType: "png") {
        	dragImageView.image = UIImage(named: imagePath)
        }
        dragImageView.contentMode = .scaleAspectFit
		dragImageView.isHidden = false
		
        // 下划线
        underline.backgroundColor = UIColor.lightGray
        
        // 布局结构
        let mainStack = UIStackView(arrangedSubviews: [imageView, labelStack, dragImageView])
        mainStack.axis = .horizontal
        mainStack.alignment = .center
        mainStack.spacing = 8
        
        labelStack.addArrangedSubview(titleLabel)
        labelStack.addArrangedSubview(summaryLabel)
        
        contentView.addSubview(mainStack)
        contentView.addSubview(underline)
        
        // 自动布局约束
        imageView.translatesAutoresizingMaskIntoConstraints = false
        mainStack.translatesAutoresizingMaskIntoConstraints = false
        underline.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            // 主布局
            mainStack.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 0),
            mainStack.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 8),
            mainStack.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -8),
            mainStack.bottomAnchor.constraint(equalTo: underline.topAnchor, constant: 0),
            
            // 图片尺寸
            imageView.widthAnchor.constraint(equalToConstant: 40),
            imageView.heightAnchor.constraint(equalToConstant: 40),
			
            // 拖拽图标
            dragImageView.widthAnchor.constraint(equalToConstant: 24),
            dragImageView.heightAnchor.constraint(equalToConstant: 24),
            
            // 下划线
            underline.leadingAnchor.constraint(equalTo: imageView.trailingAnchor, constant: 8),
            underline.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -8),
            underline.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),
            underline.heightAnchor.constraint(equalToConstant: 0.5)
        ])
    }
    
    func configure(with item: UxMovableItem, disabled: Bool, events: ClickEventHandler?) {
        self.events = events
        self.item = item
		
		// 图片
		ImageLoader.loadImage(from: item.url ?? "") { image in
            self.imageView.image = image
			self.imageView.contentMode = .scaleAspectFill
			self.imageView.layer.cornerRadius = CGFloat(item.radius ?? 0)
        }
		
		// 标题
		if let labelText = item.label, !labelText.isEmpty {
		    titleLabel.text = labelText
			if(item.labelStyle != nil) {
				titleLabel.font = UIFont.systemFont(ofSize: CGFloat(item.labelStyle!.fontSize ?? 13))
				titleLabel.textColor = UTSiOS.colorWithString(item.labelStyle!.color ?? "#ffffff")
			}
		    titleLabel.isHidden = false
		} else {
		    titleLabel.isHidden = true
		}
		
		// 副标题
		if let summaryText = item.summary, !summaryText.isEmpty {
		    summaryLabel.text = summaryText
			if(item.summaryStyle != nil) {
				summaryLabel.font = UIFont.systemFont(ofSize: CGFloat(item.summaryStyle!.fontSize ?? 13))
				summaryLabel.textColor = UTSiOS.colorWithString(item.labelStyle!.color ?? "#ffffff")
			}
		    summaryLabel.isHidden = false
		} else {
		    summaryLabel.isHidden = true
		}
		
		// 拖拽图标
		dragImageView.isHidden = !(item.showDrag ?? true)
		
		// 添加点击事件
		let tap = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
		tap.accessibilityValue = "0"
		contentView.addGestureRecognizer(tap)
    }
	
	@objc private func handleTap(_ sender: UITapGestureRecognizer) {
	    guard let identifier = sender.accessibilityValue else { return }
		events?(Int(identifier)!, item!)
	}
}

class UploadCell: UICollectionViewCell {
    var item: UxMovableItem?
	var events: ClickEventHandler?

    let button = UIButton()

    // MARK: - Constraints
    private var buttonWidthConstraint: NSLayoutConstraint!
    private var buttonHeightConstraint: NSLayoutConstraint!

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not be en implemented")
    }

    func setupUI() {
        button.contentMode = .scaleAspectFill
        button.clipsToBounds = true
        button.tintColor = .white
        button.translatesAutoresizingMaskIntoConstraints = false

        contentView.addSubview(button)

        NSLayoutConstraint.activate([
            button.centerXAnchor.constraint(equalTo: contentView.centerXAnchor),
            button.topAnchor.constraint(equalTo: contentView.topAnchor),
        ])

        buttonWidthConstraint = button.widthAnchor.constraint(equalToConstant: 0)
        buttonHeightConstraint = button.heightAnchor.constraint(equalToConstant: 0)
        buttonWidthConstraint.isActive = true
        buttonHeightConstraint.isActive = true
    }

    func configure(with item: UxMovableItem, disabled: Bool, cellSize: CGSize, events: ClickEventHandler?) {
		self.events = events
        self.item = item

        buttonWidthConstraint.constant = cellSize.width
        buttonHeightConstraint.constant = cellSize.height

        ImageLoader.loadImage(from: item.upload?.url ?? "") { image in
            self.button.setImage(image, for: .normal)
            self.button.imageView?.contentMode = .scaleAspectFill
        }
		
		if((item.upload?.backgroundColor ?? "") != "") {
			button.tintColor = UTSiOS.colorWithString(item.upload?.backgroundColor ?? "#ffffff")
		}

        button.layer.cornerRadius = CGFloat(item.radius ?? 0)
        button.isUserInteractionEnabled = true
        button.addTarget(self, action: #selector(handleButtonTap), for: .touchUpInside)
    }

    override func layoutSubviews() {
        super.layoutSubviews()
        button.layer.cornerRadius = button.layer.cornerRadius
    }

    override func apply(_ layoutAttributes: UICollectionViewLayoutAttributes) {
        super.apply(layoutAttributes)
        layer.zPosition = 0
    }

    @objc private func handleButtonTap() {
        events?(0, item!)
    }
}

// MARK: - Lifecycle Handling
extension UxMovableView {
    override public func willMove(toWindow newWindow: UIWindow?) {
        super.willMove(toWindow: newWindow)
        if newWindow == nil {
            autoScrollLink?.invalidate()
            autoScrollLink = nil
        }
    }
}

class CornerViewConstraints {
    var leading: NSLayoutConstraint?
    var trailing: NSLayoutConstraint?
    var top: NSLayoutConstraint?
    var bottom: NSLayoutConstraint?
}

enum ImageLoader {
    static func loadImage(from path: String, completion: @escaping (UIImage?) -> Void) {
        // 判断是否是网络路径
        if path.hasPrefix("http://") || path.hasPrefix("https://") {
            // 网络图片加载
            guard let url = URL(string: path) else {
                completion(nil)
                return
            }

            // 使用原生方式或第三方库
            URLSession.shared.dataTask(with: url) { data, _, _ in
                let image = data.flatMap(UIImage.init)
                DispatchQueue.main.async { completion(image) }
            }.resume()

        } else if path.hasPrefix("/static/") {
            // 沙盒本地文件路径
            let image = UIImage(contentsOfFile: UTSiOS.getResourcePath(path))
            completion(image)
        } else if FileManager.default.fileExists(atPath: path) {
            // 本地文件路径
            let image = UIImage(contentsOfFile: path)
            completion(image)
        } else {
            // Bundle 资源图片
            let image = UIImage(named: path)
            completion(image)
        }
    }
}
