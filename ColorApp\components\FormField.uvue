<template>
	<view
		v-if="!field.Hide"
		class="form-group-item"
		:class="classes"
	>
		<!-- 表单项标题 -->
		<view
			v-if="field.Title"
			class="h-row"
			:class="[
				field.Required ? 'h-form-item-required' : '',
				titleClass ? titleClass : '',
			]"
		>
			<view class="h-col h-col-xl-24 h-col-lg-24 h-col-md-24 h-col-sm-24 h-col-xs-24">
				<text class="h-form-item-label" style="width: 100%">
					<text>{{ field.Title }}</text>
				</text>
			</view>
		</view>
		
		<!-- 表单项渲染 -->
		<ux-form-item
			:label="field.Name"
			:labelCell="labelCell"
			:prop="field.Id"
			:class="'form-item-' + field.Id.toLowerCase()"
			:required="field.Required"
		>
			<!-- Input类表单 -->
			<view v-if="field.Type == 'Text'" class="h-input-group">
				<ux-input
					:type="getInputType(field.InputType)"
					:readonly="field.Readonly"
					v-model="dataModel[field.Id]"
					:maxlength="field.MaxLength > 0 ? field.MaxLength : -1"
					:disabled="field.Disabled"
					:placeholder="getPlacehoder(field)"
					:clearable="true"
					@change="handleChange(field)"
				/>
				<!-- 发送验证码按钮 -->
				<ux-button
					v-if="field.ValidationType === 'code'"
					color="primary"
					@click="handleSendCode(field)"
					:disabled="!resend"
				>{{ getCodeText }}</ux-button>
			</view>
			
			<!-- 数字输入框 -->
			<ux-input
				v-else-if="field.Type == 'NumberInput'"
				type="number"
				@change="handleChange(field)"
				v-model="dataModel[field.Id]"
				:min="field.MinDecimal"
				:max="field.MaxDecimal"
			/>
			
			<!-- 开关 -->
			<ux-switch 
				v-model="dataModel[field.Id]" 
				v-else-if="field.Type == 'Switch'"
				@change="handleChange(field)"
			/>
			
			<!-- 勾选框 -->
			<ux-checkbox-group
				v-model="dataModel[field.Id]"
				v-else-if="field.Type == 'Checkbox' || field.Type === 'CheckboxGroup'"
				@change="handleChange(field)"
			>
				<ux-checkbox
					v-for="item in getDataSource(field)"
					:key="item.id"
					:value="item.id"
				>{{ item.text }}</ux-checkbox>
			</ux-checkbox-group>
			
			<!-- 单选框 -->
			<ux-radio-group
				v-model="dataModel[field.Id]"
				v-else-if="
					field.Type == 'Radio' ||
					field.Type === 'RadioGroup' ||
					field.Type === 'ButtonGroup'
				"
				@change="handleChange(field)"
			>
				<ux-radio
					v-for="item in getDataSource(field)"
					:key="item.id"
					:value="item.id"
				>{{ item.text }}</ux-radio>
			</ux-radio-group>
			
			<!-- 标题编辑器 -->
			<ux-input
				v-model="dataModel[field.Id]"
				v-else-if="field.Type == 'InputEditor'"
				:required="field.Required"
				@change="handleChange(field)"
			/>
			
			<!-- 自动完成输入框 -->
			<ux-input
				v-else-if="field.Type == 'SelectInput'"
				v-model="dataModel[field.Id]"
				:placeholder="getPlacehoder(field)"
				@change="handleChange(field)"
				:required="field.Required"
			/>
			
			<!-- 企业搜索 -->
			<ux-input
				v-else-if="field.Type == 'CompanySearch'"
				v-model="dataModel[field.Id]"
				:placeholder="getPlacehoder(field)"
				@change="handleChange(field)"
				:required="field.Required"
			/>
			
			<!-- 下拉选择框 -->
			<ux-picker
				v-else-if="field.Type == 'Select'"
				:range="getDataSource(field)"
				v-model="dataModel[field.Id]"
				:placeholder="getPlacehoder(field)"
				:disabled="field.Disabled"
				:required="field.Required"
				@change="handleSelectChange(field, $event)"
			/>
			
			<!-- 地址选择 -->
			<ux-input
				v-else-if="field.Type == 'Address'"
				v-model="dataModel[field.Id]"
				:required="field.Required"
				:placeholder="getPlacehoder(field)"
				@change="handleChange(field)"
			/>
			
			<!-- 电话号码输入框 -->
			<ux-input
				v-else-if="field.Type == 'PhoneFormat'"
				type="tel"
				v-model="dataModel[field.Id]"
				:required="field.Required"
				:placeholder="getPlacehoder(field)"
				@change="handleChange(field)"
			/>
			
			<!-- 数字范围 -->
			<ux-input
				v-else-if="field.Type == 'NumberRange'"
				type="number"
				v-model="dataModel[field.Id]"
				:min="field.MinDecimal"
				:max="field.MaxDecimal"
				:disabled="field.Disabled"
				:placeholder="getPlacehoder(field)"
				@change="handleChange(field)"
			/>
			
			<!-- 树形选择框 -->
			<ux-picker
				v-else-if="field.Type == 'TreeSelect'"
				:range="getDataSource(field)"
				v-model="dataModel[field.Id]"
				:disabled="field.Disabled"
				:required="field.Required"
				:placeholder="getPlacehoder(field)"
				@change="handleSelectChange(field, $event)"
			/>
			
			<!-- 标签输入框 -->
			<ux-input
				v-model="dataModel[field.Id]"
				v-if="field.Type == 'Tag'"
				:required="field.Required"
				:placeholder="getPlacehoder(field)"
				@change="handleChange(field)"
			/>
			
			<!-- 日期选择框 -->
			<view v-else-if="field.Type.startsWith('Date')" class="flex-h">
				<ux-checkbox
					v-if="field.Type != 'DateRange' && field.Flags && field.Flags.indexOf('时间待定') !== -1"
					v-model="dataModel[field.Id]"
					true-value="0001/1/1"
					false-value=""
					style="margin-right: 10px"
				>时间待定</ux-checkbox>
				
				<!-- 单日期选择框 -->
				<ux-datepicker
					v-if="field.Type != 'DateRange'"
					v-model="dataModel[field.Id]"
					class="flex1"
					:mode="field.Flags && field.Flags.indexOf('datetime') !== -1 ? 'datetime' : 'date'"
					:required="field.Required"
					@change="handleChange(field)"
				/>
				
				<!-- 日期范围选择框 -->
				<ux-datepicker
					v-else
					v-model="dataModel[field.Id]"
					:mode="field.Flags && field.Flags.indexOf('datetime') !== -1 ? 'datetimerange' : 'daterange'"
					:required="field.Required"
					@change="handleChange(field)"
				/>
			</view>
			
			<!-- 视频上传 -->
			<view v-else-if="field.Type == 'Video'">
				<text>视频上传功能暂不支持</text>
			</view>
			
			<!-- 人脸上传&验证 -->
			<view v-else-if="field.Type == 'Face'">
				<text>人脸上传功能暂不支持</text>
			</view>
			
			<!-- 图片、文件、音频上传 -->
			<view
				v-else-if="
					field.Type == 'Image' || field.Type == 'File' || field.Type == 'Audio'
				"
			>
				<text>文件上传功能暂不支持</text>
			</view>
			
			<!-- TextArea -->
			<ux-textarea
				v-else-if="field.Type == 'TextArea'"
				@input="currentValue(field.MaxLength, field.Id)"
				v-model="dataModel[field.Id]"
				:class="field.Data"
				:placeholder="field.Placeholder"
				:maxlength="field.MaxLength"
				@change="handleChange(field)"
			/>
			
			<!-- TextArea字数限制提醒信息 -->
			<view v-for="words in fieldWords" :key="words.id">
				<view
					v-if="field.Id === words.id"
					class="words"
					style="font-size: 12px; line-height: 20px; padding-top: 10px"
				>
					<view v-if="words.words >= 0" style="text-align: right">
						剩余可输入字符：
						<text :class="{ green: words.words > 0, red: words.words <= 0 }">{{ words.words }}</text>
					</view>
					<view v-else class="red">
						已超出 {{ Math.abs(words.words) }} 个限制字符！
					</view>
				</view>
			</view>
			
			<!-- 表单项的Tooltip -->
			<view
				style="color: red; font-size: 12px; line-height: 20px; margin-top: 10px"
				v-if="field.Tooltip"
			>
				<text>{{ field.Tooltip }}</text>
			</view>
		</ux-form-item>
	</view>
</template>

<script setup lang="uts">
	/**
	 * FormField 表单字段组件
	 * @description 单个表单字段的渲染组件
	 * @property {Object} field - 字段配置对象
	 * @property {any} modelValue - 字段值
	 * @property {String} titleClass - 标题样式类
	 * @event {Function} change - 字段值变化事件
	 */
	
	// 字段编辑器类型枚举
	const FieldEditorType = {
		Text: 'Text',
		InputEditor: 'InputEditor',
		TextArea: 'TextArea',
		SmallEditor: 'SmallEditor',
		Switch: 'Switch',
		Select: 'Select',
		Picker: 'Picker',
		Date: 'Date',
		DateTime: 'DateTime',
		DateRange: 'DateRange',
		DateTimeRange: 'DateTimeRange',
		RadioGroup: 'RadioGroup',
		ButtonGroup: 'ButtonGroup',
		CheckboxGroup: 'CheckboxGroup',
		Checkbox: 'Checkbox',
		PhoneFormat: 'PhoneFormat',
		NumberInput: 'NumberInput'
	}
	
	// 输入类型枚举
	const InputTypes = {
		text: 'text',
		mobile: 'tel',
		email: 'email',
		number: 'number',
		idcard: 'text',
		password: 'password'
	}
	
	defineOptions({
		name: 'FormField'
	})
	
	const emit = defineEmits<{
		change: [data: UTSJSONObject]
		'update:modelValue': [value: any]
	}>()
	
	const props = defineProps({
		// 字段配置对象
		field: {
			type: Object as PropType<UTSJSONObject>,
			required: true
		},
		// 字段值
		modelValue: {
			default: null
		},
		// 标题样式类
		titleClass: {
			type: String,
			default: 'field-title-default'
		}
	})
	
	const fieldValue = ref(props.modelValue)
	const errorMessage = ref('')
	
	// 判断是否为文本输入框
	const isTextInput = (field: UTSJSONObject): boolean => {
		const type = field['Type'] as string
		return type === FieldEditorType.Text || 
			   type === FieldEditorType.InputEditor || 
			   type === FieldEditorType.PhoneFormat ||
			   type === FieldEditorType.NumberInput
	}
	
	// 判断是否为文本域
	const isTextArea = (field: UTSJSONObject): boolean => {
		const type = field['Type'] as string
		return type === FieldEditorType.TextArea || type === FieldEditorType.SmallEditor
	}
	
	// 判断是否为开关
	const isSwitch = (field: UTSJSONObject): boolean => {
		const type = field['Type'] as string
		return type === FieldEditorType.Switch
	}
	
	// 判断是否为选择器
	const isSelect = (field: UTSJSONObject): boolean => {
		const type = field['Type'] as string
		return type === FieldEditorType.Select || type === FieldEditorType.Picker
	}
	
	// 判断是否为日期选择器
	const isDatePicker = (field: UTSJSONObject): boolean => {
		const type = field['Type'] as string
		return type === FieldEditorType.Date || 
			   type === FieldEditorType.DateTime ||
			   type === FieldEditorType.DateRange ||
			   type === FieldEditorType.DateTimeRange
	}
	
	// 判断是否为单选组
	const isRadioGroup = (field: UTSJSONObject): boolean => {
		const type = field['Type'] as string
		return type === FieldEditorType.RadioGroup || type === FieldEditorType.ButtonGroup
	}
	
	// 判断是否为多选组
	const isCheckboxGroup = (field: UTSJSONObject): boolean => {
		const type = field['Type'] as string
		return type === FieldEditorType.CheckboxGroup
	}
	
	// 判断是否为单个复选框
	const isCheckbox = (field: UTSJSONObject): boolean => {
		const type = field['Type'] as string
		return type === FieldEditorType.Checkbox
	}
	
	// 获取输入框类型
	const getInputType = (field: UTSJSONObject): string => {
		const inputType = field['InputType'] as string
		return InputTypes[inputType as keyof typeof InputTypes] || 'text'
	}
	
	// 获取占位符
	const getPlaceholder = (field: UTSJSONObject): string => {
		const placeholder = field['Placeholder'] as string
		if (placeholder != null && placeholder != '') {
			return placeholder
		}
		
		const type = field['Type'] as string
		const title = field['Title'] as string || field['Name'] as string
		
		if (type === FieldEditorType.Select || type === FieldEditorType.Picker) {
			return '请选择' + title
		}
		
		return '请输入' + title
	}
	
	// 获取日期选择器模式
	const getDateMode = (field: UTSJSONObject): string => {
		const type = field['Type'] as string
		switch (type) {
			case FieldEditorType.Date:
				return 'date'
			case FieldEditorType.DateTime:
				return 'datetime'
			case FieldEditorType.DateRange:
				return 'daterange'
			case FieldEditorType.DateTimeRange:
				return 'datetimerange'
			default:
				return 'date'
		}
	}
	
	// 获取数据源选项
	const getDataSourceOptions = (field: UTSJSONObject): UTSJSONObject[] => {
		const dataSourceType = field['DataSourceType'] as string
		const dataSource = field['DataSource']
		const defaultDataSource = field['DefaultDataSource'] as string
		
		let options: UTSJSONObject[] = []
		
		if (dataSource != null) {
			if (typeof dataSource === 'string') {
				// 处理字符串数据源
				if (dataSourceType === 'Json') {
					try {
						options = JSON.parse(dataSource as string) as UTSJSONObject[]
					} catch (e) {
						// 如果解析失败，按逗号分割
						const items = (dataSource as string).split(',')
						options = items.map((item: string): UTSJSONObject => {
							return {
								id: item.trim(),
								text: item.trim()
							} as UTSJSONObject
						})
					}
				}
			} else if (dataSource instanceof Array) {
				options = dataSource as UTSJSONObject[]
			}
		} else if (defaultDataSource != null && defaultDataSource != '') {
			// 使用默认数据源
			const items = defaultDataSource.split(',')
			options = items.map((item: string): UTSJSONObject => {
				return {
					id: item.trim(),
					text: item.trim()
				} as UTSJSONObject
			})
		}
		
		// 确保选项有正确的格式
		return options.map((option: UTSJSONObject): UTSJSONObject => {
			if (option['text'] == null) {
				option['text'] = option['id'] as string
			}
			return option
		})
	}
	
	// 字段验证
	const validateField = (): boolean => {
		errorMessage.value = ''
		
		// 必填验证
		if (props.field['Required'] as boolean) {
			if (fieldValue.value == null || fieldValue.value === '' || 
				(Array.isArray(fieldValue.value) && fieldValue.value.length === 0)) {
				errorMessage.value = '请填写' + (props.field['Title'] as string || props.field['Name'] as string)
				return false
			}
		}
		
		// 长度验证
		const minLength = props.field['MinLength'] as number
		const maxLength = props.field['MaxLength'] as number
		const value = fieldValue.value as string
		
		if (value != null && typeof value === 'string') {
			if (minLength != null && minLength > 0 && value.length < minLength) {
				errorMessage.value = `最少输入${minLength}个字符`
				return false
			}
			if (maxLength != null && maxLength > 0 && value.length > maxLength) {
				errorMessage.value = `最多输入${maxLength}个字符`
				return false
			}
		}
		
		// 正则验证
		const validationType = props.field['ValidationType'] as string
		const validation = props.field['Validation'] as string
		const validMessage = props.field['ValidMesasge'] as string
		
		if (validationType === 'pattern' && validation != null && validation != '' && value != null) {
			const regex = new RegExp(validation)
			if (!regex.test(value)) {
				errorMessage.value = validMessage || '格式不正确'
				return false
			}
		}
		
		return true
	}
	
	// 字段值变化处理
	const handleChange = (value: any) => {
		fieldValue.value = value
		
		// 验证字段
		const isValid = validateField()
		
		// 触发change事件
		emit('change', {
			field: props.field,
			value: value,
			isValid: isValid,
			errorMessage: errorMessage.value
		} as UTSJSONObject)
		
		// 更新v-model
		emit('update:modelValue', value)
	}
	
	// 监听外部值变化
	watch(() => props.modelValue, (newVal: any) => {
		fieldValue.value = newVal
	})
	
	// 暴露验证方法
	defineExpose({
		validate: validateField,
		clearError: () => {
			errorMessage.value = ''
		}
	})
</script>

<style lang="scss">
	.form-field {
		margin-bottom: 20rpx;
	}
	
	.field-title {
		margin-bottom: 10rpx;
	}
	
	.field-label {
		font-size: 28rpx;
		color: #333;
		line-height: 1.4;
	}
	
	.required-mark {
		color: #ff4757;
		margin-right: 4rpx;
	}
	
	.field-content {
		position: relative;
	}
	
	.field-tooltip {
		margin-top: 8rpx;
	}
	
	.tooltip-text {
		font-size: 24rpx;
		color: #999;
		line-height: 1.4;
	}
	
	.field-error {
		margin-top: 8rpx;
	}
	
	.error-text {
		font-size: 24rpx;
		color: #ff4757;
		line-height: 1.4;
	}
	
	.field-title-default {
		/* 默认标题样式 */
	}
</style>