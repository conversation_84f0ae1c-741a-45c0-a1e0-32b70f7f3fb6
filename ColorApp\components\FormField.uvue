<template>
	<view
		v-if="!field.Hide"
		class="p-16 mb-16 bg-fff border-1 border-f0f0f0 rounded-8"
		:class="classes"
	>
		<!-- 表单项标题 -->
		<view
			v-if="field.Title"
			class="flex-row items-center mb-12"
			:class="[
				field.Required ? 'form-item-required' : '',
				titleClass ? titleClass : '',
			]"
		>
			<text class="fs-16 fw-500 c-333" style="width: 100%">
				<text v-if="field.Required" class="c-red mr-4">*</text>
				<text>{{ field.Title }}</text>
			</text>
		</view>
		
		<!-- 表单项渲染 -->
		<view class="form-field-content">
			<!-- 表单控件容器 -->
			<!-- Input类表单 -->
			<view v-if="field.Type == 'Text'" class="flex-row items-center">
				<ux-input
					theme="primary"
					:type="getInputType(field.InputType)"
					:readonly="field.Readonly"
					v-model="dataModel[field.Id]"
					:maxlength="field.MaxLength > 0 ? field.MaxLength : -1"
					:disabled="field.Disabled"
					:placeholder="getPlaceholder(field)"
					:clearable="true"
					class="flex-1"
					@change="handleChange(field)"
				/>
				<!-- 发送验证码按钮 -->
				<ux-button
					v-if="field.ValidationType === 'code'"
					color="primary"
					class="ml-12 px-16 py-8 fs-14"
					@click="handleSendCode(field)"
					:disabled="!resend"
				>{{ getCodeText }}</ux-button>
			</view>
			
			<!-- 数字输入框 -->
			<ux-input
				v-else-if="field.Type == 'NumberInput'"
				theme="primary"
				type="number"
				@change="handleChange(field)"
				v-model="dataModel[field.Id]"
				:min="field.MinDecimal"
				:max="field.MaxDecimal"
			/>
			
			<!-- 开关 -->
			<ux-switch 
				v-model="dataModel[field.Id]" 
				v-else-if="field.Type == 'Switch'"
				@change="handleChange(field)"
			/>
			
			<!-- 勾选框 - 使用radio组件替代 -->
			<ux-radio-group
				v-model="dataModel[field.Id]"
				v-else-if="field.Type == 'Checkbox' || field.Type === 'CheckboxGroup'"
				theme="primary"
				class="flex-col"
				@change="handleChange(field)"
			>
				<ux-radio
					v-for="item in getDataSource(field)"
					:key="item.id"
					:value="item.id"
					theme="primary"
					class="mb-8"
				>{{ item.text }}</ux-radio>
			</ux-radio-group>

			<!-- 单选框 -->
			<ux-radio-group
				v-model="dataModel[field.Id]"
				v-else-if="
					field.Type == 'Radio' ||
					field.Type === 'RadioGroup' ||
					field.Type === 'ButtonGroup'
				"
				theme="primary"
				class="flex-col"
				@change="handleChange(field)"
			>
				<ux-radio
					v-for="item in getDataSource(field)"
					:key="item.id"
					:value="item.id"
					theme="primary"
					class="mb-8"
				>{{ item.text }}</ux-radio>
			</ux-radio-group>
			
			<!-- 标题编辑器 -->
			<ux-input
				v-model="dataModel[field.Id]"
				v-else-if="field.Type == 'InputEditor'"
				theme="primary"
				:required="field.Required"
				@change="handleChange(field)"
			/>

			<!-- 自动完成输入框 -->
			<ux-input
				v-else-if="field.Type == 'SelectInput'"
				theme="primary"
				v-model="dataModel[field.Id]"
				:placeholder="getPlaceholder(field)"
				@change="handleChange(field)"
				:required="field.Required"
			/>

			<!-- 企业搜索 -->
			<ux-input
				v-else-if="field.Type == 'CompanySearch'"
				theme="primary"
				v-model="dataModel[field.Id]"
				:placeholder="getPlaceholder(field)"
				@change="handleChange(field)"
				:required="field.Required"
			/>

			<!-- 下拉选择框 -->
			<ux-picker
				v-else-if="field.Type == 'Select'"
				:range="getDataSource(field)"
				v-model="dataModel[field.Id]"
				:placeholder="getPlaceholder(field)"
				:disabled="field.Disabled"
				:required="field.Required"
				@change="handleSelectChange(field, $event)"
			/>

			<!-- 地址选择 -->
			<ux-input
				v-else-if="field.Type == 'Address'"
				theme="primary"
				v-model="dataModel[field.Id]"
				:required="field.Required"
				:placeholder="getPlaceholder(field)"
				@change="handleChange(field)"
			/>

			<!-- 电话号码输入框 -->
			<ux-input
				v-else-if="field.Type == 'PhoneFormat'"
				theme="primary"
				type="tel"
				v-model="dataModel[field.Id]"
				:required="field.Required"
				:placeholder="getPlaceholder(field)"
				@change="handleChange(field)"
			/>

			<!-- 数字范围 -->
			<ux-input
				v-else-if="field.Type == 'NumberRange'"
				theme="primary"
				type="number"
				v-model="dataModel[field.Id]"
				:min="field.MinDecimal"
				:max="field.MaxDecimal"
				:disabled="field.Disabled"
				:placeholder="getPlaceholder(field)"
				@change="handleChange(field)"
			/>

			<!-- 树形选择框 -->
			<ux-picker
				v-else-if="field.Type == 'TreeSelect'"
				:range="getDataSource(field)"
				v-model="dataModel[field.Id]"
				:disabled="field.Disabled"
				:required="field.Required"
				:placeholder="getPlaceholder(field)"
				@change="handleSelectChange(field, $event)"
			/>

			<!-- 标签输入框 -->
			<ux-input
				v-model="dataModel[field.Id]"
				v-if="field.Type == 'Tag'"
				theme="primary"
				:required="field.Required"
				:placeholder="getPlaceholder(field)"
				@change="handleChange(field)"
			/>
			
			<!-- 日期选择框 -->
			<view v-else-if="field.Type.startsWith('Date')" class="flex-col">
				<ux-radio
					v-if="field.Type != 'DateRange' && field.Flags && field.Flags.indexOf('时间待定') !== -1"
					v-model="dataModel[field.Id]"
					value="0001/1/1"
					theme="primary"
					class="mb-12"
				>时间待定</ux-radio>

				<!-- 单日期选择框 -->
				<ux-datepicker
					v-if="field.Type != 'DateRange'"
					v-model="dataModel[field.Id]"
					class="w-100p"
					:mode="field.Flags && field.Flags.indexOf('datetime') !== -1 ? 'datetime' : 'date'"
					:required="field.Required"
					@change="handleChange(field)"
				/>

				<!-- 日期范围选择框 -->
				<ux-datepicker
					v-else
					v-model="dataModel[field.Id]"
					class="w-100p"
					:mode="field.Flags && field.Flags.indexOf('datetime') !== -1 ? 'datetimerange' : 'daterange'"
					:required="field.Required"
					@change="handleChange(field)"
				/>
			</view>
			
			<!-- 视频上传 -->
			<ux-upload
				v-else-if="field.Type == 'Video'"
				mode="video"
				:count="field.MaxCount || 1"
				:url="field.UploadUrl || ''"
				v-model="dataModel[field.Id]"
				class="w-100p"
				@change="handleChange(field)"
			/>

			<!-- 人脸上传&验证 -->
			<ux-upload
				v-else-if="field.Type == 'Face'"
				mode="image"
				:count="1"
				:url="field.UploadUrl || ''"
				v-model="dataModel[field.Id]"
				class="w-100p"
				@change="handleChange(field)"
			/>

			<!-- 图片上传 -->
			<ux-upload
				v-else-if="field.Type == 'Image'"
				mode="image"
				:count="field.MaxCount || 9"
				:url="field.UploadUrl || ''"
				v-model="dataModel[field.Id]"
				class="w-100p"
				@change="handleChange(field)"
			/>

			<!-- 文件上传 -->
			<ux-upload
				v-else-if="field.Type == 'File'"
				mode="file"
				:count="field.MaxCount || 5"
				:url="field.UploadUrl || ''"
				v-model="dataModel[field.Id]"
				class="w-100p"
				@change="handleChange(field)"
			/>

			<!-- 音频上传 -->
			<ux-upload
				v-else-if="field.Type == 'Audio'"
				mode="file"
				:count="field.MaxCount || 3"
				:url="field.UploadUrl || ''"
				:extension="['mp3', 'wav', 'aac', 'm4a']"
				v-model="dataModel[field.Id]"
				class="w-100p"
				@change="handleChange(field)"
			/>
			
			<!-- TextArea -->
			<ux-textarea
				v-else-if="field.Type == 'TextArea'"
				@input="currentValue(field.MaxLength, field.Id)"
				v-model="dataModel[field.Id]"
				:class="field.Data"
				:placeholder="field.Placeholder"
				:maxlength="field.MaxLength"
				@change="handleChange(field)"
			/>
			
			<!-- TextArea字数限制提醒信息 -->
			<view v-for="words in fieldWords" :key="words.id">
				<view
					v-if="field.Id === words.id"
					class="mt-8 text-right fs-12 lh-20"
				>
					<view v-if="words.words >= 0" class="c-666">
						剩余可输入字符：
						<text :class="words.words > 0 ? 'c-success' : 'c-error'">{{ words.words }}</text>
					</view>
					<view v-else class="c-error">
						已超出 {{ Math.abs(words.words) }} 个限制字符！
					</view>
				</view>
			</view>

			<!-- 表单项的Tooltip -->
			<view
				class="mt-8 p-8 bg-red-10 border-l-4 border-red c-red fs-12 lh-20 rounded-4"
				v-if="field.Tooltip"
			>
				<text>{{ field.Tooltip }}</text>
			</view>

			<!-- 错误信息显示 -->
			<view
				class="mt-8 p-8 bg-red-10 border-l-4 border-red c-red fs-12 lh-20 rounded-4"
				v-if="errorMessage"
			>
				<text>{{ errorMessage }}</text>
			</view>
		</view>
	</view>
</template>

<script setup lang="uts">
	/**
	 * FormField 表单字段组件
	 * @description 单个表单字段的渲染组件
	 * @property {Object} field - 字段配置对象
	 * @property {any} modelValue - 字段值
	 * @property {String} titleClass - 标题样式类
	 * @event {Function} change - 字段值变化事件
	 */
	
	// 字段编辑器类型枚举
	const FieldEditorType = {
		Text: 'Text',
		InputEditor: 'InputEditor',
		TextArea: 'TextArea',
		SmallEditor: 'SmallEditor',
		Switch: 'Switch',
		Select: 'Select',
		Picker: 'Picker',
		Date: 'Date',
		DateTime: 'DateTime',
		DateRange: 'DateRange',
		DateTimeRange: 'DateTimeRange',
		RadioGroup: 'RadioGroup',
		ButtonGroup: 'ButtonGroup',
		CheckboxGroup: 'CheckboxGroup',
		Checkbox: 'Checkbox',
		PhoneFormat: 'PhoneFormat',
		NumberInput: 'NumberInput'
	}
	
	// 输入类型枚举
	const InputTypes = {
		text: 'text',
		mobile: 'tel',
		email: 'email',
		number: 'number',
		idcard: 'text',
		password: 'password'
	}
	
	defineOptions({
		name: 'FormField'
	})
	
	const emit = defineEmits<{
		change: [data: UTSJSONObject]
		'update:modelValue': [value: any]
	}>()
	
	const props = defineProps({
		// 字段配置对象
		field: {
			type: Object as PropType<UTSJSONObject>,
			required: true
		},
		// 字段值
		modelValue: {
			default: null
		},
		// 标题样式类
		titleClass: {
			type: String,
			default: 'field-title-default'
		}
	})
	
	const fieldValue = ref(props.modelValue)
	const errorMessage = ref('')

	// 数据模型
	const dataModel = ref<UTSJSONObject>({})

	// 表单字段字数统计
	const fieldWords = ref<UTSJSONObject[]>([])

	// 验证码相关
	const resend = ref(true)
	const codeCountdown = ref(0)

	// 样式类
	const classes = computed(() => {
		return {
			'form-field': true,
			'form-field-required': props.field['Required'] as boolean,
			'form-field-disabled': props.field['Disabled'] as boolean,
			'form-field-readonly': props.field['Readonly'] as boolean
		}
	})

	// 标签单元格配置
	const labelCell = computed(() => {
		return {
			width: '120px',
			align: 'right'
		}
	})

	// 验证码按钮文本
	const getCodeText = computed(() => {
		if (codeCountdown.value > 0) {
			return `${codeCountdown.value}s后重发`
		}
		return '发送验证码'
	})
	
	// 判断是否为文本输入框
	const isTextInput = (field: UTSJSONObject): boolean => {
		const type = field['Type'] as string
		return type === FieldEditorType.Text || 
			   type === FieldEditorType.InputEditor || 
			   type === FieldEditorType.PhoneFormat ||
			   type === FieldEditorType.NumberInput
	}
	
	// 判断是否为文本域
	const isTextArea = (field: UTSJSONObject): boolean => {
		const type = field['Type'] as string
		return type === FieldEditorType.TextArea || type === FieldEditorType.SmallEditor
	}
	
	// 判断是否为开关
	const isSwitch = (field: UTSJSONObject): boolean => {
		const type = field['Type'] as string
		return type === FieldEditorType.Switch
	}
	
	// 判断是否为选择器
	const isSelect = (field: UTSJSONObject): boolean => {
		const type = field['Type'] as string
		return type === FieldEditorType.Select || type === FieldEditorType.Picker
	}
	
	// 判断是否为日期选择器
	const isDatePicker = (field: UTSJSONObject): boolean => {
		const type = field['Type'] as string
		return type === FieldEditorType.Date || 
			   type === FieldEditorType.DateTime ||
			   type === FieldEditorType.DateRange ||
			   type === FieldEditorType.DateTimeRange
	}
	
	// 判断是否为单选组
	const isRadioGroup = (field: UTSJSONObject): boolean => {
		const type = field['Type'] as string
		return type === FieldEditorType.RadioGroup || type === FieldEditorType.ButtonGroup
	}
	
	// 判断是否为多选组
	const isCheckboxGroup = (field: UTSJSONObject): boolean => {
		const type = field['Type'] as string
		return type === FieldEditorType.CheckboxGroup
	}
	
	// 判断是否为单个复选框
	const isCheckbox = (field: UTSJSONObject): boolean => {
		const type = field['Type'] as string
		return type === FieldEditorType.Checkbox
	}
	
	// 获取输入框类型
	const getInputType = (inputType: string): string => {
		return InputTypes[inputType as keyof typeof InputTypes] || 'text'
	}

	// 获取数据源
	const getDataSource = (field: UTSJSONObject): UTSJSONObject[] => {
		return getDataSourceOptions(field)
	}
	
	// 获取占位符
	const getPlaceholder = (field: UTSJSONObject): string => {
		const placeholder = field['Placeholder'] as string
		if (placeholder != null && placeholder != '') {
			return placeholder
		}
		
		const type = field['Type'] as string
		const title = field['Title'] as string || field['Name'] as string
		
		if (type === FieldEditorType.Select || type === FieldEditorType.Picker) {
			return '请选择' + title
		}
		
		return '请输入' + title
	}
	
	// 获取日期选择器模式
	const getDateMode = (field: UTSJSONObject): string => {
		const type = field['Type'] as string
		switch (type) {
			case FieldEditorType.Date:
				return 'date'
			case FieldEditorType.DateTime:
				return 'datetime'
			case FieldEditorType.DateRange:
				return 'daterange'
			case FieldEditorType.DateTimeRange:
				return 'datetimerange'
			default:
				return 'date'
		}
	}
	
	// 获取数据源选项
	const getDataSourceOptions = (field: UTSJSONObject): UTSJSONObject[] => {
		const dataSourceType = field['DataSourceType'] as string
		const dataSource = field['DataSource']
		const defaultDataSource = field['DefaultDataSource'] as string
		
		let options: UTSJSONObject[] = []
		
		if (dataSource != null) {
			if (typeof dataSource === 'string') {
				// 处理字符串数据源
				if (dataSourceType === 'Json') {
					try {
						options = JSON.parse(dataSource as string) as UTSJSONObject[]
					} catch (e) {
						// 如果解析失败，按逗号分割
						const items = (dataSource as string).split(',')
						options = items.map((item: string): UTSJSONObject => {
							return {
								id: item.trim(),
								text: item.trim()
							} as UTSJSONObject
						})
					}
				}
			} else if (dataSource instanceof Array) {
				options = dataSource as UTSJSONObject[]
			}
		} else if (defaultDataSource != null && defaultDataSource != '') {
			// 使用默认数据源
			const items = defaultDataSource.split(',')
			options = items.map((item: string): UTSJSONObject => {
				return {
					id: item.trim(),
					text: item.trim()
				} as UTSJSONObject
			})
		}
		
		// 确保选项有正确的格式
		return options.map((option: UTSJSONObject): UTSJSONObject => {
			if (option['text'] == null) {
				option['text'] = option['id'] as string
			}
			return option
		})
	}
	
	// 字段验证
	const validateField = (): boolean => {
		errorMessage.value = ''
		
		// 必填验证
		if (props.field['Required'] as boolean) {
			if (fieldValue.value == null || fieldValue.value === '' || 
				(Array.isArray(fieldValue.value) && fieldValue.value.length === 0)) {
				errorMessage.value = '请填写' + (props.field['Title'] as string || props.field['Name'] as string)
				return false
			}
		}
		
		// 长度验证
		const minLength = props.field['MinLength'] as number
		const maxLength = props.field['MaxLength'] as number
		const value = fieldValue.value as string
		
		if (value != null && typeof value === 'string') {
			if (minLength != null && minLength > 0 && value.length < minLength) {
				errorMessage.value = `最少输入${minLength}个字符`
				return false
			}
			if (maxLength != null && maxLength > 0 && value.length > maxLength) {
				errorMessage.value = `最多输入${maxLength}个字符`
				return false
			}
		}
		
		// 正则验证
		const validationType = props.field['ValidationType'] as string
		const validation = props.field['Validation'] as string
		const validMessage = props.field['ValidMesasge'] as string
		
		if (validationType === 'pattern' && validation != null && validation != '' && value != null) {
			const regex = new RegExp(validation)
			if (!regex.test(value)) {
				errorMessage.value = validMessage || '格式不正确'
				return false
			}
		}
		
		return true
	}
	
	// 选择器变化处理
	const handleSelectChange = (field: UTSJSONObject, event: any) => {
		const value = event.detail.value
		handleChange(field, value)
	}

	// 发送验证码
	const handleSendCode = (field: UTSJSONObject) => {
		if (!resend.value) return

		// 开始倒计时
		resend.value = false
		codeCountdown.value = 60

		const timer = setInterval(() => {
			codeCountdown.value--
			if (codeCountdown.value <= 0) {
				clearInterval(timer)
				resend.value = true
			}
		}, 1000)

		// 触发发送验证码事件
		emit('change', {
			field: field,
			action: 'sendCode',
			value: dataModel.value[field['Id'] as string]
		} as UTSJSONObject)
	}

	// 文本域字数统计
	const currentValue = (maxLength: number, fieldId: string) => {
		const value = dataModel.value[fieldId] as string || ''
		const remaining = maxLength - value.length

		// 更新字数统计
		const existingIndex = fieldWords.value.findIndex(item => item['id'] === fieldId)
		const wordInfo = {
			id: fieldId,
			words: remaining
		} as UTSJSONObject

		if (existingIndex >= 0) {
			fieldWords.value[existingIndex] = wordInfo
		} else {
			fieldWords.value.push(wordInfo)
		}
	}

	// 字段值变化处理
	const handleChange = (field: UTSJSONObject, value?: any) => {
		// 如果没有传入value，从事件中获取
		if (value === undefined) {
			value = field
			field = props.field
		}

		fieldValue.value = value
		dataModel.value[field['Id'] as string] = value

		// 验证字段
		const isValid = validateField()

		// 触发change事件
		emit('change', {
			field: field,
			value: value,
			isValid: isValid,
			errorMessage: errorMessage.value
		} as UTSJSONObject)

		// 更新v-model
		emit('update:modelValue', value)
	}
	
	// 初始化数据模型
	const initDataModel = () => {
		const fieldId = props.field['Id'] as string
		if (fieldId) {
			dataModel.value[fieldId] = props.modelValue
		}
	}

	// 监听外部值变化
	watch(() => props.modelValue, (newVal: any) => {
		fieldValue.value = newVal
		const fieldId = props.field['Id'] as string
		if (fieldId) {
			dataModel.value[fieldId] = newVal
		}
	})

	// 监听字段配置变化
	watch(() => props.field, () => {
		initDataModel()
	}, { immediate: true })

	// 组件挂载时初始化
	onMounted(() => {
		initDataModel()
	})
	
	// 暴露验证方法
	defineExpose({
		validate: validateField,
		clearError: () => {
			errorMessage.value = ''
		}
	})
</script>

<style lang="scss">
	/* 使用autocss语法，减少自定义样式 */
	.form-field-content {
		position: relative;
	}

	.form-item-required {
		position: relative;
	}

	/* 兼容性样式 */
	.green {
		color: #52c41a;
	}

	.red {
		color: #ff4d4f;
	}

	.c-success {
		color: #52c41a;
	}

	.c-error {
		color: #ff4d4f;
	}

	.c-333 {
		color: #333333;
	}

	.c-666 {
		color: #666666;
	}

	.c-red {
		color: #ff4d4f;
	}

	.bg-red-10 {
		background-color: rgba(255, 77, 79, 0.1);
	}

	.border-red {
		border-color: #ff4d4f;
	}

	.border-l-4 {
		border-left-width: 4px;
		border-left-style: solid;
	}
</style>