<template>
	<view class="ux-col" :class="[flex?'ux-col-flex' : `ux-col-${span}`]" :style="[style, xstyle]" @click="click">
		<slot></slot>
	</view>
</template>

<script setup>
	/**
	 * Col 垂直布局 水平12分栏
	 * @demo pages/component/layout.uvue
	 * @tutorial https://www.uxframe.cn/component/col.html
	 * @property {Boolean}		flex										Boolean | 弹性盒子 (默认 true)
	 * @property {Number}		span										Number | 栅格占据的列数 总12等份 flex=false时生效 (默认 12 ) 
	 * @property {Any}			offset										Any | 分栏左边偏移 (默认 0 ) 
	 * @property {Any}			gutter										Any | 栅格间隔 (默认 0 )
	 * @property {String}		align = [left|right|center|around|between]	String | 水平排列方式 (默认 left ) 
	 * @value left 左对齐
	 * @value right 右对齐
	 * @value center 居中
	 * @value around 间隔相等
	 * @value between 两端对齐
	 * @property {String}		justify = [top|center|bottom|stretch]		String | 垂直对齐方式 (默认 center )
	 * @value top 上对齐
	 * @value center 居中
	 * @value bottom 下对齐
	 * @value stretch 填充
	 * @property {Array}		margin										Array | 边距 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}			mt											Any | 距上 单位px
	 * @property {Any}			mr											Any | 距右 单位px
	 * @property {Any}			mb											Any | 距下 单位px
	 * @property {Any}			ml											Any | 距左 单位px
	 * @property {Array}		padding										Array | 填充 [上 右 下 左] [上下 左右] [上下左右]
	 * @property {Any}			pt											Any | 上内边距 单位px
	 * @property {Any}			pr											Any | 右内边距 单位px
	 * @property {Any}			pb											Any | 下内边距 单位px
	 * @property {Any}			pl											Any | 左内边距 单位px
	 * @property {Array}		xstyle										Array<any> | 自定义样式
	 * @event {Function}		click										Function | 被点击时触发
	 * <AUTHOR>
	 * @date 2023-11-04 01:39:22
	 */
	
	import { $ux } from '../../index'
	import { xstyleMixin } from '../../libs/mixins/xstyle.uts'
	
	defineOptions({
		name: 'ux-col',
		mixins: [xstyleMixin]
	})
	
	const emit = defineEmits(['click'])
	
	const props = defineProps({
		flex: {
			type: Boolean,
			default: true
		},
		span: {
			type: Number,
			default: 12
		},
		offset: {
			default: 0
		},
		gutter: {
			default: 0
		},
		justify: {
			type: String,
			default: 'center'
		},
		align: {
			type: String,
			default: 'left'
		},
	})
	
	const justify = computed(() : string => {
		if (props.justify == 'left' || props.justify == 'start') {
			return 'flex-start'
		} else if (props.justify == 'right' || props.justify == 'end') {
			return 'flex-end'
		} else if (props.justify == 'around' || props.justify == 'between') {
			return `space-${props.justify}`
		}
		
		return props.justify
	})
	
	const alignItem = computed(() : string => {
		if (props.align == 'top' || props.align == 'start') {
			return 'flex-start'
		} else if (props.align == 'bottom' || props.align == 'end') {
			return 'flex-end'
		} else if (props.align == 'around' || props.align == 'between') {
			return `space-${props.align}`
		}
		
		return props.align
	})
	
	const style = computed(() : Map<string, any> => {
		let css = new Map<string, any>()
		
		css.set('justify-content', justify.value)
		css.set('align-items', alignItem.value)
		
		if ($ux.Util.getPx(props.offset) > 0) {
			css.set('margin-left', `${100 / 12 * $ux.Util.getPx(props.offset)}%`)
		}
		
		if ($ux.Util.getPx(props.gutter) > 0) {
			css.set('padding-left', `${$ux.Util.getPx(props.gutter) / 2}px`)
			css.set('padding-right', `${$ux.Util.getPx(props.gutter) / 2}px`)
		}
		
		css = $ux.Util.xStyle(css, props.margin, props.mt, props.mr, props.mb, props.ml, props.padding, props.pt, props.pr, props.pb, props.pl)
		
		return css
	})
	
	function click(e: MouseEvent) {
		emit('click', e)
	}
</script>

<style lang="scss">
	.ux-col {
		display: flex;
	}
	
	.ux-col-flex {
		flex: 1;
		/* #ifdef MP */
		width: 100%;
		/* #endif */
	}

	.ux-col-0 {
		width: 0;
	}

	.ux-col-1 {
		width: calc(100%/12);
	}

	.ux-col-2 {
		width: calc(100%/12 * 2);
	}

	.ux-col-3 {
		width: calc(100%/12 * 3);
	}

	.ux-col-4 {
		width: calc(100%/12 * 4);
	}

	.ux-col-5 {
		width: calc(100%/12 * 5);
	}

	.ux-col-6 {
		width: calc(100%/12 * 6);
	}

	.ux-col-7 {
		width: calc(100%/12 * 7);
	}

	.ux-col-8 {
		width: calc(100%/12 * 8);
	}

	.ux-col-9 {
		width: calc(100%/12 * 9);
	}

	.ux-col-10 {
		width: calc(100%/12 * 10);
	}

	.ux-col-11 {
		width: calc(100%/12 * 11);
	}

	.ux-col-12 {
		width: calc(100%/12 * 12);
	}
</style>